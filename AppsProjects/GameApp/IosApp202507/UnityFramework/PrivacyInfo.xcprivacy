<?xml version="1.0" encoding="utf-8"?>
<plist version="1.0">
  <dict>
    <key>NSPrivacyAccessedAPITypes</key>
    <array>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategorySystemBootTime</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>35F9.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryDiskSpace</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>E174.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryUserDefaults</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>CA92.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>0A2A.1</string>
          <string>C617.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>C617.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryUserDefaults</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array />
      </dict>
    </array>
    <key>NSPrivacyCollectedDataTypes</key>
    <array>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeDeviceID</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <false />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeThirdPartyAdvertising</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <true />
      </dict>
      <dict>
        <key>New item</key>
        <string></string>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypePerformanceData</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <false />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeUserID</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeDeviceID</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <true />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeCoarseLocation</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypePurchaseHistory</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <true />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeProductInteraction</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeAdvertisingData</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
          <string>NSPrivacyCollectedDataTypePurposeThirdPartyAdvertising</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypePerformanceData</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeOtherDiagnosticData</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
      <dict>
        <key>NSPrivacyCollectedDataType</key>
        <string>NSPrivacyCollectedDataTypeOtherDataTypes</string>
        <key>NSPrivacyCollectedDataTypeLinked</key>
        <true />
        <key>NSPrivacyCollectedDataTypePurposes</key>
        <array>
          <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
        </array>
        <key>NSPrivacyCollectedDataTypeTracking</key>
        <false />
      </dict>
    </array>
    <key>NSPrivacyTracking</key>
    <true />
  </dict>
</plist>
