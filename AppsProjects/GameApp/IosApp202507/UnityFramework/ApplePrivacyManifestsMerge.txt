Adding elements from Unity
	NSPrivacyAccessedAPITypes:
		0:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategorySystemBootTime
			NSPrivacyAccessedAPITypeReasons:
				0:
					35F9.1
		1:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryDiskSpace
			NSPrivacyAccessedAPITypeReasons:
				0:
					E174.1
		2:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryUserDefaults
			NSPrivacyAccessedAPITypeReasons:
				0:
					CA92.1
		3:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryFileTimestamp
			NSPrivacyAccessedAPITypeReasons:
				0:
					0A2A.1
				1:
					C617.1

--------------------------------------------------

Adding elements from Assets/Plugins/iOS/GdtMob/PrivacyInfo.xcprivacy
	NSPrivacyCollectedDataTypes:
		0:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeDeviceID
			NSPrivacyCollectedDataTypeLinked:
				False
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeThirdPartyAdvertising
			NSPrivacyCollectedDataTypeTracking:
				True
		1:
			New item:
				
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypePerformanceData
			NSPrivacyCollectedDataTypeLinked:
				False
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAppFunctionality
			NSPrivacyCollectedDataTypeTracking:
				False

	NSPrivacyAccessedAPITypes:
		0:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategorySystemBootTime
			NSPrivacyAccessedAPITypeReasons:
				0:
					35F9.1
		1:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryDiskSpace
			NSPrivacyAccessedAPITypeReasons:
				0:
					E174.1
		2:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryFileTimestamp
			NSPrivacyAccessedAPITypeReasons:
				0:
					C617.1
		3:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryUserDefaults
			NSPrivacyAccessedAPITypeReasons:
				<empty>

	NSPrivacyTracking:
		True

--------------------------------------------------

Adding elements from Library/PackageCache/com.unity.services.analytics@6.0.3/Runtime/PrivacyInfo.xcprivacy
	NSPrivacyCollectedDataTypes:
		0:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeUserID
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				False
		1:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeDeviceID
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				True
		2:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeCoarseLocation
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				False
		3:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypePurchaseHistory
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				True
		4:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeProductInteraction
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				False
		5:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeAdvertisingData
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
				1:
					NSPrivacyCollectedDataTypePurposeThirdPartyAdvertising
			NSPrivacyCollectedDataTypeTracking:
				False
		6:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypePerformanceData
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				False
		7:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeOtherDiagnosticData
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				False
		8:
			NSPrivacyCollectedDataType:
				NSPrivacyCollectedDataTypeOtherDataTypes
			NSPrivacyCollectedDataTypeLinked:
				True
			NSPrivacyCollectedDataTypePurposes:
				0:
					NSPrivacyCollectedDataTypePurposeAnalytics
			NSPrivacyCollectedDataTypeTracking:
				False

	NSPrivacyTracking:
		False

--------------------------------------------------

Adding elements from Library/PackageCache/com.unity.services.core@1.14.0/Runtime/PrivacyInfo.xcprivacy
	NSPrivacyAccessedAPITypes:
		0:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryUserDefaults
			NSPrivacyAccessedAPITypeReasons:
				0:
					CA92.1

	NSPrivacyTracking:
		False

--------------------------------------------------

Adding elements from Library/PackageCache/com.unity.purchasing@4.11.0/Plugins/UnityPurchasing/iOS/PrivacyInfo.xcprivacy
	NSPrivacyAccessedAPITypes:
		0:
			NSPrivacyAccessedAPIType:
				NSPrivacyAccessedAPICategoryFileTimestamp
			NSPrivacyAccessedAPITypeReasons:
				0:
					C617.1

	NSPrivacyTracking:
		False

