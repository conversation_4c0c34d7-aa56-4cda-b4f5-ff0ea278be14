//
//  IAppsTeamIOSUnitl.h
//  Unity-iPhone
//
//  Created by pwc on 12-12-18.
//
//

#import <Foundation/Foundation.h>
//#import "HJObjManager.h"
#import <StoreKit/StoreKit.h>
#import "AVFoundation/AVFoundation.h"

#define UMeng_APPKEY @""

@interface IAppsTeamIOSUnitl : NSObject 
{
    NSString *iap_ProductId;
    NSString *iap_SettingId;
    NSString *iap_flag;
    NSString *ourAdsAppId;
    NSString *ourAdsData;
    NSString *languageUserFlag;
    NSArray  *adsIds;
    NSTimer *myTimer;
    
    int ourAdsCount, currentAdsId, ourAdsVersion, countSecond, ourAdsRefresh;
    //HJObjManager* imgMan;
    float ourAds_pos_x, ourAds_pos_y, ourAds_width, ourAds_height;
    BOOL canAdsRefresh;
}

@property(nonatomic, copy) NSString *iap_ProductId;
@property(nonatomic, copy) NSString *iap_SettingId;
@property(nonatomic, copy) NSString *iap_flag;
@property (nonatomic, copy) NSString *languageUserFlag;


//@property (nonatomic, retain) HJObjManager* imgMan;

+ (IAppsTeamIOSUnitl*)shared;


- (BOOL)checkContentUnLock:(NSString*)productIdentifier;
- (NSString *)checkUserOSLanguage;
- (void)mobClick_beginEvent:(NSString*)eventname;
- (void)mobClick_endEvent:(NSString*)eventname;
- (void)mobClick_event:(NSString*)eventname;
- (void)umeng_updateOnlineConfig;
- (BOOL)connectedToNetwork;
- (void)showFeedBack;
- (float)getSystemVersion;
- (NSString *)getConfigParams:(NSString*)keyname;
- (void)loadOurAdsData:(float)pos_x pos_y:(float)pos_y width:(float)width height:(float)height;
- (void)removeOurAds;
- (void)createOurAdsInterstitial:(NSString*)imgUrl isGetLocal:(bool) getLocal;
- (void)removeOurAdsInterstitial;
- (void)addCodeCont:(NSString*)key_name;
- (void)addCode:(SKProduct*)product;
- (void)requestReview;
- (BOOL)isIphoneX;
- (void)unityPause:(bool)flag;
- (void)setAudioSessionCategory;
- (BOOL)isIpad;
- (BOOL)isViaWiFi;
- (NSString *)webviewAdjustedRect:(float)top left:(float)left bottom:(float)bottom right:(float)right
point_x:(float)point_x point_y:(float)point_y width:(float)width height:(float)height;
- (BOOL)checkIpadHasHomeIndicator;
@end
