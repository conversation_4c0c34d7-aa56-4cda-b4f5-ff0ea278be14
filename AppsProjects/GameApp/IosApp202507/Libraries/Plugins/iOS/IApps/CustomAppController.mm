#import "UnityAppController.h"
#import <StoreKit/StoreKit.h>
#import "RSSecrets.h"
#import "IAppsTeamIOSUnitl.h"
 
@interface CustomAppController : UnityAppController <SKPaymentTransactionObserver>
{
  
    SKProduct *_skProudct;
    
}



@end
 
IMPL_APP_CONTROLLER_SUBCLASS (CustomAppController)
 
@implementation CustomAppController
 
- (BOOL)application:(UIApplication*)application didFinishLaunchingWithOptions:(NSDictionary*)launchOptions
{
    [super application:application didFinishLaunchingWithOptions:launchOptions];
 
    [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
    [IAppsTeamIOSUnitl shared].iap_ProductId = @"LearnEnglishWordFullVersion";
    [IAppsTeamIOSUnitl shared].iap_SettingId = @"ddciyienlshidal";
    [IAppsTeamIOSUnitl shared].iap_flag = @"-AOOBB22ON";
           
    [RSSecrets removeKey:[IAppsTeamIOSUnitl shared].iap_SettingId];

    NSLog(@"CustomAppController: didFinishLaunchingWithOptions!");
    
    return YES;
}

#pragma mark Handle SKPaymentQueueObserver Events
//itms-services://?action=purchaseIntent&bundleId=com.iappsteam.ECard1Soft&productIdentifier=LearnEnglishWordFullVersion
- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions {
    
    /*
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle: @"" message:@"buy ok" delegate:self cancelButtonTitle:_(@"NewMessage_cancelButton") otherButtonTitles: _(@"NewMessage_OKBtn"), nil];
    [alertView show];
    */
    
    for (SKPaymentTransaction *transaction in transactions) {
        
        switch (transaction.transactionState) {
            case SKPaymentTransactionStatePurchased:
                NSLog(@"SKPaymentTransactionStatePurchased");
                [[IAppsTeamIOSUnitl shared] addCode:_skProudct];
                
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
                break;
            case SKPaymentTransactionStateRestored:
                NSLog(@"SKPaymentTransactionStateRestored");
                [[IAppsTeamIOSUnitl shared] addCode:_skProudct];
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
                break;
            case SKPaymentTransactionStateFailed:
                NSLog(@"SKPaymentTransactionStateFailed");
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
                break;
            case SKPaymentTransactionStateDeferred:
                NSLog(@"SKPaymentTransactionStateDeferred");
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
                break;
            default:
                break;
        }
    }
}

- (BOOL)paymentQueue:(SKPaymentQueue *)queue
shouldAddStorePayment:(SKPayment *)payment
          forProduct:(SKProduct *)product;
{
    NSLog(@"shouldAddStorePayment");
    
    _skProudct = product;
    return true;
}


@end


