//
//  IAppsTeamIOSUntilBinding.m
//  Unity-iPhone
//
//  Created by pwc on 12-12-18.
//
//

#import "IAppsTeamIOSUnitl.h"

// Converts NSString to C style string by way of copy (<PERSON><PERSON> will free it)
#define MakeStringCopy( _x_ ) ( _x_ != NULL && [_x_ isKindOfClass:[NSString class]] ) ? strdup( [_x_ UTF8String] ) : NULL

// Converts C style string to NSString
#define GetStringParam( _x_ ) ( _x_ != NULL ) ? [NSString stringWithUTF8String:_x_] : [NSString stringWithUTF8String:""]



bool _iappsteamIOSCheckContentUnLock( const char *productIdentifier )
{
    NSString *identifier = GetStringParam( productIdentifier );
    return ( [[IAppsTeamIOSUnitl shared] checkContentUnLock:identifier] == 1 );
}


const char * _checkUserOSLanguage()
{
	return MakeStringCopy( [[IAppsTeamIOSUnitl shared] checkUserOSLanguage] );
}

void _mobClick_beginEvent(const char *event_name)
{
    NSString *eventName = GetStringParam(event_name);
    [[IAppsTeamIOSUnitl shared] mobClick_beginEvent:eventName];
}

void _mobClick_endEvent(const char *event_name)
{
    NSString *eventName = GetStringParam(event_name);
    [[IAppsTeamIOSUnitl shared] mobClick_endEvent:eventName];
}

void _mobClick_event(const char *event_name)
{
    NSString *eventName = GetStringParam(event_name);
    [[IAppsTeamIOSUnitl shared] mobClick_event:eventName];
}

void _umeng_updateOnlineConfig()
{
    [[IAppsTeamIOSUnitl shared] umeng_updateOnlineConfig];
}

bool _isIpad()
{
    if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad)
	{
        NSLog(@"----this is iphone----");
        return true;
    }
    else
    {
        NSLog(@"----this is ipad----y");
        return false;
    }
}

bool _connectedToNetwork()
{
    return [[IAppsTeamIOSUnitl shared] connectedToNetwork];
}

void _showFeedBack()
{
    [[IAppsTeamIOSUnitl shared] showFeedBack];
}

float _systemVersion()
{
    return [[IAppsTeamIOSUnitl shared] getSystemVersion];
}

const char * _getConfigParams(const char *key_name)
{
    NSString *keyName = GetStringParam(key_name);
	return MakeStringCopy( [[IAppsTeamIOSUnitl shared]  getConfigParams:keyName] );
}

void _loadOurAdsData(float x, float y, float w, float h)
{
    [[IAppsTeamIOSUnitl shared] loadOurAdsData:x pos_y:y width:w height:h];
}

void _removeOurAds()
{
    [[IAppsTeamIOSUnitl shared] removeOurAds];
}

void _getLanguageUserFlag(const char *languageUserFlag)
{
    NSString *languageFlag = GetStringParam(languageUserFlag);
    [IAppsTeamIOSUnitl shared].languageUserFlag = languageFlag;
    
}

void _removeOurAdsInterstitial()
{
    [[IAppsTeamIOSUnitl shared] removeOurAdsInterstitial];
}

void _createOurAdsInterstitial(const char *imgUrl, bool isGetLocal)
{
    NSString *url = GetStringParam(imgUrl);
    [[IAppsTeamIOSUnitl shared] createOurAdsInterstitial:url isGetLocal:isGetLocal];
}

void _addCode(const char *key_name)
{
    [[IAppsTeamIOSUnitl shared] addCodeCont:GetStringParam(key_name)];
}

void _openAppSetting()
{
    if( [[UIDevice currentDevice].systemVersion floatValue] >= 8.0)
    {
        [[UIApplication sharedApplication]openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
    }
}

bool _isIphoneX()
{
    return [[IAppsTeamIOSUnitl shared] isIphoneX];
}

void _requestReview()
{
    [[IAppsTeamIOSUnitl shared] requestReview];
}

const char * _webviewAdjustedRect(float top, float left, float bottom, float right,
                                  float point_x, float point_y, float width, float height)
{
    return MakeStringCopy( [[IAppsTeamIOSUnitl shared]  webviewAdjustedRect:top left:left bottom:bottom right:right point_x:point_x point_y:point_y width:width height:height] );
}

bool _isViaWiFi()
{
    return [[IAppsTeamIOSUnitl shared] isViaWiFi];
}

bool _checkIpadHasHomeIndicator()
{
    return [[IAppsTeamIOSUnitl shared] checkIpadHasHomeIndicator];
}
