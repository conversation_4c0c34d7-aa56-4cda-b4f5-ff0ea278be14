//
//  GDTMobUTypes.h
//  Unity-iPhone
//
//  Created by Peng_F1 on 2017/10/2.
//

/// Positions to place an ad.
typedef NS_ENUM(NSUInteger, GDTNativeAdPosition) {
    kGDTNativeAdPositionCustom = -1,              ///< Custom ad position.
    kGDTNativeAdPositionTopOfScreen = 0,          ///< Top of screen.
    kGDTNativeAdPositionBottomOfScreen = 1,       ///< Bottom of screen.
    kGDTNativeAdPositionTopLeftOfScreen = 2,      ///< Top left of screen.
    kGDTNativeAdPositionTopRightOfScreen = 3,     ///< Top right of screen.
    kGDTNativeAdPositionBottomLeftOfScreen = 4,   ///< Bottom left of screen.
    kGDTNativeAdPositionBottomRightOfScreen = 5,  ///< Bottom right of screen.
    kGDTNativeAdPositionCenterOfScreen = 6        ///< Bottom right of screen.
};
