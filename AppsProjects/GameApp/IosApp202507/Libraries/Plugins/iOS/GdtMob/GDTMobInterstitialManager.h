#import <Foundation/Foundation.h>
#import "GDTMobInterstitial.h"
#import "GDTUnifiedInterstitialAd.h"

@interface GDTMobInterstitialManager: NSObject <GDTUnifiedInterstitialAdDelegate>
@property (nonatomic, retain) NSString *appKey;
@property (nonatomic, retain) NSString *placementId;
@property (nonatomic, retain) GDTUnifiedInterstitialAd *interstitialView;

+ (GDTMobInterstitialManager*)sharedManager;
- (void)requestInterstitalAd;
- (void)showInterstitialAd;
@end
