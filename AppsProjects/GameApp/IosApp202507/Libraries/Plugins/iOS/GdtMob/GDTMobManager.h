
#import <Foundation/Foundation.h>
#import "GDTUnifiedBannerView.h"

typedef enum
{
    GDTMobAdPositionTopLeft,
    GDTMobAdPositionTopCenter,
    GDTMobAdPositionTopRight,
    GDTMobAdPositionCentered,
    GDTMobAdPositionBottomLeft,
    GDTMobAdPositionBottomCenter,
    GDTMobAdPositionBottomRight
} GDTMobAdPosition;


/*
* banner条广告，广点通提供如下3中尺寸供开发者使用
* 320*50 适用于iPhone
* 468*60、728*90适用于iPad
* banner条的宽度开发者可以进行手动设置，用以满足开发场景需求或是适配最新版本的iphone
* banner条的高度广点通侧强烈建议开发者采用推荐的高度，否则显示效果会有影响
 */
typedef enum
{
    GDTMobBannerType_320x50,
    GDTMobBannerType_468x60,
    GDTMobBannerType_728x90,
} GDTMobBannerType;


@interface GDTMobManager : NSObject <GDTUnifiedBannerViewDelegate>

//@property (nonatomic, retain) GDTMobBannerView* sharedAdView;
@property (nonatomic, retain) NSString *appKey;
@property (nonatomic, retain) NSString *placementId;
@property (nonatomic) GDTMobAdPosition bannerPosition;
@property (nonatomic, retain) GDTUnifiedBannerView *sharedAdView;

+ (GDTMobManager*)sharedManager;
- (void)createBanner:(GDTMobBannerType)bannerType withPosition:(GDTMobAdPosition)position;
- (void)destroyBanner;
- (void)hiddenBanner;
- (void)showBanner;
@end
