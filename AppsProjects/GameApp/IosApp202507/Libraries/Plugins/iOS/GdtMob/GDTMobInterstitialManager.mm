#import "GDTMobInterstitialManager.h"
#import "GDTMobInterstitial.h"
#import "GDTUnifiedInterstitialAd.h"
#import "UnityAppController.h"

void UnitySendMessage( const char * className, const char * methodName, const char * param );


UIViewController *UnityGetGLViewController();

static GDTMobInterstitialManager *sharedSingleton = nil;
static NSString *INTERSTITIAL_STATE_TEXT = @"GDT_插屏状态";

@implementation GDTMobInterstitialManager

@synthesize appKey = _appKey,interstitialView = _interstitialView;

+ (GDTMobInterstitialManager*)sharedManager
{
    
    if( !sharedSingleton )
        sharedSingleton = [[GDTMobInterstitialManager alloc] init];
    
    return sharedSingleton;
}


- (void)requestInterstitalAd
{
    if (_interstitialView) {
        _interstitialView.delegate = nil;
    }
    
    NSLog( @"GDTMob requestInterstitalAd" );
    _interstitialView = [[GDTUnifiedInterstitialAd alloc] initWithPlacementId:self.placementId];
    _interstitialView.delegate = self; //设置委托
    
    //预加载广告
    //[_interstitialView loadAd];
    [_interstitialView loadFullScreenAd];
}

- (void)showInterstitialAd
{
    /*
    if (_interstitialView.isReady) {
        [_interstitialView presentFromRootViewController:UnityGetGLViewController()];
    }else {
        NSLog(@"ad not ready");
    }
    */
    //if(_interstitialView.isAdValid)
       //[_interstitialView presentAdFromRootViewController:UnityGetGLViewController()];
    /*
    if(_interstitialView != NULL) {
           UIViewController *vc =  UnityGetGLViewController();
           //[_interstitialView presentAdFromRootViewController:vc];
        [_interstitialView presentAdFromRootViewController:vc];
       }*/
    if ([_interstitialView isAdValid]) {
        //[_interstitialView presentAdFromRootViewController:UnityGetGLViewController()];
        //presentFullScreenAdFromRootViewController
        
        [_interstitialView presentFullScreenAdFromRootViewController:UnityGetGLViewController()];
    }
    else {
        NSLog(@"广告数据无效");
    }
}


/**
 *  广告预加载成功回调
 *  详解:当接收服务器返回的广告数据成功后调用该函数
 */
/*
- (void)interstitialSuccessToLoadAd:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Success Loaded.");
}

/**
 *  广告预加载失败回调
 *  详解:当接收服务器返回的广告数据失败后调用该函数
 */
/*
- (void)interstitialFailToLoadAd:(GDTMobInterstitial *)interstitial error:(NSError *)error
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Fail Loaded." );
    UnitySendMessage( "BannerControl", "GdtInterstitialFailToLoadAd", "" );
}
 */
/**
 *  插屏广告将要展示回调
 *  详解: 插屏广告即将展示回调该函数
 */
/*
- (void)interstitialWillPresentScreen:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Going to present.");
    UnityPause( 1 );
}
*/
/**
 *  插屏广告视图展示成功回调
 *  详解: 插屏广告展示成功回调该函数
 */
/*
- (void)interstitialDidPresentScreen:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Success Presented." );
}

/**
 *  插屏广告展示结束回调
 *  详解: 插屏广告展示结束回调该函数

- (void)interstitialDidDismissScreen:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Finish Presented.");
    UnityPause( 0 );
}
 */
/**
 *  应用进入后台时回调
 *  详解: 当点击下载应用时会调用系统程序打开，应用切换到后台

- (void)interstitialApplicationWillEnterBackground:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Application enter background.");
}
 */
/**
 *  插屏广告曝光时回调
 *  详解: 插屏广告曝光时回调

-(void)interstitialWillExposure:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Exposured");
}
  */
/**
 *  插屏广告点击时回调
 *  详解: 插屏广告点击时回调
 */
/*
-(void)interstitialClicked:(GDTMobInterstitial *)interstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Clicked");
}
 */

#pragma mark - GDTUnifiedInterstitialAdDelegate

/**
 *  插屏2.0广告预加载成功回调
 *  当接收服务器返回的广告数据成功后调用该函数
 */- (void)unifiedInterstitialSuccessToLoadAd:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%@", [NSString stringWithFormat:@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Load Success." ]);
}

/**
 *  插屏2.0广告预加载失败回调
 *  当接收服务器返回的广告数据失败后调用该函数
 */
- (void)unifiedInterstitialFailToLoadAd:(GDTUnifiedInterstitialAd *)unifiedInterstitial error:(NSError *)error
{
    NSLog(@"%@", [NSString stringWithFormat:@"%@:%@,Error : %@",INTERSTITIAL_STATE_TEXT,@"Fail Loaded.",error ]);
    NSLog(@"interstitial fail to load, Error : %@",error);
    
    UnitySendMessage( "BannerControl", "GdtInterstitialFailToLoadAd", "" );
}

/**
 *  插屏2.0广告将要展示回调
 *  插屏2.0广告即将展示回调该函数
 */
- (void)unifiedInterstitialWillPresentScreen:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Going to present.");
    //if(unifiedInterstitial.isAdValid) UnityPause( 1 );
    
}

/**
 *  插屏2.0广告视图展示成功回调
 *  插屏2.0广告展示成功回调该函数
 */
- (void)unifiedInterstitialDidPresentScreen:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    //self.interstitialStateLabel.text = [NSString stringWithFormat:@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Success Presented." ];
    
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Success Presented." );
}

/**
 *  插屏2.0广告展示结束回调
 *  插屏2.0广告展示结束回调该函数
 */
- (void)unifiedInterstitialDidDismissScreen:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    //self.interstitialStateLabel.text = [NSString stringWithFormat:@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Finish Presented." ];
    
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Finish Presented.");
    //UnityPause( 0 );
}

/**
 *  当点击下载应用时会调用系统程序打开
 */
- (void)unifiedInterstitialWillLeaveApplication:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    //self.interstitialStateLabel.text = [NSString stringWithFormat:@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Application enter background." ];
    
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"Application enter background.");
}

/**
 *  插屏2.0广告曝光回调
 */
- (void)unifiedInterstitialWillExposure:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%s",__FUNCTION__);
}

/**
 *  插屏2.0广告点击回调
 */
- (void)unifiedInterstitialClicked:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%s",__FUNCTION__);
    
    UnitySendMessage( "BannerControl", "GDT_UnifiedInterstitialClicked", "" );
}

/**
 *  点击插屏2.0广告以后即将弹出全屏广告页
 */
- (void)unifiedInterstitialAdWillPresentFullScreenModal:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%s",__FUNCTION__);
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"WillPresentFullScreenModal.");
    //UnityPause( 1 );
}

/**
 *  点击插屏2.0广告以后弹出全屏广告页
 */
- (void)unifiedInterstitialAdDidPresentFullScreenModal:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%s",__FUNCTION__);
}

/**
 *  全屏广告页将要关闭
 */
- (void)unifiedInterstitialAdWillDismissFullScreenModal:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%s",__FUNCTION__);
}

/**
 *  全屏广告页被关闭
 */
- (void)unifiedInterstitialAdDidDismissFullScreenModal:(GDTUnifiedInterstitialAd *)unifiedInterstitial
{
    NSLog(@"%s",__FUNCTION__);
    
    NSLog(@"%@:%@",INTERSTITIAL_STATE_TEXT,@"DidDismissFullScreenModal.");
    //UnityPause( 0 );
}


@end

