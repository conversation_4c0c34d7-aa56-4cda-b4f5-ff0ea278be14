//
//  GDTMobBing.m
//  Unity-iPhone
//
//  Created by pwc on 15/5/25.
//
//GDT Lib v4.7.1
#import "GDTMobManager.h"
#import "GDTMobInterstitialManager.h"
#import "GDTSDKConfig.h"

// Converts C style string to NSString
#define GetStringParam( _x_ ) ( _x_ != NULL ) ? [NSString stringWithUTF8String:_x_] : [NSString stringWithUTF8String:""]

// Converts C style string to NSString as long as it isnt empty
#define GetStringParamOrNil( _x_ ) ( _x_ != NULL && strlen( _x_ ) ) ? [NSString stringWithUTF8String:_x_] : nil



// Sets the publiser Id and prepares AdMob for action.  Must be called before any other methods!
void _gdtMobInit( const char * appKey, const char * placementId)
{
    BOOL result = [GDTSDKConfig registerAppId:GetStringParam( appKey )];
   
    if (result) {
        NSLog(@"注册成功");
    }

    //14：AppStore
    [GDTSDKConfig setChannel:14];
    [GDTSDKConfig enableDefaultAudioSessionSetting:NO];
    
     [GDTMobManager sharedManager].appKey = GetStringParam( appKey );
     [GDTMobManager sharedManager].placementId = GetStringParam( placementId );
}

void _gdtMobCreateBanner( int bannerType, int bannerPosition )
{
    GDTMobBannerType type = (GDTMobBannerType)bannerType;
    GDTMobAdPosition position = (GDTMobAdPosition)bannerPosition;
    
    [[GDTMobManager sharedManager] createBanner:type withPosition:position];
}

// Destroys the banner and removes it from view
void _gdtMobDestroyBanner()
{
    [[GDTMobManager sharedManager] destroyBanner];
}

void _gdtMobHiddenBanner()
{
    [[GDTMobManager sharedManager] hiddenBanner];
}

void _gdtMobShowBanner()
{
    [[GDTMobManager sharedManager] showBanner];
}

// Starts loading an interstitial ad
void _gdtMobRequestInterstitalAd( const char * appKey, const char * placementId)
{
    [GDTSDKConfig registerAppId:GetStringParam( appKey )];
    //14：AppStore
    [GDTSDKConfig setChannel:14];
    [GDTSDKConfig enableDefaultAudioSessionSetting:NO];
    
    
    [GDTMobInterstitialManager sharedManager].appKey = GetStringParam( appKey );
    [GDTMobInterstitialManager sharedManager].placementId = GetStringParam( placementId );

    
    [[GDTMobInterstitialManager sharedManager] requestInterstitalAd];
}


// If an interstitial ad is loaded this will take over the screen and show the ad
void _gdtMobShowInterstitialAd()
{
    [[GDTMobInterstitialManager sharedManager] showInterstitialAd];
}




