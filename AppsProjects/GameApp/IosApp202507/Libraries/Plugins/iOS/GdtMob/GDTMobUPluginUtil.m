//
//  GDTMobUPluginUtil.m
//  Unity-iPhone
//
//  Created by Peng_F1 on 2017/10/2.
//

#import "GDTMobUPluginUtil.h"

#import "UnityAppController.h"

@implementation GDTMobUPluginUtil

+ (UIViewController *)unityGLViewController {
    return ((UnityAppController *)[UIApplication sharedApplication].delegate).rootViewController;
}

+ (void)positionView:(UIView *)view
      inParentBounds:(CGRect)parentBounds
          adPosition:(GDTNativeAdPosition)adPosition {
    CGPoint center = CGPointMake(CGRectGetMidX(parentBounds), CGRectGetMidY(view.bounds));
    switch (adPosition) {

        case kGDTNativeAdPositionTopOfScreen:
            center = CGPointMake(CGRectGetMidX(parentBounds), CGRectGetMidY(view.bounds));
            break;
        case kGDTNativeAdPositionBottomOfScreen:
            center = CGPointMake(CGRectGetMidX(parentBounds),
                                 CGRectGetMaxY(parentBounds) - CGRectGetMidY(view.bounds));
            break;
        case kGDTNativeAdPositionTopLeftOfScreen:
            center = CGPointMake(CGRectGetMidX(view.bounds), CGRectGetMidY(view.bounds));
            break;
        case kGDTNativeAdPositionTopRightOfScreen:
            center = CGPointMake(CGRectGetMaxX(parentBounds) - CGRectGetMidX(view.bounds),
                                 CGRectGetMidY(view.bounds));
            break;
        case kGDTNativeAdPositionBottomLeftOfScreen:
            center = CGPointMake(CGRectGetMidX(view.bounds),
                                 CGRectGetMaxY(parentBounds) - CGRectGetMidY(view.bounds));
            break;
        case kGDTNativeAdPositionBottomRightOfScreen:
            center = CGPointMake(CGRectGetMaxX(parentBounds) - CGRectGetMidX(view.bounds),
                                 CGRectGetMaxY(parentBounds) - CGRectGetMidY(view.bounds));
            break;
        case kGDTNativeAdPositionCenterOfScreen:
            center = CGPointMake(CGRectGetMidX(parentBounds), CGRectGetMidY(parentBounds));
            break;
    }
    view.center = center;
}

@end
