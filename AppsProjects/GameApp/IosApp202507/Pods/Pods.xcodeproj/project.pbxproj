// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		458B188365A307B3C128ABF524D1A3E3 /* GoogleUserMessagingPlatform */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = ED320BD7709D903F42B2B6E8D9CF94C8 /* Build configuration list for PBXAggregateTarget "GoogleUserMessagingPlatform" */;
			buildPhases = (
				DAADBB9E8FE825F04DBCDE77EBF620EA /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				5DCD53AD84C99DB3B90D3208D3B83203 /* PBXTargetDependency */,
			);
			name = GoogleUserMessagingPlatform;
		};
		FEA3B3A570634836C0457F3D7CEF1699 /* Google-Mobile-Ads-SDK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = BC8EF543C240710D6B60C8021C7952CE /* Build configuration list for PBXAggregateTarget "Google-Mobile-Ads-SDK" */;
			buildPhases = (
				588BF235657382A862BD5CB32414A534 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				1CDCF2F7EF07B5A533EAE8866D2AA146 /* PBXTargetDependency */,
				B2E61B3C8C154B320A9D5D08E76AFBB9 /* PBXTargetDependency */,
			);
			name = "Google-Mobile-Ads-SDK";
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		515F1EAFAF7A1585AB262FA1BF5A4AD2 /* Pods-UnityFramework-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 79C8ECE653DCB2A3E6BFE47FCDC86FFD /* Pods-UnityFramework-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		93C83DDB407088A7861CA0950D745601 /* Pods-Unity-iPhone-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = BF1EBC5B7606BC6A6C3701557A59CEC2 /* Pods-Unity-iPhone-dummy.m */; };
		ADA18C32A670E6B352346BE4BCE17705 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		AF4391245C82871D7F198F6BCB58AC75 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F2BEF381737BDC4E4707D8255EC2172E /* PrivacyInfo.xcprivacy */; };
		B0CD4E4D7D64C735B8ECDFCBE2CA4EAB /* Pods-UnityFramework-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B731AFE18D981DE9683B5E7BFCE378A /* Pods-UnityFramework-dummy.m */; };
		D1F85354341006CBE3158D78BDCF4BD2 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		DF2C8242DAA422373F9A26EC3797753E /* Pods-Unity-iPhone-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 65970B0C0B6C5E2BFEB65FF035AE9B9E /* Pods-Unity-iPhone-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F782A7585F705656A4CC1BC3685ABFD6 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 03051C6226E890C08F78B91403227B05 /* PrivacyInfo.xcprivacy */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		07FB65AEE2D59376EB29C8DBC52E3B23 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 458B188365A307B3C128ABF524D1A3E3;
			remoteInfo = GoogleUserMessagingPlatform;
		};
		87DD759D7028374D09DB32D23A6215E8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 63A7C675C13F87669AF56006D943998B;
			remoteInfo = "GoogleUserMessagingPlatform-UserMessagingPlatformResources";
		};
		C08B80A5E34D25964F71F13EAC611D6A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 458B188365A307B3C128ABF524D1A3E3;
			remoteInfo = GoogleUserMessagingPlatform;
		};
		D74A0F8239407CB05FC8F81E52EE76FC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 36FD0958A0EC4A0FCF599E9B22719B03;
			remoteInfo = "Google-Mobile-Ads-SDK-GoogleMobileAdsResources";
		};
		ECBED1ACFFBF343C7156E168C6F51201 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FEA3B3A570634836C0457F3D7CEF1699;
			remoteInfo = "Google-Mobile-Ads-SDK";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		02F81532F1965124CF4ABA8FDB52D45D /* GoogleMobileAds.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = GoogleMobileAds.xcframework; path = Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework; sourceTree = "<group>"; };
		03051C6226E890C08F78B91403227B05 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework/ios-arm64/GoogleMobileAds.framework/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		061FD01CCB39827CD83E465185C4954B /* GoogleUserMessagingPlatform.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = GoogleUserMessagingPlatform.debug.xcconfig; sourceTree = "<group>"; };
		0ADB2282F214F2C341AA476204083B6D /* Pods-Unity-iPhone-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Unity-iPhone-Info.plist"; sourceTree = "<group>"; };
		0B731AFE18D981DE9683B5E7BFCE378A /* Pods-UnityFramework-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-UnityFramework-dummy.m"; sourceTree = "<group>"; };
		145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = GoogleUserMessagingPlatform.release.xcconfig; sourceTree = "<group>"; };
		17A632BE819A4E42EEB3D2172A61B104 /* Pods-Unity-iPhone.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Unity-iPhone.modulemap"; sourceTree = "<group>"; };
		196B222352C56A68FC59622ED469C121 /* Pods-UnityFramework-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-UnityFramework-resources.sh"; sourceTree = "<group>"; };
		25DCDF33A4C9D6E08478C0BC48EF0CF2 /* Pods-UnityFramework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-UnityFramework"; path = Pods_UnityFramework.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2A80C6AE0C2C690D32CB9671E7EFD622 /* Pods-UnityFramework.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-UnityFramework.modulemap"; sourceTree = "<group>"; };
		35A00F1DA0CC8177A784DF5421905AA4 /* Pods-UnityFramework-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-UnityFramework-Info.plist"; sourceTree = "<group>"; };
		35DAC83087DA7C88C3702E5BE89EDC8B /* Pods-UnityFramework.releaseforprofiling.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-UnityFramework.releaseforprofiling.xcconfig"; sourceTree = "<group>"; };
		384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		396C58255C53ED2E65BEF89D865E49C0 /* ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist"; sourceTree = "<group>"; };
		3998091512768CD07733CB4F02B4BD9F /* Google-Mobile-Ads-SDK-GoogleMobileAdsResources */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "Google-Mobile-Ads-SDK-GoogleMobileAdsResources"; path = GoogleMobileAdsResources.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		432C97C126CBDCAFB65B509A62A38967 /* Pods-UnityFramework-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-UnityFramework-acknowledgements.markdown"; sourceTree = "<group>"; };
		512BE0869C3CB0EDF52B2F1B65C4131F /* Pods-Unity-iPhone.releaseforprofiling.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Unity-iPhone.releaseforprofiling.xcconfig"; sourceTree = "<group>"; };
		52F01770255AF60D14C45B3B47333A4F /* Pods-UnityFramework.releaseforrunning.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-UnityFramework.releaseforrunning.xcconfig"; sourceTree = "<group>"; };
		57A4F8E82134600B413E1978FBDE1D37 /* Pods-Unity-iPhone.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Unity-iPhone.release.xcconfig"; sourceTree = "<group>"; };
		65970B0C0B6C5E2BFEB65FF035AE9B9E /* Pods-Unity-iPhone-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Unity-iPhone-umbrella.h"; sourceTree = "<group>"; };
		662F5A31DEAC129C8588176D22575027 /* UserMessagingPlatform.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = UserMessagingPlatform.xcframework; path = Frameworks/Release/UserMessagingPlatform.xcframework; sourceTree = "<group>"; };
		735FD4C3FF207181117BDA46F1D8645C /* Pods-Unity-iPhone.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Unity-iPhone.debug.xcconfig"; sourceTree = "<group>"; };
		79C8ECE653DCB2A3E6BFE47FCDC86FFD /* Pods-UnityFramework-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-UnityFramework-umbrella.h"; sourceTree = "<group>"; };
		7DA14812788DADFC728F07521E3C0BE8 /* Pods-Unity-iPhone */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Unity-iPhone"; path = Pods_Unity_iPhone.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		81EA445226FCEB33F02F38793AD6F40B /* Pods-Unity-iPhone-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Unity-iPhone-acknowledgements.plist"; sourceTree = "<group>"; };
		8A377C18F92A8A511869ADA54B5652D2 /* GoogleUserMessagingPlatform-UserMessagingPlatformResources */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "GoogleUserMessagingPlatform-UserMessagingPlatformResources"; path = UserMessagingPlatformResources.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Google-Mobile-Ads-SDK.release.xcconfig"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A402126B43836E3511D77C3223B0E149 /* Pods-Unity-iPhone.releaseforrunning.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Unity-iPhone.releaseforrunning.xcconfig"; sourceTree = "<group>"; };
		B82B0DEA828940E5FCF84AE4660A96D3 /* Pods-UnityFramework.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-UnityFramework.debug.xcconfig"; sourceTree = "<group>"; };
		BAFBF36FEA346DF49938D9860EB6028F /* Google-Mobile-Ads-SDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Google-Mobile-Ads-SDK.debug.xcconfig"; sourceTree = "<group>"; };
		BB2A4B767611BB56D673F30C2A2C389E /* GoogleUserMessagingPlatform-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "GoogleUserMessagingPlatform-xcframeworks.sh"; sourceTree = "<group>"; };
		BF1EBC5B7606BC6A6C3701557A59CEC2 /* Pods-Unity-iPhone-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Unity-iPhone-dummy.m"; sourceTree = "<group>"; };
		C893AF5C2C47205173E355DFBDDF135C /* Pods-UnityFramework-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-UnityFramework-acknowledgements.plist"; sourceTree = "<group>"; };
		D89DCBE06DFB6294F3FFB5301911737F /* Pods-UnityFramework.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-UnityFramework.release.xcconfig"; sourceTree = "<group>"; };
		DFE3439FE26D0DD55BE28053F602C333 /* Google-Mobile-Ads-SDK-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Google-Mobile-Ads-SDK-xcframeworks.sh"; sourceTree = "<group>"; };
		F171C3DB98FDC94D76D8DBAD475D28AB /* ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist"; sourceTree = "<group>"; };
		F2BEF381737BDC4E4707D8255EC2172E /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "Frameworks/Release/UserMessagingPlatform.xcframework/ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		FD42E3A3C1B481CC0BC930CF3F82B148 /* Pods-Unity-iPhone-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Unity-iPhone-acknowledgements.markdown"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13FC12CA91A783426F0A05A7D35824B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				ADA18C32A670E6B352346BE4BCE17705 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		217EA1996566312EEA8C987330E7E388 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B6D70360558CA53F2E32EB36AE02964 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D1F85354341006CBE3158D78BDCF4BD2 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E5783CE6B1304BF9A1AFEF8CD5C58B3D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		11BA09EC7FBC460B1661DC94791F7F19 /* GoogleUserMessagingPlatform */ = {
			isa = PBXGroup;
			children = (
				FABFE75267495065D6E1C8CE9EE6A1D1 /* Frameworks */,
				20F5B0E421D02CC211528CDFD8DA3FB4 /* Resources */,
				FC419229D015266B9D8657B080B4F505 /* Support Files */,
			);
			name = GoogleUserMessagingPlatform;
			path = GoogleUserMessagingPlatform;
			sourceTree = "<group>";
		};
		20F5B0E421D02CC211528CDFD8DA3FB4 /* Resources */ = {
			isa = PBXGroup;
			children = (
				F2BEF381737BDC4E4707D8255EC2172E /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		5095E7C36CD35261C90F8321307B2CE1 /* Products */ = {
			isa = PBXGroup;
			children = (
				3998091512768CD07733CB4F02B4BD9F /* Google-Mobile-Ads-SDK-GoogleMobileAdsResources */,
				8A377C18F92A8A511869ADA54B5652D2 /* GoogleUserMessagingPlatform-UserMessagingPlatformResources */,
				7DA14812788DADFC728F07521E3C0BE8 /* Pods-Unity-iPhone */,
				25DCDF33A4C9D6E08478C0BC48EF0CF2 /* Pods-UnityFramework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		634377BE0D04D6B088F4AB3E83B8501E /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				78D87C08AEE61E3216B38CBC871C055A /* Pods-Unity-iPhone */,
				95F702FC6AAAC4C0C977074573BC9B96 /* Pods-UnityFramework */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		78D87C08AEE61E3216B38CBC871C055A /* Pods-Unity-iPhone */ = {
			isa = PBXGroup;
			children = (
				17A632BE819A4E42EEB3D2172A61B104 /* Pods-Unity-iPhone.modulemap */,
				FD42E3A3C1B481CC0BC930CF3F82B148 /* Pods-Unity-iPhone-acknowledgements.markdown */,
				81EA445226FCEB33F02F38793AD6F40B /* Pods-Unity-iPhone-acknowledgements.plist */,
				BF1EBC5B7606BC6A6C3701557A59CEC2 /* Pods-Unity-iPhone-dummy.m */,
				0ADB2282F214F2C341AA476204083B6D /* Pods-Unity-iPhone-Info.plist */,
				65970B0C0B6C5E2BFEB65FF035AE9B9E /* Pods-Unity-iPhone-umbrella.h */,
				735FD4C3FF207181117BDA46F1D8645C /* Pods-Unity-iPhone.debug.xcconfig */,
				57A4F8E82134600B413E1978FBDE1D37 /* Pods-Unity-iPhone.release.xcconfig */,
				512BE0869C3CB0EDF52B2F1B65C4131F /* Pods-Unity-iPhone.releaseforprofiling.xcconfig */,
				A402126B43836E3511D77C3223B0E149 /* Pods-Unity-iPhone.releaseforrunning.xcconfig */,
			);
			name = "Pods-Unity-iPhone";
			path = "Target Support Files/Pods-Unity-iPhone";
			sourceTree = "<group>";
		};
		832A9E7D43417C5467A6C028FEEDAEBD /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				02F81532F1965124CF4ABA8FDB52D45D /* GoogleMobileAds.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		95F702FC6AAAC4C0C977074573BC9B96 /* Pods-UnityFramework */ = {
			isa = PBXGroup;
			children = (
				2A80C6AE0C2C690D32CB9671E7EFD622 /* Pods-UnityFramework.modulemap */,
				432C97C126CBDCAFB65B509A62A38967 /* Pods-UnityFramework-acknowledgements.markdown */,
				C893AF5C2C47205173E355DFBDDF135C /* Pods-UnityFramework-acknowledgements.plist */,
				0B731AFE18D981DE9683B5E7BFCE378A /* Pods-UnityFramework-dummy.m */,
				35A00F1DA0CC8177A784DF5421905AA4 /* Pods-UnityFramework-Info.plist */,
				196B222352C56A68FC59622ED469C121 /* Pods-UnityFramework-resources.sh */,
				79C8ECE653DCB2A3E6BFE47FCDC86FFD /* Pods-UnityFramework-umbrella.h */,
				B82B0DEA828940E5FCF84AE4660A96D3 /* Pods-UnityFramework.debug.xcconfig */,
				D89DCBE06DFB6294F3FFB5301911737F /* Pods-UnityFramework.release.xcconfig */,
				35DAC83087DA7C88C3702E5BE89EDC8B /* Pods-UnityFramework.releaseforprofiling.xcconfig */,
				52F01770255AF60D14C45B3B47333A4F /* Pods-UnityFramework.releaseforrunning.xcconfig */,
			);
			name = "Pods-UnityFramework";
			path = "Target Support Files/Pods-UnityFramework";
			sourceTree = "<group>";
		};
		9DF73883B330BC99A015A6240FF1DF74 /* Google-Mobile-Ads-SDK */ = {
			isa = PBXGroup;
			children = (
				832A9E7D43417C5467A6C028FEEDAEBD /* Frameworks */,
				E3710CD13C7237016D2943F815F1AF7C /* Resources */,
				F5FF9E329431A841DBBA9467176ACCB5 /* Support Files */,
			);
			name = "Google-Mobile-Ads-SDK";
			path = "Google-Mobile-Ads-SDK";
			sourceTree = "<group>";
		};
		BD8403EDA9C5E3E2B560A01140F4B72C /* Pods */ = {
			isa = PBXGroup;
			children = (
				9DF73883B330BC99A015A6240FF1DF74 /* Google-Mobile-Ads-SDK */,
				11BA09EC7FBC460B1661DC94791F7F19 /* GoogleUserMessagingPlatform */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				BD8403EDA9C5E3E2B560A01140F4B72C /* Pods */,
				5095E7C36CD35261C90F8321307B2CE1 /* Products */,
				634377BE0D04D6B088F4AB3E83B8501E /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4801F62A6B08CD9B5410329F1A18FDE /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E3710CD13C7237016D2943F815F1AF7C /* Resources */ = {
			isa = PBXGroup;
			children = (
				03051C6226E890C08F78B91403227B05 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		E4801F62A6B08CD9B5410329F1A18FDE /* iOS */ = {
			isa = PBXGroup;
			children = (
				384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		F5FF9E329431A841DBBA9467176ACCB5 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				DFE3439FE26D0DD55BE28053F602C333 /* Google-Mobile-Ads-SDK-xcframeworks.sh */,
				BAFBF36FEA346DF49938D9860EB6028F /* Google-Mobile-Ads-SDK.debug.xcconfig */,
				8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */,
				396C58255C53ED2E65BEF89D865E49C0 /* ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/Google-Mobile-Ads-SDK";
			sourceTree = "<group>";
		};
		FABFE75267495065D6E1C8CE9EE6A1D1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				662F5A31DEAC129C8588176D22575027 /* UserMessagingPlatform.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FC419229D015266B9D8657B080B4F505 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				BB2A4B767611BB56D673F30C2A2C389E /* GoogleUserMessagingPlatform-xcframeworks.sh */,
				061FD01CCB39827CD83E465185C4954B /* GoogleUserMessagingPlatform.debug.xcconfig */,
				145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */,
				F171C3DB98FDC94D76D8DBAD475D28AB /* ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/GoogleUserMessagingPlatform";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		04A1818CE66BED02FC1F545A997C03AA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				515F1EAFAF7A1585AB262FA1BF5A4AD2 /* Pods-UnityFramework-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4B5428A88BBD997B8B5FF76A67EC16D7 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF2C8242DAA422373F9A26EC3797753E /* Pods-Unity-iPhone-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		36FD0958A0EC4A0FCF599E9B22719B03 /* Google-Mobile-Ads-SDK-GoogleMobileAdsResources */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F33E52E3BF7B2B31619BB32624BCBD5A /* Build configuration list for PBXNativeTarget "Google-Mobile-Ads-SDK-GoogleMobileAdsResources" */;
			buildPhases = (
				6B83B028AB9BFE733F55315FCB536786 /* Sources */,
				E5783CE6B1304BF9A1AFEF8CD5C58B3D /* Frameworks */,
				602A2AAC5288F35EDB381D7E1D210D66 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Google-Mobile-Ads-SDK-GoogleMobileAdsResources";
			productName = GoogleMobileAdsResources;
			productReference = 3998091512768CD07733CB4F02B4BD9F /* Google-Mobile-Ads-SDK-GoogleMobileAdsResources */;
			productType = "com.apple.product-type.bundle";
		};
		63A7C675C13F87669AF56006D943998B /* GoogleUserMessagingPlatform-UserMessagingPlatformResources */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9CEF0673D3F161350D3E4F3E9A3175F5 /* Build configuration list for PBXNativeTarget "GoogleUserMessagingPlatform-UserMessagingPlatformResources" */;
			buildPhases = (
				A698064198CC6C5D365212C3DAAB263F /* Sources */,
				217EA1996566312EEA8C987330E7E388 /* Frameworks */,
				2DD04F3ED4A91EA4C38EC7333450E46E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "GoogleUserMessagingPlatform-UserMessagingPlatformResources";
			productName = UserMessagingPlatformResources;
			productReference = 8A377C18F92A8A511869ADA54B5652D2 /* GoogleUserMessagingPlatform-UserMessagingPlatformResources */;
			productType = "com.apple.product-type.bundle";
		};
		B0317381620A11CC5B648A3E77B1453B /* Pods-Unity-iPhone */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDB4AB1D4F07E0BFBBFAA7004D421103 /* Build configuration list for PBXNativeTarget "Pods-Unity-iPhone" */;
			buildPhases = (
				4B5428A88BBD997B8B5FF76A67EC16D7 /* Headers */,
				9519EFE2550222DCBA0652445F341042 /* Sources */,
				5B6D70360558CA53F2E32EB36AE02964 /* Frameworks */,
				D65E4F496FDD7AAB81CBE78B23990BC0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Pods-Unity-iPhone";
			productName = Pods_Unity_iPhone;
			productReference = 7DA14812788DADFC728F07521E3C0BE8 /* Pods-Unity-iPhone */;
			productType = "com.apple.product-type.framework";
		};
		B4FF6DA8F9BC9B1CB442ED949FBD3ECC /* Pods-UnityFramework */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 287E7CEC16D823AC041EF7E9A0CB513A /* Build configuration list for PBXNativeTarget "Pods-UnityFramework" */;
			buildPhases = (
				04A1818CE66BED02FC1F545A997C03AA /* Headers */,
				05B06DEC441EBE4755258C836323D9F5 /* Sources */,
				13FC12CA91A783426F0A05A7D35824B3 /* Frameworks */,
				1EBD12529D74C78B4B7C264A672B9029 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4926254135BA13A5485D9F84AF749012 /* PBXTargetDependency */,
				D0DB437A536FADDEDC8DFA93264A066A /* PBXTargetDependency */,
			);
			name = "Pods-UnityFramework";
			productName = Pods_UnityFramework;
			productReference = 25DCDF33A4C9D6E08478C0BC48EF0CF2 /* Pods-UnityFramework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 5095E7C36CD35261C90F8321307B2CE1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FEA3B3A570634836C0457F3D7CEF1699 /* Google-Mobile-Ads-SDK */,
				36FD0958A0EC4A0FCF599E9B22719B03 /* Google-Mobile-Ads-SDK-GoogleMobileAdsResources */,
				458B188365A307B3C128ABF524D1A3E3 /* GoogleUserMessagingPlatform */,
				63A7C675C13F87669AF56006D943998B /* GoogleUserMessagingPlatform-UserMessagingPlatformResources */,
				B0317381620A11CC5B648A3E77B1453B /* Pods-Unity-iPhone */,
				B4FF6DA8F9BC9B1CB442ED949FBD3ECC /* Pods-UnityFramework */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1EBD12529D74C78B4B7C264A672B9029 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DD04F3ED4A91EA4C38EC7333450E46E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF4391245C82871D7F198F6BCB58AC75 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		602A2AAC5288F35EDB381D7E1D210D66 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F782A7585F705656A4CC1BC3685ABFD6 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D65E4F496FDD7AAB81CBE78B23990BC0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		588BF235657382A862BD5CB32414A534 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Google-Mobile-Ads-SDK/Google-Mobile-Ads-SDK-xcframeworks.sh",
				"${PODS_ROOT}/Google-Mobile-Ads-SDK/Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework",
			);
			name = "[CP] Copy XCFrameworks";
			outputPaths = (
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Google-Mobile-Ads-SDK/GoogleMobileAds.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Google-Mobile-Ads-SDK/Google-Mobile-Ads-SDK-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DAADBB9E8FE825F04DBCDE77EBF620EA /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/GoogleUserMessagingPlatform/GoogleUserMessagingPlatform-xcframeworks.sh",
				"${PODS_ROOT}/GoogleUserMessagingPlatform/Frameworks/Release/UserMessagingPlatform.xcframework",
			);
			name = "[CP] Copy XCFrameworks";
			outputPaths = (
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleUserMessagingPlatform/UserMessagingPlatform.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/GoogleUserMessagingPlatform/GoogleUserMessagingPlatform-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		05B06DEC441EBE4755258C836323D9F5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B0CD4E4D7D64C735B8ECDFCBE2CA4EAB /* Pods-UnityFramework-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B83B028AB9BFE733F55315FCB536786 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9519EFE2550222DCBA0652445F341042 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				93C83DDB407088A7861CA0950D745601 /* Pods-Unity-iPhone-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A698064198CC6C5D365212C3DAAB263F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1CDCF2F7EF07B5A533EAE8866D2AA146 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Google-Mobile-Ads-SDK-GoogleMobileAdsResources";
			target = 36FD0958A0EC4A0FCF599E9B22719B03 /* Google-Mobile-Ads-SDK-GoogleMobileAdsResources */;
			targetProxy = D74A0F8239407CB05FC8F81E52EE76FC /* PBXContainerItemProxy */;
		};
		4926254135BA13A5485D9F84AF749012 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Google-Mobile-Ads-SDK";
			target = FEA3B3A570634836C0457F3D7CEF1699 /* Google-Mobile-Ads-SDK */;
			targetProxy = ECBED1ACFFBF343C7156E168C6F51201 /* PBXContainerItemProxy */;
		};
		5DCD53AD84C99DB3B90D3208D3B83203 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "GoogleUserMessagingPlatform-UserMessagingPlatformResources";
			target = 63A7C675C13F87669AF56006D943998B /* GoogleUserMessagingPlatform-UserMessagingPlatformResources */;
			targetProxy = 87DD759D7028374D09DB32D23A6215E8 /* PBXContainerItemProxy */;
		};
		B2E61B3C8C154B320A9D5D08E76AFBB9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = GoogleUserMessagingPlatform;
			target = 458B188365A307B3C128ABF524D1A3E3 /* GoogleUserMessagingPlatform */;
			targetProxy = C08B80A5E34D25964F71F13EAC611D6A /* PBXContainerItemProxy */;
		};
		D0DB437A536FADDEDC8DFA93264A066A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = GoogleUserMessagingPlatform;
			target = 458B188365A307B3C128ABF524D1A3E3 /* GoogleUserMessagingPlatform */;
			targetProxy = 07FB65AEE2D59376EB29C8DBC52E3B23 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		03669A924A3BB647240934B7AA3DB81E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B82B0DEA828940E5FCF84AE4660A96D3 /* Pods-UnityFramework.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		0A2022FC455CAABD9B49B8412BF3A51E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		0D2FCC765E82612FBFDB3D7DB3164EC8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 735FD4C3FF207181117BDA46F1D8645C /* Pods-Unity-iPhone.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2F555992B6A9031D1DC001DD246F3654 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3319A6E7C3B2568AA7C2D8A0969A9080 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D89DCBE06DFB6294F3FFB5301911737F /* Pods-UnityFramework.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		476637EE5FB52F82FBE7CB9661F9E019 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BAFBF36FEA346DF49938D9860EB6028F /* Google-Mobile-Ads-SDK.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4A4584172B585F1C7E403A03961F3EE8 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE_FOR_PROFILING=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = ReleaseForProfiling;
		};
		4E7C166326F0502FB36E12F134A5326A /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 512BE0869C3CB0EDF52B2F1B65C4131F /* Pods-Unity-iPhone.releaseforprofiling.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = ReleaseForProfiling;
		};
		56C523C1A23478621C46EF95257ECE05 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForRunning;
		};
		5AA4F9E8F1C2074EDC51EC3D1197951D /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUserMessagingPlatform";
				IBSC_MODULE = GoogleUserMessagingPlatform;
				INFOPLIST_FILE = "Target Support Files/GoogleUserMessagingPlatform/ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = UserMessagingPlatformResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = ReleaseForRunning;
		};
		5F539AB36459FF8B9F92B96C0228EC35 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 35DAC83087DA7C88C3702E5BE89EDC8B /* Pods-UnityFramework.releaseforprofiling.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = ReleaseForProfiling;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		68ECBD3762C6BF8559340670156FC167 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 061FD01CCB39827CD83E465185C4954B /* GoogleUserMessagingPlatform.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6B0FF2852B928C887F5FC53535C887E2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BAFBF36FEA346DF49938D9860EB6028F /* Google-Mobile-Ads-SDK.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/Google-Mobile-Ads-SDK";
				IBSC_MODULE = Google_Mobile_Ads_SDK;
				INFOPLIST_FILE = "Target Support Files/Google-Mobile-Ads-SDK/ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = GoogleMobileAdsResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		6C12740132A1717AA602C07872729C3F /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A402126B43836E3511D77C3223B0E149 /* Pods-Unity-iPhone.releaseforrunning.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = ReleaseForRunning;
		};
		6DB570B5B64FBDEA2CE3E6FDC9D3BA1D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/Google-Mobile-Ads-SDK";
				IBSC_MODULE = Google_Mobile_Ads_SDK;
				INFOPLIST_FILE = "Target Support Files/Google-Mobile-Ads-SDK/ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = GoogleMobileAdsResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		8BD4E212CB744F8BA897930DB9FFD6B9 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUserMessagingPlatform";
				IBSC_MODULE = GoogleUserMessagingPlatform;
				INFOPLIST_FILE = "Target Support Files/GoogleUserMessagingPlatform/ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = UserMessagingPlatformResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = ReleaseForProfiling;
		};
		8D012E24179002496F93C0946069693A /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForProfiling;
		};
		8D4081477511AC554AA5AFA4A1C96D22 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForProfiling;
		};
		9FDC12CE20496DD590F9E75FDE796805 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/Google-Mobile-Ads-SDK";
				IBSC_MODULE = Google_Mobile_Ads_SDK;
				INFOPLIST_FILE = "Target Support Files/Google-Mobile-Ads-SDK/ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = GoogleMobileAdsResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = ReleaseForRunning;
		};
		C06B8D309DAD4D66388B918B150C1C61 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 57A4F8E82134600B413E1978FBDE1D37 /* Pods-Unity-iPhone.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		CC71AD277FF850037C5438D895D6460D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 145714E58E6C500A8F2D0183B3EB2C8D /* GoogleUserMessagingPlatform.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUserMessagingPlatform";
				IBSC_MODULE = GoogleUserMessagingPlatform;
				INFOPLIST_FILE = "Target Support Files/GoogleUserMessagingPlatform/ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = UserMessagingPlatformResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		CE1F821C17354B2D0145A61B75A07286 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 061FD01CCB39827CD83E465185C4954B /* GoogleUserMessagingPlatform.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUserMessagingPlatform";
				IBSC_MODULE = GoogleUserMessagingPlatform;
				INFOPLIST_FILE = "Target Support Files/GoogleUserMessagingPlatform/ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = UserMessagingPlatformResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		CFE170157095E5A9BB47BA6CDF89F46A /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForRunning;
		};
		D07094A479A2A61BE70512067D6385A8 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8CC4EE16A4FCC1667B427A523586D21F /* Google-Mobile-Ads-SDK.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/Google-Mobile-Ads-SDK";
				IBSC_MODULE = Google_Mobile_Ads_SDK;
				INFOPLIST_FILE = "Target Support Files/Google-Mobile-Ads-SDK/ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = GoogleMobileAdsResources;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = ReleaseForProfiling;
		};
		EBE8D4AE931517FB7B7974CE2CA770A3 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE_FOR_RUNNING=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = ReleaseForRunning;
		};
		FCEA06830B314E4B4C3D1B0CCE6FC054 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 52F01770255AF60D14C45B3B47333A4F /* Pods-UnityFramework.releaseforrunning.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = ReleaseForRunning;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		287E7CEC16D823AC041EF7E9A0CB513A /* Build configuration list for PBXNativeTarget "Pods-UnityFramework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03669A924A3BB647240934B7AA3DB81E /* Debug */,
				3319A6E7C3B2568AA7C2D8A0969A9080 /* Release */,
				5F539AB36459FF8B9F92B96C0228EC35 /* ReleaseForProfiling */,
				FCEA06830B314E4B4C3D1B0CCE6FC054 /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
				4A4584172B585F1C7E403A03961F3EE8 /* ReleaseForProfiling */,
				EBE8D4AE931517FB7B7974CE2CA770A3 /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9CEF0673D3F161350D3E4F3E9A3175F5 /* Build configuration list for PBXNativeTarget "GoogleUserMessagingPlatform-UserMessagingPlatformResources" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE1F821C17354B2D0145A61B75A07286 /* Debug */,
				CC71AD277FF850037C5438D895D6460D /* Release */,
				8BD4E212CB744F8BA897930DB9FFD6B9 /* ReleaseForProfiling */,
				5AA4F9E8F1C2074EDC51EC3D1197951D /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BC8EF543C240710D6B60C8021C7952CE /* Build configuration list for PBXAggregateTarget "Google-Mobile-Ads-SDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				476637EE5FB52F82FBE7CB9661F9E019 /* Debug */,
				2F555992B6A9031D1DC001DD246F3654 /* Release */,
				8D4081477511AC554AA5AFA4A1C96D22 /* ReleaseForProfiling */,
				CFE170157095E5A9BB47BA6CDF89F46A /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		ED320BD7709D903F42B2B6E8D9CF94C8 /* Build configuration list for PBXAggregateTarget "GoogleUserMessagingPlatform" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				68ECBD3762C6BF8559340670156FC167 /* Debug */,
				0A2022FC455CAABD9B49B8412BF3A51E /* Release */,
				8D012E24179002496F93C0946069693A /* ReleaseForProfiling */,
				56C523C1A23478621C46EF95257ECE05 /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F33E52E3BF7B2B31619BB32624BCBD5A /* Build configuration list for PBXNativeTarget "Google-Mobile-Ads-SDK-GoogleMobileAdsResources" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6B0FF2852B928C887F5FC53535C887E2 /* Debug */,
				6DB570B5B64FBDEA2CE3E6FDC9D3BA1D /* Release */,
				D07094A479A2A61BE70512067D6385A8 /* ReleaseForProfiling */,
				9FDC12CE20496DD590F9E75FDE796805 /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDB4AB1D4F07E0BFBBFAA7004D421103 /* Build configuration list for PBXNativeTarget "Pods-Unity-iPhone" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0D2FCC765E82612FBFDB3D7DB3164EC8 /* Debug */,
				C06B8D309DAD4D66388B918B150C1C61 /* Release */,
				4E7C166326F0502FB36E12F134A5326A /* ReleaseForProfiling */,
				6C12740132A1717AA602C07872729C3F /* ReleaseForRunning */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
