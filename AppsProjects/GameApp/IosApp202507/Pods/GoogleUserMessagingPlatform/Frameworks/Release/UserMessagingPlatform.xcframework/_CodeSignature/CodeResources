<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<data>
		2lMP03pgZTfbXyqQ48rkpaNV3SI=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<data>
		C02/ii7oYFfOLWictPhnogHPW84=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<data>
		CgvWixFHxqTnvo+oRg8UwxZ29p0=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<data>
		usSUO/EZMZjLLzt6Bg4bkhp61Go=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<data>
		lRGVA2eDj/nQObv+IcIdOFMjbBo=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<data>
		9RCFJSaqhLHeEgqG9d/eph7W3lI=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Info.plist</key>
		<data>
		H8lUOexfQ1oAhXmtzb/smDa/Qf8=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<data>
		RkG2jbWyWyEbQ8hlPX98EMJhvZc=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<data>
		k9OFlDPBRWfteJ+bfG12WAoBuls=
		</data>
		<key>ios-arm64/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<data>
		Kaf915oNZTF4ZTDxcQLxGY/KQWc=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<data>
		2lMP03pgZTfbXyqQ48rkpaNV3SI=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<data>
		C02/ii7oYFfOLWictPhnogHPW84=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<data>
		CgvWixFHxqTnvo+oRg8UwxZ29p0=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<data>
		usSUO/EZMZjLLzt6Bg4bkhp61Go=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<data>
		lRGVA2eDj/nQObv+IcIdOFMjbBo=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<data>
		9RCFJSaqhLHeEgqG9d/eph7W3lI=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Info.plist</key>
		<data>
		j6R4xcg5Ha1t+sFG6FJ/7X3f2rQ=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<data>
		RkG2jbWyWyEbQ8hlPX98EMJhvZc=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<data>
		k9OFlDPBRWfteJ+bfG12WAoBuls=
		</data>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<data>
		1abAfaYjd85/QzTTCs2rzqEtMRg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<dict>
			<key>hash</key>
			<data>
			2lMP03pgZTfbXyqQ48rkpaNV3SI=
			</data>
			<key>hash2</key>
			<data>
			OGR3CVReDGdhF476PHjsQA+dnmB1qXlWTshtpkNbLsQ=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<dict>
			<key>hash</key>
			<data>
			C02/ii7oYFfOLWictPhnogHPW84=
			</data>
			<key>hash2</key>
			<data>
			jEklOFAyB6o25/UCZUbQ2X1Vtr7FIeymhTW5kcpaTS4=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<dict>
			<key>hash</key>
			<data>
			CgvWixFHxqTnvo+oRg8UwxZ29p0=
			</data>
			<key>hash2</key>
			<data>
			nxlsktTDYoEqLpbvPzb/dDukiLVUtdWmBNlwZe6Xbrg=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<dict>
			<key>hash</key>
			<data>
			usSUO/EZMZjLLzt6Bg4bkhp61Go=
			</data>
			<key>hash2</key>
			<data>
			f2mZpSBzcQnWg4ub+p28Am7ZLOdzSC77ONoI6fcQ9cw=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			lRGVA2eDj/nQObv+IcIdOFMjbBo=
			</data>
			<key>hash2</key>
			<data>
			holOsZHrCHaB4aoAVb2WUtNDImvtZbTfPOm/0zt8G00=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<dict>
			<key>hash</key>
			<data>
			9RCFJSaqhLHeEgqG9d/eph7W3lI=
			</data>
			<key>hash2</key>
			<data>
			oIfOo52szNDhoW7/k2wegqpLbm/gh4ehYlgE9ik6M2U=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			H8lUOexfQ1oAhXmtzb/smDa/Qf8=
			</data>
			<key>hash2</key>
			<data>
			TXxMVh2snV135AD+cqGPVC3KY4F1y6U6sirrH18XeOk=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			RkG2jbWyWyEbQ8hlPX98EMJhvZc=
			</data>
			<key>hash2</key>
			<data>
			58hJ+pDAaGDwJPOU6I9Te4Vv2O/oXyVD+XQQyTwdk94=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			k9OFlDPBRWfteJ+bfG12WAoBuls=
			</data>
			<key>hash2</key>
			<data>
			RbetuZ/NLZYqHIAPv8wTJbV6f6QWX0FhgjI7tf/pyQA=
			</data>
		</dict>
		<key>ios-arm64/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<dict>
			<key>hash</key>
			<data>
			Kaf915oNZTF4ZTDxcQLxGY/KQWc=
			</data>
			<key>hash2</key>
			<data>
			3I8G27Yx2TGCYTSn7JX9vQqzWJebS1iY2R7mMG+EqRA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentForm.h</key>
		<dict>
			<key>hash</key>
			<data>
			2lMP03pgZTfbXyqQ48rkpaNV3SI=
			</data>
			<key>hash2</key>
			<data>
			OGR3CVReDGdhF476PHjsQA+dnmB1qXlWTshtpkNbLsQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPConsentInformation.h</key>
		<dict>
			<key>hash</key>
			<data>
			C02/ii7oYFfOLWictPhnogHPW84=
			</data>
			<key>hash2</key>
			<data>
			jEklOFAyB6o25/UCZUbQ2X1Vtr7FIeymhTW5kcpaTS4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPDebugSettings.h</key>
		<dict>
			<key>hash</key>
			<data>
			CgvWixFHxqTnvo+oRg8UwxZ29p0=
			</data>
			<key>hash2</key>
			<data>
			nxlsktTDYoEqLpbvPzb/dDukiLVUtdWmBNlwZe6Xbrg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPError.h</key>
		<dict>
			<key>hash</key>
			<data>
			usSUO/EZMZjLLzt6Bg4bkhp61Go=
			</data>
			<key>hash2</key>
			<data>
			f2mZpSBzcQnWg4ub+p28Am7ZLOdzSC77ONoI6fcQ9cw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UMPRequestParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			lRGVA2eDj/nQObv+IcIdOFMjbBo=
			</data>
			<key>hash2</key>
			<data>
			holOsZHrCHaB4aoAVb2WUtNDImvtZbTfPOm/0zt8G00=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Headers/UserMessagingPlatform.h</key>
		<dict>
			<key>hash</key>
			<data>
			9RCFJSaqhLHeEgqG9d/eph7W3lI=
			</data>
			<key>hash2</key>
			<data>
			oIfOo52szNDhoW7/k2wegqpLbm/gh4ehYlgE9ik6M2U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			j6R4xcg5Ha1t+sFG6FJ/7X3f2rQ=
			</data>
			<key>hash2</key>
			<data>
			VwSrc7ZpR4zz7K3dfalw85qGpQ2HMTt34LX1edLKnX8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			RkG2jbWyWyEbQ8hlPX98EMJhvZc=
			</data>
			<key>hash2</key>
			<data>
			58hJ+pDAaGDwJPOU6I9Te4Vv2O/oXyVD+XQQyTwdk94=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			k9OFlDPBRWfteJ+bfG12WAoBuls=
			</data>
			<key>hash2</key>
			<data>
			RbetuZ/NLZYqHIAPv8wTJbV6f6QWX0FhgjI7tf/pyQA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/UserMessagingPlatform.framework/UserMessagingPlatform</key>
		<dict>
			<key>hash</key>
			<data>
			1abAfaYjd85/QzTTCs2rzqEtMRg=
			</data>
			<key>hash2</key>
			<data>
			5nlXi++/Tiq7zXk3nskrceOBlhdYI1q2JeyX+fK/G4k=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
