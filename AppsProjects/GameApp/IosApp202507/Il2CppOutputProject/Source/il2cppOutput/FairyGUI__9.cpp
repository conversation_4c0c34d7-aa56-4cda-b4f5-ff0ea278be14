﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct String_t;
struct ZipEntry_t882B2BAC24133DEB53D3506FAE0A0168DFD07B5B;



IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CPrivateImplementationDetailsU3E_t82CF5D5BA976051977853C7FB9C15968839EC5A2  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct ZipEntry_t882B2BAC24133DEB53D3506FAE0A0168DFD07B5B  : public RuntimeObject
{
	String_t* ___name;
	int32_t ___compress;
	uint32_t ___crc;
	int32_t ___size;
	int32_t ___sourceSize;
	int32_t ___offset;
	bool ___isDirectory;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D216_t49461613A1E6AC6097F1ABD40E831A8D5C5DB650 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D216_t49461613A1E6AC6097F1ABD40E831A8D5C5DB650__padding[216];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D36_t0E04B5935435CAF50171A4F1755361825C118CF9 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D36_t0E04B5935435CAF50171A4F1755361825C118CF9__padding[36];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D5456_t86136EF7D0D0291BC545230E516847B24EC04C74 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D5456_t86136EF7D0D0291BC545230E516847B24EC04C74__padding[5456];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D64_t428F26152D8BE0972DFF21E8A323086E14BE6D27 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D64_t428F26152D8BE0972DFF21E8A323086E14BE6D27__padding[64];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D80_t0321C4434D88AAE86A22275ACEEB4F765B81DD87 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D80_t0321C4434D88AAE86A22275ACEEB4F765B81DD87__padding[80];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D9312_tCE701F761565AB46D8116828668FF9558052ECB0 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D9312_tCE701F761565AB46D8116828668FF9558052ECB0__padding[9312];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D96_t5190CCFC659FDFE592AA60EA5D9A2886879C12FD 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D96_t5190CCFC659FDFE592AA60EA5D9A2886879C12FD__padding[96];
	};
};
#pragma pack(pop, tp)
struct U3CPrivateImplementationDetailsU3E_t82CF5D5BA976051977853C7FB9C15968839EC5A2_StaticFields
{
	int64_t ___0D53D986AC9AC138A39C2D64E1588FCD87299A81C8ADD9ED9FD9D98955C8CCC5;
	int64_t ___0EEE47048E50C250A2ADDB45A92446A0E929B91307560A759A5CFD44AD29DCE6;
	int64_t ___1F4870B08C47BFF9029BB6078DAD53700D95E592F9ED91103FD75B89C4537112;
	int64_t ___2B33DEA6CD38D4CE686C1D154716D6B7EF94321C797CBEB771D5581E59CC061F;
	int64_t ___3375C0EBF42EB9560D400FCA92E7407C27D6246AD87E3AFAFB4AA10F3EDDDD1A;
	int64_t ___33ABAB3973FDD2036D9F9287F624B6D9ABD5DA6BFA58A02DADB7932188EFFF07;
	int64_t ___3629E8CDF2D4F629072F8463B3C49CD3AB93F695C4D4E33A9CABA6FB18EA8BAE;
	int64_t ___3CDF0387EAFF6E6E4EFF901E9E6CC7B4FA6358CC5AD83FF2BECCDBE64C4AA021;
	int64_t ___429D2BF09BBACDEB57473E7C76B01E6BF292AD1523953244632D8B01D9FE9085;
	int64_t ___46CC7A59A429332A210CB4D933B1D4B5379398F0682CB775BC04F993C3D0B0AA;
	int64_t ___4F0AB0DA7BFC3F488135B803EF12279D108341F6F5872234676E438EA1F5BC44;
	int64_t ___50E92C11B512B57563B4519FC45CC36931BE5EBB5C58B969BAD1C740677C582D;
	int64_t ___6039A839576CB450A68D98D9B3803789853EEDB9111A2EA5E6BBA91F701FE191;
	int64_t ___6D62730626CCC8B49CA5CF81AF4F7D2EF875E741DA3D8C6570488AEA013473DA;
	int64_t ___72B74EFC99D3B924ADDEA31E8DF371AC2918DD200D914B018CB7350448F23F0C;
	int64_t ___7F5323E27833E5ACD4967E9A8E009CCC1465419B8A02C7D3C7EC3AA52E71B2D4;
	int64_t ___807911344F9807A1B582873D2F0F38B194E077DD886C3AFC830BC065EBF2CFAE;
	int64_t ___8908FCCC1DF13468816CA7A3A4FBB8A7F96FB3AC00BAA34D70C5397C9B88F9C4;
	int64_t ___89B71AE7D45DE935FC05FF4BF4987BE1426359E2C389AD6149699D8050D0F75B;
	int64_t ___8DCFC69C77BE23ED6C460B27389CCC94F51C0A43E2947AD4FDFCBAA6C13EC607;
	int64_t ___8EE9327ECC967FC1AB49834169C4F6C562D46189C5CFF4C25C084E82FB924BFA;
	int64_t ___8F8DDC25F7C3822D3BE8C39FF1DE83005C8BAE2572F324A2F1E0E53AD5588E1E;
	int64_t ___9291CB95205633E925E98E9CB0DB4912E4A7BF5B18F7557D52090962CC045ED1;
	__StaticArrayInitTypeSizeU3D9312_tCE701F761565AB46D8116828668FF9558052ECB0 ___98C2837235E0E58EB8BF519FF920B528B41DDAE3ED696A21E2CB8828B9CBEC04;
	__StaticArrayInitTypeSizeU3D64_t428F26152D8BE0972DFF21E8A323086E14BE6D27 ___98D1685B5E9C9FD24988D4F12B619330F10308FA20A82CC135A7D339A3B3F0CB;
	int64_t ___9C70DD87C645ADD1E55A15DD75BFC3980636D2EC17C832852F7616C8947C5524;
	__StaticArrayInitTypeSizeU3D80_t0321C4434D88AAE86A22275ACEEB4F765B81DD87 ___A0EAAFC684A8BCF5FEEDF47A528FCEACF98A57D5CE1A9FD18201BD07010ACA4A;
	int64_t ___A9C5757FBFD31E7C729ED509C347B743BED698564E003AEA467DBC483FA0F4D9;
	__StaticArrayInitTypeSizeU3D5456_t86136EF7D0D0291BC545230E516847B24EC04C74 ___AA3C4E62FB1F35496443662B3982F8436095C51BC60BEC78ECEE06E8DBF910D2;
	int64_t ___AD13E92D9C1D3305D4AFA5B2674500224AD34925FB6A767E825BD67C45332C3B;
	int64_t ___AE465A420BC05BA9297282F4201C8C25D7CBEFDCF3C220435F13110BCD642267;
	int64_t ___AFDFA8643900371FE41615B3C293ABD2511CC874390DF92EC2A79C227329333A;
	int64_t ___B3C846C9496A4C09CD134A48383754195F07F2CBB7FF09A5F7D053B31B03141B;
	int64_t ___B586AA924B045960FF6BBC3D68DEA0A98B4B1BFFC64F90B9029AB72A5841856B;
	int64_t ___B99CF1EB09EDB7DC1869D9E6F54A8D60795752019177C8CF5F4E2755758C846F;
	__StaticArrayInitTypeSizeU3D216_t49461613A1E6AC6097F1ABD40E831A8D5C5DB650 ___B99E19B575DAEA6926B690C9B8B622902A7992D460C7E78AE72C12DF8A1B76D9;
	int64_t ___C2AD3E40941848EBFD50C59D86CA2F0C6F10C5DBFAED314E8B3E49EB52C9B38E;
	int64_t ___C4508EFC736EAC1E91CA30F728EC3AE622E4360F93E6A7F557A5C7590C4E56DD;
	__StaticArrayInitTypeSizeU3D36_t0E04B5935435CAF50171A4F1755361825C118CF9 ___C4EB100C3C763F47842E818CE6C154E0FD34D2EC7108A3A68012BDB513BC7AE3;
	int64_t ___C94BE9E5997BFC7172BFF0E1698DA9ACC0DD14181040A90033F0AD0815979975;
	int64_t ___CAD276F8E0D6D4F23CBE291840AAC59B5D72E7D4A4078ED9719558145C37ADC3;
	int64_t ___CED93B5430FE4CAFC5BFFF7C0A499B024657650D5A67770FD16C5CCB8C0D3B3A;
	int64_t ___DAC2C0AEB7C896D6D8D4B54DBCA3B807C3ABAD013DCEDFF4C5D4DB412DD0F5A0;
	int64_t ___DDC0D2A22A9CB07513EDC1C5FE5890BD09CF5FBD58DAE047D70E9D3BBEC7BF33;
	int64_t ___E2CB7C37304F6E20D427BA14E13C306CB5FB7B6CC9E6541A165225961BC36562;
	int64_t ___E79ED44CAC3EC1E173133141A47D3BD3564B09E991F40E9F557DB8A1B064988B;
	int64_t ___EA544CA6FD079BF61EBA836428B5D9EF2662A238CE9DF540D8667148A53321B2;
	__StaticArrayInitTypeSizeU3D96_t5190CCFC659FDFE592AA60EA5D9A2886879C12FD ___F358F089F2B1C4134F6EA2EC349D724D1A21F0A73CDBEE63915B1AA162F93EDF;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3 (String_t* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZipEntry__ctor_mE51B67640ECC34E3C5B907F0B2E539BC36D11164 (ZipEntry_t882B2BAC24133DEB53D3506FAE0A0168DFD07B5B* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t U3CPrivateImplementationDetailsU3E_ComputeStringHash_mC0575179003D48E3FEC370AA8EE4C53419417136 (String_t* ___0_s, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		String_t* L_0 = ___0_s;
		if (!L_0)
		{
			goto IL_002a;
		}
	}
	{
		V_0 = ((int32_t)-2128831035);
		V_1 = 0;
		goto IL_0021;
	}

IL_000d:
	{
		String_t* L_1 = ___0_s;
		int32_t L_2 = V_1;
		NullCheck(L_1);
		Il2CppChar L_3;
		L_3 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_1, L_2, NULL);
		uint32_t L_4 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_multiply(((int32_t)((int32_t)L_3^(int32_t)L_4)), ((int32_t)16777619)));
		int32_t L_5 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0021:
	{
		int32_t L_6 = V_1;
		String_t* L_7 = ___0_s;
		NullCheck(L_7);
		int32_t L_8;
		L_8 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_7, NULL);
		if ((((int32_t)L_6) < ((int32_t)L_8)))
		{
			goto IL_000d;
		}
	}

IL_002a:
	{
		uint32_t L_9 = V_0;
		return L_9;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____stringLength;
		return L_0;
	}
}
