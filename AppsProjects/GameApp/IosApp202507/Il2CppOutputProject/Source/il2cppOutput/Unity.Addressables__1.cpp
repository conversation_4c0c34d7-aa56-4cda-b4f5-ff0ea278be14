﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Action_1_t84AF53BD4007CE3C0DE9F29034F579B456DC98DF;
struct Action_1_tA57E75E816C50B3444F6BEFBEBF19A03BA2DF22E;
struct Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A;
struct AsyncOperationBase_1_t6C0B1FFBBFC6F03B8212E7C1CA3679364DDE83E9;
struct AsyncOperationBase_1_t8BFB6142CEBF919881F2972D476E2B0A36EE6EC3;
struct AsyncOperationBase_1_t9B8CD2C389C84545C0CAD66F10A3746E480D09D9;
struct AsyncOperationBase_1_tA0C44504D80F2CFD43277EC431A5308325E7E836;
struct AsyncOperationBase_1_t710981A6C015CC62ADE562002416C75827C2D70B;
struct AsyncOperationBase_1_t705BFA43974D35E55B0DE97C36A4210FC6716379;
struct DelegateList_1_tAA76594B21CB4057F8F336D0B4236581CCA762B5;
struct DelegateList_1_tC070A3D40FCD92D36D6C762C004DDB78978B4F88;
struct DelegateList_1_t472259E3E09904EE80A15B306399DBFE8998BAAD;
struct Dictionary_2_t541829361B0DD1A3A1AE6593794CF04EA26537A3;
struct Dictionary_2_tCF0DE186C024D133CC46FA0B271E5359DBFAD9B4;
struct Func_2_t65EC28300D7949DC379319C3EA3426C72F877CE4;
struct HashSet_1_t7FD3E09F5E6CC9B45ADC269EC25CEBBA5BCDE4E1;
struct IList_1_tFEC432B87E444FFF0D7B8EFD6CBB918523AD6827;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4;
struct List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A;
struct List_1_tA8CDD10241272F52D820B8D34B369A175EF12A7C;
struct TaskCompletionSource_1_t499C43CEFAC274C9F02F0122032A42FD713C35F1;
struct TaskCompletionSource_1_tB4EF81F69CCF7C4F0D956F9B26127C0634A24A37;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct ObjectInitializationDataU5BU5D_t27CD67BDEC9A5CFBEC907601E4762EE9188560B7;
struct ResourceLocationDataU5BU5D_t4B1EAD1E8E9A3BF73241223BC1AACFBCC6992DD3;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AddressablesImpl_tD285C19BAF4079094A9A8FE5007ED1C57C256337;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Exception_t;
struct IInstanceProvider_t6AD5AE64CB95B873B111117A37B8D967FC67FA37;
struct IResourceLocation_tB0706DA8EB5339ECB4B64C7D2DAFC1EB35D2915C;
struct IResourceLocator_tB4E18FC0113846B03B2C4E0714C30F699F0B78BE;
struct IResourceProvider_tAD356B19CE0730462C3D5B6E7FBAD905342B9247;
struct ISceneProvider_t8975F536F686AA01DF259F7AAB973512B5098581;
struct InitalizationObjectsOperation_t814FDE8C63D213641D19602F6E55C952F8559E93;
struct InitializationOperation_tF2DD6F78860670C1471F4C60EF9ECA4EADE9BC82;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct ResourceLocationMap_t29F0D501724BE36EAC9021F77FECC589B630F87F;
struct ResourceManager_t23CA39B6F8FB4F38DFCA7F6FDAEE868D709C933D;
struct ResourceManagerDiagnostics_t49595E6E9EFEA3A70F3DE5D5AB2DEE8F0E3B3F45;
struct ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120;
struct String_t;
struct Type_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570;
struct U3CU3Ec__DisplayClass16_0_tABF4259DC01C6989E6EE39472B7ED108FB384D36;
struct U3CU3Ec__DisplayClass18_0_t2A9989BF6E6973C801C6AA463A40FCFF755204A3;

IL2CPP_EXTERN_C RuntimeClass* List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mF0091D9762D4F640D9EEB3F007D4778367DED47C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* ContentCatalogProvider_tB392BDEB7F03B789A37F776823936BCCD89D995F_0_0_0_var;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4  : public RuntimeObject
{
	ObjectInitializationDataU5BU5D_t27CD67BDEC9A5CFBEC907601E4762EE9188560B7* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A  : public RuntimeObject
{
	ResourceLocationDataU5BU5D_t4B1EAD1E8E9A3BF73241223BC1AACFBCC6992DD3* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t67D6AE99C3981B201312A10C0D3A123AEFCE55D8  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct ResourceLocationMap_t29F0D501724BE36EAC9021F77FECC589B630F87F  : public RuntimeObject
{
	String_t* ___U3CLocatorIdU3Ek__BackingField;
	Dictionary_2_t541829361B0DD1A3A1AE6593794CF04EA26537A3* ___locations;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570  : public RuntimeObject
{
};
struct U3CU3Ec__DisplayClass16_0_tABF4259DC01C6989E6EE39472B7ED108FB384D36  : public RuntimeObject
{
	AddressablesImpl_tD285C19BAF4079094A9A8FE5007ED1C57C256337* ___addressables;
	String_t* ___providerSuffix;
	RuntimeObject* ___remoteHashLocation;
};
struct U3CU3Ec__DisplayClass18_0_t2A9989BF6E6973C801C6AA463A40FCFF755204A3  : public RuntimeObject
{
	InitializationOperation_tF2DD6F78860670C1471F4C60EF9ECA4EADE9BC82* ___U3CU3E4__this;
	RuntimeObject* ___catalogs;
	ResourceLocationMap_t29F0D501724BE36EAC9021F77FECC589B630F87F* ___locMap;
	int32_t ___index;
	RuntimeObject* ___remoteHashLocation;
};
struct AsyncOperationHandle_1_tB06077285DDBB8B950592F6ACC37C333ADDD997F 
{
	AsyncOperationBase_1_t6C0B1FFBBFC6F03B8212E7C1CA3679364DDE83E9* ___m_InternalOp;
	int32_t ___m_Version;
	String_t* ___m_LocationName;
};
struct AsyncOperationHandle_1_tAA8468BA8C5994CD242CA70D05A8474AF68F7646 
{
	AsyncOperationBase_1_t8BFB6142CEBF919881F2972D476E2B0A36EE6EC3* ___m_InternalOp;
	int32_t ___m_Version;
	String_t* ___m_LocationName;
};
struct AsyncOperationHandle_1_tF0C18B1708F42632D0DCFCD51AFCC737C73CDD66 
{
	AsyncOperationBase_1_t9B8CD2C389C84545C0CAD66F10A3746E480D09D9* ___m_InternalOp;
	int32_t ___m_Version;
	String_t* ___m_LocationName;
};
struct AsyncOperationHandle_1_t67A61B7E2D5527AE9D96AFC6D7DBF7F3C84644BF 
{
	AsyncOperationBase_1_tA0C44504D80F2CFD43277EC431A5308325E7E836* ___m_InternalOp;
	int32_t ___m_Version;
	String_t* ___m_LocationName;
};
struct AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC 
{
	AsyncOperationBase_1_t710981A6C015CC62ADE562002416C75827C2D70B* ___m_InternalOp;
	int32_t ___m_Version;
	String_t* ___m_LocationName;
};
struct AsyncOperationHandle_1_tE0FBC9643F615A524F7C241C5125D1F83B6977BD 
{
	AsyncOperationBase_1_t705BFA43974D35E55B0DE97C36A4210FC6716379* ___m_InternalOp;
	int32_t ___m_Version;
	String_t* ___m_LocationName;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1 
{
	String_t* ___m_AssemblyName;
	String_t* ___m_ClassName;
	Type_t* ___m_CachedType;
	bool ___U3CValueChangedU3Ek__BackingField;
};
struct SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1_marshaled_pinvoke
{
	char* ___m_AssemblyName;
	char* ___m_ClassName;
	Type_t* ___m_CachedType;
	int32_t ___U3CValueChangedU3Ek__BackingField;
};
struct SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1_marshaled_com
{
	Il2CppChar* ___m_AssemblyName;
	Il2CppChar* ___m_ClassName;
	Type_t* ___m_CachedType;
	int32_t ___U3CValueChangedU3Ek__BackingField;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D2589_tF0BCC4113CD227E53F3E9DE8C6DD4A7D062A7918 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D2589_tF0BCC4113CD227E53F3E9DE8C6DD4A7D062A7918__padding[2589];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D2943_t4D14DECED00E3BCAAFB5C32A36865B688FD5EC4B 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D2943_t4D14DECED00E3BCAAFB5C32A36865B688FD5EC4B__padding[2943];
	};
};
#pragma pack(pop, tp)
struct AddressablesImpl_tD285C19BAF4079094A9A8FE5007ED1C57C256337  : public RuntimeObject
{
	ResourceManager_t23CA39B6F8FB4F38DFCA7F6FDAEE868D709C933D* ___m_ResourceManager;
	RuntimeObject* ___m_InstanceProvider;
	int32_t ___m_CatalogRequestsTimeout;
	RuntimeObject* ___SceneProvider;
	List_1_tA8CDD10241272F52D820B8D34B369A175EF12A7C* ___m_ResourceLocators;
	AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC ___m_InitializationOperation;
	AsyncOperationHandle_1_tAA8468BA8C5994CD242CA70D05A8474AF68F7646 ___m_ActiveCheckUpdateOperation;
	AsyncOperationHandle_1_tB06077285DDBB8B950592F6ACC37C333ADDD997F ___m_ActiveUpdateOperation;
	Action_1_t84AF53BD4007CE3C0DE9F29034F579B456DC98DF* ___m_OnHandleCompleteAction;
	Action_1_t84AF53BD4007CE3C0DE9F29034F579B456DC98DF* ___m_OnSceneHandleCompleteAction;
	Action_1_t84AF53BD4007CE3C0DE9F29034F579B456DC98DF* ___m_OnHandleDestroyedAction;
	Dictionary_2_tCF0DE186C024D133CC46FA0B271E5359DBFAD9B4* ___m_resultToHandle;
	HashSet_1_t7FD3E09F5E6CC9B45ADC269EC25CEBBA5BCDE4E1* ___m_SceneInstances;
	AsyncOperationHandle_1_tF0C18B1708F42632D0DCFCD51AFCC737C73CDD66 ___m_ActiveCleanBundleCacheOperation;
	bool ___hasStartedInitialization;
};
struct AsyncOperationStatus_t1D89963C21E9A4C34EF3312FDFE60060339C2A88 
{
	int32_t ___value__;
};
struct ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120  : public RuntimeObject
{
	String_t* ___m_buildTarget;
	String_t* ___m_SettingsHash;
	List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A* ___m_CatalogLocations;
	bool ___m_ProfileEvents;
	bool ___m_LogResourceManagerExceptions;
	List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4* ___m_ExtraInitializationData;
	bool ___m_DisableCatalogUpdateOnStart;
	bool ___m_IsLocalCatalogInBundle;
	SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1 ___m_CertificateHandlerType;
	String_t* ___m_AddressablesVersion;
	int32_t ___m_maxConcurrentWebRequests;
	int32_t ___m_CatalogRequestsTimeout;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct AsyncOperationBase_1_t710981A6C015CC62ADE562002416C75827C2D70B  : public RuntimeObject
{
	RuntimeObject* ___U3CResultU3Ek__BackingField;
	int32_t ___m_referenceCount;
	int32_t ___m_Status;
	Exception_t* ___m_Error;
	ResourceManager_t23CA39B6F8FB4F38DFCA7F6FDAEE868D709C933D* ___m_RM;
	int32_t ___m_Version;
	DelegateList_1_tC070A3D40FCD92D36D6C762C004DDB78978B4F88* ___m_DestroyedAction;
	DelegateList_1_tAA76594B21CB4057F8F336D0B4236581CCA762B5* ___m_CompletedActionT;
	Action_1_tA57E75E816C50B3444F6BEFBEBF19A03BA2DF22E* ___m_OnDestroyAction;
	Action_1_t84AF53BD4007CE3C0DE9F29034F579B456DC98DF* ___m_dependencyCompleteAction;
	bool ___HasExecuted;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___Executed;
	bool ___U3CIsRunningU3Ek__BackingField;
	TaskCompletionSource_1_t499C43CEFAC274C9F02F0122032A42FD713C35F1* ___m_taskCompletionSource;
	TaskCompletionSource_1_tB4EF81F69CCF7C4F0D956F9B26127C0634A24A37* ___m_taskCompletionSourceTypeless;
	bool ___m_InDeferredCallbackQueue;
	DelegateList_1_t472259E3E09904EE80A15B306399DBFE8998BAAD* ___m_UpdateCallbacks;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___m_UpdateCallback;
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct InitializationOperation_tF2DD6F78860670C1471F4C60EF9ECA4EADE9BC82  : public AsyncOperationBase_1_t710981A6C015CC62ADE562002416C75827C2D70B
{
	AsyncOperationHandle_1_tE0FBC9643F615A524F7C241C5125D1F83B6977BD ___m_rtdOp;
	AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC ___m_loadCatalogOp;
	String_t* ___m_ProviderSuffix;
	AddressablesImpl_tD285C19BAF4079094A9A8FE5007ED1C57C256337* ___m_Addressables;
	ResourceManagerDiagnostics_t49595E6E9EFEA3A70F3DE5D5AB2DEE8F0E3B3F45* ___m_Diagnostics;
	InitalizationObjectsOperation_t814FDE8C63D213641D19602F6E55C952F8559E93* ___m_InitGroupOps;
};
struct List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4_StaticFields
{
	ObjectInitializationDataU5BU5D_t27CD67BDEC9A5CFBEC907601E4762EE9188560B7* ___s_emptyArray;
};
struct List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A_StaticFields
{
	ResourceLocationDataU5BU5D_t4B1EAD1E8E9A3BF73241223BC1AACFBCC6992DD3* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_t67D6AE99C3981B201312A10C0D3A123AEFCE55D8_StaticFields
{
	__StaticArrayInitTypeSizeU3D2943_t4D14DECED00E3BCAAFB5C32A36865B688FD5EC4B ___665ECC1C05160C0F32D3061E27307CCD620C172A96858BE3F9B40764886531FC;
	__StaticArrayInitTypeSizeU3D2589_tF0BCC4113CD227E53F3E9DE8C6DD4A7D062A7918 ___915F098662DB855AFE64CBFACF6839FC652CED0140B9FFA733C3BDEBD84E36E7;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_StaticFields
{
	U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570* ___U3CU3E9;
	Func_2_t65EC28300D7949DC379319C3EA3426C72F877CE4* ___U3CU3E9__13_0;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4_gshared (List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4* __this, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m400FA93F30A0788073EEF09EFDA850B0DD08B1D1 (U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC (Type_t* ___0_left, Type_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC InitializationOperation_OnCatalogDataLoaded_mDC85CF69F44EFB5D9A383C9AEF77D59C0FF699D9 (AddressablesImpl_tD285C19BAF4079094A9A8FE5007ED1C57C256337* ___0_addressables, AsyncOperationHandle_1_t67A61B7E2D5527AE9D96AFC6D7DBF7F3C84644BF ___1_op, String_t* ___2_providerSuffix, RuntimeObject* ___3_remoteHashLocation, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InitializationOperation_LoadOpComplete_m7411329487C62A65E027BE07B055CF290C09C009 (InitializationOperation_tF2DD6F78860670C1471F4C60EF9ECA4EADE9BC82* __this, AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC ___0_op, RuntimeObject* ___1_catalogs, ResourceLocationMap_t29F0D501724BE36EAC9021F77FECC589B630F87F* ___2_locMap, int32_t ___3_index, RuntimeObject* ___4_remoteHashLocation, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* SerializedType_get_Value_m8AC9DC985380FD7524D147E2C5C95664FAB9A10A (SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SerializedType_set_Value_m1AB236ECAE27E66A4B2302B4573EB71A7762F679 (SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1* __this, Type_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline (int32_t ___0_value, int32_t ___1_min, int32_t ___2_max, const RuntimeMethod* method) ;
inline void List_1__ctor_mF0091D9762D4F640D9EEB3F007D4778367DED47C (List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline void List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4 (List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4*, const RuntimeMethod*))List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4_gshared)(__this, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_mE9258D312AFF75B5AE671A1B5B0B352B98A96F29 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570* L_0 = (U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570*)il2cpp_codegen_object_new(U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m400FA93F30A0788073EEF09EFDA850B0DD08B1D1(L_0, NULL);
		((U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m400FA93F30A0788073EEF09EFDA850B0DD08B1D1 (U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CU3Ec_U3CExecuteU3Eb__13_0_mAED31DB7FABB1ABC307A8C6610EC50871F29A002 (U3CU3Ec_t46D69A719E3B4BCAA079778064C23683839D4570* __this, RuntimeObject* ___0_rp, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContentCatalogProvider_tB392BDEB7F03B789A37F776823936BCCD89D995F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_rp;
		NullCheck(L_0);
		Type_t* L_1;
		L_1 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_0, NULL);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (ContentCatalogProvider_tB392BDEB7F03B789A37F776823936BCCD89D995F_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		bool L_4;
		L_4 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_1, L_3, NULL);
		return L_4;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass16_0__ctor_m8DD3D41CB6ADB76E0EA33F4AF0F24D8492D77AB6 (U3CU3Ec__DisplayClass16_0_tABF4259DC01C6989E6EE39472B7ED108FB384D36* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC U3CU3Ec__DisplayClass16_0_U3CLoadContentCatalogU3Eb__0_m66DB2E022694F69F39CCFFC3154CB5AB5976645F (U3CU3Ec__DisplayClass16_0_tABF4259DC01C6989E6EE39472B7ED108FB384D36* __this, AsyncOperationHandle_1_t67A61B7E2D5527AE9D96AFC6D7DBF7F3C84644BF ___0_res, const RuntimeMethod* method) 
{
	{
		AddressablesImpl_tD285C19BAF4079094A9A8FE5007ED1C57C256337* L_0 = __this->___addressables;
		AsyncOperationHandle_1_t67A61B7E2D5527AE9D96AFC6D7DBF7F3C84644BF L_1 = ___0_res;
		String_t* L_2 = __this->___providerSuffix;
		RuntimeObject* L_3 = __this->___remoteHashLocation;
		AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC L_4;
		L_4 = InitializationOperation_OnCatalogDataLoaded_mDC85CF69F44EFB5D9A383C9AEF77D59C0FF699D9(L_0, L_1, L_2, L_3, NULL);
		return L_4;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass18_0__ctor_m99303C3B088C7B4052A6F8AA53CAA1BC21EB1D5A (U3CU3Ec__DisplayClass18_0_t2A9989BF6E6973C801C6AA463A40FCFF755204A3* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass18_0_U3CLoadContentCatalogInternalU3Eb__0_m8E4BA6A17387BF7F02B23A139D7E8A5C782D43BA (U3CU3Ec__DisplayClass18_0_t2A9989BF6E6973C801C6AA463A40FCFF755204A3* __this, AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC ___0_op, const RuntimeMethod* method) 
{
	{
		InitializationOperation_tF2DD6F78860670C1471F4C60EF9ECA4EADE9BC82* L_0 = __this->___U3CU3E4__this;
		AsyncOperationHandle_1_t42BB61262B05D32A3C3C0E42ADB3294DFE46A8AC L_1 = ___0_op;
		RuntimeObject* L_2 = __this->___catalogs;
		ResourceLocationMap_t29F0D501724BE36EAC9021F77FECC589B630F87F* L_3 = __this->___locMap;
		int32_t L_4 = __this->___index;
		RuntimeObject* L_5 = __this->___remoteHashLocation;
		NullCheck(L_0);
		InitializationOperation_LoadOpComplete_m7411329487C62A65E027BE07B055CF290C09C009(L_0, L_1, L_2, L_3, L_4, L_5, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ResourceManagerRuntimeData_get_BuildTarget_mB64F1595BD714F5D79138ED161B4F05D3A0FE121 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_buildTarget;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_BuildTarget_m0958B3EC4A849CEB55CD6E94277F093AE824B83A (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___m_buildTarget = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_buildTarget), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ResourceManagerRuntimeData_get_SettingsHash_m05B636625500B0CADC8C1116A2C880DD90BD9D46 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_SettingsHash;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_SettingsHash_mC0115B10FB9904CB21A385433EEBA3E8AB47A74A (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___m_SettingsHash = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_SettingsHash), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A* ResourceManagerRuntimeData_get_CatalogLocations_m6BDA2FA48B12635AD67A780C5E197C02BB411172 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A* L_0 = __this->___m_CatalogLocations;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ResourceManagerRuntimeData_get_ProfileEvents_mD05FEC5FBD8C8A04EB35B153297CA9FE0A92220B (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_ProfileEvents;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_ProfileEvents_m41B9D67AD80113BC75407C5432F45E6BC8CD7551 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___m_ProfileEvents = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ResourceManagerRuntimeData_get_LogResourceManagerExceptions_m421154EADF056D9461BFAE4D8C98B3818457BF2C (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_LogResourceManagerExceptions;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_LogResourceManagerExceptions_m815094B1BF1DDA5C333ED3F3333E8D074C4396B4 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___m_LogResourceManagerExceptions = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4* ResourceManagerRuntimeData_get_InitializationObjects_m0C2D008C79BD52C0F9895F093E4AFD3AFDB9E014 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4* L_0 = __this->___m_ExtraInitializationData;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ResourceManagerRuntimeData_get_DisableCatalogUpdateOnStartup_m2EB745669A9AC1D07E47D345DE2EF87442D56907 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_DisableCatalogUpdateOnStart;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_DisableCatalogUpdateOnStartup_m43EF818B02F4E5A00C57BA9BF19BCFF68779834D (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___m_DisableCatalogUpdateOnStart = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ResourceManagerRuntimeData_get_IsLocalCatalogInBundle_m5D312C49BFCB754034EC7BBFD1EAF7BF1AF6B312 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_IsLocalCatalogInBundle;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_IsLocalCatalogInBundle_m6931A3CBF1E368065CC96972855EA73BFD7E8D56 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___m_IsLocalCatalogInBundle = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* ResourceManagerRuntimeData_get_CertificateHandlerType_m1194D6694DE8A1FBBF29D5025C2E9C9DB1FD941F (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1* L_0 = (SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1*)(&__this->___m_CertificateHandlerType);
		Type_t* L_1;
		L_1 = SerializedType_get_Value_m8AC9DC985380FD7524D147E2C5C95664FAB9A10A(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_CertificateHandlerType_m3276FCE0F13D2E6C72B7DBDFDDE1B6339A9D633F (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, Type_t* ___0_value, const RuntimeMethod* method) 
{
	{
		SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1* L_0 = (SerializedType_t1FB0F9A8B8F766AC18F067F0882CE74E0014C6F1*)(&__this->___m_CertificateHandlerType);
		Type_t* L_1 = ___0_value;
		SerializedType_set_Value_m1AB236ECAE27E66A4B2302B4573EB71A7762F679(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ResourceManagerRuntimeData_get_AddressablesVersion_m0B7B76DF108ABD855470460DF6D862480D1A00D7 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_AddressablesVersion;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_AddressablesVersion_m77531E4740B232F4052281D8E0E061F8B1E04556 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_value;
		__this->___m_AddressablesVersion = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_AddressablesVersion), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ResourceManagerRuntimeData_get_MaxConcurrentWebRequests_m1CF41EB91ABB3B99C338122E3507D3D196A838F0 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_maxConcurrentWebRequests;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_MaxConcurrentWebRequests_m197F95E0235FC2BE257A01E923013464687EB8B9 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		int32_t L_1;
		L_1 = Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline(L_0, 1, ((int32_t)1024), NULL);
		__this->___m_maxConcurrentWebRequests = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ResourceManagerRuntimeData_get_CatalogRequestsTimeout_mED5A093F40FE50DF355C12FE18D6FCFB2AC993D1 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___m_CatalogRequestsTimeout;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData_set_CatalogRequestsTimeout_mF4DBF74FCF5A3AC5D15D257666EEC56771B5B097 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* G_B2_0 = NULL;
	ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* G_B3_1 = NULL;
	{
		int32_t L_0 = ___0_value;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			G_B2_0 = __this;
			goto IL_0008;
		}
		G_B1_0 = __this;
	}
	{
		int32_t L_1 = ___0_value;
		G_B3_0 = L_1;
		G_B3_1 = G_B1_0;
		goto IL_0009;
	}

IL_0008:
	{
		G_B3_0 = 0;
		G_B3_1 = G_B2_0;
	}

IL_0009:
	{
		NullCheck(G_B3_1);
		G_B3_1->___m_CatalogRequestsTimeout = G_B3_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceManagerRuntimeData__ctor_m5AD4B872EB21CB8C9613B008ADA0A55980AB4B75 (ResourceManagerRuntimeData_t02E4171B60AD2790A9F0683CEBE3D3FD9D643120* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mF0091D9762D4F640D9EEB3F007D4778367DED47C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A* L_0 = (List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A*)il2cpp_codegen_object_new(List_1_t7D40AA1D93492F1E4E83CB8970C6083D2F2D8D9A_il2cpp_TypeInfo_var);
		List_1__ctor_mF0091D9762D4F640D9EEB3F007D4778367DED47C(L_0, List_1__ctor_mF0091D9762D4F640D9EEB3F007D4778367DED47C_RuntimeMethod_var);
		__this->___m_CatalogLocations = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CatalogLocations), (void*)L_0);
		__this->___m_LogResourceManagerExceptions = (bool)1;
		List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4* L_1 = (List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4*)il2cpp_codegen_object_new(List_1_t674B89A0B228CC9549D5F52D35743D082C3B5FC4_il2cpp_TypeInfo_var);
		List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4(L_1, List_1__ctor_m2B3B3831C04033E325534A3A148815D75B3CA6F4_RuntimeMethod_var);
		__this->___m_ExtraInitializationData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_ExtraInitializationData), (void*)L_1);
		__this->___m_maxConcurrentWebRequests = ((int32_t)500);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Mathf_Clamp_m4DC36EEFDBE5F07C16249DA568023C5ECCFF0E7B_inline (int32_t ___0_value, int32_t ___1_min, int32_t ___2_max, const RuntimeMethod* method) 
{
	bool V_0 = false;
	bool V_1 = false;
	int32_t V_2 = 0;
	{
		int32_t L_0 = ___0_value;
		int32_t L_1 = ___1_min;
		V_0 = (bool)((((int32_t)L_0) < ((int32_t)L_1))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_000e;
		}
	}
	{
		int32_t L_3 = ___1_min;
		___0_value = L_3;
		goto IL_0019;
	}

IL_000e:
	{
		int32_t L_4 = ___0_value;
		int32_t L_5 = ___2_max;
		V_1 = (bool)((((int32_t)L_4) > ((int32_t)L_5))? 1 : 0);
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0019;
		}
	}
	{
		int32_t L_7 = ___2_max;
		___0_value = L_7;
	}

IL_0019:
	{
		int32_t L_8 = ___0_value;
		V_2 = L_8;
		goto IL_001d;
	}

IL_001d:
	{
		int32_t L_9 = V_2;
		return L_9;
	}
}
