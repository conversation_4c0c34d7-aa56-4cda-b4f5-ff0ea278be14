﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void JsonData__ctor_m6811B52CF3A742B6E425A0C8C02A7F475EC70C22 (void);
extern void JsonData__ctor_mA02A77C55F31BBE7F62D84ADEAC445BB4D6BF6F0 (void);
extern void JsonData_System_Collections_ICollection_get_Count_m1928821DE12D36BC1ADD18A8640515E26712421D (void);
extern void JsonData_System_Collections_ICollection_get_IsSynchronized_m916ECDC97D55234918E33C68EBC7096AC3E9BB8C (void);
extern void JsonData_System_Collections_ICollection_get_SyncRoot_mBAB43E3293672F6EB626FA687E552B51DC2F1FC2 (void);
extern void JsonData_System_Collections_IDictionary_get_IsReadOnly_m4DDCFBDE28ED5393BC1DC2871B454D50271BB4BB (void);
extern void JsonData_System_Collections_IDictionary_get_Keys_m09C045695EBC903E21448D3DA5A9A5D787683D96 (void);
extern void JsonData_System_Collections_IDictionary_get_Values_m5C4630F668BCD06DF24FF2A25D78D1A52D249099 (void);
extern void JsonData_System_Collections_IList_get_IsFixedSize_m5E943C4DE3DD920E91CD7C1F2E53192FA28200AE (void);
extern void JsonData_System_Collections_IList_get_IsReadOnly_m6E271699DD735D2E1123085A3508AC27D735C4F7 (void);
extern void JsonData_System_Collections_IDictionary_get_Item_m8E67E7723DA1D430AC2E2DDD1E592C708EFFB032 (void);
extern void JsonData_System_Collections_IDictionary_set_Item_m9E5C2463B2821FC2B3BE28F28F1050AE388902F9 (void);
extern void JsonData_System_Collections_IList_get_Item_mD896EB10F113D680EDFBC3D495131B2FCDEC55EF (void);
extern void JsonData_System_Collections_IList_set_Item_mB7EAC712FD88B435CF5D885BF0A678B09C19C60E (void);
extern void JsonData_System_Collections_ICollection_CopyTo_m718153CFF6828AF6E374C7FDF80D7FD675544520 (void);
extern void JsonData_System_Collections_IDictionary_Add_m8F766EE45CE3613EEE5136B5AC233C3FF3A16A1A (void);
extern void JsonData_System_Collections_IDictionary_Clear_m285AB467A43A7C41A8427B796E17F213AEA51528 (void);
extern void JsonData_System_Collections_IDictionary_Contains_m02095723C5D92D7C080B137650156C56261FA23C (void);
extern void JsonData_System_Collections_IDictionary_GetEnumerator_mC7F4FE47A8BD7DD7A9941C7D62BDC8906FBE7AAE (void);
extern void JsonData_System_Collections_IDictionary_Remove_m16DF2045A4C3B23570ACBFBF890CEABF481F81CF (void);
extern void JsonData_System_Collections_IEnumerable_GetEnumerator_mFE8CBE09E06D74A6EFDBA81CC6755C65451862E2 (void);
extern void JsonData_LitJson_IJsonWrapper_SetBoolean_mF61DEBF6BDEFCEEC1CA6786DB676415813DCB014 (void);
extern void JsonData_LitJson_IJsonWrapper_SetDouble_mDE0713F66AB143C6F3705DA877F01C9E60D76553 (void);
extern void JsonData_LitJson_IJsonWrapper_SetInt_m2FD29586CD2F56322C23D12E7622A30053C704D5 (void);
extern void JsonData_LitJson_IJsonWrapper_SetLong_mE40BE10F5427485DBFE8018E34C61EA862B18716 (void);
extern void JsonData_LitJson_IJsonWrapper_SetString_m1D50848A9C4D6FDA2EA3A0B48AB58F27B859BFBA (void);
extern void JsonData_System_Collections_IList_Add_mBB90F2B605DA48CF3B2BB283D83E3B68982D7857 (void);
extern void JsonData_System_Collections_IList_Clear_m480936A33C1592C34894FCCB427D2A650841D15B (void);
extern void JsonData_System_Collections_IList_Contains_m524B6DEDCF030862D76E60DE0EF23CE305052A16 (void);
extern void JsonData_System_Collections_IList_IndexOf_mC8D5D8C8B485992303ACF9F535A63BEBABDAC186 (void);
extern void JsonData_System_Collections_IList_Insert_m968CA5587708151BD18906CD3A30CA3BB452D4F1 (void);
extern void JsonData_System_Collections_IList_Remove_m6BE0CE268E656B03AFDF0E8F532C2D7C2A35611E (void);
extern void JsonData_System_Collections_IList_RemoveAt_mE575B66B97A5547B92B86551F53E8316411A1A4F (void);
extern void JsonData_System_Collections_Specialized_IOrderedDictionary_GetEnumerator_m277AB6743B8845FE83E7DB3A71456CAAFB8620D5 (void);
extern void JsonData_get_Count_m59A660054922D869F9CC95947848D1829AD2680C (void);
extern void JsonData_get_Item_m6A08F7BE15696506EC603C2464772EE42AC216E7 (void);
extern void JsonData_set_Item_m6FE3B6A0E6C79F7B3661945A402CBEAB66371202 (void);
extern void JsonData_get_Item_m86C371130EC3E4EC35A09E33C860CD825276A10D (void);
extern void JsonData_set_Item_m874D334A667986B4FBC1515687EF6B551D71FAE8 (void);
extern void JsonData_EnsureCollection_mFB01D813BC8765113931EF533783BA5BC764C283 (void);
extern void JsonData_EnsureDictionary_m402293ECDF1F18AE8CA8259A102E698ADD3A8EB2 (void);
extern void JsonData_EnsureList_m6FCDB06F594222F9584291F20B3DA2FF4E5CE718 (void);
extern void JsonData_ToJsonData_mD42F841238609F7087B993D39CC6EC3F39F8BACC (void);
extern void JsonData_Add_mA88DA1756863A2C121F7D72986EDF2EF6E0B88E1 (void);
extern void JsonData_Equals_m7369F0352ABADD880C05AC75E5B755FC38A60B45 (void);
extern void JsonData_SetJsonType_m1E8E1D8FA51CA48796DB13ECD7DA808A7576A7C3 (void);
extern void JsonData_ToString_m7F6F96196EDD1B0767AED023EC8640959B6D9382 (void);
extern void OrderedDictionaryEnumerator__ctor_mE835230F6C9BF7EBC082A282BE74976A450B07A1 (void);
extern void OrderedDictionaryEnumerator_get_Current_m26CAEFF45373A3A4EB6AA364345D4728F9616E6D (void);
extern void OrderedDictionaryEnumerator_get_Entry_mD2E0B7A30035474EB30FC5AB71B4C299E0C9124A (void);
extern void OrderedDictionaryEnumerator_get_Key_mD0051B27FCA09C476E348C40D1B8E8E48F3B8F9B (void);
extern void OrderedDictionaryEnumerator_get_Value_m666D246C6E8795EACCD622E49139F5B0A1AD8D2D (void);
extern void OrderedDictionaryEnumerator_MoveNext_m0862F64D1B571A878D85F0EA7B91F84C44A02B61 (void);
extern void OrderedDictionaryEnumerator_Reset_m28A5AD433A2A8AF31DEF35FA549C0FFB356A3533 (void);
extern void JsonException__ctor_mD12D72A1D7949F48B846F1207BFA40EB8CB61E23 (void);
extern void JsonException__ctor_m267C4D3DA6783E685C755BEB2B354A15AB620363 (void);
extern void JsonException__ctor_m400377A21C20F6CAEE97CE7958869D819BB94BB5 (void);
extern void ExporterFunc__ctor_mE42C7A27446C000BC923E32171289DA0E9CDC5D2 (void);
extern void ExporterFunc_Invoke_mE96AA8DA43B26B809D4D5D09A1A22966C81D2BDD (void);
extern void ImporterFunc__ctor_m630470C9B15EC4550BDC9B0A860B2F9C3B66C029 (void);
extern void ImporterFunc_Invoke_m143089455BBD8920FDB4F1B683E685B50FB8077A (void);
extern void WrapperFactory__ctor_mDCB80755422A0F810CF4702BBE931A9B4AE5C809 (void);
extern void WrapperFactory_Invoke_mDEC930B07F8B4652EBECE1E6386E92701A93C7D5 (void);
extern void JsonMapper__cctor_mB5697D724FF6E6DB1BBAB0F6128A410F9D2E124A (void);
extern void JsonMapper_ReadValue_m1E79EF3FC740E018A6C14D7D4A5D9FBE12239856 (void);
extern void JsonMapper_RegisterBaseExporters_m1866D55E6CA556A86EB3F4E4C484706A596A34D9 (void);
extern void JsonMapper_RegisterBaseImporters_m108AF061EDD0D019D96B7276A59E909010EF6F79 (void);
extern void JsonMapper_RegisterImporter_m091B42BD8AE8DBD08A21E950E11677E1B87363ED (void);
extern void JsonMapper_ToObject_m4CE0C423373F28612B2140D6D1A3DCE4F9161FBA (void);
extern void JsonMapper_ToWrapper_mA6DFAACCFE4197B6B71E3AB7B325EF9C5D0739E6 (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__1_m110FF8AA5363BCD46796FAD1F2D8FED836BFDDBF (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__2_m0FD239C7D31079CE0675AFB2A018A1FC3F43B011 (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__3_mA29C3028DA92212563CFE9D81330CF54DAB65B8B (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__4_mCBDFEBE0258C9356C1344220ED444CE561296DAF (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__5_mF9877906A7374E0EF585198C87932BA56E702E16 (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__6_m990DB5417D13B4039F5ECF7EE748C4A26358B8CD (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__7_mCADDB28F9A068F794CFFCA92C11FEAB6789505F4 (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__8_mDA9844C013501A93EB4D8E6EAEE1F16FF13A6575 (void);
extern void JsonMapper_U3CRegisterBaseExportersU3Em__9_m94AD3AE236D76E40347D15F45F76CC0B27ECC821 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__A_mA1F5749E268B8573AE59CA4AFDA294E06C5B495D (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__B_m3ADD4F61880F2DBF16970403F0C4AB6D0CA5C866 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__C_m8F5FA54FC18A79A77BD4A8063E6EFDD101FBE7A9 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__D_m82DF7F475A2E0DA9EB74C493C41475475EC56DF7 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__E_mFF7D39CF1F9C6539031853BF7282E96823BFDD6F (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__F_mBD6BB1B68A7163693A2F8207F22CA0E9DBD1CDB3 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__10_m373921C4F987CB8342108CA7B892AFC9359FEA94 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__11_m3DB56AA4D337205711CC07D354E67D28C1C1C586 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__12_m31F2706B1EDA98CF528C80328ACB64DBF7A3B95D (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__13_m881B1EBE03FFD1ECAAEAD3FB23ACA14D2F639B07 (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__14_mDD8CDBB9BBD6B45989EDBA945F727DE509618E2C (void);
extern void JsonMapper_U3CRegisterBaseImportersU3Em__15_m6951826F16113B0A6299D08457BFC32A6F46C08C (void);
extern void JsonMapper_U3CToObjectU3Em__18_mAE23DE92E73609349AFAEEF64FD17CFC4849BB08 (void);
extern void JsonReader__ctor_mA7B3448630453E385BCAADCA985410AFFCE4ACC3 (void);
extern void JsonReader__ctor_mDC4C842CE01B549E193647B6BFEE5420187C446A (void);
extern void JsonReader__cctor_mA3D3F44D91D95EE86E0813A91391537E68F2EF5E (void);
extern void JsonReader_get_Token_m519B39A4B87C3A9A7B424AECAE7FDA64B946E975 (void);
extern void JsonReader_get_Value_mF219BCB43B2F020C1B67935AA63A40ECD1EE4C05 (void);
extern void JsonReader_PopulateParseTable_mCC3169C58AC1DD19682E113FF9AD2F71BF95980C (void);
extern void JsonReader_TableAddCol_m4A0AD9C37635A1C83AF78E463F603162DB292700 (void);
extern void JsonReader_TableAddRow_m22740E32484278985D3C41B39D5BE6CFD7EFBE1F (void);
extern void JsonReader_ProcessNumber_mF3EF4D91F87594C1D6357FDE08D539DDB66A624D (void);
extern void JsonReader_ProcessSymbol_m0E855A59E8E386619529E923A6A9BABFEE63769C (void);
extern void JsonReader_ReadToken_m5C6E65529B9286DFC6270FE4A926FA12620894B9 (void);
extern void JsonReader_Close_m91AEDB0DE517F63898D830A3140B83032CFE8F2F (void);
extern void JsonReader_Read_mD8D28369934A79C6A0125279F425E48D1D35AE7B (void);
extern void WriterContext__ctor_m776EF36E46B33DFC054B4AD1E6F3139B038AF037 (void);
extern void JsonWriter__ctor_mFF4C2DE4CAD061714750C2D87291FCF9D0AE4EF0 (void);
extern void JsonWriter__cctor_m64A42C0DCDC27BC8FFB605455420856AC720739F (void);
extern void JsonWriter_DoValidation_m3F03AD6E422930F933D182AD01154E08F32EFF3B (void);
extern void JsonWriter_Init_m9DD0FC6AED98A7D661B9374FFDFF534411DDB838 (void);
extern void JsonWriter_IntToHex_mF5E06CC13D2F0D5229D2BD63C5FD75D07CD2E2FE (void);
extern void JsonWriter_Put_mFABB67DDE2B46F39CB3BD04A8A883D908934CDF0 (void);
extern void JsonWriter_PutNewline_mFE1588405B1DBE7ADC65A867FB1DB57A0D9CD3C4 (void);
extern void JsonWriter_PutNewline_mC6A36636BB9D628BCAB9C2F1B4C27546A9D07100 (void);
extern void JsonWriter_PutString_m8BD1DBB2B72F8027F3B9DE4CD5D4807C5849F3D1 (void);
extern void JsonWriter_ToString_m787891ECDDA05AC2F51A2571A273ED2A5FF984FC (void);
extern void JsonWriter_Write_m2042A55B478D11A2C4BB399CE96C17DB3D85133A (void);
extern void JsonWriter_Write_m3A3246FEFCA9BCEB4F3E7D2D5AC0FF49B8BBD3A7 (void);
extern void JsonWriter_Write_mC6C394BE18186CAEB8597055057A1AECDEE56916 (void);
extern void JsonWriter_Write_mA2318C6CCFB4A565564457D435FF8EDEDB0F6EAB (void);
extern void FsmContext__ctor_m65740EA47916AE8C2881DD66B2EEE0CECA3E7189 (void);
extern void Lexer__ctor_mEF8BD28E77BCC5B98C34C9CE51CD2DCF786CA5A9 (void);
extern void Lexer__cctor_m45DEF85CA6C7779F89F2D875FC750EE22B0B12E9 (void);
extern void Lexer_get_EndOfInput_m450A1AC83F893FDE38826AFD2A726428956491AC (void);
extern void Lexer_get_Token_m77727EC36916D1258709D74850710E0487B67F99 (void);
extern void Lexer_get_StringValue_mE4230649FF6DC2B66F9FAA021BB30FC8B7EBA5FF (void);
extern void Lexer_HexValue_m5FF113620372573CAA3AAA87DE9BFDFE9D1AA83A (void);
extern void Lexer_PopulateFsmTables_m4FBE23E5C025665992DDA5398537C9D180A15BF7 (void);
extern void Lexer_ProcessEscChar_m1E23F62F3A8846AA52FD98E49917B3A8C9C168E3 (void);
extern void Lexer_State1_m35B7B80F34B274EC767AAE1FB3F61D2DB4A64699 (void);
extern void Lexer_State2_m205EFB31447C24D81684803A8952AE47F34289E2 (void);
extern void Lexer_State3_mD4320873954DA33A7B26AA9B6CF7CD64A8588783 (void);
extern void Lexer_State4_m88410392996F1D5DAB6DADC91AE16FC7CA54FF86 (void);
extern void Lexer_State5_mA1FA2D1CC7595C3D70A02973568DADDD27CFFA87 (void);
extern void Lexer_State6_m4B7FE39523E86CA3283F618065D8647CCFC6C0B3 (void);
extern void Lexer_State7_m5464256E9F4EF412B26461E52B133812F79EFA41 (void);
extern void Lexer_State8_mEE57AB611D759135E18E796C685799AF59653DF1 (void);
extern void Lexer_State9_m2A2DF75F8B8AEFAAFF778393AFCC9605ED84C01C (void);
extern void Lexer_State10_m5904073970AD0D36A52E6EF3528D8932E8588D59 (void);
extern void Lexer_State11_m5A9D0CA38509AB81A532487CF5D2DC65D6B71F04 (void);
extern void Lexer_State12_m958CFDBC029123955374295C3E9913D1599A196D (void);
extern void Lexer_State13_m560152ABC44290068A66CB6BFC939C5AAFEC51FF (void);
extern void Lexer_State14_m8C25AB90286E32F384E390E17E726F1BC08BC9DD (void);
extern void Lexer_State15_m00E0DE3464F1159F7EB835A3286EFFA40B558403 (void);
extern void Lexer_State16_m8BD82487456FA900D6BBDED72C8C975696D67804 (void);
extern void Lexer_State17_mDFB66C6B09C25B8B8CC2EC226F446FEF70AFE493 (void);
extern void Lexer_State18_m2C29A371734A3AF4B66ACD39EA4356FCEEB42703 (void);
extern void Lexer_State19_mD468E390FBA1C3B79566FD0CF8AB183BEF7B9A7C (void);
extern void Lexer_State20_mB134E41DBE7A01B14B8ADBBEDFAE16596CB76C1A (void);
extern void Lexer_State21_m98740912488BE0C617E7112B1EFA0CC52080297A (void);
extern void Lexer_State22_m40E243F9BE396E171757231B9D5045880E92EC2D (void);
extern void Lexer_State23_m14C029F3770CEF5E40A14DE7B3D3C2952D9C385F (void);
extern void Lexer_State24_mD981E66641429ED14661F0D83F155ACB0ADE601E (void);
extern void Lexer_State25_m846973336CD8B49363EBCFB9EF83676772B9B6F3 (void);
extern void Lexer_State26_m464EBDB9597D118A7666A693F407F7E7126752EB (void);
extern void Lexer_State27_mFDC378B071D2ABA7045A6CE1B8AE7C7F40D99638 (void);
extern void Lexer_State28_m151FEC6858FB9E2A515CABFEFEDE5DAB3F736F91 (void);
extern void Lexer_GetChar_m240416C419AACCDFAD8C74F764421740C53A6253 (void);
extern void Lexer_NextChar_m4416B29A729201704EF5494475D04A81665E7D9D (void);
extern void Lexer_NextToken_m1FC49AB01B8C3D281961541D1D7D1A8721B06083 (void);
extern void Lexer_UngetChar_m1FBA3B548EFBD21F87308A001FD32ECCCF86C5D4 (void);
extern void StateHandler__ctor_m33A94C96682C78721802E376BDC6673C9CDB08E1 (void);
extern void StateHandler_Invoke_mAED7B745D78A2D2DC62A793247492E7718A3210E (void);
static Il2CppMethodPointer s_methodPointers[169] = 
{
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	JsonData__ctor_m6811B52CF3A742B6E425A0C8C02A7F475EC70C22,
	JsonData__ctor_mA02A77C55F31BBE7F62D84ADEAC445BB4D6BF6F0,
	JsonData_System_Collections_ICollection_get_Count_m1928821DE12D36BC1ADD18A8640515E26712421D,
	JsonData_System_Collections_ICollection_get_IsSynchronized_m916ECDC97D55234918E33C68EBC7096AC3E9BB8C,
	JsonData_System_Collections_ICollection_get_SyncRoot_mBAB43E3293672F6EB626FA687E552B51DC2F1FC2,
	JsonData_System_Collections_IDictionary_get_IsReadOnly_m4DDCFBDE28ED5393BC1DC2871B454D50271BB4BB,
	JsonData_System_Collections_IDictionary_get_Keys_m09C045695EBC903E21448D3DA5A9A5D787683D96,
	JsonData_System_Collections_IDictionary_get_Values_m5C4630F668BCD06DF24FF2A25D78D1A52D249099,
	JsonData_System_Collections_IList_get_IsFixedSize_m5E943C4DE3DD920E91CD7C1F2E53192FA28200AE,
	JsonData_System_Collections_IList_get_IsReadOnly_m6E271699DD735D2E1123085A3508AC27D735C4F7,
	JsonData_System_Collections_IDictionary_get_Item_m8E67E7723DA1D430AC2E2DDD1E592C708EFFB032,
	JsonData_System_Collections_IDictionary_set_Item_m9E5C2463B2821FC2B3BE28F28F1050AE388902F9,
	JsonData_System_Collections_IList_get_Item_mD896EB10F113D680EDFBC3D495131B2FCDEC55EF,
	JsonData_System_Collections_IList_set_Item_mB7EAC712FD88B435CF5D885BF0A678B09C19C60E,
	JsonData_System_Collections_ICollection_CopyTo_m718153CFF6828AF6E374C7FDF80D7FD675544520,
	JsonData_System_Collections_IDictionary_Add_m8F766EE45CE3613EEE5136B5AC233C3FF3A16A1A,
	JsonData_System_Collections_IDictionary_Clear_m285AB467A43A7C41A8427B796E17F213AEA51528,
	JsonData_System_Collections_IDictionary_Contains_m02095723C5D92D7C080B137650156C56261FA23C,
	JsonData_System_Collections_IDictionary_GetEnumerator_mC7F4FE47A8BD7DD7A9941C7D62BDC8906FBE7AAE,
	JsonData_System_Collections_IDictionary_Remove_m16DF2045A4C3B23570ACBFBF890CEABF481F81CF,
	JsonData_System_Collections_IEnumerable_GetEnumerator_mFE8CBE09E06D74A6EFDBA81CC6755C65451862E2,
	JsonData_LitJson_IJsonWrapper_SetBoolean_mF61DEBF6BDEFCEEC1CA6786DB676415813DCB014,
	JsonData_LitJson_IJsonWrapper_SetDouble_mDE0713F66AB143C6F3705DA877F01C9E60D76553,
	JsonData_LitJson_IJsonWrapper_SetInt_m2FD29586CD2F56322C23D12E7622A30053C704D5,
	JsonData_LitJson_IJsonWrapper_SetLong_mE40BE10F5427485DBFE8018E34C61EA862B18716,
	JsonData_LitJson_IJsonWrapper_SetString_m1D50848A9C4D6FDA2EA3A0B48AB58F27B859BFBA,
	JsonData_System_Collections_IList_Add_mBB90F2B605DA48CF3B2BB283D83E3B68982D7857,
	JsonData_System_Collections_IList_Clear_m480936A33C1592C34894FCCB427D2A650841D15B,
	JsonData_System_Collections_IList_Contains_m524B6DEDCF030862D76E60DE0EF23CE305052A16,
	JsonData_System_Collections_IList_IndexOf_mC8D5D8C8B485992303ACF9F535A63BEBABDAC186,
	JsonData_System_Collections_IList_Insert_m968CA5587708151BD18906CD3A30CA3BB452D4F1,
	JsonData_System_Collections_IList_Remove_m6BE0CE268E656B03AFDF0E8F532C2D7C2A35611E,
	JsonData_System_Collections_IList_RemoveAt_mE575B66B97A5547B92B86551F53E8316411A1A4F,
	JsonData_System_Collections_Specialized_IOrderedDictionary_GetEnumerator_m277AB6743B8845FE83E7DB3A71456CAAFB8620D5,
	JsonData_get_Count_m59A660054922D869F9CC95947848D1829AD2680C,
	JsonData_get_Item_m6A08F7BE15696506EC603C2464772EE42AC216E7,
	JsonData_set_Item_m6FE3B6A0E6C79F7B3661945A402CBEAB66371202,
	JsonData_get_Item_m86C371130EC3E4EC35A09E33C860CD825276A10D,
	JsonData_set_Item_m874D334A667986B4FBC1515687EF6B551D71FAE8,
	JsonData_EnsureCollection_mFB01D813BC8765113931EF533783BA5BC764C283,
	JsonData_EnsureDictionary_m402293ECDF1F18AE8CA8259A102E698ADD3A8EB2,
	JsonData_EnsureList_m6FCDB06F594222F9584291F20B3DA2FF4E5CE718,
	JsonData_ToJsonData_mD42F841238609F7087B993D39CC6EC3F39F8BACC,
	JsonData_Add_mA88DA1756863A2C121F7D72986EDF2EF6E0B88E1,
	JsonData_Equals_m7369F0352ABADD880C05AC75E5B755FC38A60B45,
	JsonData_SetJsonType_m1E8E1D8FA51CA48796DB13ECD7DA808A7576A7C3,
	JsonData_ToString_m7F6F96196EDD1B0767AED023EC8640959B6D9382,
	OrderedDictionaryEnumerator__ctor_mE835230F6C9BF7EBC082A282BE74976A450B07A1,
	OrderedDictionaryEnumerator_get_Current_m26CAEFF45373A3A4EB6AA364345D4728F9616E6D,
	OrderedDictionaryEnumerator_get_Entry_mD2E0B7A30035474EB30FC5AB71B4C299E0C9124A,
	OrderedDictionaryEnumerator_get_Key_mD0051B27FCA09C476E348C40D1B8E8E48F3B8F9B,
	OrderedDictionaryEnumerator_get_Value_m666D246C6E8795EACCD622E49139F5B0A1AD8D2D,
	OrderedDictionaryEnumerator_MoveNext_m0862F64D1B571A878D85F0EA7B91F84C44A02B61,
	OrderedDictionaryEnumerator_Reset_m28A5AD433A2A8AF31DEF35FA549C0FFB356A3533,
	JsonException__ctor_mD12D72A1D7949F48B846F1207BFA40EB8CB61E23,
	JsonException__ctor_m267C4D3DA6783E685C755BEB2B354A15AB620363,
	JsonException__ctor_m400377A21C20F6CAEE97CE7958869D819BB94BB5,
	ExporterFunc__ctor_mE42C7A27446C000BC923E32171289DA0E9CDC5D2,
	ExporterFunc_Invoke_mE96AA8DA43B26B809D4D5D09A1A22966C81D2BDD,
	ImporterFunc__ctor_m630470C9B15EC4550BDC9B0A860B2F9C3B66C029,
	ImporterFunc_Invoke_m143089455BBD8920FDB4F1B683E685B50FB8077A,
	WrapperFactory__ctor_mDCB80755422A0F810CF4702BBE931A9B4AE5C809,
	WrapperFactory_Invoke_mDEC930B07F8B4652EBECE1E6386E92701A93C7D5,
	JsonMapper__cctor_mB5697D724FF6E6DB1BBAB0F6128A410F9D2E124A,
	JsonMapper_ReadValue_m1E79EF3FC740E018A6C14D7D4A5D9FBE12239856,
	JsonMapper_RegisterBaseExporters_m1866D55E6CA556A86EB3F4E4C484706A596A34D9,
	JsonMapper_RegisterBaseImporters_m108AF061EDD0D019D96B7276A59E909010EF6F79,
	JsonMapper_RegisterImporter_m091B42BD8AE8DBD08A21E950E11677E1B87363ED,
	JsonMapper_ToObject_m4CE0C423373F28612B2140D6D1A3DCE4F9161FBA,
	JsonMapper_ToWrapper_mA6DFAACCFE4197B6B71E3AB7B325EF9C5D0739E6,
	JsonMapper_U3CRegisterBaseExportersU3Em__1_m110FF8AA5363BCD46796FAD1F2D8FED836BFDDBF,
	JsonMapper_U3CRegisterBaseExportersU3Em__2_m0FD239C7D31079CE0675AFB2A018A1FC3F43B011,
	JsonMapper_U3CRegisterBaseExportersU3Em__3_mA29C3028DA92212563CFE9D81330CF54DAB65B8B,
	JsonMapper_U3CRegisterBaseExportersU3Em__4_mCBDFEBE0258C9356C1344220ED444CE561296DAF,
	JsonMapper_U3CRegisterBaseExportersU3Em__5_mF9877906A7374E0EF585198C87932BA56E702E16,
	JsonMapper_U3CRegisterBaseExportersU3Em__6_m990DB5417D13B4039F5ECF7EE748C4A26358B8CD,
	JsonMapper_U3CRegisterBaseExportersU3Em__7_mCADDB28F9A068F794CFFCA92C11FEAB6789505F4,
	JsonMapper_U3CRegisterBaseExportersU3Em__8_mDA9844C013501A93EB4D8E6EAEE1F16FF13A6575,
	JsonMapper_U3CRegisterBaseExportersU3Em__9_m94AD3AE236D76E40347D15F45F76CC0B27ECC821,
	JsonMapper_U3CRegisterBaseImportersU3Em__A_mA1F5749E268B8573AE59CA4AFDA294E06C5B495D,
	JsonMapper_U3CRegisterBaseImportersU3Em__B_m3ADD4F61880F2DBF16970403F0C4AB6D0CA5C866,
	JsonMapper_U3CRegisterBaseImportersU3Em__C_m8F5FA54FC18A79A77BD4A8063E6EFDD101FBE7A9,
	JsonMapper_U3CRegisterBaseImportersU3Em__D_m82DF7F475A2E0DA9EB74C493C41475475EC56DF7,
	JsonMapper_U3CRegisterBaseImportersU3Em__E_mFF7D39CF1F9C6539031853BF7282E96823BFDD6F,
	JsonMapper_U3CRegisterBaseImportersU3Em__F_mBD6BB1B68A7163693A2F8207F22CA0E9DBD1CDB3,
	JsonMapper_U3CRegisterBaseImportersU3Em__10_m373921C4F987CB8342108CA7B892AFC9359FEA94,
	JsonMapper_U3CRegisterBaseImportersU3Em__11_m3DB56AA4D337205711CC07D354E67D28C1C1C586,
	JsonMapper_U3CRegisterBaseImportersU3Em__12_m31F2706B1EDA98CF528C80328ACB64DBF7A3B95D,
	JsonMapper_U3CRegisterBaseImportersU3Em__13_m881B1EBE03FFD1ECAAEAD3FB23ACA14D2F639B07,
	JsonMapper_U3CRegisterBaseImportersU3Em__14_mDD8CDBB9BBD6B45989EDBA945F727DE509618E2C,
	JsonMapper_U3CRegisterBaseImportersU3Em__15_m6951826F16113B0A6299D08457BFC32A6F46C08C,
	JsonMapper_U3CToObjectU3Em__18_mAE23DE92E73609349AFAEEF64FD17CFC4849BB08,
	JsonReader__ctor_mA7B3448630453E385BCAADCA985410AFFCE4ACC3,
	JsonReader__ctor_mDC4C842CE01B549E193647B6BFEE5420187C446A,
	JsonReader__cctor_mA3D3F44D91D95EE86E0813A91391537E68F2EF5E,
	JsonReader_get_Token_m519B39A4B87C3A9A7B424AECAE7FDA64B946E975,
	JsonReader_get_Value_mF219BCB43B2F020C1B67935AA63A40ECD1EE4C05,
	JsonReader_PopulateParseTable_mCC3169C58AC1DD19682E113FF9AD2F71BF95980C,
	JsonReader_TableAddCol_m4A0AD9C37635A1C83AF78E463F603162DB292700,
	JsonReader_TableAddRow_m22740E32484278985D3C41B39D5BE6CFD7EFBE1F,
	JsonReader_ProcessNumber_mF3EF4D91F87594C1D6357FDE08D539DDB66A624D,
	JsonReader_ProcessSymbol_m0E855A59E8E386619529E923A6A9BABFEE63769C,
	JsonReader_ReadToken_m5C6E65529B9286DFC6270FE4A926FA12620894B9,
	JsonReader_Close_m91AEDB0DE517F63898D830A3140B83032CFE8F2F,
	JsonReader_Read_mD8D28369934A79C6A0125279F425E48D1D35AE7B,
	WriterContext__ctor_m776EF36E46B33DFC054B4AD1E6F3139B038AF037,
	JsonWriter__ctor_mFF4C2DE4CAD061714750C2D87291FCF9D0AE4EF0,
	JsonWriter__cctor_m64A42C0DCDC27BC8FFB605455420856AC720739F,
	JsonWriter_DoValidation_m3F03AD6E422930F933D182AD01154E08F32EFF3B,
	JsonWriter_Init_m9DD0FC6AED98A7D661B9374FFDFF534411DDB838,
	JsonWriter_IntToHex_mF5E06CC13D2F0D5229D2BD63C5FD75D07CD2E2FE,
	JsonWriter_Put_mFABB67DDE2B46F39CB3BD04A8A883D908934CDF0,
	JsonWriter_PutNewline_mFE1588405B1DBE7ADC65A867FB1DB57A0D9CD3C4,
	JsonWriter_PutNewline_mC6A36636BB9D628BCAB9C2F1B4C27546A9D07100,
	JsonWriter_PutString_m8BD1DBB2B72F8027F3B9DE4CD5D4807C5849F3D1,
	JsonWriter_ToString_m787891ECDDA05AC2F51A2571A273ED2A5FF984FC,
	JsonWriter_Write_m2042A55B478D11A2C4BB399CE96C17DB3D85133A,
	JsonWriter_Write_m3A3246FEFCA9BCEB4F3E7D2D5AC0FF49B8BBD3A7,
	JsonWriter_Write_mC6C394BE18186CAEB8597055057A1AECDEE56916,
	JsonWriter_Write_mA2318C6CCFB4A565564457D435FF8EDEDB0F6EAB,
	FsmContext__ctor_m65740EA47916AE8C2881DD66B2EEE0CECA3E7189,
	Lexer__ctor_mEF8BD28E77BCC5B98C34C9CE51CD2DCF786CA5A9,
	Lexer__cctor_m45DEF85CA6C7779F89F2D875FC750EE22B0B12E9,
	Lexer_get_EndOfInput_m450A1AC83F893FDE38826AFD2A726428956491AC,
	Lexer_get_Token_m77727EC36916D1258709D74850710E0487B67F99,
	Lexer_get_StringValue_mE4230649FF6DC2B66F9FAA021BB30FC8B7EBA5FF,
	Lexer_HexValue_m5FF113620372573CAA3AAA87DE9BFDFE9D1AA83A,
	Lexer_PopulateFsmTables_m4FBE23E5C025665992DDA5398537C9D180A15BF7,
	Lexer_ProcessEscChar_m1E23F62F3A8846AA52FD98E49917B3A8C9C168E3,
	Lexer_State1_m35B7B80F34B274EC767AAE1FB3F61D2DB4A64699,
	Lexer_State2_m205EFB31447C24D81684803A8952AE47F34289E2,
	Lexer_State3_mD4320873954DA33A7B26AA9B6CF7CD64A8588783,
	Lexer_State4_m88410392996F1D5DAB6DADC91AE16FC7CA54FF86,
	Lexer_State5_mA1FA2D1CC7595C3D70A02973568DADDD27CFFA87,
	Lexer_State6_m4B7FE39523E86CA3283F618065D8647CCFC6C0B3,
	Lexer_State7_m5464256E9F4EF412B26461E52B133812F79EFA41,
	Lexer_State8_mEE57AB611D759135E18E796C685799AF59653DF1,
	Lexer_State9_m2A2DF75F8B8AEFAAFF778393AFCC9605ED84C01C,
	Lexer_State10_m5904073970AD0D36A52E6EF3528D8932E8588D59,
	Lexer_State11_m5A9D0CA38509AB81A532487CF5D2DC65D6B71F04,
	Lexer_State12_m958CFDBC029123955374295C3E9913D1599A196D,
	Lexer_State13_m560152ABC44290068A66CB6BFC939C5AAFEC51FF,
	Lexer_State14_m8C25AB90286E32F384E390E17E726F1BC08BC9DD,
	Lexer_State15_m00E0DE3464F1159F7EB835A3286EFFA40B558403,
	Lexer_State16_m8BD82487456FA900D6BBDED72C8C975696D67804,
	Lexer_State17_mDFB66C6B09C25B8B8CC2EC226F446FEF70AFE493,
	Lexer_State18_m2C29A371734A3AF4B66ACD39EA4356FCEEB42703,
	Lexer_State19_mD468E390FBA1C3B79566FD0CF8AB183BEF7B9A7C,
	Lexer_State20_mB134E41DBE7A01B14B8ADBBEDFAE16596CB76C1A,
	Lexer_State21_m98740912488BE0C617E7112B1EFA0CC52080297A,
	Lexer_State22_m40E243F9BE396E171757231B9D5045880E92EC2D,
	Lexer_State23_m14C029F3770CEF5E40A14DE7B3D3C2952D9C385F,
	Lexer_State24_mD981E66641429ED14661F0D83F155ACB0ADE601E,
	Lexer_State25_m846973336CD8B49363EBCFB9EF83676772B9B6F3,
	Lexer_State26_m464EBDB9597D118A7666A693F407F7E7126752EB,
	Lexer_State27_mFDC378B071D2ABA7045A6CE1B8AE7C7F40D99638,
	Lexer_State28_m151FEC6858FB9E2A515CABFEFEDE5DAB3F736F91,
	Lexer_GetChar_m240416C419AACCDFAD8C74F764421740C53A6253,
	Lexer_NextChar_m4416B29A729201704EF5494475D04A81665E7D9D,
	Lexer_NextToken_m1FC49AB01B8C3D281961541D1D7D1A8721B06083,
	Lexer_UngetChar_m1FBA3B548EFBD21F87308A001FD32ECCCF86C5D4,
	StateHandler__ctor_m33A94C96682C78721802E376BDC6673C9CDB08E1,
	StateHandler_Invoke_mAED7B745D78A2D2DC62A793247492E7718A3210E,
};
static const int32_t s_InvokerIndices[169] = 
{
	0,
	0,
	0,
	0,
	0,
	0,
	7120,
	5806,
	6957,
	6887,
	6992,
	6887,
	6992,
	6992,
	6887,
	6887,
	5181,
	3363,
	5178,
	3140,
	3358,
	3363,
	7120,
	4261,
	6992,
	5806,
	6992,
	5703,
	5735,
	5774,
	5775,
	5806,
	4927,
	7120,
	4261,
	4927,
	3140,
	5806,
	5774,
	6992,
	6957,
	5181,
	3363,
	5178,
	3140,
	6992,
	6992,
	6992,
	5181,
	4927,
	4261,
	5774,
	6992,
	5806,
	6992,
	6914,
	6992,
	6992,
	6887,
	7120,
	3140,
	5774,
	5806,
	3361,
	3363,
	3361,
	5181,
	3361,
	6992,
	10455,
	9381,
	10455,
	10455,
	8411,
	10054,
	9381,
	9630,
	9630,
	9630,
	9630,
	9630,
	9630,
	9630,
	9630,
	9630,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10054,
	10420,
	5806,
	3349,
	10455,
	6957,
	6992,
	10455,
	8908,
	10288,
	5806,
	7120,
	6887,
	7120,
	6887,
	7120,
	7120,
	10455,
	5774,
	7120,
	9578,
	5806,
	7120,
	5703,
	5806,
	6992,
	5731,
	5774,
	5806,
	5911,
	7120,
	5806,
	10455,
	6887,
	6957,
	6992,
	9938,
	10455,
	10221,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	9834,
	6887,
	6957,
	6887,
	7120,
	3361,
	4261,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_LitJson_CodeGenModule;
const Il2CppCodeGenModule g_LitJson_CodeGenModule = 
{
	"LitJson.dll",
	169,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
