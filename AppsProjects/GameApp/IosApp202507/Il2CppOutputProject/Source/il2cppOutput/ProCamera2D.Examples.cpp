﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404;
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87;
struct Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A;
struct Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D;
struct Action_1_t17E52B12DC24FA6C9DD52F87043C85BEA889BB81;
struct Action_2_tD7438462601D3939500ED67463331FE00CFFBDB8;
struct Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC;
struct Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812;
struct Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D;
struct List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57;
struct List_1_tD710F2DCC60EC6645D6D0ADEFA55C09149CA01C7;
struct List_1_tB29463B71ADEE1E700A39C09DD9E97D7BF861B56;
struct List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9;
struct List_1_t1791E7DFA1D93557B4182A3A22DB71EB334CAF26;
struct List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B;
struct List_1_tA3C7FC5F5E0B4B4569A099562D024EC820B7A736;
struct List_1_tD57C9D53046AC3BB8C09D4663F4EBFC90DE5DD3D;
struct List_1_tAE8C17631329643D4DD91E2AFC733129518F1658;
struct List_1_t22C8706215DB8F66A4ECB4526AE9EACCDDD09F13;
struct List_1_t846754A72A8D0F5018C71915DBB1733C81EA768A;
struct List_1_t8A4448F486334EEE52C4B80E9DCFEACC9A34E2A5;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9;
struct List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D;
struct List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct ConstantShakePresetU5BU5D_t49CC04ABE05C22062D7209C1E117F5F709E8EFA1;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA;
struct RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A;
struct ShakePresetU5BU5D_tE359554819939831E9CDCACFFBEFD440483F25C8;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct TransformU5BU5D_tBB9C5F5686CAE82E3D97D43DF0F3D68ABF75EC24;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct BasicBlit_tA071AFA0D92DFC9033375D6F3F91F72AF607BE36;
struct Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
struct CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A;
struct CinematicEvent_tDAE6664FB36E9107D40341F5B7D3D64A49BCFF09;
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D;
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct DollyZoomExample_t814B3A59AA027B91333AE71A085F411B6608C9EA;
struct Door_t83EFA7B7B191E0674D7E862310163478739F3412;
struct DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C;
struct EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7;
struct EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9;
struct EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5;
struct EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598;
struct EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A;
struct GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580;
struct GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD;
struct Goal_tC19E5DD1B9AF8D52A6A53E91481D91ADBF168DC7;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5;
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
struct InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F;
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7;
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct ObjectMove_tE3F80C31E2C5F3347A7A776EDBE04057B4A577D5;
struct ObjectRotate_t6CE493E48B3F500888371E20F836EFD742953929;
struct PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB;
struct PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25;
struct PlayerFire_t2C8120136448E86CF633452034219BED662DD897;
struct PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D;
struct PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08;
struct PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139;
struct PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349;
struct Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884;
struct PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967;
struct ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A;
struct ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3;
struct ProCamera2DLetterbox_t2F110298BC485EC44EA05404C6B06F2777A19B35;
struct ProCamera2DNumericBoundaries_t893DFE2AFEF9E650771DE8450BD32AED8E11FB81;
struct ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F;
struct ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D;
struct RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct RoomsEventsExample_t9DE410B556E96CD3D68F1801AADF2540C218EB0A;
struct RotateTowardsMouse_tD99BE10BDFBE8522BF3CFFA7243C56DDCCFAEF81;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct ShakeExample_t86ED4DE4D0913A0F315628DBC608F21E830AB469;
struct ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7;
struct String_t;
struct SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct ToggleCinematics_tEC2D374385ABC25CBA232CF013389B2531E2E860;
struct ToggleTransitionsFX_t490E8D3A95F46EF4DC1E08B1346906DAE04187EC;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t0F4A91E1BC4BF24B882873D8B2A23857DA9A8068;
struct Version_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663;
struct WaitForFixedUpdate_t86F5BC0B6A668AEF2903DE9ADB52062E457BD1B7;
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;
struct WillRenderCanvases_tA4A6E66DBA797DCB45B995DBA449A9D1D80D0FBC;
struct U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9;
struct U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259;
struct U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846;
struct U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6;
struct U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74;
struct U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2;
struct U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21;
struct U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205;
struct U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095;
struct U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED;
struct U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF;

IL2CPP_EXTERN_C RuntimeClass* Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Physics_t1244C2983AEAFA149425AFFC3DF53BC91C18ED56_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF____26665D041D2A2C9069CD019A28650232FAEF84B773FB5A2211529863B37E4A5C_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF____50F0C09C9E97F43467073A473E64F2F8D052FEF62B4E4B7A91A1FEF7459C2B9D_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0B29FFB04736A3117D7DA4AFEC9AE0B53E5646AB;
IL2CPP_EXTERN_C String_t* _stringLiteral0E43178D749CE80F6CE0900EB962639B261C328C;
IL2CPP_EXTERN_C String_t* _stringLiteral0E713ACF633164E12AFEC250F19E44318CD64482;
IL2CPP_EXTERN_C String_t* _stringLiteral1B765F0B9EC3B53F914342CAD5D040B5BDA2B472;
IL2CPP_EXTERN_C String_t* _stringLiteral265E15F1F86F1C766555899D5771CF29055DE75A;
IL2CPP_EXTERN_C String_t* _stringLiteral424CB2B1DDF0703D9F4845DFE8DF6E1D6E3B2EF9;
IL2CPP_EXTERN_C String_t* _stringLiteral49A840DE0DCD373EE5A1FD25D9FA425B31514AEC;
IL2CPP_EXTERN_C String_t* _stringLiteral4B39F111AB408DD7B675B3A73AD399E3BB0534FE;
IL2CPP_EXTERN_C String_t* _stringLiteral51D444D0D1FBB9356E618B00E52BA7230594A377;
IL2CPP_EXTERN_C String_t* _stringLiteral52CFCE10BC1C6F4D8459EED296A6E6259CB5A668;
IL2CPP_EXTERN_C String_t* _stringLiteral5538C14EC1E9AB85033B87760A6F2A11378A2EFF;
IL2CPP_EXTERN_C String_t* _stringLiteral5CE77766E15B47A06E71528457D3B0D979972409;
IL2CPP_EXTERN_C String_t* _stringLiteral65962AB7981D7DED4606F8986A6AA25EAE045B40;
IL2CPP_EXTERN_C String_t* _stringLiteral7632F406A3FA667DF7FF219692D6CD7654A29FB5;
IL2CPP_EXTERN_C String_t* _stringLiteral7F8C014BD4810CC276D0F9F81A1E759C7B098B1E;
IL2CPP_EXTERN_C String_t* _stringLiteral800C762D9EF92B399EC87C776239043ACEEC0717;
IL2CPP_EXTERN_C String_t* _stringLiteral8243A16D425F93AF62CAAB2BFAE01A2D6246A5FE;
IL2CPP_EXTERN_C String_t* _stringLiteral8964425D5AFA567D9602F7E19CD9E3B473DEBE0C;
IL2CPP_EXTERN_C String_t* _stringLiteral91230C09BBDA2AAD234635AF7C71EAC80771ED5D;
IL2CPP_EXTERN_C String_t* _stringLiteral956BD0602781733C18B694B8A7EBF75AF0604E9B;
IL2CPP_EXTERN_C String_t* _stringLiteral984FD2E5A843365D516B82897422143B40AE6B1A;
IL2CPP_EXTERN_C String_t* _stringLiteral9C7B3F1A3587CF2D890224D68044EF93011CC700;
IL2CPP_EXTERN_C String_t* _stringLiteralA24E8F43CBB762D069E3CE9638229CF0C314A006;
IL2CPP_EXTERN_C String_t* _stringLiteralA63846471F4FC7765968B3C6F42D5D2F4DB13743;
IL2CPP_EXTERN_C String_t* _stringLiteralBA3F223B0C372907E1ABF6FBB30F7DC516F3DB35;
IL2CPP_EXTERN_C String_t* _stringLiteralBA4FB70D8B307B91EB3CF9506CE8AC90AB2F08D1;
IL2CPP_EXTERN_C String_t* _stringLiteralC1A583013E708DA8E4D0F8DD299B6F59C7DD3195;
IL2CPP_EXTERN_C String_t* _stringLiteralCAF8804297181FF007CA835529DD4477CFD94A70;
IL2CPP_EXTERN_C String_t* _stringLiteralEF8AE9E6CBCFDABA932FBEB4C85964F450F724F5;
IL2CPP_EXTERN_C String_t* _stringLiteralF9F9900BCECE7884F52D555FF41B6CCEDE17B92E;
IL2CPP_EXTERN_C String_t* _stringLiteralFA1AF611B4DB4F50DD6ACB8DA170DC839A9CE0DD;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisEnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7_m0D967AF2579E9A205BDBE9224A50D30F221E853E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisEnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598_m5AE3E308D5D73FD9B4E5AD243EC030B07B6AA3B5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisEnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A_m80D4F7D42F81AB057F3EBCB6E7066FEC020250E5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisPatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB_mE6206CB158AF20B0B0718C08770946DA32D87CDB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_AddComponent_TisPoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967_m6DF34A43B92F7DE1925F4B4363A4DDEA778B117F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m36829EC89855C8C23CEDA8C5F5B12B76ADFE2248_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_RemoveAt_m3196E18C5CF157CAC58991FACEB9DFD441702260_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m447372C1EF7141193B93090A77395B786C72C7BC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mDC3E95DC5C927A867B9B42EDE1945F909B894268_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m2A804E6BB29E093E4995E7B9EEEC6879AB1D237E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m4C37ED2D928D63B80F55AF434730C2D64EEB9F22_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mB5E64608D47703A98476E026480AE38671047C87_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mF9EB0FFC5F687B97D08AB83657389E0E03C99DD8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m51C0846BFE6B5087A59640E0F6F4371DED3C771C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m70B7B7632B52F9FA2C101091272BC44317CEFAE0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_mE8DBE527F24D9CFED839C34216C475B716169979_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Object_FindObjectsOfType_TisProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3_m4EEF64F06A956AFFC4E5199CA2B0F124B2CD49A9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m10D87C6E0708CA912BBB02555BF7D0FBC5D7A2B3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayerInput_U3CStartU3Eb__8_0_m76653BA2097A5C65BA097230660F8D321A4076EC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayerInput_U3CStartU3Eb__8_1_mCAAC9ACD09B0DB47A5E94AA1F4B1E0698584CF65_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_Reset_m055746713979B4B4C1F920905D84B3C0D4F55C9B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CFireU3Ed__14_System_Collections_IEnumerator_Reset_mAAE10DF1D0AD7E34390EA24E0B24C2284739132C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CFireU3Ed__8_System_Collections_IEnumerator_Reset_mA16B82A9DF02A5BBB3009AA868FFB6175BDBDBED_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_Reset_m298311DFA1E697CC6529B47667B96F65D1C91BFF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CHitAnimU3Ed__13_System_Collections_IEnumerator_Reset_mC293E5D1F71063700FC867EC86C325635844BC88_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CHitAnimU3Ed__5_System_Collections_IEnumerator_Reset_m21C0332FD0DD74A1C22BCD2F196B9EAE65CB047A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_Reset_m07A15C1319B75C4CCC440FCC4C0B203C523BF341_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m83038F31C79F09ADC8371A69D36E3CD9EA9FBDBC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_Reset_m49E9633C06623C867A599BEC40859603D93CBE8C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_Reset_m5C882BB510DBCD5495FF38F35446C2A45EAA96C1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CStartU3Ed__10_System_Collections_IEnumerator_Reset_mDFE86914DF5B0391F9DE7A36C764BBFEEAAEE343_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;
struct GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com;
struct GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke;
struct RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_com;
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA;
struct RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t142894041757D375FF24EB9B4BA4801EF39A4FD5 
{
};
struct List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9  : public RuntimeObject
{
	ConstantShakePresetU5BU5D_t49CC04ABE05C22062D7209C1E117F5F709E8EFA1* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B  : public RuntimeObject
{
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9  : public RuntimeObject
{
	ShakePresetU5BU5D_tE359554819939831E9CDCACFFBEFD440483F25C8* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D  : public RuntimeObject
{
	TransformU5BU5D_tBB9C5F5686CAE82E3D97D43DF0F3D68ABF75EC24* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8  : public RuntimeObject
{
	InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382* ___m_Calls;
	PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25* ___m_PersistentCalls;
	bool ___m_CallsDirty;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t0F4A91E1BC4BF24B882873D8B2A23857DA9A8068  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D  : public RuntimeObject
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
};
struct U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* ___U3CU3E4__this;
};
struct U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* ___U3CU3E4__this;
};
struct U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* ___U3CU3E4__this;
};
struct U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* ___U3CU3E4__this;
};
struct U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* ___U3CU3E4__this;
};
struct U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* ___U3CU3E4__this;
};
struct U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	PlayerFire_t2C8120136448E86CF633452034219BED662DD897* ___U3CU3E4__this;
};
struct U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* ___U3CU3E4__this;
};
struct U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* ___U3CU3E4__this;
	WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663* ___U3CwaitForEndOfFrameU3E5__2;
};
struct U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* ___U3CU3E4__this;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB 
{
	int32_t ___m_Mask;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct Scene_tA1DC762B79745EB5140F054C884855B922318356 
{
	int32_t ___m_Handle;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977  : public UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___m_InvokeArray;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
};
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
	float ___m_Seconds;
};
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_marshaled_pinvoke : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
	float ___m_Seconds;
};
struct WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_marshaled_com : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
	float ___m_Seconds;
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D1600_t3774CF31C1BF6D3C5466B5D7EE179E42E6DAB291 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D1600_t3774CF31C1BF6D3C5466B5D7EE179E42E6DAB291__padding[1600];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D2063_t404AF1C2A2B49DEE604212CFCD1E95BF12A6554A 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D2063_t404AF1C2A2B49DEE604212CFCD1E95BF12A6554A__padding[2063];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE 
{
	bool ___hasValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___value;
};
struct CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4  : public RuntimeObject
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___TargetTransform;
	float ___TargetInfluenceH;
	float ___TargetInfluenceV;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___TargetOffset;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____targetPosition;
};
struct CollisionFlags_t3132E5D974C485D3F3C97B7AF475965AB0C3F9C1 
{
	int32_t ___value__;
};
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
	intptr_t ___m_Ptr;
};
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B_marshaled_pinvoke : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
};
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B_marshaled_com : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
	intptr_t ___m_Ptr;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct DoorDirection_tF6CA6F3D4C8792411120CDBCE731EBC08CE5C597 
{
	int32_t ___value__;
};
struct EaseType_t1FC67ECE18F0BCF3C9D072EF542517CD5E7DFB70 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct KeyCode_t75B9ECCC26D858F55040DDFF9523681E996D17E9 
{
	int32_t ___value__;
};
struct MovementAxis_t946C7D8B5D37F946995D2E084A44DEB1C5B1311B 
{
	int32_t ___value__;
};
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7  : public RuntimeObject
{
	intptr_t ___m_Ptr;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___m_Corners;
};
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_marshaled_com
{
	intptr_t ___m_Ptr;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___m_Corners;
};
struct NavMeshPathStatus_t1BCDA84284F9D364B8BB3BDF741D50D38932B205 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Point;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Normal;
	uint32_t ___m_FaceID;
	float ___m_Distance;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_UV;
	int32_t ___m_Collider;
};
struct RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5  : public RuntimeObject
{
	intptr_t ___m_Ptr;
	RuntimeObject* ___m_SourceStyle;
};
struct RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	Il2CppIUnknown* ___m_SourceStyle;
};
struct RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_com
{
	intptr_t ___m_Ptr;
	Il2CppIUnknown* ___m_SourceStyle;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct SendMessageOptions_t8C6881C01B06BF874EE578D27D8CF237EC2BFD54 
{
	int32_t ___value__;
};
struct TransitionFXDirection_tEBF6E97316E90450DE3947C396C89B8289A0A5A2 
{
	int32_t ___value__;
};
struct TransitionFXSide_t49A960F0736879C65CF917B23DCCA354D4BFB00C 
{
	int32_t ___value__;
};
struct TransitionsFXShaders_t06A162B0B48F1FBA5789E7907B2F4DA5EB4B189E 
{
	int32_t ___value__;
};
struct UpdateType_tC78D6C16E0565CC7C393A050CF169983047B10D7 
{
	int32_t ___value__;
};
struct U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	float ___delay;
	Door_t83EFA7B7B191E0674D7E862310163478739F3412* ___U3CU3E4__this;
	float ___duration;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___newPos;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CorigPosU3E5__2;
	float ___U3CtU3E5__3;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580  : public RuntimeObject
{
	intptr_t ___m_Ptr;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_Normal;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_Hover;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_Active;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_Focused;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_OnNormal;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_OnHover;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_OnActive;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95* ___m_OnFocused;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5* ___m_Border;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5* ___m_Padding;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5* ___m_Margin;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5* ___m_Overflow;
	String_t* ___m_Name;
};
struct GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_Normal;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_Hover;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_Active;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_Focused;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_OnNormal;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_OnHover;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_OnActive;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_pinvoke* ___m_OnFocused;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_pinvoke ___m_Border;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_pinvoke ___m_Padding;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_pinvoke ___m_Margin;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_pinvoke ___m_Overflow;
	char* ___m_Name;
};
struct GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_marshaled_com
{
	intptr_t ___m_Ptr;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_Normal;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_Hover;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_Active;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_Focused;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_OnNormal;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_OnHover;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_OnActive;
	GUIStyleState_t7A948723D9DCDFD8EE4F418B6EC909C18E023F95_marshaled_com* ___m_OnFocused;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_com* ___m_Border;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_com* ___m_Padding;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_com* ___m_Margin;
	RectOffset_t6358774A0DEEABA4586840CB9BC7DC88B39660B5_marshaled_com* ___m_Overflow;
	Il2CppChar* ___m_Name;
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87  : public MulticastDelegate_t
{
};
struct Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D  : public MulticastDelegate_t
{
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	float ___Intensity;
	List_1_tB29463B71ADEE1E700A39C09DD9E97D7BF861B56* ___Layers;
};
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___Strength;
	float ___Duration;
	int32_t ___Vibrato;
	float ___Randomness;
	float ___Smoothness;
	bool ___UseRandomInitialAngle;
	float ___InitialAngle;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___Rotation;
	bool ___IgnoreTimeScale;
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7  : public MulticastDelegate_t
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A  : public Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct BasePC2D_t160A3CFA0BA76B8D53AFE009ED12E10C7C0459A7  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ____pc2D;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3H;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3V;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3D;
	Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812* ___VectorHV;
	Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* ___VectorHVD;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
	bool ____enabled;
	int32_t ____serializedAxis;
};
struct Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___BulletDuration;
	float ___BulletSpeed;
	float ___SkinWidth;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___CollisionMask;
	float ___BulletDamage;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
	RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 ____raycastHit;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ____collisionPoint;
	float ____startTime;
	bool ____exploding;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____lastPos;
};
struct DollyZoomExample_t814B3A59AA027B91333AE71A085F411B6608C9EA  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___TargetFOV;
	float ___Duration;
	int32_t ___EaseType;
	float ___ZoomAmount;
};
struct Door_t83EFA7B7B191E0674D7E862310163478739F3412  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ____isOpen;
	int32_t ___DoorDirection;
	float ___MovementRange;
	float ___AnimDuration;
	float ___OpenDelay;
	float ___CloseDelay;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____origPos;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____moveCoroutine;
};
struct DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Door_t83EFA7B7B191E0674D7E862310163478739F3412* ___Door;
	String_t* ___PickupTag;
	ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* ___Cinematics;
};
struct EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___RotationSpeed;
	Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* ___BulletPool;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___WeaponTip;
	float ___FireRate;
	float ___FireAngleRandomness;
	bool ____hasTarget;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____target;
	NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* ____navMeshAgent;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
};
struct EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	int32_t ___Health;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___AttackColor;
	DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* ___Key;
	EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* ____sight;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* ____attack;
	EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* ____wander;
	RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* ____renderers;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ____originalColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ____currentColor;
};
struct EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___PathContainer;
	float ___WaypointOffset;
	bool ___Loop;
	bool ___IsPaused;
	NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* ____navMeshAgent;
	List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* ____path;
	int32_t ____currentWaypoint;
	bool ____hasReachedDestination;
	float ____stopTime;
};
struct EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* ___OnPlayerInSight;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnPlayerOutOfSight;
	float ___RefreshRate;
	float ___fieldOfViewAngle;
	float ___ViewDistance;
	bool ___playerInSight;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___player;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___LayerMask;
	RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5 ____hit;
};
struct EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___WanderDuration;
	float ___WaypointOffset;
	float ___WanderRadius;
	NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* ____navMeshAgent;
	bool ____hasReachedDestination;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____startingPos;
	float ____startingTime;
};
struct GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___GameOverScreen;
};
struct Goal_tC19E5DD1B9AF8D52A6A53E91481D91ADBF168DC7  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* ___GameOverScreen;
};
struct ObjectMove_tE3F80C31E2C5F3347A7A776EDBE04057B4A577D5  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___Amplitude;
	float ___Frequency;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
};
struct ObjectRotate_t6CE493E48B3F500888371E20F836EFD742953929  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___Rotation;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
};
struct PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___StopProbability;
	float ___StopDuration;
	float ___StopDurationVariation;
};
struct PlayerFire_t2C8120136448E86CF633452034219BED662DD897  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* ___BulletPool;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___WeaponTip;
	float ___FireRate;
	float ___FireShakeForce;
	float ___FireShakeDuration;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
};
struct PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	int32_t ___Health;
	RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* ____renderers;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ____originalColor;
};
struct PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___Body;
	float ___gravity;
	float ___runSpeed;
	float ___acceleration;
	float ___jumpHeight;
	int32_t ___jumpsAllowed;
	float ___currentSpeed;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___amountToMove;
	int32_t ___totalJumps;
	CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* ____characterController;
};
struct PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___RunSpeed;
	float ___Acceleration;
	float ____currentSpeedH;
	float ____currentSpeedV;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____amountToMove;
	int32_t ____totalJumps;
	CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* ____characterController;
	bool ____movementAllowed;
};
struct PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___Body;
	float ___gravity;
	float ___runSpeed;
	float ___acceleration;
	float ___jumpHeight;
	int32_t ___jumpsAllowed;
	float ___currentSpeed;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___amountToMove;
	int32_t ___totalJumps;
	bool ____fakeInputJump;
	float ____fakeInputHorizontalAxis;
	CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* ____characterController;
};
struct Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___thing;
	List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* ___things;
};
struct PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* ___pool;
};
struct ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57* ___CameraTargets;
	bool ___CenterTargetOnStart;
	int32_t ___Axis;
	int32_t ___UpdateType;
	bool ___FollowHorizontal;
	float ___HorizontalFollowSmoothness;
	bool ___FollowVertical;
	float ___VerticalFollowSmoothness;
	float ___OffsetX;
	float ___OffsetY;
	bool ___IsRelativeOffset;
	bool ___ZoomWithFOV;
	bool ___IgnoreTimeScale;
	float ____cameraTargetHorizontalPositionSmoothed;
	float ____cameraTargetVerticalPositionSmoothed;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CStartScreenSizeInWorldCoordinatesU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CScreenSizeInWorldCoordinatesU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CPreviousTargetsMidPointU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CTargetsMidPointU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CCameraTargetPositionU3Ek__BackingField;
	float ___U3CDeltaTimeU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CParentPositionU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____influencesSum;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___PreMoveUpdate;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___PostMoveUpdate;
	Action_1_t17E52B12DC24FA6C9DD52F87043C85BEA889BB81* ___OnCameraResize;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___OnUpdateScreenSizeFinished;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___OnDollyZoomFinished;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnReset;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___ExclusiveTargetPosition;
	int32_t ___CurrentZoomTriggerID;
	bool ___IsCameraPositionLeftBounded;
	bool ___IsCameraPositionRightBounded;
	bool ___IsCameraPositionTopBounded;
	bool ___IsCameraPositionBottomBounded;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___GameCamera;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3H;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3V;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3D;
	Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812* ___VectorHV;
	Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* ___VectorHVD;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____updateScreenSizeCoroutine;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____dollyZoomRoutine;
	List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B* ____influences;
	float ____originalCameraDepthSign;
	float ____previousCameraTargetHorizontalPositionSmoothed;
	float ____previousCameraTargetVerticalPositionSmoothed;
	int32_t ____previousScreenWidth;
	int32_t ____previousScreenHeight;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____previousCameraPosition;
	WaitForFixedUpdate_t86F5BC0B6A668AEF2903DE9ADB52062E457BD1B7* ____waitForFixedUpdate;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
	List_1_t22C8706215DB8F66A4ECB4526AE9EACCDDD09F13* ____preMovers;
	List_1_tA3C7FC5F5E0B4B4569A099562D024EC820B7A736* ____positionDeltaChangers;
	List_1_tD57C9D53046AC3BB8C09D4663F4EBFC90DE5DD3D* ____positionOverriders;
	List_1_t846754A72A8D0F5018C71915DBB1733C81EA768A* ____sizeDeltaChangers;
	List_1_t8A4448F486334EEE52C4B80E9DCFEACC9A34E2A5* ____sizeOverriders;
	List_1_tAE8C17631329643D4DD91E2AFC733129518F1658* ____postMovers;
};
struct RoomsEventsExample_t9DE410B556E96CD3D68F1801AADF2540C218EB0A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct RotateTowardsMouse_tD99BE10BDFBE8522BF3CFFA7243C56DDCCFAEF81  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___Ease;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
};
struct ShakeExample_t86ED4DE4D0913A0F315628DBC608F21E830AB469  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ____constantShaking;
};
struct SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	String_t* ____cameraMode;
};
struct ToggleCinematics_tEC2D374385ABC25CBA232CF013389B2531E2E860  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* ___Cinematics;
};
struct ToggleTransitionsFX_t490E8D3A95F46EF4DC1E08B1346906DAE04187EC  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3  : public BasePC2D_t160A3CFA0BA76B8D53AFE009ED12E10C7C0459A7
{
	UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* ___OnCinematicStarted;
	CinematicEvent_tDAE6664FB36E9107D40341F5B7D3D64A49BCFF09* ___OnCinematicTargetReached;
	UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* ___OnCinematicFinished;
	bool ____isPlaying;
	List_1_tD710F2DCC60EC6645D6D0ADEFA55C09149CA01C7* ___CinematicTargets;
	float ___EndDuration;
	int32_t ___EndEaseType;
	bool ___UseNumericBoundaries;
	bool ___UseLetterbox;
	float ___LetterboxAmount;
	float ___LetterboxAnimDuration;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___LetterboxColor;
	float ____initialCameraSize;
	ProCamera2DNumericBoundaries_t893DFE2AFEF9E650771DE8450BD32AED8E11FB81* ____numericBoundaries;
	bool ____numericBoundariesPreviousState;
	ProCamera2DLetterbox_t2F110298BC485EC44EA05404C6B06F2777A19B35* ____letterbox;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____startCinematicRoutine;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____goToCinematicRoutine;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____endCinematicRoutine;
	bool ____skipTarget;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____newPos;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____originalPos;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____startPos;
	float ____newSize;
	bool ____paused;
	int32_t ____poOrder;
	int32_t ____soOrder;
};
struct ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F  : public BasePC2D_t160A3CFA0BA76B8D53AFE009ED12E10C7C0459A7
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnShakeCompleted;
	List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9* ___ShakePresets;
	List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9* ___ConstantShakePresets;
	ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* ___StartConstantShakePreset;
	ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* ___CurrentConstantShakePreset;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____shakeParent;
	List_1_t1791E7DFA1D93557B4182A3A22DB71EB334CAF26* ____applyInfluencesCoroutines;
	List_1_t1791E7DFA1D93557B4182A3A22DB71EB334CAF26* ____shakeTimedCoroutines;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____shakeCoroutine;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____shakeVelocity;
	List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B* ____shakePositions;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ____rotationTarget;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ____originalRotation;
	float ____rotationTime;
	float ____rotationVelocity;
	List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B* ____influences;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____influencesSum;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ____constantShakePositions;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____constantShakePosition;
	bool ____isConstantShaking;
};
struct ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D  : public BasePC2D_t160A3CFA0BA76B8D53AFE009ED12E10C7C0459A7
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnTransitionEnterStarted;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnTransitionEnterEnded;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnTransitionExitStarted;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnTransitionExitEnded;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnTransitionStarted;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnTransitionEnded;
	int32_t ___TransitionShaderEnter;
	float ___DurationEnter;
	float ___DelayEnter;
	int32_t ___EaseTypeEnter;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___BackgroundColorEnter;
	int32_t ___SideEnter;
	int32_t ___DirectionEnter;
	int32_t ___BlindsEnter;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___TextureEnter;
	float ___TextureSmoothingEnter;
	int32_t ___TransitionShaderExit;
	float ___DurationExit;
	float ___DelayExit;
	int32_t ___EaseTypeExit;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___BackgroundColorExit;
	int32_t ___SideExit;
	int32_t ___DirectionExit;
	int32_t ___BlindsExit;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___TextureExit;
	float ___TextureSmoothingExit;
	bool ___StartSceneOnEnterState;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____transitionCoroutine;
	float ____step;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ____transitionEnterMaterial;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ____transitionExitMaterial;
	BasicBlit_tA071AFA0D92DFC9033375D6F3F91F72AF607BE36* ____blit;
	int32_t ____material_StepID;
	int32_t ____material_BackgroundColorID;
	String_t* ____previousEnterShader;
	String_t* ____previousExitShader;
};
struct List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9_StaticFields
{
	ConstantShakePresetU5BU5D_t49CC04ABE05C22062D7209C1E117F5F709E8EFA1* ___s_emptyArray;
};
struct List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B_StaticFields
{
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9_StaticFields
{
	ShakePresetU5BU5D_tE359554819939831E9CDCACFFBEFD440483F25C8* ___s_emptyArray;
};
struct List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D_StaticFields
{
	TransformU5BU5D_tBB9C5F5686CAE82E3D97D43DF0F3D68ABF75EC24* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF_StaticFields
{
	__StaticArrayInitTypeSizeU3D1600_t3774CF31C1BF6D3C5466B5D7EE179E42E6DAB291 ___26665D041D2A2C9069CD019A28650232FAEF84B773FB5A2211529863B37E4A5C;
	__StaticArrayInitTypeSizeU3D2063_t404AF1C2A2B49DEE604212CFCD1E95BF12A6554A ___50F0C09C9E97F43467073A473E64F2F8D052FEF62B4E4B7A91A1FEF7459C2B9D;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_StaticFields
{
	bool ___showKeyboardFocus;
	GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580* ___s_None;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26_StaticFields
{
	WillRenderCanvases_tA4A6E66DBA797DCB45B995DBA449A9D1D80D0FBC* ___preWillRenderCanvases;
	WillRenderCanvases_tA4A6E66DBA797DCB45B995DBA449A9D1D80D0FBC* ___willRenderCanvases;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___U3CexternBeginRenderOverlaysU3Ek__BackingField;
	Action_2_tD7438462601D3939500ED67463331FE00CFFBDB8* ___U3CexternRenderOverlaysBeforeU3Ek__BackingField;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___U3CexternEndRenderOverlaysU3Ek__BackingField;
};
struct ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_StaticFields
{
	Version_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7* ___Version;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ____instance;
};
struct ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3_StaticFields
{
	String_t* ___ExtensionName;
};
struct ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_StaticFields
{
	String_t* ___ExtensionName;
	ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* ____instance;
};
struct ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D_StaticFields
{
	String_t* ___ExtensionName;
	ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D* ____instance;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A  : public RuntimeArray
{
	ALIGN_FIELD (8) Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* m_Items[1];

	inline Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA  : public RuntimeArray
{
	ALIGN_FIELD (8) Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 m_Items[1];

	inline Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 value)
	{
		m_Items[index] = value;
	}
};
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C  : public RuntimeArray
{
	ALIGN_FIELD (8) Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 m_Items[1];

	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 value)
	{
		m_Items[index] = value;
	}
};
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C  : public RuntimeArray
{
	ALIGN_FIELD (8) float m_Items[1];

	inline float GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline float* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, float value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline float GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline float* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, float value)
	{
		m_Items[index] = value;
	}
};
struct ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA  : public RuntimeArray
{
	ALIGN_FIELD (8) ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* m_Items[1];

	inline ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponentInChildren_TisRuntimeObject_mE483A27E876DE8E4E6901D6814837F81D7C42F65_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* Component_GetComponentsInChildren_TisRuntimeObject_m1F5B6FC0689B07D4FAAC0C605D9B2933A9B32543_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* Object_FindObjectsOfType_TisRuntimeObject_m0B4DF4B8AB4C71E0F471BC9D0440B40844DA221D_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Object_Instantiate_TisRuntimeObject_m90A1E6C4C2B445D2E848DB75C772D1B95AAC046A_gshared (RuntimeObject* ___0_original, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* GameObject_AddComponent_TisRuntimeObject_m69B93700FACCF372F5753371C6E8FB780800B824_gshared (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987 (String_t* ___0_format, RuntimeObject* ___1_arg0, RuntimeObject* ___2_arg1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, float ___0_x, float ___1_y, float ___2_width, float ___3_height, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9 (GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GUI_Label_m0D7BA53414421D71010DFF628EAA6CCCB3DE737E (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_position, String_t* ___1_text, GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580* ___2_style, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float GUI_HorizontalSlider_mEED3CE859B1AF830E1851B3625AE127B4E6C5408 (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_position, float ___1_value, float ___2_leftValue, float ___3_rightValue, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_position, String_t* ___1_text, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2D_DollyZoom_mF2A6A56F4F4F55279FF3415812F9955047D58297 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, float ___0_targetFOV, float ___1_duration, int32_t ___2_easeType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2D_Zoom_m1D404B5C3D895095CAD74F7D61EEAF0D2B258E89 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, float ___0_zoomAmount, float ___1_duration, int32_t ___2_easeType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C (const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_m2A804E6BB29E093E4995E7B9EEEC6879AB1D237E_inline (List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Random_Range_m6763D9767F033357F88B6637F048F4ACA4123B68 (int32_t ___0_minInclusive, int32_t ___1_maxExclusive, const RuntimeMethod* method) ;
inline ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* List_1_get_Item_m51C0846BFE6B5087A59640E0F6F4371DED3C771C (List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* (*) (List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DShake_Shake_mE8B4BCA0C5CDE8FF4E46B3652E3C9C3966EDB2CA (ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* __this, ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* ___0_preset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DShake_StopConstantShaking_mDE370598162F40CD59FDB341CAF76CEF6A26C190 (ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* __this, float ___0_duration, const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_mF9EB0FFC5F687B97D08AB83657389E0E03C99DD8_inline (List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* List_1_get_Item_m70B7B7632B52F9FA2C101091272BC44317CEFAE0 (List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* (*) (List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DShake_ConstantShake_mC6A3FE872FF5DF20546CE0D4BDF99EFC0BD2B76E (ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* __this, ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* ___0_preset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_translation, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_magnitude_mF0D6017E90B345F1F52D1CC564C640F1A847AF2D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t LayerMask_op_Implicit_m7F5A5B9D079281AC445ED39DEE1FCFA9D795810D (LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___0_mask, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_origin, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_direction, RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* ___2_hitInfo, float ___3_maxDistance, int32_t ___4_layerMask, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_up_m1FBA5A97E5057747AC027AD5897EDE80A554D554 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_Collide_m6E81F876F22642A9655243F40B79445F404F7378 (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_Disable_mD396EDE0986A2FADE0AE307FD5180825D2DAFD6E (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_v, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Component_SendMessageUpwards_m00AF786700C3D6E42C7736F2AFA7166461409723 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, String_t* ___0_methodName, RuntimeObject* ___1_value, int32_t ___2_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91 (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_newPos, float ___1_duration, float ___2_delay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour_StopCoroutine_mB0FC91BE84203BD8E360B3FBAE5B958B4C5ED22A (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ___0_routine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Door_MoveRoutine_m6F58F24265CBB412BAB13B2AFC2F185077E0E274 (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_newPos, float ___1_duration, float ___2_delay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812 (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, RuntimeObject* ___0_routine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveRoutineU3Ed__14__ctor_m607707C035642A19E1DB9DC86F282FA23C0B54C3 (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* __this, float ___0_seconds, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31 (float ___0_start, float ___1_end, float ___2_value, int32_t ___3_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Component_CompareTag_mE6F8897E84F12DF12D302FFC4D58204D51096FC5 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, String_t* ___0_tag, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Door_get_IsOpen_m3E6B5405808EA7EDFE85AFBD191419C7F1E98ABD_inline (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door_OpenDoor_m2ED0EC299966E5A5DB499414EF8CCD5D2FB5286E (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, float ___0_openDelay, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DCinematics_Play_m0EC4378CCBB85B084497DBB3D3E6D3A0487A084B (ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, const RuntimeMethod* method) ;
inline NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponentInChildren_TisRuntimeObject_mE483A27E876DE8E4E6901D6814837F81D7C42F65_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NavMeshAgent_set_updateRotation_mBF6EDBC9BBAF32490229D7DD6BC821A420C3399D (NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyAttack_LookAtTarget_mF7E1575BAE9820A7EDEF1E877FFE48C6B7B34817 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyAttack_FollowTarget_m93D435D7A86AAC69D7F588E7B8CC45A3B34324B4 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyAttack_Fire_m67DACC353A9273C61DFBBBB66B8AEC6148A1800D (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLookAtTargetU3Ed__12__ctor_mE673E3FF67DBAE553F5C2C89180EB7BB25C97E04 (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFollowTargetU3Ed__13__ctor_m0EA79959FB6E07AEF9E7A9FA804EF113BA68D2A4 (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__14__ctor_mF974A778358856524656CBD0DE30B0DF026A72FB (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Random_get_insideUnitCircle_m**************************************** (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2_Normalize_m56DABCAB5967DF37A6B96710477D3660D800C652_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Pool_get_nextThing_mBB707503DABD3A940FA5D9E841E12C7CA3C3A0F1 (Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494 (float ___0_minInclusive, float ___1_maxInclusive, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_op_Multiply_mCB375FCCC12A2EC8F9EB824A1BFB4453B58C2012_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_lhs, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NavMeshAgent_set_destination_m5F0A8E4C8ED93798D6B9CE496B10FCE5B7461B95 (NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_LookRotation_mFB02EDC8F733774DFAC3BEA4B4BB265A228F8307 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_forward, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_upwards, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Slerp_m0A9969F500E7716EA4F6BC4E7D5464372D8E9E15 (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_a, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___1_b, float ___2_t, const RuntimeMethod* method) ;
inline EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* Component_GetComponent_TisEnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598_m5AE3E308D5D73FD9B4E5AD243EC030B07B6AA3B5 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
inline EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* Component_GetComponent_TisEnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7_m0D967AF2579E9A205BDBE9224A50D30F221E853E (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
inline EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* Component_GetComponent_TisEnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A_m80D4F7D42F81AB057F3EBCB6E7066FEC020250E5 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
inline RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponentsInChildren_TisRuntimeObject_m1F5B6FC0689B07D4FAAC0C605D9B2933A9B32543_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Material_get_color_mA4B7D4B96200D9D8B4F36BF19957E9DA81071DBB (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, const RuntimeMethod* method) ;
inline void Action_1__ctor_mCF523C720DF70BEA3148133C85868568FA91276D (Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_StartWandering_m2FFB17766D4C97045A8E29AB89C9276A5C7C35F5 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyFSM_HitAnim_m2E5E2A327FA865C218278AED5EBE2642611898DC (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Die_m71FBAD0CCFF374720C3A00DD8B2A9BEC447ADB5D (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__13__ctor_m44EAC43C99235EE91342314684E606005E438330 (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_StopWandering_mACE0DF86A60D0743E3B5E09CF35B917B9F96BAAA (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyAttack_Attack_mC8175509964CC1FDECB2489E43ABB9942BE4E041 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_target, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* ProCamera2D_AddCameraTarget_m552E7B3D6D1A458676149A0342E16A6C739667B7 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_targetTransform, float ___1_targetInfluenceH, float ___2_targetInfluenceV, float ___3_duration, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___4_targetOffset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_color, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyAttack_StopAttack_m06D74602CE6B55BCBE6D1E61794AEC0D1D1A9F31 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2D_RemoveCameraTarget_m1C694525D3A70E6F97E1A59887B7A355C91A2E03 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_targetTransform, float ___1_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_set_color_m5C32DEBB215FF9EE35E7B575297D8C2F29CC2A2D (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DShake_Shake_m96A4C403647156FFF72E5EEA997EA9EFA6AA050B (ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* __this, String_t* ___0_presetName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_DropLoot_m430F27F92C046E4BC274B64F0A2DE062CD1E631A (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, float ___1_t, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline (const RuntimeMethod* method) ;
inline void List_1__ctor_mDC3E95DC5C927A867B9B42EDE1945F909B894268 (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Transform_GetEnumerator_mA7E1C882ACA0C33E284711CD09971DEA3FFEF404 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
inline void List_1_Add_m36829EC89855C8C23CEDA8C5F5B12B76ADFE2248_inline (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D*, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float NavMeshAgent_get_remainingDistance_m051C1B408E2740A95B5A5577C5EC7222311AA73A (NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* __this, const RuntimeMethod* method) ;
inline Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* (*) (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* Component_GetComponent_TisPatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB_mE6206CB158AF20B0B0718C08770946DA32D87CDB (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Random_get_value_m2CEA87FADF5222EF9E13D32695F15E2BA282E24B (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, int32_t ___0_waypoint, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NavMeshAgent_set_isStopped_mF374E697F39845233B84D8C4873DEABC3AA490DF (NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* __this, bool ___0_value, const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_mB5E64608D47703A98476E026480AE38671047C87_inline (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool NavMeshAgent_SetDestination_mD5D960933827F1F14B29CF4A3B6F305C064EBF46 (NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_target, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CStartU3Ed__10__ctor_m05C20E28E3D83795CEF6B8F439B82FC39BC25D83 (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_from, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_to, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
inline void Action_1_Invoke_mC4804BC714747F815506CC787400EB84BA024D68_inline (Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_GoToWaypoint_mC46CF5C24F723424F8878E3A417A6DBE78AAAD15 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyWander_CheckAgentPosition_m7BFEDDFC1A658844F1B307FD28EECFA19B43CD71 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour_StopAllCoroutines_m872033451D42013A99867D09337490017E9ED318 (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CCheckAgentPositionU3Ed__10__ctor_mD1E04080E9A832ECEE6A50A4D142844CE11B33C9 (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NavMeshPath__ctor_mEA40BFC2492814FFC97A71C3AEC2154A9415C37F (NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Random_get_insideUnitSphere_mA488D6859560B73565B8D890ED6B39DB4091C54F (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool NavMeshAgent_CalculatePath_mF692688572E61C3B9FE776AA3784E14A08C259C2 (NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_targetPosition, NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* ___1_path, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t NavMeshPath_get_status_m63B0AEDA3149C7053987C4D0A02B3FE8B41BD74B (NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Time_set_timeScale_mEF84EE4B2376A458387648079B426B267862D331 (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Scene_tA1DC762B79745EB5140F054C884855B922318356 SceneManager_GetActiveScene_m0B320EC4302F51A71495D1CCD1A0FF9C2ED1FDC8 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Scene_get_name_m3C818DFA663E159274DAD823B780C7616C5E2A8C (Scene_tA1DC762B79745EB5140F054C884855B922318356* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E (String_t* ___0_sceneName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameOver_ShowScreen_m91A0DCC3CA95E7AC8C9895D6B7DF9E0BF7B480D4 (GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline (float ___0_d, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Rotate_m2A308205498AFEEA3DF784B1C86E4F7C126CA2EE (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_eulers, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2 (int32_t ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3 (int32_t ___0_button, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerFire_Fire_mC9C434664104BFAF935929AA4133DC30D6117C20 (PlayerFire_t2C8120136448E86CF633452034219BED662DD897* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__8__ctor_m1D08ACF93FC08FAEEE86A325184A3B83189EAD93 (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ProCamera2DShake_ApplyShakesTimed_mE54EEA471792ED1872EA7BD0B4C20D94CD8D44E9 (ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* __this, Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___0_shakes, Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___1_rotations, SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___2_durations, float ___3_smoothness, bool ___4_ignoreTimeScale, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434 (int32_t ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA (int32_t ___0_button, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerHealth_HitAnim_m502FE49C7A0F44B646B2D62B886D220235D48EE1 (PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__5__ctor_m7E9DD4E9433C185C734A9D36477091BCA69D2192 (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
inline CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
inline ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* Object_FindObjectsOfType_TisProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3_m4EEF64F06A956AFFC4E5199CA2B0F124B2CD49A9 (const RuntimeMethod* method)
{
	return ((  ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* (*) (const RuntimeMethod*))Object_FindObjectsOfType_TisRuntimeObject_m0B4DF4B8AB4C71E0F471BC9D0440B40844DA221D_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131 (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302 (UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* __this, UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___0_call, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62 (String_t* ___0_axisName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlayerInput_IncrementTowards_mD355CF9B9D9CDBC87C7C1DF45A2C7D3BE306C349 (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, float ___0_n, float ___1_target, float ___2_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4 (CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_motion, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline (float ___0_f, const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_m4C37ED2D928D63B80F55AF434730C2D64EEB9F22_inline (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m10D87C6E0708CA912BBB02555BF7D0FBC5D7A2B3 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___0_original, const RuntimeMethod* method)
{
	return ((  GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))Object_Instantiate_TisRuntimeObject_m90A1E6C4C2B445D2E848DB75C772D1B95AAC046A_gshared)(___0_original, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_value, const RuntimeMethod* method) ;
inline void List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_inline (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* __this, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B*, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967* GameObject_AddComponent_TisPoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967_m6DF34A43B92F7DE1925F4B4363A4DDEA778B117F (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_AddComponent_TisRuntimeObject_m69B93700FACCF372F5753371C6E8FB780800B824_gshared)(__this, method);
}
inline GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* List_1_get_Item_mE8DBE527F24D9CFED839C34216C475B716169979 (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* (*) (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline void List_1_RemoveAt_m3196E18C5CF157CAC58991FACEB9DFD441702260 (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	((  void (*) (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B*, int32_t, const RuntimeMethod*))List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared)(__this, ___0_index, method);
}
inline void List_1__ctor_m447372C1EF7141193B93090A77395B786C72C7BC (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Pool_set_nextThing_m85C1B38EC0E3F26779600AAB56ABAA3C592033CA (Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* __this, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Camera_WorldToScreenPoint_m26B4C8945C3B5731F1CC5944CFD96BF17126BAA3 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_position, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline (float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SwitchCameraProjection_Switch_m0A36CA85AC6C211D8CED6B7FFF760C07BEF90953 (SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Screen_get_width_mF608FF3252213E7EFA1F0D2F744C28110E9E5AC9 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Camera_get_orthographic_m904DEFC76C54DA4E30C20A62A86D5D87B7D4DD8F (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerPrefs_SetInt_m956D3E2DB966F20CF42F842880DDF9E2BE94D948 (String_t* ___0_key, int32_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlayerPrefs_GetInt_m8AD1FA8BA54CC6CE2B2AEEE36B6D75587BB1692D (String_t* ___0_key, int32_t ___1_defaultValue, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_orthographic_m64915C0840A68E526830A69F1C40257206185020 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E (CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlayerInput_IncrementTowards_mD28E8347BA822363232A5157709A19E5E15EE43A (PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08* __this, float ___0_n, float ___1_target, float ___2_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerInputBot_RandomInputJump_m088DD669939E0E38E25CBAA1FFBB40CD797B9D85 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerInputBot_RandomInputSpeed_mBD5BE6DB4021C7F69A581CB6B02087AA5ABCF2D6 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputJumpU3Ed__13__ctor_mB8DCD6A6EDB7F831EF765A45D6678A392D5EFFA5 (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputSpeedU3Ed__14__ctor_mF9403523C795C886A933D9B8DF002059891CA68A (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlayerInputBot_IncrementTowards_m03CD9814F330F30FE7D763E8B679BD7F52777629 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, float ___0_n, float ___1_target, float ___2_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WaitForEndOfFrame__ctor_m4AF7E576C01E6B04443BB898B1AE5D645F7D45AB (WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5_inline (ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DCinematics_Stop_m5430BE4DC9FB51CAEA12AB1A0403031966BE7415 (ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DCinematics_GoToNextTarget_m1F8BCC330356EBD1818DB49BE438D136B2CCDED6 (ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D* ProCamera2DTransitionsFX_get_Instance_mFF94EBFD9B64ABF81E88E2C0FE7EF919907DB1C8 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DTransitionsFX_TransitionEnter_m97589A7091603A387838B1F5AAAC32A061C90781 (ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2DTransitionsFX_TransitionExit_m604CC64907B1AB598DA732CC2B2B8509D7B4EAB4 (ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Division_m57A2DCD71E0CE7420851D705D1951F9238902AAB_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline (float ___0_value, float ___1_min, float ___2_max, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_Internal_ToEulerRad_m5BD0EEC543120C320DC77FCCDFD2CE2E6BD3F1A8 (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_rotation, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_Internal_MakePositive_m73E2D01920CB0DFE661A55022C129E8617F0C9A8 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RoomsEventsExample_StartedRoomTransition_mB4B5DF0B9F5DD9A8C2B04182DAC6EC2F41E96059 (RoomsEventsExample_t9DE410B556E96CD3D68F1801AADF2540C218EB0A* __this, int32_t ___0_currentRoom, int32_t ___1_previousRoom, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0E713ACF633164E12AFEC250F19E44318CD64482);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_currentRoom;
		int32_t L_1 = L_0;
		RuntimeObject* L_2 = Box(Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, &L_1);
		int32_t L_3 = ___1_previousRoom;
		int32_t L_4 = L_3;
		RuntimeObject* L_5 = Box(Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, &L_4);
		String_t* L_6;
		L_6 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteral0E713ACF633164E12AFEC250F19E44318CD64482, L_2, L_5, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RoomsEventsExample_FinishedRoomTransition_mD7442168C08B2B1E8C47074D2E88E617C4A025AC (RoomsEventsExample_t9DE410B556E96CD3D68F1801AADF2540C218EB0A* __this, int32_t ___0_currentRoom, int32_t ___1_previousRoom, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7632F406A3FA667DF7FF219692D6CD7654A29FB5);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_currentRoom;
		int32_t L_1 = L_0;
		RuntimeObject* L_2 = Box(Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, &L_1);
		int32_t L_3 = ___1_previousRoom;
		int32_t L_4 = L_3;
		RuntimeObject* L_5 = Box(Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var, &L_4);
		String_t* L_6;
		L_6 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteral7632F406A3FA667DF7FF219692D6CD7654A29FB5, L_2, L_5, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RoomsEventsExample__ctor_mF1F0EB6594E59B82B766065AD81CDD0F286AC5C6 (RoomsEventsExample_t9DE410B556E96CD3D68F1801AADF2540C218EB0A* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PatrolWaypoint__ctor_m4346B8DEF361999BB13B6D7CEAAA2CA98B095604 (PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* __this, const RuntimeMethod* method) 
{
	{
		__this->___StopProbability = (0.5f);
		__this->___StopDuration = (3.0f);
		__this->___StopDurationVariation = (2.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mED291E3179D552DAE229D554963CA65348EB7B68 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF____26665D041D2A2C9069CD019A28650232FAEF84B773FB5A2211529863B37E4A5C_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF____50F0C09C9E97F43467073A473E64F2F8D052FEF62B4E4B7A91A1FEF7459C2B9D_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)2063));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF____50F0C09C9E97F43467073A473E64F2F8D052FEF62B4E4B7A91A1FEF7459C2B9D_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)1600));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tB4F9AC96D5B1688E6AF1047AE0FE3BD65A1371FF____26665D041D2A2C9069CD019A28650232FAEF84B773FB5A2211529863B37E4A5C_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = ((int32_t)27);
		(&V_0)->___TotalTypes = ((int32_t)27);
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m2D993A6E821DDD613FD105724211AE0DF3CFCEEB (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t0F4A91E1BC4BF24B882873D8B2A23857DA9A8068* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshal_pinvoke(const MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47& unmarshaled, MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshal_pinvoke_back(const MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_pinvoke& marshaled, MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshal_pinvoke_cleanup(MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshal_com(const MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47& unmarshaled, MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshal_com_back(const MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_com& marshaled, MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshal_com_cleanup(MonoScriptData_tCEAECFC5039E375C3B059761E7579B113F29CF47_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DollyZoomExample_OnGUI_mA72711315474DA4120F4B724CCFA2D7D1DFA8093 (DollyZoomExample_t814B3A59AA027B91333AE71A085F411B6608C9EA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral49A840DE0DCD373EE5A1FD25D9FA425B31514AEC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral65962AB7981D7DED4606F8986A6AA25EAE045B40);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA63846471F4FC7765968B3C6F42D5D2F4DB13743);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBA3F223B0C372907E1ABF6FBB30F7DC516F3DB35);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0;
		memset((&L_0), 0, sizeof(L_0));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_0), (5.0f), (5.0f), (100.0f), (30.0f), NULL);
		GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580* L_1 = (GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580*)il2cpp_codegen_object_new(GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_il2cpp_TypeInfo_var);
		GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9(L_1, NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		GUI_Label_m0D7BA53414421D71010DFF628EAA6CCCB3DE737E(L_0, _stringLiteral49A840DE0DCD373EE5A1FD25D9FA425B31514AEC, L_1, NULL);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_2;
		memset((&L_2), 0, sizeof(L_2));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_2), (100.0f), (5.0f), (100.0f), (30.0f), NULL);
		float L_3 = __this->___TargetFOV;
		float L_4;
		L_4 = GUI_HorizontalSlider_mEED3CE859B1AF830E1851B3625AE127B4E6C5408(L_2, L_3, (0.100000001f), (179.899994f), NULL);
		__this->___TargetFOV = L_4;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_5;
		memset((&L_5), 0, sizeof(L_5));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_5), (5.0f), (35.0f), (100.0f), (30.0f), NULL);
		GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580* L_6 = (GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580*)il2cpp_codegen_object_new(GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_il2cpp_TypeInfo_var);
		GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9(L_6, NULL);
		GUI_Label_m0D7BA53414421D71010DFF628EAA6CCCB3DE737E(L_5, _stringLiteral65962AB7981D7DED4606F8986A6AA25EAE045B40, L_6, NULL);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_7;
		memset((&L_7), 0, sizeof(L_7));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_7), (100.0f), (35.0f), (100.0f), (30.0f), NULL);
		float L_8 = __this->___Duration;
		float L_9;
		L_9 = GUI_HorizontalSlider_mEED3CE859B1AF830E1851B3625AE127B4E6C5408(L_7, L_8, (0.0f), (10.0f), NULL);
		__this->___Duration = L_9;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_10;
		memset((&L_10), 0, sizeof(L_10));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_10), (5.0f), (65.0f), (100.0f), (30.0f), NULL);
		GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580* L_11 = (GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580*)il2cpp_codegen_object_new(GUIStyle_t20BA2F9F3FE9D13AAA607EEEBE5547835A6F6580_il2cpp_TypeInfo_var);
		GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9(L_11, NULL);
		GUI_Label_m0D7BA53414421D71010DFF628EAA6CCCB3DE737E(L_10, _stringLiteralA63846471F4FC7765968B3C6F42D5D2F4DB13743, L_11, NULL);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_12;
		memset((&L_12), 0, sizeof(L_12));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_12), (100.0f), (65.0f), (100.0f), (30.0f), NULL);
		float L_13 = __this->___ZoomAmount;
		float L_14;
		L_14 = GUI_HorizontalSlider_mEED3CE859B1AF830E1851B3625AE127B4E6C5408(L_12, L_13, (-1.0f), (1.0f), NULL);
		__this->___ZoomAmount = L_14;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_15;
		memset((&L_15), 0, sizeof(L_15));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_15), (5.0f), (95.0f), (150.0f), (30.0f), NULL);
		bool L_16;
		L_16 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(L_15, _stringLiteralBA3F223B0C372907E1ABF6FBB30F7DC516F3DB35, NULL);
		if (!L_16)
		{
			goto IL_0171;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_17;
		L_17 = ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28(NULL);
		float L_18 = __this->___TargetFOV;
		float L_19 = __this->___Duration;
		int32_t L_20 = __this->___EaseType;
		NullCheck(L_17);
		ProCamera2D_DollyZoom_mF2A6A56F4F4F55279FF3415812F9955047D58297(L_17, L_18, L_19, L_20, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_21;
		L_21 = ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28(NULL);
		float L_22 = __this->___ZoomAmount;
		float L_23 = __this->___Duration;
		int32_t L_24 = __this->___EaseType;
		NullCheck(L_21);
		ProCamera2D_Zoom_m1D404B5C3D895095CAD74F7D61EEAF0D2B258E89(L_21, L_22, L_23, L_24, NULL);
	}

IL_0171:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DollyZoomExample__ctor_m78D040E25B0EF690CB3E04EEBE0585A8A6462E82 (DollyZoomExample_t814B3A59AA027B91333AE71A085F411B6608C9EA* __this, const RuntimeMethod* method) 
{
	{
		__this->___TargetFOV = (30.0f);
		__this->___Duration = (2.0f);
		__this->___ZoomAmount = (-0.200000003f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShakeExample_OnGUI_mA8F1CA91AA26DBAD6541D10FF8FFAE61812C6FCB (ShakeExample_t86ED4DE4D0913A0F315628DBC608F21E830AB469* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m2A804E6BB29E093E4995E7B9EEEC6879AB1D237E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mF9EB0FFC5F687B97D08AB83657389E0E03C99DD8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m51C0846BFE6B5087A59640E0F6F4371DED3C771C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m70B7B7632B52F9FA2C101091272BC44317CEFAE0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0B29FFB04736A3117D7DA4AFEC9AE0B53E5646AB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral424CB2B1DDF0703D9F4845DFE8DF6E1D6E3B2EF9);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral51D444D0D1FBB9356E618B00E52BA7230594A377);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral91230C09BBDA2AAD234635AF7C71EAC80771ED5D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFA1AF611B4DB4F50DD6ACB8DA170DC839A9CE0DD);
		s_Il2CppMethodInitialized = true;
	}
	ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* V_0 = NULL;
	ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* V_1 = NULL;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B4_0;
	memset((&G_B4_0), 0, sizeof(G_B4_0));
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B3_0;
	memset((&G_B3_0), 0, sizeof(G_B3_0));
	String_t* G_B5_0 = NULL;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B5_1;
	memset((&G_B5_1), 0, sizeof(G_B5_1));
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0;
		memset((&L_0), 0, sizeof(L_0));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_0), (5.0f), (5.0f), (150.0f), (30.0f), NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(L_0, _stringLiteral424CB2B1DDF0703D9F4845DFE8DF6E1D6E3B2EF9, NULL);
		if (!L_1)
		{
			goto IL_006a;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_2;
		L_2 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_2);
		List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9* L_3 = L_2->___ShakePresets;
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_4;
		L_4 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_4);
		List_1_t72F7AAFC5EC7B67AA9FC405D0F505D049E9339C9* L_5 = L_4->___ShakePresets;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = List_1_get_Count_m2A804E6BB29E093E4995E7B9EEEC6879AB1D237E_inline(L_5, List_1_get_Count_m2A804E6BB29E093E4995E7B9EEEC6879AB1D237E_RuntimeMethod_var);
		int32_t L_7;
		L_7 = Random_Range_m6763D9767F033357F88B6637F048F4ACA4123B68(0, L_6, NULL);
		NullCheck(L_3);
		ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* L_8;
		L_8 = List_1_get_Item_m51C0846BFE6B5087A59640E0F6F4371DED3C771C(L_3, L_7, List_1_get_Item_m51C0846BFE6B5087A59640E0F6F4371DED3C771C_RuntimeMethod_var);
		V_0 = L_8;
		ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* L_9 = V_0;
		NullCheck(L_9);
		String_t* L_10;
		L_10 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_9, NULL);
		String_t* L_11;
		L_11 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral51D444D0D1FBB9356E618B00E52BA7230594A377, L_10, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(L_11, NULL);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_12;
		L_12 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		ShakePreset_t36E5151C4A7C31205AD7970FAC4F091FF9FD4DE7* L_13 = V_0;
		NullCheck(L_12);
		ProCamera2DShake_Shake_mE8B4BCA0C5CDE8FF4E46B3652E3C9C3966EDB2CA(L_12, L_13, NULL);
	}

IL_006a:
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_14;
		memset((&L_14), 0, sizeof(L_14));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_14), (5.0f), (45.0f), (150.0f), (30.0f), NULL);
		bool L_15 = __this->____constantShaking;
		if (L_15)
		{
			G_B4_0 = L_14;
			goto IL_0092;
		}
		G_B3_0 = L_14;
	}
	{
		G_B5_0 = _stringLiteral0B29FFB04736A3117D7DA4AFEC9AE0B53E5646AB;
		G_B5_1 = G_B3_0;
		goto IL_0097;
	}

IL_0092:
	{
		G_B5_0 = _stringLiteralFA1AF611B4DB4F50DD6ACB8DA170DC839A9CE0DD;
		G_B5_1 = G_B4_0;
	}

IL_0097:
	{
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_16;
		L_16 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(G_B5_1, G_B5_0, NULL);
		if (!L_16)
		{
			goto IL_0109;
		}
	}
	{
		bool L_17 = __this->____constantShaking;
		if (!L_17)
		{
			goto IL_00bd;
		}
	}
	{
		__this->____constantShaking = (bool)0;
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_18;
		L_18 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_18);
		ProCamera2DShake_StopConstantShaking_mDE370598162F40CD59FDB341CAF76CEF6A26C190(L_18, (0.300000012f), NULL);
		return;
	}

IL_00bd:
	{
		__this->____constantShaking = (bool)1;
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_19;
		L_19 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_19);
		List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9* L_20 = L_19->___ConstantShakePresets;
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_21;
		L_21 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_21);
		List_1_t77941CE5D6EB634D2522EDFCCE1CF40E8B0A8EB9* L_22 = L_21->___ConstantShakePresets;
		NullCheck(L_22);
		int32_t L_23;
		L_23 = List_1_get_Count_mF9EB0FFC5F687B97D08AB83657389E0E03C99DD8_inline(L_22, List_1_get_Count_mF9EB0FFC5F687B97D08AB83657389E0E03C99DD8_RuntimeMethod_var);
		int32_t L_24;
		L_24 = Random_Range_m6763D9767F033357F88B6637F048F4ACA4123B68(0, L_23, NULL);
		NullCheck(L_20);
		ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* L_25;
		L_25 = List_1_get_Item_m70B7B7632B52F9FA2C101091272BC44317CEFAE0(L_20, L_24, List_1_get_Item_m70B7B7632B52F9FA2C101091272BC44317CEFAE0_RuntimeMethod_var);
		V_1 = L_25;
		ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* L_26 = V_1;
		NullCheck(L_26);
		String_t* L_27;
		L_27 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_26, NULL);
		String_t* L_28;
		L_28 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral91230C09BBDA2AAD234635AF7C71EAC80771ED5D, L_27, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(L_28, NULL);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_29;
		L_29 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		ConstantShakePreset_t5A91E9BF5370092F422BB5A842569B9E05418C0D* L_30 = V_1;
		NullCheck(L_29);
		ProCamera2DShake_ConstantShake_mC6A3FE872FF5DF20546CE0D4BDF99EFC0BD2B76E(L_29, L_30, NULL);
	}

IL_0109:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShakeExample__ctor_mFF5F4C02B8CEC2BBB13CF12A0514ADF20E6437C9 (ShakeExample_t86ED4DE4D0913A0F315628DBC608F21E830AB469* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_Awake_m90EFE3F944600E559BAC59C1052C037E099F89BE (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->____transform = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_OnEnable_m90C0777990E296671E2E6681FAB13276E4339622 (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) 
{
	{
		__this->____exploding = (bool)0;
		float L_0;
		L_0 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		__this->____startTime = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_Update_m8D3E684DC99ED194BE13E5EA49BFC94803B91F2F (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Physics_t1244C2983AEAFA149425AFFC3DF53BC91C18ED56_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		bool L_0 = __this->____exploding;
		if (!L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1 = __this->____transform;
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_1, NULL);
		__this->____lastPos = L_2;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3 = __this->____transform;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline(NULL);
		float L_5 = __this->___BulletSpeed;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_4, L_5, NULL);
		float L_7;
		L_7 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_6, L_7, NULL);
		NullCheck(L_3);
		Transform_Translate_m018D015E89C8CB743C54A21B4A1C5202EBF6297A(L_3, L_8, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = __this->____lastPos;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10 = __this->____transform;
		NullCheck(L_10);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_10, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = __this->____lastPos;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_11, L_12, NULL);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_14 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->____raycastHit);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15 = __this->____lastPos;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_16 = __this->____transform;
		NullCheck(L_16);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_17;
		L_17 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_16, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18;
		L_18 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_15, L_17, NULL);
		V_0 = L_18;
		float L_19;
		L_19 = Vector3_get_magnitude_mF0D6017E90B345F1F52D1CC564C640F1A847AF2D_inline((&V_0), NULL);
		float L_20 = __this->___SkinWidth;
		LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB L_21 = __this->___CollisionMask;
		int32_t L_22;
		L_22 = LayerMask_op_Implicit_m7F5A5B9D079281AC445ED39DEE1FCFA9D795810D(L_21, NULL);
		il2cpp_codegen_runtime_class_init_inline(Physics_t1244C2983AEAFA149425AFFC3DF53BC91C18ED56_il2cpp_TypeInfo_var);
		bool L_23;
		L_23 = Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12(L_9, L_13, L_14, ((float)il2cpp_codegen_add(L_19, L_20)), L_22, NULL);
		if (!L_23)
		{
			goto IL_00ca;
		}
	}
	{
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_24 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->____raycastHit);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25;
		L_25 = RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39(L_24, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_26;
		L_26 = Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline(L_25, NULL);
		__this->____collisionPoint = L_26;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_27 = __this->____transform;
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_28 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->____raycastHit);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_29;
		L_29 = RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5(L_28, NULL);
		NullCheck(L_27);
		Transform_set_up_m1FBA5A97E5057747AC027AD5897EDE80A554D554(L_27, L_29, NULL);
		Bullet_Collide_m6E81F876F22642A9655243F40B79445F404F7378(__this, NULL);
	}

IL_00ca:
	{
		float L_30;
		L_30 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_31 = __this->____startTime;
		float L_32 = __this->___BulletDuration;
		if ((!(((float)((float)il2cpp_codegen_subtract(L_30, L_31))) > ((float)L_32))))
		{
			goto IL_00e4;
		}
	}
	{
		Bullet_Disable_mD396EDE0986A2FADE0AE307FD5180825D2DAFD6E(__this, NULL);
	}

IL_00e4:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_Collide_m6E81F876F22642A9655243F40B79445F404F7378 (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8964425D5AFA567D9602F7E19CD9E3B473DEBE0C);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->____exploding = (bool)1;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->____transform;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = __this->____collisionPoint;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline(L_1, NULL);
		NullCheck(L_0);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_0, L_2, NULL);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_3 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&__this->____raycastHit);
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_4;
		L_4 = RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D(L_3, NULL);
		float L_5 = __this->___BulletDamage;
		float L_6 = L_5;
		RuntimeObject* L_7 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_6);
		NullCheck(L_4);
		Component_SendMessageUpwards_m00AF786700C3D6E42C7736F2AFA7166461409723(L_4, _stringLiteral8964425D5AFA567D9602F7E19CD9E3B473DEBE0C, L_7, 1, NULL);
		Bullet_Disable_mD396EDE0986A2FADE0AE307FD5180825D2DAFD6E(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet_Disable_mD396EDE0986A2FADE0AE307FD5180825D2DAFD6E (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) 
{
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0;
		L_0 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_0);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_0, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Bullet__ctor_mD35FE001CE7989D4DABAAF677152980B5FCDF425 (Bullet_tA0D518AACE8F3E0D70DF253A972DC2FEB17F3BE5* __this, const RuntimeMethod* method) 
{
	{
		__this->___BulletDuration = (1.0f);
		__this->___BulletSpeed = (50.0f);
		__this->___SkinWidth = (0.100000001f);
		__this->___BulletDamage = (10.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Door_get_IsOpen_m3E6B5405808EA7EDFE85AFBD191419C7F1E98ABD (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____isOpen;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door_Awake_m41F8DB1897D3C52BE7B0A45A6E4BEF3D172DFE78 (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_0, NULL);
		__this->____origPos = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door_OpenDoor_m2ED0EC299966E5A5DB499414EF8CCD5D2FB5286E (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, float ___0_openDelay, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		float L_0 = ___0_openDelay;
		if ((!(((float)L_0) == ((float)(-1.0f)))))
		{
			goto IL_0010;
		}
	}
	{
		float L_1 = __this->___OpenDelay;
		___0_openDelay = L_1;
	}

IL_0010:
	{
		__this->____isOpen = (bool)1;
		int32_t L_2 = __this->___DoorDirection;
		V_0 = L_2;
		int32_t L_3 = V_0;
		switch (L_3)
		{
			case 0:
			{
				goto IL_0091;
			}
			case 1:
			{
				goto IL_00bf;
			}
			case 2:
			{
				goto IL_0035;
			}
			case 3:
			{
				goto IL_0063;
			}
		}
	}
	{
		return;
	}

IL_0035:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = __this->____origPos;
		float L_5 = __this->___MovementRange;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_6), (0.0f), (0.0f), L_5, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_4, L_6, NULL);
		float L_8 = __this->___AnimDuration;
		float L_9 = ___0_openDelay;
		Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91(__this, L_7, L_8, L_9, NULL);
		return;
	}

IL_0063:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = __this->____origPos;
		float L_11 = __this->___MovementRange;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), (0.0f), (0.0f), L_11, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_10, L_12, NULL);
		float L_14 = __this->___AnimDuration;
		float L_15 = ___0_openDelay;
		Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91(__this, L_13, L_14, L_15, NULL);
		return;
	}

IL_0091:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = __this->____origPos;
		float L_17 = __this->___MovementRange;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18;
		memset((&L_18), 0, sizeof(L_18));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_18), L_17, (0.0f), (0.0f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_16, L_18, NULL);
		float L_20 = __this->___AnimDuration;
		float L_21 = ___0_openDelay;
		Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91(__this, L_19, L_20, L_21, NULL);
		return;
	}

IL_00bf:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22 = __this->____origPos;
		float L_23 = __this->___MovementRange;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24;
		memset((&L_24), 0, sizeof(L_24));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_24), L_23, (0.0f), (0.0f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25;
		L_25 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_22, L_24, NULL);
		float L_26 = __this->___AnimDuration;
		float L_27 = ___0_openDelay;
		Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91(__this, L_25, L_26, L_27, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door_CloseDoor_mACA48C2055FF840E1323FD838A26AEAC3A76AD5F (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, const RuntimeMethod* method) 
{
	{
		__this->____isOpen = (bool)0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->____origPos;
		float L_1 = __this->___AnimDuration;
		float L_2 = __this->___CloseDelay;
		Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91(__this, L_0, L_1, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91 (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_newPos, float ___1_duration, float ___2_delay, const RuntimeMethod* method) 
{
	{
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_0 = __this->____moveCoroutine;
		if (!L_0)
		{
			goto IL_0014;
		}
	}
	{
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_1 = __this->____moveCoroutine;
		MonoBehaviour_StopCoroutine_mB0FC91BE84203BD8E360B3FBAE5B958B4C5ED22A(__this, L_1, NULL);
	}

IL_0014:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_newPos;
		float L_3 = ___1_duration;
		float L_4 = ___2_delay;
		RuntimeObject* L_5;
		L_5 = Door_MoveRoutine_m6F58F24265CBB412BAB13B2AFC2F185077E0E274(__this, L_2, L_3, L_4, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_6;
		L_6 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_5, NULL);
		__this->____moveCoroutine = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____moveCoroutine), (void*)L_6);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Door_MoveRoutine_m6F58F24265CBB412BAB13B2AFC2F185077E0E274 (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_newPos, float ___1_duration, float ___2_delay, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* L_0 = (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9*)il2cpp_codegen_object_new(U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9_il2cpp_TypeInfo_var);
		U3CMoveRoutineU3Ed__14__ctor_m607707C035642A19E1DB9DC86F282FA23C0B54C3(L_0, 0, NULL);
		U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* L_2 = L_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_newPos;
		NullCheck(L_2);
		L_2->___newPos = L_3;
		U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* L_4 = L_2;
		float L_5 = ___1_duration;
		NullCheck(L_4);
		L_4->___duration = L_5;
		U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* L_6 = L_4;
		float L_7 = ___2_delay;
		NullCheck(L_6);
		L_6->___delay = L_7;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Door__ctor_mD0A0A61EA2319044DCC266542E718EF863EF6CC6 (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, const RuntimeMethod* method) 
{
	{
		__this->___MovementRange = (5.0f);
		__this->___AnimDuration = (1.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveRoutineU3Ed__14__ctor_m607707C035642A19E1DB9DC86F282FA23C0B54C3 (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveRoutineU3Ed__14_System_IDisposable_Dispose_m043EA3A98F1E54AF3AC73B27BEC4DA9095319A00 (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CMoveRoutineU3Ed__14_MoveNext_m39F20AEE4BB6211FCBFFE4A7B450B028FAFFABF8 (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	Door_t83EFA7B7B191E0674D7E862310163478739F3412* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		Door_t83EFA7B7B191E0674D7E862310163478739F3412* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		switch (L_2)
		{
			case 0:
			{
				goto IL_0022;
			}
			case 1:
			{
				goto IL_0043;
			}
			case 2:
			{
				goto IL_010a;
			}
		}
	}
	{
		return (bool)0;
	}

IL_0022:
	{
		__this->___U3CU3E1__state = (-1);
		float L_3 = __this->___delay;
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_4 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_4, L_3, NULL);
		__this->___U3CU3E2__current = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_4);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0043:
	{
		__this->___U3CU3E1__state = (-1);
		Door_t83EFA7B7B191E0674D7E862310163478739F3412* L_5 = V_1;
		NullCheck(L_5);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6;
		L_6 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_5, NULL);
		NullCheck(L_6);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_6, NULL);
		__this->___U3CorigPosU3E5__2 = L_7;
		__this->___U3CtU3E5__3 = (0.0f);
		goto IL_0111;
	}

IL_006b:
	{
		float L_8 = __this->___U3CtU3E5__3;
		float L_9;
		L_9 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_10 = __this->___duration;
		__this->___U3CtU3E5__3 = ((float)il2cpp_codegen_add(L_8, ((float)(L_9/L_10))));
		Door_t83EFA7B7B191E0674D7E862310163478739F3412* L_11 = V_1;
		NullCheck(L_11);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12;
		L_12 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_11, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_13 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___U3CorigPosU3E5__2);
		float L_14 = L_13->___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_15 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___newPos);
		float L_16 = L_15->___x;
		float L_17 = __this->___U3CtU3E5__3;
		float L_18;
		L_18 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_14, L_16, L_17, 1, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_19 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___U3CorigPosU3E5__2);
		float L_20 = L_19->___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_21 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___newPos);
		float L_22 = L_21->___y;
		float L_23 = __this->___U3CtU3E5__3;
		float L_24;
		L_24 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_20, L_22, L_23, 1, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_25 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___U3CorigPosU3E5__2);
		float L_26 = L_25->___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_27 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___newPos);
		float L_28 = L_27->___z;
		float L_29 = __this->___U3CtU3E5__3;
		float L_30;
		L_30 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_26, L_28, L_29, 1, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_31;
		memset((&L_31), 0, sizeof(L_31));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_31), L_18, L_24, L_30, NULL);
		NullCheck(L_12);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_12, L_31, NULL);
		__this->___U3CU3E2__current = NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)NULL);
		__this->___U3CU3E1__state = 2;
		return (bool)1;
	}

IL_010a:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_0111:
	{
		float L_32 = __this->___U3CtU3E5__3;
		if ((((float)L_32) <= ((float)(1.0f))))
		{
			goto IL_006b;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CMoveRoutineU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBCC5B169B9498C34846F6CBEB7564EDB6493A66B (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m83038F31C79F09ADC8371A69D36E3CD9EA9FBDBC (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m83038F31C79F09ADC8371A69D36E3CD9EA9FBDBC_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_get_Current_m8B9300B5A036631DE6796995FCC857C6209C34C6 (U3CMoveRoutineU3Ed__14_t10DF4FBD1B2EFB7B6D4CEB7A8AFCAEFB879706C9* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DoorKey_OnTriggerEnter_mFF97DB2F123A569B03CAEBA82681D29D6D084BC2 (DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* __this, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___0_other, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_0 = ___0_other;
		NullCheck(L_0);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1;
		L_1 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_0, NULL);
		String_t* L_2 = __this->___PickupTag;
		NullCheck(L_1);
		bool L_3;
		L_3 = Component_CompareTag_mE6F8897E84F12DF12D302FFC4D58204D51096FC5(L_1, L_2, NULL);
		if (!L_3)
		{
			goto IL_0054;
		}
	}
	{
		Door_t83EFA7B7B191E0674D7E862310163478739F3412* L_4 = __this->___Door;
		NullCheck(L_4);
		bool L_5;
		L_5 = Door_get_IsOpen_m3E6B5405808EA7EDFE85AFBD191419C7F1E98ABD_inline(L_4, NULL);
		if (L_5)
		{
			goto IL_0054;
		}
	}
	{
		Door_t83EFA7B7B191E0674D7E862310163478739F3412* L_6 = __this->___Door;
		NullCheck(L_6);
		Door_OpenDoor_m2ED0EC299966E5A5DB499414EF8CCD5D2FB5286E(L_6, (-1.0f), NULL);
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_7 = __this->___Cinematics;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_8;
		L_8 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_7, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_8)
		{
			goto IL_0049;
		}
	}
	{
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_9 = __this->___Cinematics;
		NullCheck(L_9);
		ProCamera2DCinematics_Play_m0EC4378CCBB85B084497DBB3D3E6D3A0487A084B(L_9, NULL);
	}

IL_0049:
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_10;
		L_10 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(L_10, NULL);
	}

IL_0054:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DoorKey__ctor_m06CBB092E887F34E310CD07241548378C4C82A84 (DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCAF8804297181FF007CA835529DD4477CFD94A70);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___PickupTag = _stringLiteralCAF8804297181FF007CA835529DD4477CFD94A70;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___PickupTag), (void*)_stringLiteralCAF8804297181FF007CA835529DD4477CFD94A70);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyAttack_Awake_m11C309606A16AFDFB551F13AF341DE48F6131A0F (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->____transform = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform), (void*)L_0);
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_1;
		L_1 = Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD(__this, Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var);
		__this->____navMeshAgent = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____navMeshAgent), (void*)L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyAttack_Attack_mC8175509964CC1FDECB2489E43ABB9942BE4E041 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_target, const RuntimeMethod* method) 
{
	{
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_0 = __this->____navMeshAgent;
		NullCheck(L_0);
		NavMeshAgent_set_updateRotation_mBF6EDBC9BBAF32490229D7DD6BC821A420C3399D(L_0, (bool)0, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1 = ___0_target;
		__this->____target = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____target), (void*)L_1);
		__this->____hasTarget = (bool)1;
		RuntimeObject* L_2;
		L_2 = EnemyAttack_LookAtTarget_mF7E1575BAE9820A7EDEF1E877FFE48C6B7B34817(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_3;
		L_3 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_2, NULL);
		RuntimeObject* L_4;
		L_4 = EnemyAttack_FollowTarget_m93D435D7A86AAC69D7F588E7B8CC45A3B34324B4(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_5;
		L_5 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_4, NULL);
		RuntimeObject* L_6;
		L_6 = EnemyAttack_Fire_m67DACC353A9273C61DFBBBB66B8AEC6148A1800D(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_7;
		L_7 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyAttack_StopAttack_m06D74602CE6B55BCBE6D1E61794AEC0D1D1A9F31 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) 
{
	{
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_0 = __this->____navMeshAgent;
		NullCheck(L_0);
		NavMeshAgent_set_updateRotation_mBF6EDBC9BBAF32490229D7DD6BC821A420C3399D(L_0, (bool)1, NULL);
		__this->____hasTarget = (bool)0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyAttack_LookAtTarget_mF7E1575BAE9820A7EDEF1E877FFE48C6B7B34817 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* L_0 = (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6*)il2cpp_codegen_object_new(U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6_il2cpp_TypeInfo_var);
		U3CLookAtTargetU3Ed__12__ctor_mE673E3FF67DBAE553F5C2C89180EB7BB25C97E04(L_0, 0, NULL);
		U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyAttack_FollowTarget_m93D435D7A86AAC69D7F588E7B8CC45A3B34324B4 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* L_0 = (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846*)il2cpp_codegen_object_new(U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846_il2cpp_TypeInfo_var);
		U3CFollowTargetU3Ed__13__ctor_m0EA79959FB6E07AEF9E7A9FA804EF113BA68D2A4(L_0, 0, NULL);
		U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyAttack_Fire_m67DACC353A9273C61DFBBBB66B8AEC6148A1800D (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* L_0 = (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259*)il2cpp_codegen_object_new(U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259_il2cpp_TypeInfo_var);
		U3CFireU3Ed__14__ctor_mF974A778358856524656CBD0DE30B0DF026A72FB(L_0, 0, NULL);
		U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 EnemyAttack_RandomOnUnitCircle2_m**************************************** (float ___0_radius, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0;
		L_0 = Random_get_insideUnitCircle_m****************************************(NULL);
		V_0 = L_0;
		Vector2_Normalize_m56DABCAB5967DF37A6B96710477D3660D800C652_inline((&V_0), NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = V_0;
		float L_2 = ___0_radius;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3;
		L_3 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_1, L_2, NULL);
		V_0 = L_3;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyAttack__ctor_mCD4691EDE8565527169EFDDA781859825CDA8199 (EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* __this, const RuntimeMethod* method) 
{
	{
		__this->___RotationSpeed = (2.0f);
		__this->___FireRate = (0.300000012f);
		__this->___FireAngleRandomness = (10.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__14__ctor_mF974A778358856524656CBD0DE30B0DF026A72FB (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__14_System_IDisposable_Dispose_mE3801C079CD2919B97406B77DAAE1C0052250CE0 (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CFireU3Ed__14_MoveNext_mA6F8D319F38027A3D275693D2F44DFCE16FFEE7A (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_00a7;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_00ae;
	}

IL_0026:
	{
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_4 = V_1;
		NullCheck(L_4);
		Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* L_5 = L_4->___BulletPool;
		NullCheck(L_5);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6;
		L_6 = Pool_get_nextThing_mBB707503DABD3A940FA5D9E841E12C7CA3C3A0F1(L_5, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_7 = L_6;
		NullCheck(L_7);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_7, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_9 = V_1;
		NullCheck(L_9);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10 = L_9->___WeaponTip;
		NullCheck(L_10);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_10, NULL);
		NullCheck(L_8);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_8, L_11, NULL);
		NullCheck(L_7);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12;
		L_12 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_7, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_13 = V_1;
		NullCheck(L_13);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14 = L_13->____transform;
		NullCheck(L_14);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_15;
		L_15 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_14, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_16 = V_1;
		NullCheck(L_16);
		float L_17 = L_16->___FireAngleRandomness;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_18 = V_1;
		NullCheck(L_18);
		float L_19 = L_18->___FireAngleRandomness;
		float L_20;
		L_20 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494(((-L_17)), L_19, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21;
		memset((&L_21), 0, sizeof(L_21));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_21), (0.0f), ((float)il2cpp_codegen_add((-90.0f), L_20)), (0.0f), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22;
		L_22 = Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline(L_21, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_23;
		L_23 = Quaternion_op_Multiply_mCB375FCCC12A2EC8F9EB824A1BFB4453B58C2012_inline(L_15, L_22, NULL);
		NullCheck(L_12);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_12, L_23, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_24 = V_1;
		NullCheck(L_24);
		float L_25 = L_24->___FireRate;
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_26 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_26, L_25, NULL);
		__this->___U3CU3E2__current = L_26;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_26);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_00a7:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_00ae:
	{
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_27 = V_1;
		NullCheck(L_27);
		bool L_28 = L_27->____hasTarget;
		if (L_28)
		{
			goto IL_0026;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CFireU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEF3CD6F581D7BA5C693DBE52A7D1AF9D2E1890F9 (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__14_System_Collections_IEnumerator_Reset_mAAE10DF1D0AD7E34390EA24E0B24C2284739132C (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CFireU3Ed__14_System_Collections_IEnumerator_Reset_mAAE10DF1D0AD7E34390EA24E0B24C2284739132C_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CFireU3Ed__14_System_Collections_IEnumerator_get_Current_m527DE4601E490AE577C1491EBD29F4A99D6D902A (U3CFireU3Ed__14_t6E91D221062753BE5DF471B8677A6F8BC5AB1259* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFollowTargetU3Ed__13__ctor_m0EA79959FB6E07AEF9E7A9FA804EF113BA68D2A4 (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFollowTargetU3Ed__13_System_IDisposable_Dispose_m0E36F547E951B227EF000EF15726A0F0B62B78AA (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CFollowTargetU3Ed__13_MoveNext_m0666111C44C021A7DA1C2B902DFCF99D4559706B (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* V_1 = NULL;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_2;
	memset((&V_2), 0, sizeof(V_2));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_009f;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_00a6;
	}

IL_0026:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4;
		L_4 = Random_get_insideUnitCircle_m****************************************(NULL);
		V_2 = L_4;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_5 = V_1;
		NullCheck(L_5);
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_6 = L_5->____navMeshAgent;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_7 = V_1;
		NullCheck(L_7);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8 = L_7->____target;
		NullCheck(L_8);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_8, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_10 = V_1;
		NullCheck(L_10);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_11 = L_10->____target;
		NullCheck(L_11);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_11, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_13 = V_1;
		NullCheck(L_13);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14 = L_13->____transform;
		NullCheck(L_14);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15;
		L_15 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_14, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		L_16 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_12, L_15, NULL);
		V_3 = L_16;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_17;
		L_17 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_3), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18;
		L_18 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_17, (5.0f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_9, L_18, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_20 = V_2;
		float L_21 = L_20.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_22 = V_2;
		float L_23 = L_22.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24;
		memset((&L_24), 0, sizeof(L_24));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_24), L_21, (0.0f), L_23, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25;
		L_25 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_19, L_24, NULL);
		NullCheck(L_6);
		NavMeshAgent_set_destination_m5F0A8E4C8ED93798D6B9CE496B10FCE5B7461B95(L_6, L_25, NULL);
		__this->___U3CU3E2__current = NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)NULL);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_009f:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_00a6:
	{
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_26 = V_1;
		NullCheck(L_26);
		bool L_27 = L_26->____hasTarget;
		if (L_27)
		{
			goto IL_0026;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CFollowTargetU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA992CE3AB889CBC006D539BCB6C9B4409BA66CE4 (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_Reset_m298311DFA1E697CC6529B47667B96F65D1C91BFF (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_Reset_m298311DFA1E697CC6529B47667B96F65D1C91BFF_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_get_Current_m2FAEE37FEA5CB5340328103FEE4B2D63C339FC7D (U3CFollowTargetU3Ed__13_t50F74D654436AC06DBF3D1E770720A1821D71846* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLookAtTargetU3Ed__12__ctor_mE673E3FF67DBAE553F5C2C89180EB7BB25C97E04 (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLookAtTargetU3Ed__12_System_IDisposable_Dispose_m8C2F6EBF4D50429EA0CBFF9E7BDC6EE6DFBE2A90 (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CLookAtTargetU3Ed__12_MoveNext_m135683B9F0C5D17F3B9A2EB79E6F72B9DB65D5EC (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* V_1 = NULL;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_00ae;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_00b5;
	}

IL_0026:
	{
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_4 = V_1;
		NullCheck(L_4);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5 = L_4->____target;
		NullCheck(L_5);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_5, NULL);
		float L_7 = L_6.___x;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_8 = V_1;
		NullCheck(L_8);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_9 = L_8->____transform;
		NullCheck(L_9);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_9, NULL);
		float L_11 = L_10.___y;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_12 = V_1;
		NullCheck(L_12);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_13 = L_12->____target;
		NullCheck(L_13);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14;
		L_14 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_13, NULL);
		float L_15 = L_14.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		memset((&L_16), 0, sizeof(L_16));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_16), L_7, L_11, L_15, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_17 = V_1;
		NullCheck(L_17);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_18 = L_17->____transform;
		NullCheck(L_18);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_18, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20;
		L_20 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_16, L_19, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21;
		L_21 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22;
		L_22 = Quaternion_LookRotation_mFB02EDC8F733774DFAC3BEA4B4BB265A228F8307(L_20, L_21, NULL);
		V_2 = L_22;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_23 = V_1;
		NullCheck(L_23);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_24 = L_23->____transform;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_25 = V_1;
		NullCheck(L_25);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_26 = L_25->____transform;
		NullCheck(L_26);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_27;
		L_27 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_26, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_28 = V_2;
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_29 = V_1;
		NullCheck(L_29);
		float L_30 = L_29->___RotationSpeed;
		float L_31;
		L_31 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_32;
		L_32 = Quaternion_Slerp_m0A9969F500E7716EA4F6BC4E7D5464372D8E9E15(L_27, L_28, ((float)il2cpp_codegen_multiply(L_30, L_31)), NULL);
		NullCheck(L_24);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_24, L_32, NULL);
		__this->___U3CU3E2__current = NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)NULL);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_00ae:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_00b5:
	{
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_33 = V_1;
		NullCheck(L_33);
		bool L_34 = L_33->____hasTarget;
		if (L_34)
		{
			goto IL_0026;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CLookAtTargetU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0F14DFD66B82EAA6C2D3E231A176F9574DA74156 (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_Reset_m07A15C1319B75C4CCC440FCC4C0B203C523BF341 (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_Reset_m07A15C1319B75C4CCC440FCC4C0B203C523BF341_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_get_Current_m36B6379AF74D5A3B846A9667E6A8F991D52F2BA0 (U3CLookAtTargetU3Ed__12_tA76A0322B6C159DAED6FD54AE9383659B6765CD6* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Awake_m0514E35DDA932033623F72BF29E519BE06AB125F (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisEnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7_m0D967AF2579E9A205BDBE9224A50D30F221E853E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisEnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598_m5AE3E308D5D73FD9B4E5AD243EC030B07B6AA3B5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisEnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A_m80D4F7D42F81AB057F3EBCB6E7066FEC020250E5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_0;
		L_0 = Component_GetComponent_TisEnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598_m5AE3E308D5D73FD9B4E5AD243EC030B07B6AA3B5(__this, Component_GetComponent_TisEnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598_m5AE3E308D5D73FD9B4E5AD243EC030B07B6AA3B5_RuntimeMethod_var);
		__this->____sight = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____sight), (void*)L_0);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_1;
		L_1 = Component_GetComponent_TisEnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7_m0D967AF2579E9A205BDBE9224A50D30F221E853E(__this, Component_GetComponent_TisEnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7_m0D967AF2579E9A205BDBE9224A50D30F221E853E_RuntimeMethod_var);
		__this->____attack = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____attack), (void*)L_1);
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_2;
		L_2 = Component_GetComponent_TisEnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A_m80D4F7D42F81AB057F3EBCB6E7066FEC020250E5(__this, Component_GetComponent_TisEnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A_m80D4F7D42F81AB057F3EBCB6E7066FEC020250E5_RuntimeMethod_var);
		__this->____wander = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____wander), (void*)L_2);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_3;
		L_3 = Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7(__this, Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7_RuntimeMethod_var);
		__this->____renderers = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____renderers), (void*)L_3);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_4 = __this->____renderers;
		NullCheck(L_4);
		int32_t L_5 = 0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_6 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_5));
		NullCheck(L_6);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_7;
		L_7 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_6, NULL);
		NullCheck(L_7);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8;
		L_8 = Material_get_color_mA4B7D4B96200D9D8B4F36BF19957E9DA81071DBB(L_7, NULL);
		__this->____originalColor = L_8;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9 = __this->____originalColor;
		__this->____currentColor = L_9;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_10 = __this->____sight;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_11 = L_10;
		NullCheck(L_11);
		Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* L_12 = L_11->___OnPlayerInSight;
		Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* L_13 = (Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*)il2cpp_codegen_object_new(Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var);
		Action_1__ctor_mCF523C720DF70BEA3148133C85868568FA91276D(L_13, __this, (intptr_t)((void*)EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161_RuntimeMethod_var), NULL);
		Delegate_t* L_14;
		L_14 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_12, L_13, NULL);
		NullCheck(L_11);
		L_11->___OnPlayerInSight = ((Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*)Castclass((RuntimeObject*)L_14, Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&L_11->___OnPlayerInSight), (void*)((Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*)Castclass((RuntimeObject*)L_14, Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var)));
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_15 = __this->____sight;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_16 = L_15;
		NullCheck(L_16);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_17 = L_16->___OnPlayerOutOfSight;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_18 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_18, __this, (intptr_t)((void*)EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57_RuntimeMethod_var), NULL);
		Delegate_t* L_19;
		L_19 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_17, L_18, NULL);
		NullCheck(L_16);
		L_16->___OnPlayerOutOfSight = ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_19, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&L_16->___OnPlayerOutOfSight), (void*)((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_19, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)));
		DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* L_20 = __this->___Key;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_21;
		L_21 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_20, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_21)
		{
			goto IL_00c1;
		}
	}
	{
		DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* L_22 = __this->___Key;
		NullCheck(L_22);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_23;
		L_23 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_22, NULL);
		NullCheck(L_23);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_23, (bool)0, NULL);
	}

IL_00c1:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Start_m7CBEBC35A2703910AD84DBB006FADB1DBCF9A19B (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_0 = __this->____wander;
		NullCheck(L_0);
		EnemyWander_StartWandering_m2FFB17766D4C97045A8E29AB89C9276A5C7C35F5(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_OnDestroy_m48A5BE63191B9091C47AAF1B02D92F9A2802C556 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_0 = __this->____sight;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_1 = L_0;
		NullCheck(L_1);
		Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* L_2 = L_1->___OnPlayerInSight;
		Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* L_3 = (Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*)il2cpp_codegen_object_new(Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var);
		Action_1__ctor_mCF523C720DF70BEA3148133C85868568FA91276D(L_3, __this, (intptr_t)((void*)EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161_RuntimeMethod_var), NULL);
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		NullCheck(L_1);
		L_1->___OnPlayerInSight = ((Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*)Castclass((RuntimeObject*)L_4, Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___OnPlayerInSight), (void*)((Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D*)Castclass((RuntimeObject*)L_4, Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D_il2cpp_TypeInfo_var)));
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_5 = __this->____sight;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_6 = L_5;
		NullCheck(L_6);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = L_6->___OnPlayerOutOfSight;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_8, __this, (intptr_t)((void*)EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57_RuntimeMethod_var), NULL);
		Delegate_t* L_9;
		L_9 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_7, L_8, NULL);
		NullCheck(L_6);
		L_6->___OnPlayerOutOfSight = ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_9, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&L_6->___OnPlayerOutOfSight), (void*)((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_9, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Hit_mC7B0EFEDCB7593E3ED9D9346A26675ABEF718F19 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, int32_t ___0_damage, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___Health;
		if ((((int32_t)L_0) > ((int32_t)0)))
		{
			goto IL_000a;
		}
	}
	{
		return;
	}

IL_000a:
	{
		int32_t L_1 = __this->___Health;
		int32_t L_2 = ___0_damage;
		__this->___Health = ((int32_t)il2cpp_codegen_subtract(L_1, L_2));
		RuntimeObject* L_3;
		L_3 = EnemyFSM_HitAnim_m2E5E2A327FA865C218278AED5EBE2642611898DC(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_4;
		L_4 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_3, NULL);
		int32_t L_5 = __this->___Health;
		if ((((int32_t)L_5) > ((int32_t)0)))
		{
			goto IL_0034;
		}
	}
	{
		EnemyFSM_Die_m71FBAD0CCFF374720C3A00DD8B2A9BEC447ADB5D(__this, NULL);
	}

IL_0034:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyFSM_HitAnim_m2E5E2A327FA865C218278AED5EBE2642611898DC (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* L_0 = (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74*)il2cpp_codegen_object_new(U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74_il2cpp_TypeInfo_var);
		U3CHitAnimU3Ed__13__ctor_m44EAC43C99235EE91342314684E606005E438330(L_0, 0, NULL);
		U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_0 = __this->____wander;
		NullCheck(L_0);
		EnemyWander_StopWandering_mACE0DF86A60D0743E3B5E09CF35B917B9F96BAAA(L_0, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_1 = __this->____attack;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2 = ___0_obj;
		NullCheck(L_1);
		EnemyAttack_Attack_mC8175509964CC1FDECB2489E43ABB9942BE4E041(L_1, L_2, NULL);
		il2cpp_codegen_runtime_class_init_inline(ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_3;
		L_3 = ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28(NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_4;
		L_4 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		il2cpp_codegen_initobj((&V_0), sizeof(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5 = V_0;
		NullCheck(L_3);
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_6;
		L_6 = ProCamera2D_AddCameraTarget_m552E7B3D6D1A458676149A0342E16A6C739667B7(L_3, L_4, (1.0f), (1.0f), (0.0f), L_5, NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7 = __this->___AttackColor;
		__this->____currentColor = L_7;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = __this->____currentColor;
		EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8(__this, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_0 = __this->____wander;
		NullCheck(L_0);
		EnemyWander_StartWandering_m2FFB17766D4C97045A8E29AB89C9276A5C7C35F5(L_0, NULL);
		EnemyAttack_t3AD34100EDB721F764A9E821EBE982D19ABAEAA7* L_1 = __this->____attack;
		NullCheck(L_1);
		EnemyAttack_StopAttack_m06D74602CE6B55BCBE6D1E61794AEC0D1D1A9F31(L_1, NULL);
		il2cpp_codegen_runtime_class_init_inline(ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_il2cpp_TypeInfo_var);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_2;
		L_2 = ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28(NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_2);
		ProCamera2D_RemoveCameraTarget_m1C694525D3A70E6F97E1A59887B7A355C91A2E03(L_2, L_3, (2.0f), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = __this->____originalColor;
		__this->____currentColor = L_4;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5 = __this->____currentColor;
		EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8(__this, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8 (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_color, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		V_0 = 0;
		goto IL_001b;
	}

IL_0004:
	{
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_0 = __this->____renderers;
		int32_t L_1 = V_0;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		NullCheck(L_3);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_4;
		L_4 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_3, NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5 = ___0_color;
		NullCheck(L_4);
		Material_set_color_m5C32DEBB215FF9EE35E7B575297D8C2F29CC2A2D(L_4, L_5, NULL);
		int32_t L_6 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_6, 1));
	}

IL_001b:
	{
		int32_t L_7 = V_0;
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_8 = __this->____renderers;
		NullCheck(L_8);
		if ((((int32_t)L_7) < ((int32_t)((int32_t)(((RuntimeArray*)L_8)->max_length)))))
		{
			goto IL_0004;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_DropLoot_m430F27F92C046E4BC274B64F0A2DE062CD1E631A (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* L_0 = __this->___Key;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0053;
		}
	}
	{
		DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* L_2 = __this->___Key;
		NullCheck(L_2);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_2, NULL);
		NullCheck(L_3);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_3, (bool)1, NULL);
		DoorKey_t29A52A3C49F2A0AC811F9B7E35F387C0CAE6B73C* L_4 = __this->___Key;
		NullCheck(L_4);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5;
		L_5 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_4, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6;
		L_6 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_6);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_6, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), (0.0f), (3.0f), (0.0f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_7, L_8, NULL);
		NullCheck(L_5);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_5, L_9, NULL);
	}

IL_0053:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM_Die_m71FBAD0CCFF374720C3A00DD8B2A9BEC447ADB5D (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBA4FB70D8B307B91EB3CF9506CE8AC90AB2F08D1);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_0;
		L_0 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_0);
		ProCamera2DShake_Shake_m96A4C403647156FFF72E5EEA997EA9EFA6AA050B(L_0, _stringLiteralBA4FB70D8B307B91EB3CF9506CE8AC90AB2F08D1, NULL);
		EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57(__this, NULL);
		EnemyFSM_DropLoot_m430F27F92C046E4BC274B64F0A2DE062CD1E631A(__this, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_1;
		L_1 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_m064F2A066491D2DC4FFCFBDBE5FCFFB807A04436(L_1, (0.200000003f), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyFSM__ctor_m3E733A6E1B13A41DCAEC2C6195B4CB6665DB67CE (EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* __this, const RuntimeMethod* method) 
{
	{
		__this->___Health = ((int32_t)100);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		L_0 = Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D_inline(NULL);
		__this->___AttackColor = L_0;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__13__ctor_m44EAC43C99235EE91342314684E606005E438330 (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__13_System_IDisposable_Dispose_m8B0AAE54D2EB6F782CE68D60CAA62ED5848EB0FC (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CHitAnimU3Ed__13_MoveNext_mB6CFBCC1AED03B4D5ECCBD32A9657817153B5D65 (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0042;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state = (-1);
		EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* L_4 = V_1;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5;
		L_5 = Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline(NULL);
		NullCheck(L_4);
		EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8(L_4, L_5, NULL);
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_6 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_6, (0.0500000007f), NULL);
		__this->___U3CU3E2__current = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_6);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0042:
	{
		__this->___U3CU3E1__state = (-1);
		EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* L_7 = V_1;
		EnemyFSM_t933981DDB7D533CA9227C61BAEA6001A6DCDFBE9* L_8 = V_1;
		NullCheck(L_8);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9 = L_8->____currentColor;
		NullCheck(L_7);
		EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8(L_7, L_9, NULL);
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CHitAnimU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB2E175D2290FEF4EBB8508BBD07E09857285BF6F (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__13_System_Collections_IEnumerator_Reset_mC293E5D1F71063700FC867EC86C325635844BC88 (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CHitAnimU3Ed__13_System_Collections_IEnumerator_Reset_mC293E5D1F71063700FC867EC86C325635844BC88_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CHitAnimU3Ed__13_System_Collections_IEnumerator_get_Current_m681FDD9D07ACD1C96BBBD43A8279632591303FED (U3CHitAnimU3Ed__13_t676382EA9F9CF881A219C79136809B9849888D74* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_Awake_mDB0A16ADCF61BF6978DB3A7F721D5A9EE04CF2B6 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m36829EC89855C8C23CEDA8C5F5B12B76ADFE2248_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mDC3E95DC5C927A867B9B42EDE1945F909B894268_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5538C14EC1E9AB85033B87760A6F2A11378A2EFF);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* V_1 = NULL;
	RuntimeObject* V_2 = NULL;
	{
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_0;
		L_0 = Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD(__this, Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var);
		__this->____navMeshAgent = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____navMeshAgent), (void*)L_0);
		List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* L_1 = (List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D*)il2cpp_codegen_object_new(List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D_il2cpp_TypeInfo_var);
		List_1__ctor_mDC3E95DC5C927A867B9B42EDE1945F909B894268(L_1, List_1__ctor_mDC3E95DC5C927A867B9B42EDE1945F909B894268_RuntimeMethod_var);
		__this->____path = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____path), (void*)L_1);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2 = __this->___PathContainer;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0066;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_4 = __this->___PathContainer;
		NullCheck(L_4);
		RuntimeObject* L_5;
		L_5 = Transform_GetEnumerator_mA7E1C882ACA0C33E284711CD09971DEA3FFEF404(L_4, NULL);
		V_0 = L_5;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0055:
			{
				{
					RuntimeObject* L_6 = V_0;
					V_2 = ((RuntimeObject*)IsInst((RuntimeObject*)L_6, IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var));
					RuntimeObject* L_7 = V_2;
					if (!L_7)
					{
						goto IL_0065;
					}
				}
				{
					RuntimeObject* L_8 = V_2;
					NullCheck(L_8);
					InterfaceActionInvoker0::Invoke(0, IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var, L_8);
				}

IL_0065:
				{
					return;
				}
			}
		});
		try
		{
			{
				goto IL_004b_1;
			}

IL_0033_1:
			{
				RuntimeObject* L_9 = V_0;
				NullCheck(L_9);
				RuntimeObject* L_10;
				L_10 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(1, IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var, L_9);
				V_1 = ((Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*)CastclassClass((RuntimeObject*)L_10, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var));
				List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* L_11 = __this->____path;
				Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12 = V_1;
				NullCheck(L_11);
				List_1_Add_m36829EC89855C8C23CEDA8C5F5B12B76ADFE2248_inline(L_11, L_12, List_1_Add_m36829EC89855C8C23CEDA8C5F5B12B76ADFE2248_RuntimeMethod_var);
			}

IL_004b_1:
			{
				RuntimeObject* L_13 = V_0;
				NullCheck(L_13);
				bool L_14;
				L_14 = InterfaceFuncInvoker0< bool >::Invoke(0, IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var, L_13);
				if (L_14)
				{
					goto IL_0033_1;
				}
			}
			{
				goto IL_0070;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0066:
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(_stringLiteral5538C14EC1E9AB85033B87760A6F2A11378A2EFF, NULL);
	}

IL_0070:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_Update_m70910553F4F7228F3625371F1448846507CD8E84 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisPatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB_mE6206CB158AF20B0B0718C08770946DA32D87CDB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* V_0 = NULL;
	{
		bool L_0 = __this->___IsPaused;
		if (!L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_1 = __this->____navMeshAgent;
		NullCheck(L_1);
		float L_2;
		L_2 = NavMeshAgent_get_remainingDistance_m051C1B408E2740A95B5A5577C5EC7222311AA73A(L_1, NULL);
		float L_3 = __this->___WaypointOffset;
		if ((!(((float)L_2) <= ((float)L_3))))
		{
			goto IL_008b;
		}
	}
	{
		bool L_4 = __this->____hasReachedDestination;
		if (L_4)
		{
			goto IL_008b;
		}
	}
	{
		__this->____hasReachedDestination = (bool)1;
		List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* L_5 = __this->____path;
		int32_t L_6 = __this->____currentWaypoint;
		NullCheck(L_5);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_7;
		L_7 = List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA(L_5, L_6, List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA_RuntimeMethod_var);
		NullCheck(L_7);
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_8;
		L_8 = Component_GetComponent_TisPatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB_mE6206CB158AF20B0B0718C08770946DA32D87CDB(L_7, Component_GetComponent_TisPatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB_mE6206CB158AF20B0B0718C08770946DA32D87CDB_RuntimeMethod_var);
		V_0 = L_8;
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_9 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_10;
		L_10 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_9, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_10)
		{
			goto IL_0085;
		}
	}
	{
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_11 = V_0;
		NullCheck(L_11);
		float L_12 = L_11->___StopDuration;
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_13 = V_0;
		NullCheck(L_13);
		float L_14 = L_13->___StopDurationVariation;
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_15 = V_0;
		NullCheck(L_15);
		float L_16 = L_15->___StopDuration;
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_17 = V_0;
		NullCheck(L_17);
		float L_18 = L_17->___StopDurationVariation;
		float L_19;
		L_19 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494(((float)il2cpp_codegen_subtract(L_12, L_14)), ((float)il2cpp_codegen_add(L_16, L_18)), NULL);
		__this->____stopTime = L_19;
		float L_20;
		L_20 = Random_get_value_m2CEA87FADF5222EF9E13D32695F15E2BA282E24B(NULL);
		PatrolWaypoint_tD955C980DB5E7830EA0F472212813D82CE402DDB* L_21 = V_0;
		NullCheck(L_21);
		float L_22 = L_21->___StopProbability;
		if ((!(((float)L_20) >= ((float)L_22))))
		{
			goto IL_008b;
		}
	}
	{
		EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96(__this, NULL);
		goto IL_008b;
	}

IL_0085:
	{
		EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96(__this, NULL);
	}

IL_008b:
	{
		bool L_23 = __this->____hasReachedDestination;
		if (!L_23)
		{
			goto IL_00b8;
		}
	}
	{
		float L_24 = __this->____stopTime;
		float L_25;
		L_25 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		__this->____stopTime = ((float)il2cpp_codegen_subtract(L_24, L_25));
		float L_26 = __this->____stopTime;
		if ((!(((float)L_26) <= ((float)(0.0f)))))
		{
			goto IL_00b8;
		}
	}
	{
		EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96(__this, NULL);
	}

IL_00b8:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_StartPatrol_mD0558D3C453D136B64FE856BAB40518D47240A68 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	{
		EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4(__this, 0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_PausePatrol_m63C754BC41C88234EA100ED242875DFC8F145A7F (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	{
		__this->___IsPaused = (bool)1;
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_0 = __this->____navMeshAgent;
		NullCheck(L_0);
		NavMeshAgent_set_isStopped_mF374E697F39845233B84D8C4873DEABC3AA490DF(L_0, (bool)1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_ResumePatrol_mA6030EE1A672F3A8BAE00100973483705845A3C3 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____currentWaypoint;
		EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mB5E64608D47703A98476E026480AE38671047C87_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1B765F0B9EC3B53F914342CAD5D040B5BDA2B472);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = __this->____currentWaypoint;
		List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* L_1 = __this->____path;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = List_1_get_Count_mB5E64608D47703A98476E026480AE38671047C87_inline(L_1, List_1_get_Count_mB5E64608D47703A98476E026480AE38671047C87_RuntimeMethod_var);
		if ((((int32_t)L_0) >= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_2, 1)))))
		{
			goto IL_0025;
		}
	}
	{
		int32_t L_3 = __this->____currentWaypoint;
		__this->____currentWaypoint = ((int32_t)il2cpp_codegen_add(L_3, 1));
		goto IL_0040;
	}

IL_0025:
	{
		bool L_4 = __this->___Loop;
		if (!L_4)
		{
			goto IL_0036;
		}
	}
	{
		__this->____currentWaypoint = 0;
		goto IL_0040;
	}

IL_0036:
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(_stringLiteral1B765F0B9EC3B53F914342CAD5D040B5BDA2B472, NULL);
	}

IL_0040:
	{
		int32_t L_5 = __this->____currentWaypoint;
		EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4(__this, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, int32_t ___0_waypoint, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		__this->___IsPaused = (bool)0;
		__this->____hasReachedDestination = (bool)0;
		int32_t L_0 = ___0_waypoint;
		__this->____currentWaypoint = L_0;
		List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* L_1 = __this->____path;
		int32_t L_2 = __this->____currentWaypoint;
		NullCheck(L_1);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA(L_1, L_2, List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA_RuntimeMethod_var);
		NullCheck(L_3);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_3, NULL);
		float L_5 = L_4.___x;
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_6 = __this->____navMeshAgent;
		NullCheck(L_6);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_7;
		L_7 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_6, NULL);
		NullCheck(L_7);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_7, NULL);
		float L_9 = L_8.___y;
		List_1_t991BBC5A1D51F59A450367DF944DAA207F22D06D* L_10 = __this->____path;
		int32_t L_11 = __this->____currentWaypoint;
		NullCheck(L_10);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12;
		L_12 = List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA(L_10, L_11, List_1_get_Item_m8EAA91B4CE37CBB6C720FD238E4505097B29FFDA_RuntimeMethod_var);
		NullCheck(L_12);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_12, NULL);
		float L_14 = L_13.___z;
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&V_0), L_5, L_9, L_14, NULL);
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_15 = __this->____navMeshAgent;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = V_0;
		NullCheck(L_15);
		bool L_17;
		L_17 = NavMeshAgent_SetDestination_mD5D960933827F1F14B29CF4A3B6F305C064EBF46(L_15, L_16, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyPatrol__ctor_m623FEFA07A17946CE2E68DD6A9A5FEB0CFFE4A38 (EnemyPatrol_t1B79FBAFE5295D40E2CED868EF80AA84E3F736F5* __this, const RuntimeMethod* method) 
{
	{
		__this->___WaypointOffset = (0.100000001f);
		__this->___Loop = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemySight_Awake_mF30310C2F76237E2F257E52FC922043D3B3A3A64 (EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___RefreshRate;
		float L_1 = __this->___RefreshRate;
		float L_2 = __this->___RefreshRate;
		float L_3;
		L_3 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494(((float)il2cpp_codegen_multiply(((-L_1)), (0.200000003f))), ((float)il2cpp_codegen_multiply(L_2, (0.200000003f))), NULL);
		__this->___RefreshRate = ((float)il2cpp_codegen_add(L_0, L_3));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemySight_Start_mEFBF736E90D4325211846E30933BFA1948FD2482 (EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* L_0 = (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2*)il2cpp_codegen_object_new(U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2_il2cpp_TypeInfo_var);
		U3CStartU3Ed__10__ctor_m05C20E28E3D83795CEF6B8F439B82FC39BC25D83(L_0, 0, NULL);
		U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemySight__ctor_m992A9356FD2E86D59753BBDEE9ADC134E4B76E88 (EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* __this, const RuntimeMethod* method) 
{
	{
		__this->___RefreshRate = (1.0f);
		__this->___fieldOfViewAngle = (110.0f);
		__this->___ViewDistance = (30.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CStartU3Ed__10__ctor_m05C20E28E3D83795CEF6B8F439B82FC39BC25D83 (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CStartU3Ed__10_System_IDisposable_Dispose_mC938CE609305244877A6357A56CC271AE57A9137 (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CStartU3Ed__10_MoveNext_mEB568DFE8985DBF6D401574AC5BA20AB1EAA9440 (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Physics_t1244C2983AEAFA149425AFFC3DF53BC91C18ED56_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* V_1 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0131;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_0021:
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_4 = V_1;
		NullCheck(L_4);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5 = L_4->___player;
		NullCheck(L_5);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_5, NULL);
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_7 = V_1;
		NullCheck(L_7);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_7, NULL);
		NullCheck(L_8);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_8, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_6, L_9, NULL);
		V_2 = L_10;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = V_2;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_12 = V_1;
		NullCheck(L_12);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_13;
		L_13 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_12, NULL);
		NullCheck(L_13);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14;
		L_14 = Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F(L_13, NULL);
		float L_15;
		L_15 = Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline(L_11, L_14, NULL);
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_16 = V_1;
		NullCheck(L_16);
		float L_17 = L_16->___fieldOfViewAngle;
		if ((!(((float)L_15) < ((float)((float)il2cpp_codegen_multiply(L_17, (0.5f)))))))
		{
			goto IL_00f5;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_18 = V_1;
		NullCheck(L_18);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19;
		L_19 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_18, NULL);
		NullCheck(L_19);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20;
		L_20 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_19, NULL);
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_21 = V_1;
		NullCheck(L_21);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_22;
		L_22 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_21, NULL);
		NullCheck(L_22);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_23;
		L_23 = Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2(L_22, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24;
		L_24 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_20, L_23, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25;
		L_25 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_2), NULL);
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_26 = V_1;
		NullCheck(L_26);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_27 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&L_26->____hit);
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_28 = V_1;
		NullCheck(L_28);
		float L_29 = L_28->___ViewDistance;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_30 = V_1;
		NullCheck(L_30);
		LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB L_31 = L_30->___LayerMask;
		int32_t L_32;
		L_32 = LayerMask_op_Implicit_m7F5A5B9D079281AC445ED39DEE1FCFA9D795810D(L_31, NULL);
		il2cpp_codegen_runtime_class_init_inline(Physics_t1244C2983AEAFA149425AFFC3DF53BC91C18ED56_il2cpp_TypeInfo_var);
		bool L_33;
		L_33 = Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12(L_24, L_25, L_27, L_29, L_32, NULL);
		if (!L_33)
		{
			goto IL_00f5;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_34 = V_1;
		NullCheck(L_34);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_35 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&L_34->____hit);
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_36;
		L_36 = RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D(L_35, NULL);
		NullCheck(L_36);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_37;
		L_37 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_36, NULL);
		NullCheck(L_37);
		int32_t L_38;
		L_38 = Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A(L_37, NULL);
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_39 = V_1;
		NullCheck(L_39);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_40 = L_39->___player;
		NullCheck(L_40);
		int32_t L_41;
		L_41 = Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A(L_40, NULL);
		if ((!(((uint32_t)L_38) == ((uint32_t)L_41))))
		{
			goto IL_00f5;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_42 = V_1;
		NullCheck(L_42);
		bool L_43 = L_42->___playerInSight;
		if (L_43)
		{
			goto IL_0117;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_44 = V_1;
		NullCheck(L_44);
		L_44->___playerInSight = (bool)1;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_45 = V_1;
		NullCheck(L_45);
		Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* L_46 = L_45->___OnPlayerInSight;
		if (!L_46)
		{
			goto IL_0117;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_47 = V_1;
		NullCheck(L_47);
		Action_1_t10D7C827ADC73ED438E0CA8F04465BA6F2BAED7D* L_48 = L_47->___OnPlayerInSight;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_49 = V_1;
		NullCheck(L_49);
		RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5* L_50 = (RaycastHit_t6F30BD0B38B56401CA833A1B87BD74F2ACD2F2B5*)(&L_49->____hit);
		Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* L_51;
		L_51 = RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D(L_50, NULL);
		NullCheck(L_51);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_52;
		L_52 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_51, NULL);
		NullCheck(L_48);
		Action_1_Invoke_mC4804BC714747F815506CC787400EB84BA024D68_inline(L_48, L_52, NULL);
		goto IL_0117;
	}

IL_00f5:
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_53 = V_1;
		NullCheck(L_53);
		bool L_54 = L_53->___playerInSight;
		if (!L_54)
		{
			goto IL_0117;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_55 = V_1;
		NullCheck(L_55);
		L_55->___playerInSight = (bool)0;
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_56 = V_1;
		NullCheck(L_56);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_57 = L_56->___OnPlayerOutOfSight;
		if (!L_57)
		{
			goto IL_0117;
		}
	}
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_58 = V_1;
		NullCheck(L_58);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_59 = L_58->___OnPlayerOutOfSight;
		NullCheck(L_59);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_59, NULL);
	}

IL_0117:
	{
		EnemySight_t530BCF0944C7C844909CB2E65BB6B3D98344F598* L_60 = V_1;
		NullCheck(L_60);
		float L_61 = L_60->___RefreshRate;
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_62 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_62, L_61, NULL);
		__this->___U3CU3E2__current = L_62;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_62);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0131:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_0021;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CStartU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m059C8C17FB2C78D3F39C0D5F51B8FC9C1A90BFBC (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CStartU3Ed__10_System_Collections_IEnumerator_Reset_mDFE86914DF5B0391F9DE7A36C764BBFEEAAEE343 (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CStartU3Ed__10_System_Collections_IEnumerator_Reset_mDFE86914DF5B0391F9DE7A36C764BBFEEAAEE343_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CStartU3Ed__10_System_Collections_IEnumerator_get_Current_mD9E5DAB3B5419BC16047DCF96A05022875686660 (U3CStartU3Ed__10_tC26BBCC094EB62BCE481D40639845E50E60EE4E2* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_Awake_m3E1BC61CC3C36C7879B4D0C4F6C57F927C2F7E2C (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_0;
		L_0 = Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD(__this, Component_GetComponentInChildren_TisNavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F_mF258B768A3AFAFEAA9D44CC0357ECACCC058E7FD_RuntimeMethod_var);
		__this->____navMeshAgent = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____navMeshAgent), (void*)L_0);
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_1 = __this->____navMeshAgent;
		NullCheck(L_1);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2;
		L_2 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_1, NULL);
		NullCheck(L_2);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_2, NULL);
		__this->____startingPos = L_3;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_StartWandering_m2FFB17766D4C97045A8E29AB89C9276A5C7C35F5 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) 
{
	{
		float L_0;
		L_0 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		__this->____startingTime = L_0;
		EnemyWander_GoToWaypoint_mC46CF5C24F723424F8878E3A417A6DBE78AAAD15(__this, NULL);
		RuntimeObject* L_1;
		L_1 = EnemyWander_CheckAgentPosition_m7BFEDDFC1A658844F1B307FD28EECFA19B43CD71(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_2;
		L_2 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_StopWandering_mACE0DF86A60D0743E3B5E09CF35B917B9F96BAAA (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour_StopAllCoroutines_m872033451D42013A99867D09337490017E9ED318(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* EnemyWander_CheckAgentPosition_m7BFEDDFC1A658844F1B307FD28EECFA19B43CD71 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* L_0 = (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21*)il2cpp_codegen_object_new(U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21_il2cpp_TypeInfo_var);
		U3CCheckAgentPositionU3Ed__10__ctor_mD1E04080E9A832ECEE6A50A4D142844CE11B33C9(L_0, 0, NULL);
		U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander_GoToWaypoint_mC46CF5C24F723424F8878E3A417A6DBE78AAAD15 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* V_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* L_0 = (NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7*)il2cpp_codegen_object_new(NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_il2cpp_TypeInfo_var);
		NavMeshPath__ctor_mEA40BFC2492814FFC97A71C3AEC2154A9415C37F(L_0, NULL);
		V_0 = L_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		V_1 = L_1;
		goto IL_004c;
	}

IL_000e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Random_get_insideUnitSphere_mA488D6859560B73565B8D890ED6B39DB4091C54F(NULL);
		float L_3 = __this->___WanderRadius;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_2, L_3, NULL);
		V_2 = L_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_5 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->____startingPos);
		float L_6 = L_5->___y;
		(&V_2)->___y = L_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = __this->____startingPos;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = V_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_7, L_8, NULL);
		V_1 = L_9;
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_10 = __this->____navMeshAgent;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = V_1;
		NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* L_12 = V_0;
		NullCheck(L_10);
		bool L_13;
		L_13 = NavMeshAgent_CalculatePath_mF692688572E61C3B9FE776AA3784E14A08C259C2(L_10, L_11, L_12, NULL);
	}

IL_004c:
	{
		NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* L_14 = V_0;
		NullCheck(L_14);
		int32_t L_15;
		L_15 = NavMeshPath_get_status_m63B0AEDA3149C7053987C4D0A02B3FE8B41BD74B(L_14, NULL);
		if ((((int32_t)L_15) == ((int32_t)1)))
		{
			goto IL_000e;
		}
	}
	{
		NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7* L_16 = V_0;
		NullCheck(L_16);
		int32_t L_17;
		L_17 = NavMeshPath_get_status_m63B0AEDA3149C7053987C4D0A02B3FE8B41BD74B(L_16, NULL);
		if ((((int32_t)L_17) == ((int32_t)2)))
		{
			goto IL_000e;
		}
	}
	{
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_18 = __this->____navMeshAgent;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19 = V_1;
		NullCheck(L_18);
		bool L_20;
		L_20 = NavMeshAgent_SetDestination_mD5D960933827F1F14B29CF4A3B6F305C064EBF46(L_18, L_19, NULL);
		__this->____hasReachedDestination = (bool)0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EnemyWander__ctor_mD8B13D46CF1A8D22BA85FE5C061BCA7D5AF6DA56 (EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* __this, const RuntimeMethod* method) 
{
	{
		__this->___WanderDuration = (10.0f);
		__this->___WaypointOffset = (0.100000001f);
		__this->___WanderRadius = (10.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CCheckAgentPositionU3Ed__10__ctor_mD1E04080E9A832ECEE6A50A4D142844CE11B33C9 (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CCheckAgentPositionU3Ed__10_System_IDisposable_Dispose_m5079AFD4C832B5D40E85A1757DB8D4505198CA43 (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CCheckAgentPositionU3Ed__10_MoveNext_m1FBDE9F9BBDBC9983EF86367633488A9B44AFA4B (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA24E8F43CBB762D069E3CE9638229CF0C314A006);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0083;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_001e:
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_4 = V_1;
		NullCheck(L_4);
		NavMeshAgent_t5D0CCC0B3B78242F286C9BD8EDD87C3CCBD0A66F* L_5 = L_4->____navMeshAgent;
		NullCheck(L_5);
		float L_6;
		L_6 = NavMeshAgent_get_remainingDistance_m051C1B408E2740A95B5A5577C5EC7222311AA73A(L_5, NULL);
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_7 = V_1;
		NullCheck(L_7);
		float L_8 = L_7->___WaypointOffset;
		if ((!(((float)L_6) <= ((float)L_8))))
		{
			goto IL_0073;
		}
	}
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_9 = V_1;
		NullCheck(L_9);
		bool L_10 = L_9->____hasReachedDestination;
		if (L_10)
		{
			goto IL_0073;
		}
	}
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_11 = V_1;
		NullCheck(L_11);
		L_11->____hasReachedDestination = (bool)1;
		float L_12;
		L_12 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_13 = V_1;
		NullCheck(L_13);
		float L_14 = L_13->____startingTime;
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_15 = V_1;
		NullCheck(L_15);
		float L_16 = L_15->___WanderDuration;
		if ((!(((float)((float)il2cpp_codegen_subtract(L_12, L_14))) >= ((float)L_16))))
		{
			goto IL_006d;
		}
	}
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_17 = V_1;
		NullCheck(L_17);
		float L_18 = L_17->___WanderDuration;
		if ((!(((float)L_18) > ((float)(0.0f)))))
		{
			goto IL_006d;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_Log_m87A9A3C761FF5C43ED8A53B16190A53D08F818BB(_stringLiteralA24E8F43CBB762D069E3CE9638229CF0C314A006, NULL);
		goto IL_0073;
	}

IL_006d:
	{
		EnemyWander_tF6587850FFE6269D17C570E275381BBCF6FA473A* L_19 = V_1;
		NullCheck(L_19);
		EnemyWander_GoToWaypoint_mC46CF5C24F723424F8878E3A417A6DBE78AAAD15(L_19, NULL);
	}

IL_0073:
	{
		__this->___U3CU3E2__current = NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)NULL);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0083:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_001e;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CCheckAgentPositionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0CDF703AA428A431258E05DE289FC7A4AD8E923 (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_Reset_m055746713979B4B4C1F920905D84B3C0D4F55C9B (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_Reset_m055746713979B4B4C1F920905D84B3C0D4F55C9B_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_get_Current_mD02DE6AC35BC9D6F84B83BAB74CB2BD9E4B342CD (U3CCheckAgentPositionU3Ed__10_tC4D16FDB87C86D43213CDE1AB1F1F7FE3C823F21* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameOver_Awake_mE8746FFD083A7B470F2CB93113A0A8605162DD17 (GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* __this, const RuntimeMethod* method) 
{
	{
		Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* L_0 = __this->___GameOverScreen;
		NullCheck(L_0);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_1;
		L_1 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_0, NULL);
		NullCheck(L_1);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_1, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameOver_ShowScreen_m91A0DCC3CA95E7AC8C9895D6B7DF9E0BF7B480D4 (GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* __this, const RuntimeMethod* method) 
{
	{
		Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* L_0 = __this->___GameOverScreen;
		NullCheck(L_0);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_1;
		L_1 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(L_0, NULL);
		NullCheck(L_1);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_1, (bool)1, NULL);
		Time_set_timeScale_mEF84EE4B2376A458387648079B426B267862D331((0.0f), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameOver_PlayAgain_m5130E860511E1D3A53FDE8DEC65340AD43BE0656 (GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Scene_tA1DC762B79745EB5140F054C884855B922318356 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Time_set_timeScale_mEF84EE4B2376A458387648079B426B267862D331((1.0f), NULL);
		il2cpp_codegen_runtime_class_init_inline(SceneManager_tA0EF56A88ACA4A15731AF7FDC10A869FA4C698FA_il2cpp_TypeInfo_var);
		Scene_tA1DC762B79745EB5140F054C884855B922318356 L_0;
		L_0 = SceneManager_GetActiveScene_m0B320EC4302F51A71495D1CCD1A0FF9C2ED1FDC8(NULL);
		V_0 = L_0;
		String_t* L_1;
		L_1 = Scene_get_name_m3C818DFA663E159274DAD823B780C7616C5E2A8C((&V_0), NULL);
		SceneManager_LoadScene_mBB3DBC1601A21F8F4E8A5D68FED30EA9412F218E(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameOver__ctor_m1FBF6EDFA0F5310848A79816D6A01AF5A6D6CA1D (GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Goal_OnTriggerEnter_m397A4D3BEE326F3C16B4581EFADF7FD0E27119A8 (Goal_tC19E5DD1B9AF8D52A6A53E91481D91ADBF168DC7* __this, Collider_t1CC3163924FCD6C4CC2E816373A929C1E3D55E76* ___0_other, const RuntimeMethod* method) 
{
	{
		GameOver_t41BDAC37DBFB45132C2AD94E4C17297EA66FA0BD* L_0 = __this->___GameOverScreen;
		NullCheck(L_0);
		GameOver_ShowScreen_m91A0DCC3CA95E7AC8C9895D6B7DF9E0BF7B480D4(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Goal__ctor_mF0029EB41C7DB255447EF0321DF92A006F5A9D4F (Goal_tC19E5DD1B9AF8D52A6A53E91481D91ADBF168DC7* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectMove_Awake_m3A0F42D78BE8F9DA31716FB3F072CDCB790D82FC (ObjectMove_tE3F80C31E2C5F3347A7A776EDBE04057B4A577D5* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->____transform = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectMove_LateUpdate_m2C898044689A3751D189011C66220447100641BE (ObjectMove_tE3F80C31E2C5F3347A7A776EDBE04057B4A577D5* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->____transform;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1 = L_0;
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_1, NULL);
		float L_3 = __this->___Amplitude;
		float L_4 = __this->___Frequency;
		float L_5;
		L_5 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_6;
		L_6 = sinf(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((6.28318548f), L_4)), L_5)));
		float L_7 = __this->___Frequency;
		float L_8;
		L_8 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_9;
		L_9 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_10;
		L_10 = sinf(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((6.28318548f), L_7)), ((float)il2cpp_codegen_subtract(L_8, L_9)))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline(((float)il2cpp_codegen_multiply(L_3, ((float)il2cpp_codegen_subtract(L_6, L_10)))), L_11, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_2, L_12, NULL);
		NullCheck(L_1);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_1, L_13, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectMove__ctor_m8DF486D96BAB3438CAA7FC6CC50F46EE0D0FB1A0 (ObjectMove_tE3F80C31E2C5F3347A7A776EDBE04057B4A577D5* __this, const RuntimeMethod* method) 
{
	{
		__this->___Amplitude = (1.0f);
		__this->___Frequency = (1.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectRotate_Awake_mEF1E2172BD8C35E5B2A94B6B7E6578FC368A709B (ObjectRotate_t6CE493E48B3F500888371E20F836EFD742953929* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->____transform = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectRotate_LateUpdate_m83C084CEAC89133F7431DD76D960F14404FBF61A (ObjectRotate_t6CE493E48B3F500888371E20F836EFD742953929* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->____transform;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = __this->___Rotation;
		NullCheck(L_0);
		Transform_Rotate_m2A308205498AFEEA3DF784B1C86E4F7C126CA2EE(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectRotate__ctor_m3B44A3B585DF7089576EA3FFCF29DD25C8E640CB (ObjectRotate_t6CE493E48B3F500888371E20F836EFD742953929* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02_inline(NULL);
		__this->___Rotation = L_0;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerFire_Awake_m948F53E4F2DCCB95C92DF1BF1D3F3533E5112B54 (PlayerFire_t2C8120136448E86CF633452034219BED662DD897* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->____transform = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerFire_Update_m561CAFB0D567A65E4A85A252CBC735D191F9B2E7 (PlayerFire_t2C8120136448E86CF633452034219BED662DD897* __this, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)32), NULL);
		if (L_0)
		{
			goto IL_0011;
		}
	}
	{
		bool L_1;
		L_1 = Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3(0, NULL);
		if (!L_1)
		{
			goto IL_001e;
		}
	}

IL_0011:
	{
		RuntimeObject* L_2;
		L_2 = PlayerFire_Fire_mC9C434664104BFAF935929AA4133DC30D6117C20(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_3;
		L_3 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_2, NULL);
	}

IL_001e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerFire_Fire_mC9C434664104BFAF935929AA4133DC30D6117C20 (PlayerFire_t2C8120136448E86CF633452034219BED662DD897* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* L_0 = (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205*)il2cpp_codegen_object_new(U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205_il2cpp_TypeInfo_var);
		U3CFireU3Ed__8__ctor_m1D08ACF93FC08FAEEE86A325184A3B83189EAD93(L_0, 0, NULL);
		U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerFire__ctor_m471056A5E1EB8DAE8A1083B097032619350B2974 (PlayerFire_t2C8120136448E86CF633452034219BED662DD897* __this, const RuntimeMethod* method) 
{
	{
		__this->___FireRate = (0.300000012f);
		__this->___FireShakeForce = (0.200000003f);
		__this->___FireShakeDuration = (0.0500000007f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__8__ctor_m1D08ACF93FC08FAEEE86A325184A3B83189EAD93 (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__8_System_IDisposable_Dispose_mC0B77D9A4BA0100468D66FD603456DD5AA21BE41 (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CFireU3Ed__8_MoveNext_m53B9D3C88B05B1D2A80AE6708387D2413E3E90CC (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	PlayerFire_t2C8120136448E86CF633452034219BED662DD897* V_1 = NULL;
	float V_2 = 0.0f;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_3;
	memset((&V_3), 0, sizeof(V_3));
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_4;
	memset((&V_4), 0, sizeof(V_4));
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_00fb;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_0102;
	}

IL_0026:
	{
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_4 = V_1;
		NullCheck(L_4);
		Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* L_5 = L_4->___BulletPool;
		NullCheck(L_5);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6;
		L_6 = Pool_get_nextThing_mBB707503DABD3A940FA5D9E841E12C7CA3C3A0F1(L_5, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_7 = L_6;
		NullCheck(L_7);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_7, NULL);
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_9 = V_1;
		NullCheck(L_9);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10 = L_9->___WeaponTip;
		NullCheck(L_10);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_10, NULL);
		NullCheck(L_8);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_8, L_11, NULL);
		NullCheck(L_7);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12;
		L_12 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_7, NULL);
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_13 = V_1;
		NullCheck(L_13);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14 = L_13->____transform;
		NullCheck(L_14);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_15;
		L_15 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_14, NULL);
		NullCheck(L_12);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_12, L_15, NULL);
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_16 = V_1;
		NullCheck(L_16);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_17 = L_16->____transform;
		NullCheck(L_17);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_18;
		L_18 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_17, NULL);
		V_4 = L_18;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_4), NULL);
		float L_20 = L_19.___y;
		V_2 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_20, (90.0f))), (0.0174532924f)));
		float L_21 = V_2;
		float L_22;
		L_22 = sinf(L_21);
		float L_23 = V_2;
		float L_24;
		L_24 = cosf(L_23);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_25;
		memset((&L_25), 0, sizeof(L_25));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_25), ((float)L_22), ((float)L_24), NULL);
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_26 = V_1;
		NullCheck(L_26);
		float L_27 = L_26->___FireShakeForce;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_28;
		L_28 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_25, L_27, NULL);
		V_3 = L_28;
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_29;
		L_29 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* L_30 = (Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA*)(Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA*)SZArrayNew(Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA_il2cpp_TypeInfo_var, (uint32_t)1);
		Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* L_31 = L_30;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_32 = V_3;
		NullCheck(L_31);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(0), (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7)L_32);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_33 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)1);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_34 = L_33;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_35;
		L_35 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		NullCheck(L_34);
		(L_34)->SetAt(static_cast<il2cpp_array_size_t>(0), (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2)L_35);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_36 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)1);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_37 = L_36;
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_38 = V_1;
		NullCheck(L_38);
		float L_39 = L_38->___FireShakeDuration;
		NullCheck(L_37);
		(L_37)->SetAt(static_cast<il2cpp_array_size_t>(0), (float)L_39);
		NullCheck(L_29);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_40;
		L_40 = ProCamera2DShake_ApplyShakesTimed_mE54EEA471792ED1872EA7BD0B4C20D94CD8D44E9(L_29, L_31, L_34, L_37, (0.100000001f), (bool)0, NULL);
		PlayerFire_t2C8120136448E86CF633452034219BED662DD897* L_41 = V_1;
		NullCheck(L_41);
		float L_42 = L_41->___FireRate;
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_43 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_43, L_42, NULL);
		__this->___U3CU3E2__current = L_43;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_43);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_00fb:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_0102:
	{
		bool L_44;
		L_44 = Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434(((int32_t)32), NULL);
		if (L_44)
		{
			goto IL_0026;
		}
	}
	{
		bool L_45;
		L_45 = Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA(0, NULL);
		if (L_45)
		{
			goto IL_0026;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CFireU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m31DAE23A4BB45B6A91D9CDFF9BC1CD4BF35EFC52 (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CFireU3Ed__8_System_Collections_IEnumerator_Reset_mA16B82A9DF02A5BBB3009AA868FFB6175BDBDBED (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CFireU3Ed__8_System_Collections_IEnumerator_Reset_mA16B82A9DF02A5BBB3009AA868FFB6175BDBDBED_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CFireU3Ed__8_System_Collections_IEnumerator_get_Current_m9D956562E85667ABD8C2D44CC91C10F5F1AEBF1C (U3CFireU3Ed__8_t46B10E60099F2EA1E3EAA75DFC241CC5FFB45205* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerHealth_Awake_m4EF1CCD86D25B865858E6C9E1255073FFA897BBF (PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_0;
		L_0 = Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7(__this, Component_GetComponentsInChildren_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_m35AC34F858BD2F34770712CD020AA0518D9409C7_RuntimeMethod_var);
		__this->____renderers = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____renderers), (void*)L_0);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_1 = __this->____renderers;
		NullCheck(L_1);
		int32_t L_2 = 0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_3 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		NullCheck(L_3);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_4;
		L_4 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_3, NULL);
		NullCheck(L_4);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5;
		L_5 = Material_get_color_mA4B7D4B96200D9D8B4F36BF19957E9DA81071DBB(L_4, NULL);
		__this->____originalColor = L_5;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerHealth_Hit_m2E72191B49697C1ABFA9050F7202D5F3D5B02543 (PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* __this, int32_t ___0_damage, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___Health;
		int32_t L_1 = ___0_damage;
		__this->___Health = ((int32_t)il2cpp_codegen_subtract(L_0, L_1));
		RuntimeObject* L_2;
		L_2 = PlayerHealth_HitAnim_m502FE49C7A0F44B646B2D62B886D220235D48EE1(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_3;
		L_3 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_2, NULL);
		int32_t L_4 = __this->___Health;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerHealth_HitAnim_m502FE49C7A0F44B646B2D62B886D220235D48EE1 (PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* L_0 = (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095*)il2cpp_codegen_object_new(U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095_il2cpp_TypeInfo_var);
		U3CHitAnimU3Ed__5__ctor_m7E9DD4E9433C185C734A9D36477091BCA69D2192(L_0, 0, NULL);
		U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerHealth__ctor_m8B821C450A0FFF4BFEB2823CE37F8A895456C8DA (PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* __this, const RuntimeMethod* method) 
{
	{
		__this->___Health = ((int32_t)100);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__5__ctor_m7E9DD4E9433C185C734A9D36477091BCA69D2192 (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__5_System_IDisposable_Dispose_m3009F9D3448A7899D6D03FAFB05F2D3A919B5C55 (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CHitAnimU3Ed__5_MoveNext_m4B4F74EB9BF2AA3323CD7F6542118207E1D11634 (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF9F9900BCECE7884F52D555FF41B6CCEDE17B92E);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0070;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state = (-1);
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F_il2cpp_TypeInfo_var);
		ProCamera2DShake_tED814BA1DC70C6A3E1B6D8DB6F97E0F42BA2BB1F* L_4;
		L_4 = ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C(NULL);
		NullCheck(L_4);
		ProCamera2DShake_Shake_m96A4C403647156FFF72E5EEA997EA9EFA6AA050B(L_4, _stringLiteralF9F9900BCECE7884F52D555FF41B6CCEDE17B92E, NULL);
		V_2 = 0;
		goto IL_004c;
	}

IL_0031:
	{
		PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* L_5 = V_1;
		NullCheck(L_5);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_6 = L_5->____renderers;
		int32_t L_7 = V_2;
		NullCheck(L_6);
		int32_t L_8 = L_7;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_9 = (L_6)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		NullCheck(L_9);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_10;
		L_10 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_9, NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_11;
		L_11 = Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline(NULL);
		NullCheck(L_10);
		Material_set_color_m5C32DEBB215FF9EE35E7B575297D8C2F29CC2A2D(L_10, L_11, NULL);
		int32_t L_12 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_12, 1));
	}

IL_004c:
	{
		int32_t L_13 = V_2;
		PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* L_14 = V_1;
		NullCheck(L_14);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_15 = L_14->____renderers;
		NullCheck(L_15);
		if ((((int32_t)L_13) < ((int32_t)((int32_t)(((RuntimeArray*)L_15)->max_length)))))
		{
			goto IL_0031;
		}
	}
	{
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_16 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_16, (0.0500000007f), NULL);
		__this->___U3CU3E2__current = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_16);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0070:
	{
		__this->___U3CU3E1__state = (-1);
		V_3 = 0;
		goto IL_0097;
	}

IL_007b:
	{
		PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* L_17 = V_1;
		NullCheck(L_17);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_18 = L_17->____renderers;
		int32_t L_19 = V_3;
		NullCheck(L_18);
		int32_t L_20 = L_19;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_21 = (L_18)->GetAt(static_cast<il2cpp_array_size_t>(L_20));
		NullCheck(L_21);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_22;
		L_22 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_21, NULL);
		PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* L_23 = V_1;
		NullCheck(L_23);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_24 = L_23->____originalColor;
		NullCheck(L_22);
		Material_set_color_m5C32DEBB215FF9EE35E7B575297D8C2F29CC2A2D(L_22, L_24, NULL);
		int32_t L_25 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_25, 1));
	}

IL_0097:
	{
		int32_t L_26 = V_3;
		PlayerHealth_tCCD7A28C931C890E679697999983211E82FB785D* L_27 = V_1;
		NullCheck(L_27);
		RendererU5BU5D_t32FDD782F67917B2291EA4FF242719877440A02A* L_28 = L_27->____renderers;
		NullCheck(L_28);
		if ((((int32_t)L_26) < ((int32_t)((int32_t)(((RuntimeArray*)L_28)->max_length)))))
		{
			goto IL_007b;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CHitAnimU3Ed__5_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m888E818BD1863568CBF8D43974C209464612D7D3 (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CHitAnimU3Ed__5_System_Collections_IEnumerator_Reset_m21C0332FD0DD74A1C22BCD2F196B9EAE65CB047A (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CHitAnimU3Ed__5_System_Collections_IEnumerator_Reset_m21C0332FD0DD74A1C22BCD2F196B9EAE65CB047A_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CHitAnimU3Ed__5_System_Collections_IEnumerator_get_Current_m60C2346A67FF5DEF4E21787E195108E09F03445B (U3CHitAnimU3Ed__5_t71E1C7C30B9577798C74FAAAA8250BF518AAC095* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput_Start_mD0291A721D4AD067A4814D9033E030A602BCC180 (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_FindObjectsOfType_TisProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3_m4EEF64F06A956AFFC4E5199CA2B0F124B2CD49A9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayerInput_U3CStartU3Eb__8_0_m76653BA2097A5C65BA097230660F8D321A4076EC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayerInput_U3CStartU3Eb__8_1_mCAAC9ACD09B0DB47A5E94AA1F4B1E0698584CF65_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* V_0 = NULL;
	int32_t V_1 = 0;
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_0;
		L_0 = Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040(__this, Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var);
		__this->____characterController = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____characterController), (void*)L_0);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* L_1;
		L_1 = Object_FindObjectsOfType_TisProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3_m4EEF64F06A956AFFC4E5199CA2B0F124B2CD49A9(Object_FindObjectsOfType_TisProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3_m4EEF64F06A956AFFC4E5199CA2B0F124B2CD49A9_RuntimeMethod_var);
		V_0 = L_1;
		V_1 = 0;
		goto IL_004c;
	}

IL_0016:
	{
		ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* L_2 = V_0;
		int32_t L_3 = V_1;
		NullCheck(L_2);
		int32_t L_4 = L_3;
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_5 = (L_2)->GetAt(static_cast<il2cpp_array_size_t>(L_4));
		NullCheck(L_5);
		UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* L_6 = L_5->___OnCinematicStarted;
		UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* L_7 = (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7*)il2cpp_codegen_object_new(UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131(L_7, __this, (intptr_t)((void*)PlayerInput_U3CStartU3Eb__8_0_m76653BA2097A5C65BA097230660F8D321A4076EC_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302(L_6, L_7, NULL);
		ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* L_8 = V_0;
		int32_t L_9 = V_1;
		NullCheck(L_8);
		int32_t L_10 = L_9;
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_11 = (L_8)->GetAt(static_cast<il2cpp_array_size_t>(L_10));
		NullCheck(L_11);
		UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* L_12 = L_11->___OnCinematicFinished;
		UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* L_13 = (UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7*)il2cpp_codegen_object_new(UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7_il2cpp_TypeInfo_var);
		UnityAction__ctor_mC53E20D6B66E0D5688CD81B88DBB34F5A58B7131(L_13, __this, (intptr_t)((void*)PlayerInput_U3CStartU3Eb__8_1_mCAAC9ACD09B0DB47A5E94AA1F4B1E0698584CF65_RuntimeMethod_var), NULL);
		NullCheck(L_12);
		UnityEvent_AddListener_m8AA4287C16628486B41DA41CA5E7A856A706D302(L_12, L_13, NULL);
		int32_t L_14 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_14, 1));
	}

IL_004c:
	{
		int32_t L_15 = V_1;
		ProCamera2DCinematicsU5BU5D_tCB2DF5097CCF7D2AE43082E6015A14044D6C58DA* L_16 = V_0;
		NullCheck(L_16);
		if ((((int32_t)L_15) < ((int32_t)((int32_t)(((RuntimeArray*)L_16)->max_length)))))
		{
			goto IL_0016;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput_Update_m62F19B56CFB86CA0ACF41F97793395F56D855AC5 (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral265E15F1F86F1C766555899D5771CF29055DE75A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7F8C014BD4810CC276D0F9F81A1E759C7B098B1E);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	{
		bool L_0 = __this->____movementAllowed;
		if (L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		float L_1;
		L_1 = Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62(_stringLiteral7F8C014BD4810CC276D0F9F81A1E759C7B098B1E, NULL);
		float L_2 = __this->___RunSpeed;
		V_0 = ((float)il2cpp_codegen_multiply(L_1, L_2));
		float L_3 = __this->____currentSpeedH;
		float L_4 = V_0;
		float L_5 = __this->___Acceleration;
		float L_6;
		L_6 = PlayerInput_IncrementTowards_mD355CF9B9D9CDBC87C7C1DF45A2C7D3BE306C349(__this, L_3, L_4, L_5, NULL);
		__this->____currentSpeedH = L_6;
		float L_7;
		L_7 = Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62(_stringLiteral265E15F1F86F1C766555899D5771CF29055DE75A, NULL);
		float L_8 = __this->___RunSpeed;
		V_1 = ((float)il2cpp_codegen_multiply(L_7, L_8));
		float L_9 = __this->____currentSpeedV;
		float L_10 = V_1;
		float L_11 = __this->___Acceleration;
		float L_12;
		L_12 = PlayerInput_IncrementTowards_mD355CF9B9D9CDBC87C7C1DF45A2C7D3BE306C349(__this, L_9, L_10, L_11, NULL);
		__this->____currentSpeedV = L_12;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_13 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->____amountToMove);
		float L_14 = __this->____currentSpeedH;
		L_13->___x = L_14;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_15 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->____amountToMove);
		float L_16 = __this->____currentSpeedV;
		L_15->___z = L_16;
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_17 = __this->____characterController;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18 = __this->____amountToMove;
		float L_19;
		L_19 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20;
		L_20 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_18, L_19, NULL);
		NullCheck(L_17);
		int32_t L_21;
		L_21 = CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4(L_17, L_20, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlayerInput_IncrementTowards_mD355CF9B9D9CDBC87C7C1DF45A2C7D3BE306C349 (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, float ___0_n, float ___1_target, float ___2_a, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_n;
		float L_1 = ___1_target;
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0006;
		}
	}
	{
		float L_2 = ___0_n;
		return L_2;
	}

IL_0006:
	{
		float L_3 = ___1_target;
		float L_4 = ___0_n;
		float L_5;
		L_5 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(((float)il2cpp_codegen_subtract(L_3, L_4)), NULL);
		V_0 = L_5;
		float L_6 = ___0_n;
		float L_7 = ___2_a;
		float L_8;
		L_8 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_9 = V_0;
		___0_n = ((float)il2cpp_codegen_add(L_6, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_7, L_8)), L_9))));
		float L_10 = V_0;
		float L_11 = ___1_target;
		float L_12 = ___0_n;
		float L_13;
		L_13 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(((float)il2cpp_codegen_subtract(L_11, L_12)), NULL);
		if ((((float)L_10) == ((float)L_13)))
		{
			goto IL_0029;
		}
	}
	{
		float L_14 = ___1_target;
		return L_14;
	}

IL_0029:
	{
		float L_15 = ___0_n;
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput__ctor_m6B9027968AA8D70AFEEAFB6F03F8D79BC420286B (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, const RuntimeMethod* method) 
{
	{
		__this->___RunSpeed = (12.0f);
		__this->___Acceleration = (30.0f);
		__this->____movementAllowed = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput_U3CStartU3Eb__8_0_m76653BA2097A5C65BA097230660F8D321A4076EC (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, const RuntimeMethod* method) 
{
	{
		__this->____movementAllowed = (bool)0;
		__this->____currentSpeedH = (0.0f);
		__this->____currentSpeedV = (0.0f);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput_U3CStartU3Eb__8_1_mCAAC9ACD09B0DB47A5E94AA1F4B1E0698584CF65 (PlayerInput_t349D1DD0B8070A23963610E102DDE4BF92D5B139* __this, const RuntimeMethod* method) 
{
	{
		__this->____movementAllowed = (bool)1;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Pool_get_nextThing_mBB707503DABD3A940FA5D9E841E12C7CA3C3A0F1 (Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_AddComponent_TisPoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967_m6DF34A43B92F7DE1925F4B4363A4DDEA778B117F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_m3196E18C5CF157CAC58991FACEB9DFD441702260_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m4C37ED2D928D63B80F55AF434730C2D64EEB9F22_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mE8DBE527F24D9CFED839C34216C475B716169979_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m10D87C6E0708CA912BBB02555BF7D0FBC5D7A2B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* V_0 = NULL;
	{
		List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* L_0 = __this->___things;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = List_1_get_Count_m4C37ED2D928D63B80F55AF434730C2D64EEB9F22_inline(L_0, List_1_get_Count_m4C37ED2D928D63B80F55AF434730C2D64EEB9F22_RuntimeMethod_var);
		if ((((int32_t)L_1) >= ((int32_t)1)))
		{
			goto IL_004a;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_2 = __this->___thing;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m10D87C6E0708CA912BBB02555BF7D0FBC5D7A2B3(L_2, Object_Instantiate_TisGameObject_t76FEDD663AB33C991A9C9A23129337651094216F_m10D87C6E0708CA912BBB02555BF7D0FBC5D7A2B3_RuntimeMethod_var);
		V_0 = L_3;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_4 = V_0;
		NullCheck(L_4);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5;
		L_5 = GameObject_get_transform_m0BC10ADFA1632166AE5544BDF9038A2650C2AE56(L_4, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6;
		L_6 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_5);
		Transform_set_parent_m9BD5E563B539DD5BEC342736B03F97B38A243234(L_5, L_6, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_7 = V_0;
		NullCheck(L_7);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_7, (bool)0, NULL);
		List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* L_8 = __this->___things;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_9 = V_0;
		NullCheck(L_8);
		List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_inline(L_8, L_9, List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_RuntimeMethod_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_10 = V_0;
		NullCheck(L_10);
		PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967* L_11;
		L_11 = GameObject_AddComponent_TisPoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967_m6DF34A43B92F7DE1925F4B4363A4DDEA778B117F(L_10, GameObject_AddComponent_TisPoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967_m6DF34A43B92F7DE1925F4B4363A4DDEA778B117F_RuntimeMethod_var);
		NullCheck(L_11);
		L_11->___pool = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_11->___pool), (void*)__this);
	}

IL_004a:
	{
		List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* L_12 = __this->___things;
		NullCheck(L_12);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_13;
		L_13 = List_1_get_Item_mE8DBE527F24D9CFED839C34216C475B716169979(L_12, 0, List_1_get_Item_mE8DBE527F24D9CFED839C34216C475B716169979_RuntimeMethod_var);
		List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* L_14 = __this->___things;
		NullCheck(L_14);
		List_1_RemoveAt_m3196E18C5CF157CAC58991FACEB9DFD441702260(L_14, 0, List_1_RemoveAt_m3196E18C5CF157CAC58991FACEB9DFD441702260_RuntimeMethod_var);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_15 = L_13;
		NullCheck(L_15);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_15, (bool)1, NULL);
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Pool_set_nextThing_m85C1B38EC0E3F26779600AAB56ABAA3C592033CA (Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* __this, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_0 = ___0_value;
		NullCheck(L_0);
		GameObject_SetActive_m638E92E1E75E519E5B24CF150B08CA8E0CDFAB92(L_0, (bool)0, NULL);
		List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* L_1 = __this->___things;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_2 = ___0_value;
		NullCheck(L_1);
		List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_inline(L_1, L_2, List_1_Add_m43FBF207375C6E06B8C45ECE614F9B8008FB686E_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Pool__ctor_mEDE0372E7C92CB10BEF27EBCB54F74BD76987D7B (Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m447372C1EF7141193B93090A77395B786C72C7BC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B* L_0 = (List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B*)il2cpp_codegen_object_new(List_1_tB951CE80B58D1BF9650862451D8DAD8C231F207B_il2cpp_TypeInfo_var);
		List_1__ctor_m447372C1EF7141193B93090A77395B786C72C7BC(L_0, List_1__ctor_m447372C1EF7141193B93090A77395B786C72C7BC_RuntimeMethod_var);
		__this->___things = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___things), (void*)L_0);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PoolMember_OnDisable_m97FBA0D7B7BA4C015BB8A5041EB572CFD3EF45FA (PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967* __this, const RuntimeMethod* method) 
{
	{
		Pool_t0D8E85F94CE1C56440DC4387645F751D8F8AB884* L_0 = __this->___pool;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_1;
		L_1 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_0);
		Pool_set_nextThing_m85C1B38EC0E3F26779600AAB56ABAA3C592033CA(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PoolMember__ctor_m7070035AA0BE5D19A5C2B7E664F8BF888306AD98 (PoolMember_tDABE30AE89993365E434ABE9438A6A8B1EBD0967* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RotateTowardsMouse_Awake_mA64A3A4C9057CA30CD3F50B4ADC5B58A82F0EF82 (RotateTowardsMouse_tD99BE10BDFBE8522BF3CFFA7243C56DDCCFAEF81* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->____transform = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____transform), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RotateTowardsMouse_Update_mE51D6F4E0E8CB2B9536825426096CB06431B8B18 (RotateTowardsMouse_tD99BE10BDFBE8522BF3CFFA7243C56DDCCFAEF81* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_2;
	memset((&V_2), 0, sizeof(V_2));
	float V_3 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		V_0 = L_0;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_1;
		L_1 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2 = __this->____transform;
		NullCheck(L_2);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95(L_2, NULL);
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Camera_WorldToScreenPoint_m26B4C8945C3B5731F1CC5944CFD96BF17126BAA3(L_1, L_3, NULL);
		V_1 = L_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = V_0;
		float L_6 = L_5.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_1;
		float L_8 = L_7.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = V_0;
		float L_10 = L_9.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = V_1;
		float L_12 = L_11.___y;
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&V_2), ((float)il2cpp_codegen_subtract(L_6, L_8)), ((float)il2cpp_codegen_subtract(L_10, L_12)), NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_13 = V_2;
		float L_14 = L_13.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_15 = V_2;
		float L_16 = L_15.___x;
		float L_17;
		L_17 = atan2f(L_14, L_16);
		V_3 = ((float)il2cpp_codegen_multiply(L_17, (57.2957802f)));
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_18 = __this->____transform;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19 = __this->____transform;
		NullCheck(L_19);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_20;
		L_20 = Transform_get_rotation_m32AF40CA0D50C797DA639A696F8EAEC7524C179C(L_19, NULL);
		float L_21 = V_3;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22;
		L_22 = Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline((0.0f), ((-L_21)), (0.0f), NULL);
		float L_23 = __this->___Ease;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_24;
		L_24 = Quaternion_Slerp_m0A9969F500E7716EA4F6BC4E7D5464372D8E9E15(L_20, L_22, L_23, NULL);
		NullCheck(L_18);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_18, L_24, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RotateTowardsMouse__ctor_mC2822F31F15D5DB1DDF42AC2F64EE5B3CB838933 (RotateTowardsMouse_tD99BE10BDFBE8522BF3CFFA7243C56DDCCFAEF81* __this, const RuntimeMethod* method) 
{
	{
		__this->___Ease = (0.150000006f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SwitchCameraProjection_Awake_m4B57A739BA2FF19BD5E2285F39E959DE44E374C1 (SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* __this, const RuntimeMethod* method) 
{
	{
		SwitchCameraProjection_Switch_m0A36CA85AC6C211D8CED6B7FFF760C07BEF90953(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SwitchCameraProjection_OnGUI_mF03DD99C8C0D379BC2A9EA253DE827F1672966E7 (SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral52CFCE10BC1C6F4D8459EED296A6E6259CB5A668);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral956BD0602781733C18B694B8A7EBF75AF0604E9B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9C7B3F1A3587CF2D890224D68044EF93011CC700);
		s_Il2CppMethodInitialized = true;
	}
	String_t* G_B3_0 = NULL;
	String_t* G_B2_0 = NULL;
	int32_t G_B4_0 = 0;
	String_t* G_B4_1 = NULL;
	{
		int32_t L_0;
		L_0 = Screen_get_width_mF608FF3252213E7EFA1F0D2F744C28110E9E5AC9(NULL);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_1;
		memset((&L_1), 0, sizeof(L_1));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_1), ((float)((int32_t)il2cpp_codegen_subtract(L_0, ((int32_t)210)))), (10.0f), (200.0f), (30.0f), NULL);
		String_t* L_2 = __this->____cameraMode;
		String_t* L_3;
		L_3 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteral956BD0602781733C18B694B8A7EBF75AF0604E9B, L_2, _stringLiteral52CFCE10BC1C6F4D8459EED296A6E6259CB5A668, NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_4;
		L_4 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(L_1, L_3, NULL);
		if (!L_4)
		{
			goto IL_005c;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_5;
		L_5 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		NullCheck(L_5);
		bool L_6;
		L_6 = Camera_get_orthographic_m904DEFC76C54DA4E30C20A62A86D5D87B7D4DD8F(L_5, NULL);
		if (L_6)
		{
			G_B3_0 = _stringLiteral9C7B3F1A3587CF2D890224D68044EF93011CC700;
			goto IL_0050;
		}
		G_B2_0 = _stringLiteral9C7B3F1A3587CF2D890224D68044EF93011CC700;
	}
	{
		G_B4_0 = 1;
		G_B4_1 = G_B2_0;
		goto IL_0051;
	}

IL_0050:
	{
		G_B4_0 = 0;
		G_B4_1 = G_B3_0;
	}

IL_0051:
	{
		PlayerPrefs_SetInt_m956D3E2DB966F20CF42F842880DDF9E2BE94D948(G_B4_1, G_B4_0, NULL);
		SwitchCameraProjection_Switch_m0A36CA85AC6C211D8CED6B7FFF760C07BEF90953(__this, NULL);
	}

IL_005c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SwitchCameraProjection_Switch_m0A36CA85AC6C211D8CED6B7FFF760C07BEF90953 (SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0E43178D749CE80F6CE0900EB962639B261C328C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9C7B3F1A3587CF2D890224D68044EF93011CC700);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC1A583013E708DA8E4D0F8DD299B6F59C7DD3195);
		s_Il2CppMethodInitialized = true;
	}
	SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* G_B2_0 = NULL;
	SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* G_B1_0 = NULL;
	String_t* G_B3_0 = NULL;
	SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* G_B3_1 = NULL;
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0;
		L_0 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		int32_t L_1;
		L_1 = PlayerPrefs_GetInt_m8AD1FA8BA54CC6CE2B2AEEE36B6D75587BB1692D(_stringLiteral9C7B3F1A3587CF2D890224D68044EF93011CC700, 0, NULL);
		NullCheck(L_0);
		Camera_set_orthographic_m64915C0840A68E526830A69F1C40257206185020(L_0, (bool)((((int32_t)L_1) == ((int32_t)1))? 1 : 0), NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_2;
		L_2 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = Camera_get_orthographic_m904DEFC76C54DA4E30C20A62A86D5D87B7D4DD8F(L_2, NULL);
		if (L_3)
		{
			G_B2_0 = __this;
			goto IL_002c;
		}
		G_B1_0 = __this;
	}
	{
		G_B3_0 = _stringLiteral0E43178D749CE80F6CE0900EB962639B261C328C;
		G_B3_1 = G_B1_0;
		goto IL_0031;
	}

IL_002c:
	{
		G_B3_0 = _stringLiteralC1A583013E708DA8E4D0F8DD299B6F59C7DD3195;
		G_B3_1 = G_B2_0;
	}

IL_0031:
	{
		NullCheck(G_B3_1);
		G_B3_1->____cameraMode = G_B3_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B3_1->____cameraMode), (void*)G_B3_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SwitchCameraProjection__ctor_m4621DC1A9C8DCBE5CE8DA4B6A6D630A07CD89A97 (SwitchCameraProjection_tDEF344D65D1DFCDF2F34627385BF739FFD73EE4F* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput_Start_mA030D60FD01D12C3C5B3D9A9613EBBD659DFA4BE (PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_0;
		L_0 = Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040(__this, Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var);
		__this->____characterController = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____characterController), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput_Update_m3519692162C77C08ECC1EDB53A68D94A8D890A6A (PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7F8C014BD4810CC276D0F9F81A1E759C7B098B1E);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_0 = __this->____characterController;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E(L_0, NULL);
		if (!((int32_t)((int32_t)L_1&1)))
		{
			goto IL_001a;
		}
	}
	{
		__this->___currentSpeed = (0.0f);
	}

IL_001a:
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_2 = __this->____characterController;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E(L_2, NULL);
		if (!((int32_t)((int32_t)L_3&4)))
		{
			goto IL_0042;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_4 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		L_4->___y = (-1.0f);
		__this->___totalJumps = 0;
		goto IL_005d;
	}

IL_0042:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_5 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float* L_6 = (float*)(&L_5->___y);
		float* L_7 = L_6;
		float L_8 = *((float*)L_7);
		float L_9 = __this->___gravity;
		float L_10;
		L_10 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		*((float*)L_7) = (float)((float)il2cpp_codegen_subtract(L_8, ((float)il2cpp_codegen_multiply(L_9, L_10))));
	}

IL_005d:
	{
		bool L_11;
		L_11 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)119), NULL);
		if (L_11)
		{
			goto IL_007b;
		}
	}
	{
		bool L_12;
		L_12 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)32), NULL);
		if (L_12)
		{
			goto IL_007b;
		}
	}
	{
		bool L_13;
		L_13 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(((int32_t)273), NULL);
		if (!L_13)
		{
			goto IL_00a8;
		}
	}

IL_007b:
	{
		int32_t L_14 = __this->___totalJumps;
		int32_t L_15 = __this->___jumpsAllowed;
		if ((((int32_t)L_14) >= ((int32_t)L_15)))
		{
			goto IL_00a8;
		}
	}
	{
		int32_t L_16 = __this->___totalJumps;
		__this->___totalJumps = ((int32_t)il2cpp_codegen_add(L_16, 1));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_17 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_18 = __this->___jumpHeight;
		L_17->___y = L_18;
	}

IL_00a8:
	{
		float L_19;
		L_19 = Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62(_stringLiteral7F8C014BD4810CC276D0F9F81A1E759C7B098B1E, NULL);
		float L_20 = __this->___runSpeed;
		V_0 = ((float)il2cpp_codegen_multiply(L_19, L_20));
		float L_21 = __this->___currentSpeed;
		float L_22 = V_0;
		float L_23 = __this->___acceleration;
		float L_24;
		L_24 = PlayerInput_IncrementTowards_mD28E8347BA822363232A5157709A19E5E15EE43A(__this, L_21, L_22, L_23, NULL);
		__this->___currentSpeed = L_24;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_25;
		L_25 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_25);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_26;
		L_26 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_25, NULL);
		float L_27 = L_26.___z;
		if ((((float)L_27) == ((float)(0.0f))))
		{
			goto IL_0106;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_28 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_29;
		L_29 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_29);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_30;
		L_30 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_29, NULL);
		float L_31 = L_30.___z;
		L_28->___z = ((-L_31));
	}

IL_0106:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_32 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_33 = __this->___currentSpeed;
		L_32->___x = L_33;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_34 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_35 = L_34->___x;
		if ((((float)L_35) == ((float)(0.0f))))
		{
			goto IL_0174;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_36 = __this->___Body;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_37 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_38 = L_37->___x;
		float L_39;
		L_39 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(L_38, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_40 = __this->___Body;
		NullCheck(L_40);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_41;
		L_41 = Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F(L_40, NULL);
		float L_42 = L_41.___x;
		float L_43;
		L_43 = fabsf(L_42);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_44 = __this->___Body;
		NullCheck(L_44);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_45;
		L_45 = Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F(L_44, NULL);
		float L_46 = L_45.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_47;
		memset((&L_47), 0, sizeof(L_47));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_47), ((float)il2cpp_codegen_multiply(L_39, L_43)), L_46, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_48;
		L_48 = Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline(L_47, NULL);
		NullCheck(L_36);
		Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633(L_36, L_48, NULL);
	}

IL_0174:
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_49 = __this->____characterController;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_50 = __this->___amountToMove;
		float L_51;
		L_51 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_52;
		L_52 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_50, L_51, NULL);
		NullCheck(L_49);
		int32_t L_53;
		L_53 = CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4(L_49, L_52, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlayerInput_IncrementTowards_mD28E8347BA822363232A5157709A19E5E15EE43A (PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08* __this, float ___0_n, float ___1_target, float ___2_a, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_n;
		float L_1 = ___1_target;
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0006;
		}
	}
	{
		float L_2 = ___0_n;
		return L_2;
	}

IL_0006:
	{
		float L_3 = ___1_target;
		float L_4 = ___0_n;
		float L_5;
		L_5 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(((float)il2cpp_codegen_subtract(L_3, L_4)), NULL);
		V_0 = L_5;
		float L_6 = ___0_n;
		float L_7 = ___2_a;
		float L_8;
		L_8 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_9 = V_0;
		___0_n = ((float)il2cpp_codegen_add(L_6, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_7, L_8)), L_9))));
		float L_10 = V_0;
		float L_11 = ___1_target;
		float L_12 = ___0_n;
		float L_13;
		L_13 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(((float)il2cpp_codegen_subtract(L_11, L_12)), NULL);
		if ((((float)L_10) == ((float)L_13)))
		{
			goto IL_0029;
		}
	}
	{
		float L_14 = ___1_target;
		return L_14;
	}

IL_0029:
	{
		float L_15 = ___0_n;
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInput__ctor_m7B90EFECE5655E3E732BBA4F86708565AEBB0385 (PlayerInput_tCAF73316D24D8D4C1AE04F17B59D86088A8E0B08* __this, const RuntimeMethod* method) 
{
	{
		__this->___gravity = (20.0f);
		__this->___runSpeed = (12.0f);
		__this->___acceleration = (30.0f);
		__this->___jumpHeight = (12.0f);
		__this->___jumpsAllowed = 2;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInputBot_Start_mB9848527D69090273F7F40462DC2DA8B66DE9178 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_0;
		L_0 = Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040(__this, Component_GetComponent_TisCharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A_m96B48A644EDC97C5C82F154D1FEA551B2E392040_RuntimeMethod_var);
		__this->____characterController = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____characterController), (void*)L_0);
		RuntimeObject* L_1;
		L_1 = PlayerInputBot_RandomInputJump_m088DD669939E0E38E25CBAA1FFBB40CD797B9D85(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_2;
		L_2 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_1, NULL);
		RuntimeObject* L_3;
		L_3 = PlayerInputBot_RandomInputSpeed_mBD5BE6DB4021C7F69A581CB6B02087AA5ABCF2D6(__this, NULL);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_4;
		L_4 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(__this, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerInputBot_RandomInputJump_m088DD669939E0E38E25CBAA1FFBB40CD797B9D85 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* L_0 = (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED*)il2cpp_codegen_object_new(U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED_il2cpp_TypeInfo_var);
		U3CRandomInputJumpU3Ed__13__ctor_mB8DCD6A6EDB7F831EF765A45D6678A392D5EFFA5(L_0, 0, NULL);
		U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlayerInputBot_RandomInputSpeed_mBD5BE6DB4021C7F69A581CB6B02087AA5ABCF2D6 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* L_0 = (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF*)il2cpp_codegen_object_new(U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF_il2cpp_TypeInfo_var);
		U3CRandomInputSpeedU3Ed__14__ctor_mF9403523C795C886A933D9B8DF002059891CA68A(L_0, 0, NULL);
		U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInputBot_Update_m31C836C012EC83E4E61D6EF9165A4FC717A62949 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_0 = __this->____characterController;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E(L_0, NULL);
		if (!((int32_t)((int32_t)L_1&1)))
		{
			goto IL_001a;
		}
	}
	{
		__this->___currentSpeed = (0.0f);
	}

IL_001a:
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_2 = __this->____characterController;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = CharacterController_get_collisionFlags_m2C5CBA83541CB45D0F6B620FB56BF86F8477BD1E(L_2, NULL);
		if (!((int32_t)((int32_t)L_3&4)))
		{
			goto IL_0042;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_4 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		L_4->___y = (-1.0f);
		__this->___totalJumps = 0;
		goto IL_005d;
	}

IL_0042:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_5 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float* L_6 = (float*)(&L_5->___y);
		float* L_7 = L_6;
		float L_8 = *((float*)L_7);
		float L_9 = __this->___gravity;
		float L_10;
		L_10 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		*((float*)L_7) = (float)((float)il2cpp_codegen_subtract(L_8, ((float)il2cpp_codegen_multiply(L_9, L_10))));
	}

IL_005d:
	{
		bool L_11 = __this->____fakeInputJump;
		if (!L_11)
		{
			goto IL_0092;
		}
	}
	{
		int32_t L_12 = __this->___totalJumps;
		int32_t L_13 = __this->___jumpsAllowed;
		if ((((int32_t)L_12) >= ((int32_t)L_13)))
		{
			goto IL_0092;
		}
	}
	{
		int32_t L_14 = __this->___totalJumps;
		__this->___totalJumps = ((int32_t)il2cpp_codegen_add(L_14, 1));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_15 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_16 = __this->___jumpHeight;
		L_15->___y = L_16;
	}

IL_0092:
	{
		float L_17 = __this->____fakeInputHorizontalAxis;
		float L_18 = __this->___runSpeed;
		V_0 = ((float)il2cpp_codegen_multiply(L_17, L_18));
		float L_19 = __this->___currentSpeed;
		float L_20 = V_0;
		float L_21 = __this->___acceleration;
		float L_22;
		L_22 = PlayerInputBot_IncrementTowards_m03CD9814F330F30FE7D763E8B679BD7F52777629(__this, L_19, L_20, L_21, NULL);
		__this->___currentSpeed = L_22;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_23;
		L_23 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_23);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24;
		L_24 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_23, NULL);
		float L_25 = L_24.___z;
		if ((((float)L_25) == ((float)(0.0f))))
		{
			goto IL_00ec;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_26 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_27;
		L_27 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_27);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_28;
		L_28 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_27, NULL);
		float L_29 = L_28.___z;
		L_26->___z = ((-L_29));
	}

IL_00ec:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_30 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_31 = __this->___currentSpeed;
		L_30->___x = L_31;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_32 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_33 = L_32->___x;
		if ((((float)L_33) == ((float)(0.0f))))
		{
			goto IL_0139;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_34 = __this->___Body;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_35 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___amountToMove);
		float L_36 = L_35->___x;
		float L_37;
		L_37 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(L_36, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_38;
		memset((&L_38), 0, sizeof(L_38));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_38), L_37, (1.0f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_39;
		L_39 = Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline(L_38, NULL);
		NullCheck(L_34);
		Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633(L_34, L_39, NULL);
	}

IL_0139:
	{
		CharacterController_t847C1A2719F60547D7D6077B648D6CE2D1EF3A6A* L_40 = __this->____characterController;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_41 = __this->___amountToMove;
		float L_42;
		L_42 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43;
		L_43 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_41, L_42, NULL);
		NullCheck(L_40);
		int32_t L_44;
		L_44 = CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4(L_40, L_43, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlayerInputBot_IncrementTowards_m03CD9814F330F30FE7D763E8B679BD7F52777629 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, float ___0_n, float ___1_target, float ___2_a, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_n;
		float L_1 = ___1_target;
		if ((!(((float)L_0) == ((float)L_1))))
		{
			goto IL_0006;
		}
	}
	{
		float L_2 = ___0_n;
		return L_2;
	}

IL_0006:
	{
		float L_3 = ___1_target;
		float L_4 = ___0_n;
		float L_5;
		L_5 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(((float)il2cpp_codegen_subtract(L_3, L_4)), NULL);
		V_0 = L_5;
		float L_6 = ___0_n;
		float L_7 = ___2_a;
		float L_8;
		L_8 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		float L_9 = V_0;
		___0_n = ((float)il2cpp_codegen_add(L_6, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_7, L_8)), L_9))));
		float L_10 = V_0;
		float L_11 = ___1_target;
		float L_12 = ___0_n;
		float L_13;
		L_13 = Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline(((float)il2cpp_codegen_subtract(L_11, L_12)), NULL);
		if ((((float)L_10) == ((float)L_13)))
		{
			goto IL_0029;
		}
	}
	{
		float L_14 = ___1_target;
		return L_14;
	}

IL_0029:
	{
		float L_15 = ___0_n;
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayerInputBot__ctor_m9C0D7B64314D75EECB98C462DD5C31F9A60FD6B5 (PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* __this, const RuntimeMethod* method) 
{
	{
		__this->___gravity = (20.0f);
		__this->___runSpeed = (12.0f);
		__this->___acceleration = (30.0f);
		__this->___jumpHeight = (12.0f);
		__this->___jumpsAllowed = 2;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputJumpU3Ed__13__ctor_mB8DCD6A6EDB7F831EF765A45D6678A392D5EFFA5 (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputJumpU3Ed__13_System_IDisposable_Dispose_m7C5B6E60F836226EA8CA408D093C668A84A217C9 (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CRandomInputJumpU3Ed__13_MoveNext_mCF805A089DA0A13695DF8FC54B77ED1A610EF7D5 (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		switch (L_2)
		{
			case 0:
			{
				goto IL_0026;
			}
			case 1:
			{
				goto IL_0054;
			}
			case 2:
			{
				goto IL_0070;
			}
			case 3:
			{
				goto IL_00a1;
			}
		}
	}
	{
		return (bool)0;
	}

IL_0026:
	{
		__this->___U3CU3E1__state = (-1);
		WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663* L_3 = (WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663*)il2cpp_codegen_object_new(WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663_il2cpp_TypeInfo_var);
		WaitForEndOfFrame__ctor_m4AF7E576C01E6B04443BB898B1AE5D645F7D45AB(L_3, NULL);
		__this->___U3CwaitForEndOfFrameU3E5__2 = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CwaitForEndOfFrameU3E5__2), (void*)L_3);
	}

IL_0038:
	{
		PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* L_4 = V_1;
		NullCheck(L_4);
		L_4->____fakeInputJump = (bool)1;
		WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663* L_5 = __this->___U3CwaitForEndOfFrameU3E5__2;
		__this->___U3CU3E2__current = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_5);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0054:
	{
		__this->___U3CU3E1__state = (-1);
		WaitForEndOfFrame_tE38D80923E3F8380069B423968C25ABE50A46663* L_6 = __this->___U3CwaitForEndOfFrameU3E5__2;
		__this->___U3CU3E2__current = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_6);
		__this->___U3CU3E1__state = 2;
		return (bool)1;
	}

IL_0070:
	{
		__this->___U3CU3E1__state = (-1);
		PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* L_7 = V_1;
		NullCheck(L_7);
		L_7->____fakeInputJump = (bool)0;
		float L_8;
		L_8 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494((0.200000003f), (1.0f), NULL);
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_9 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_9, L_8, NULL);
		__this->___U3CU3E2__current = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_9);
		__this->___U3CU3E1__state = 3;
		return (bool)1;
	}

IL_00a1:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_0038;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CRandomInputJumpU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBB657D69CEB99A139B9E89184AF916896EF94808 (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_Reset_m49E9633C06623C867A599BEC40859603D93CBE8C (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_Reset_m49E9633C06623C867A599BEC40859603D93CBE8C_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_get_Current_mE9B95957B4F2D96650FF229E4567F3F16119BA1D (U3CRandomInputJumpU3Ed__13_tD8AAC567423544C7EA964EE33942D958EA8AA5ED* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputSpeedU3Ed__14__ctor_mF9403523C795C886A933D9B8DF002059891CA68A (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputSpeedU3Ed__14_System_IDisposable_Dispose_m9BC5DBA756ABEE6955B06C1FB02BD54F4EA2100B (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CRandomInputSpeedU3Ed__14_MoveNext_mB2221D545B780984750471F989349D01D73B0079 (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0056;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_001e:
	{
		PlayerInputBot_tBE0643B0BC4B52BAD4B236F4DC47E6AD2C9CE349* L_4 = V_1;
		float L_5;
		L_5 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494((-1.0f), (1.0f), NULL);
		NullCheck(L_4);
		L_4->____fakeInputHorizontalAxis = L_5;
		float L_6;
		L_6 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494((1.0f), (3.0f), NULL);
		WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3* L_7 = (WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3*)il2cpp_codegen_object_new(WaitForSeconds_tF179DF251655B8DF044952E70A60DF4B358A3DD3_il2cpp_TypeInfo_var);
		WaitForSeconds__ctor_m579F95BADEDBAB4B3A7E302C6EE3995926EF2EFC(L_7, L_6, NULL);
		__this->___U3CU3E2__current = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_7);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0056:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_001e;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CRandomInputSpeedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43BA88FB08DFAD375D69D7F9558FC26AAC9181AC (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_Reset_m5C882BB510DBCD5495FF38F35446C2A45EAA96C1 (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_Reset_m5C882BB510DBCD5495FF38F35446C2A45EAA96C1_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_get_Current_m4AF1BF5A3C32967740C7385D352302258BE058D7 (U3CRandomInputSpeedU3Ed__14_t883FE151DB1D94A951640F368434CBCA16376DBF* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ToggleCinematics_OnGUI_m2051D42958ED9A0B8B9A98DD8926502A4789CBF2 (ToggleCinematics_tEC2D374385ABC25CBA232CF013389B2531E2E860* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral800C762D9EF92B399EC87C776239043ACEEC0717);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8243A16D425F93AF62CAAB2BFAE01A2D6246A5FE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral984FD2E5A843365D516B82897422143B40AE6B1A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEF8AE9E6CBCFDABA932FBEB4C85964F450F724F5);
		s_Il2CppMethodInitialized = true;
	}
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B2_0;
	memset((&G_B2_0), 0, sizeof(G_B2_0));
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B1_0;
	memset((&G_B1_0), 0, sizeof(G_B1_0));
	String_t* G_B3_0 = NULL;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D G_B3_1;
	memset((&G_B3_1), 0, sizeof(G_B3_1));
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0;
		memset((&L_0), 0, sizeof(L_0));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_0), (5.0f), (5.0f), (180.0f), (30.0f), NULL);
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_1 = __this->___Cinematics;
		NullCheck(L_1);
		bool L_2;
		L_2 = ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5_inline(L_1, NULL);
		if (L_2)
		{
			G_B2_0 = L_0;
			goto IL_002d;
		}
		G_B1_0 = L_0;
	}
	{
		G_B3_0 = _stringLiteral8243A16D425F93AF62CAAB2BFAE01A2D6246A5FE;
		G_B3_1 = G_B1_0;
		goto IL_0032;
	}

IL_002d:
	{
		G_B3_0 = _stringLiteral800C762D9EF92B399EC87C776239043ACEEC0717;
		G_B3_1 = G_B2_0;
	}

IL_0032:
	{
		String_t* L_3;
		L_3 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(G_B3_0, _stringLiteral984FD2E5A843365D516B82897422143B40AE6B1A, NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_4;
		L_4 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(G_B3_1, L_3, NULL);
		if (!L_4)
		{
			goto IL_0068;
		}
	}
	{
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_5 = __this->___Cinematics;
		NullCheck(L_5);
		bool L_6;
		L_6 = ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5_inline(L_5, NULL);
		if (!L_6)
		{
			goto IL_005d;
		}
	}
	{
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_7 = __this->___Cinematics;
		NullCheck(L_7);
		ProCamera2DCinematics_Stop_m5430BE4DC9FB51CAEA12AB1A0403031966BE7415(L_7, NULL);
		goto IL_0068;
	}

IL_005d:
	{
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_8 = __this->___Cinematics;
		NullCheck(L_8);
		ProCamera2DCinematics_Play_m0EC4378CCBB85B084497DBB3D3E6D3A0487A084B(L_8, NULL);
	}

IL_0068:
	{
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_9 = __this->___Cinematics;
		NullCheck(L_9);
		bool L_10;
		L_10 = ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5_inline(L_9, NULL);
		if (!L_10)
		{
			goto IL_00a5;
		}
	}
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_11;
		memset((&L_11), 0, sizeof(L_11));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_11), (195.0f), (5.0f), (40.0f), (30.0f), NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_12;
		L_12 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(L_11, _stringLiteralEF8AE9E6CBCFDABA932FBEB4C85964F450F724F5, NULL);
		if (!L_12)
		{
			goto IL_00a5;
		}
	}
	{
		ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* L_13 = __this->___Cinematics;
		NullCheck(L_13);
		ProCamera2DCinematics_GoToNextTarget_m1F8BCC330356EBD1818DB49BE438D136B2CCDED6(L_13, NULL);
	}

IL_00a5:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ToggleCinematics__ctor_mA06EF3C0F3591B5E13DE3534B911448054F41C4C (ToggleCinematics_tEC2D374385ABC25CBA232CF013389B2531E2E860* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ToggleTransitionsFX_OnGUI_mC99F88436FC1AFE5B11791C517C2B16927EA427B (ToggleTransitionsFX_t490E8D3A95F46EF4DC1E08B1346906DAE04187EC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4B39F111AB408DD7B675B3A73AD399E3BB0534FE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5CE77766E15B47A06E71528457D3B0D979972409);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_0;
		memset((&L_0), 0, sizeof(L_0));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_0), (5.0f), (5.0f), (180.0f), (30.0f), NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(L_0, _stringLiteral5CE77766E15B47A06E71528457D3B0D979972409, NULL);
		if (!L_1)
		{
			goto IL_002f;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D_il2cpp_TypeInfo_var);
		ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D* L_2;
		L_2 = ProCamera2DTransitionsFX_get_Instance_mFF94EBFD9B64ABF81E88E2C0FE7EF919907DB1C8(NULL);
		NullCheck(L_2);
		ProCamera2DTransitionsFX_TransitionEnter_m97589A7091603A387838B1F5AAAC32A061C90781(L_2, NULL);
	}

IL_002f:
	{
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_3;
		memset((&L_3), 0, sizeof(L_3));
		Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline((&L_3), (5.0f), (45.0f), (180.0f), (30.0f), NULL);
		il2cpp_codegen_runtime_class_init_inline(GUI_tA9CDB3D69DB13D51AD83ABDB587EF95947EC2D2A_il2cpp_TypeInfo_var);
		bool L_4;
		L_4 = GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B(L_3, _stringLiteral4B39F111AB408DD7B675B3A73AD399E3BB0534FE, NULL);
		if (!L_4)
		{
			goto IL_005e;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D_il2cpp_TypeInfo_var);
		ProCamera2DTransitionsFX_t4E9EA50CF828B3093C6D036C9DBF1069484CDD7D* L_5;
		L_5 = ProCamera2DTransitionsFX_get_Instance_mFF94EBFD9B64ABF81E88E2C0FE7EF919907DB1C8(NULL);
		NullCheck(L_5);
		ProCamera2DTransitionsFX_TransitionExit_m604CC64907B1AB598DA732CC2B2B8509D7B4EAB4(L_5, NULL);
	}

IL_005e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ToggleTransitionsFX__ctor_m741E30759E3313807DAE5011C96215E44B82964A (ToggleTransitionsFX_t490E8D3A95F46EF4DC1E08B1346906DAE04187EC* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Rect__ctor_m18C3033D135097BEE424AAA68D91C706D2647F23_inline (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* __this, float ___0_x, float ___1_y, float ___2_width, float ___3_height, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___m_XMin = L_0;
		float L_1 = ___1_y;
		__this->___m_YMin = L_1;
		float L_2 = ___2_width;
		__this->___m_Width = L_2;
		float L_3 = ___3_height;
		__this->___m_Height = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_right_mFF573AFBBB2186E7AFA1BA7CA271A78DF67E4EA0_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___rightVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_magnitude_mF0D6017E90B345F1F52D1CC564C640F1A847AF2D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___y;
		float L_3 = __this->___y;
		float L_4 = __this->___z;
		float L_5 = __this->___z;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_6;
		L_6 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3)))), ((float)il2cpp_codegen_multiply(L_4, L_5))))));
		V_0 = ((float)L_6);
		goto IL_0034;
	}

IL_0034:
	{
		float L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_v;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_v;
		float L_3 = L_2.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_4), L_1, L_3, NULL);
		V_0 = L_4;
		goto IL_0015;
	}

IL_0015:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector2_op_Implicit_m6D9CABB2C791A192867D7A4559D132BE86DD3EB7_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_v, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_v;
		float L_1 = L_0.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = ___0_v;
		float L_3 = L_2.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_4), L_1, L_3, (0.0f), NULL);
		V_0 = L_4;
		goto IL_001a;
	}

IL_001a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Door_get_IsOpen_m3E6B5405808EA7EDFE85AFBD191419C7F1E98ABD_inline (Door_t83EFA7B7B191E0674D7E862310163478739F3412* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____isOpen;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2_Normalize_m56DABCAB5967DF37A6B96710477D3660D800C652_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	bool V_1 = false;
	{
		float L_0;
		L_0 = Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE_inline(__this, NULL);
		V_0 = L_0;
		float L_1 = V_0;
		V_1 = (bool)((((float)L_1) > ((float)(9.99999975E-06f)))? 1 : 0);
		bool L_2 = V_1;
		if (!L_2)
		{
			goto IL_0028;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = (*(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)__this);
		float L_4 = V_0;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5;
		L_5 = Vector2_op_Division_m57A2DCD71E0CE7420851D705D1951F9238902AAB_inline(L_3, L_4, NULL);
		*(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)__this = L_5;
		goto IL_0033;
	}

IL_0028:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6;
		L_6 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		*(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)__this = L_6;
	}

IL_0033:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_6), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), NULL);
		V_0 = L_6;
		goto IL_0019;
	}

IL_0019:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) 
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_euler;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_0, (0.0174532924f), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_2;
		L_2 = Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E(L_1, NULL);
		V_0 = L_2;
		goto IL_0014;
	}

IL_0014:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_3 = V_0;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_op_Multiply_mCB375FCCC12A2EC8F9EB824A1BFB4453B58C2012_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_lhs, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___1_rhs, const RuntimeMethod* method) 
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = ___0_lhs;
		float L_1 = L_0.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_4 = ___0_lhs;
		float L_5 = L_4.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6 = ___1_rhs;
		float L_7 = L_6.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_8 = ___0_lhs;
		float L_9 = L_8.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_12 = ___0_lhs;
		float L_13 = L_12.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_14 = ___1_rhs;
		float L_15 = L_14.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_16 = ___0_lhs;
		float L_17 = L_16.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_18 = ___1_rhs;
		float L_19 = L_18.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_20 = ___0_lhs;
		float L_21 = L_20.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_22 = ___1_rhs;
		float L_23 = L_22.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_24 = ___0_lhs;
		float L_25 = L_24.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_26 = ___1_rhs;
		float L_27 = L_26.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_28 = ___0_lhs;
		float L_29 = L_28.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_30 = ___1_rhs;
		float L_31 = L_30.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_32 = ___0_lhs;
		float L_33 = L_32.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_34 = ___1_rhs;
		float L_35 = L_34.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_36 = ___0_lhs;
		float L_37 = L_36.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_38 = ___1_rhs;
		float L_39 = L_38.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_40 = ___0_lhs;
		float L_41 = L_40.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_42 = ___1_rhs;
		float L_43 = L_42.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_44 = ___0_lhs;
		float L_45 = L_44.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_46 = ___1_rhs;
		float L_47 = L_46.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_48 = ___0_lhs;
		float L_49 = L_48.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_50 = ___1_rhs;
		float L_51 = L_50.___w;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_52 = ___0_lhs;
		float L_53 = L_52.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_54 = ___1_rhs;
		float L_55 = L_54.___x;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_56 = ___0_lhs;
		float L_57 = L_56.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_58 = ___1_rhs;
		float L_59 = L_58.___y;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_60 = ___0_lhs;
		float L_61 = L_60.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_62 = ___1_rhs;
		float L_63 = L_62.___z;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_64;
		memset((&L_64), 0, sizeof(L_64));
		Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_inline((&L_64), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11)))), ((float)il2cpp_codegen_multiply(L_13, L_15)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_17, L_19)), ((float)il2cpp_codegen_multiply(L_21, L_23)))), ((float)il2cpp_codegen_multiply(L_25, L_27)))), ((float)il2cpp_codegen_multiply(L_29, L_31)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_33, L_35)), ((float)il2cpp_codegen_multiply(L_37, L_39)))), ((float)il2cpp_codegen_multiply(L_41, L_43)))), ((float)il2cpp_codegen_multiply(L_45, L_47)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(L_49, L_51)), ((float)il2cpp_codegen_multiply(L_53, L_55)))), ((float)il2cpp_codegen_multiply(L_57, L_59)))), ((float)il2cpp_codegen_multiply(L_61, L_63)))), NULL);
		V_0 = L_64;
		goto IL_00e5;
	}

IL_00e5:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_65 = V_0;
		return L_65;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline(L_0, NULL);
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___upVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (1.0f), (0.0f), (0.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (1.0f), (1.0f), (1.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Angle_mB16906B482814C140FE5BA9D041D2DC11E42A68D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_from, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_to, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	bool V_2 = false;
	float V_3 = 0.0f;
	{
		float L_0;
		L_0 = Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline((&___0_from), NULL);
		float L_1;
		L_1 = Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline((&___1_to), NULL);
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_2;
		L_2 = sqrt(((double)((float)il2cpp_codegen_multiply(L_0, L_1))));
		V_0 = ((float)L_2);
		float L_3 = V_0;
		V_2 = (bool)((((float)L_3) < ((float)(1.0E-15f)))? 1 : 0);
		bool L_4 = V_2;
		if (!L_4)
		{
			goto IL_002c;
		}
	}
	{
		V_3 = (0.0f);
		goto IL_0056;
	}

IL_002c:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = ___0_from;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_to;
		float L_7;
		L_7 = Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline(L_5, L_6, NULL);
		float L_8 = V_0;
		float L_9;
		L_9 = Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline(((float)(L_7/L_8)), (-1.0f), (1.0f), NULL);
		V_1 = L_9;
		float L_10 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_11;
		L_11 = acos(((double)L_10));
		V_3 = ((float)il2cpp_codegen_multiply(((float)L_11), (57.2957802f)));
		goto IL_0056;
	}

IL_0056:
	{
		float L_12 = V_3;
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline (float ___0_d, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_a, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___1_a;
		float L_1 = L_0.___x;
		float L_2 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___1_a;
		float L_4 = L_3.___y;
		float L_5 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_a;
		float L_7 = L_6.___z;
		float L_8 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___oneVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = (*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)__this);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Quaternion_Internal_ToEulerRad_m5BD0EEC543120C320DC77FCCDFD2CE2E6BD3F1A8(L_0, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_1, (57.2957802f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Quaternion_Internal_MakePositive_m73E2D01920CB0DFE661A55022C129E8617F0C9A8(L_2, NULL);
		V_0 = L_3;
		goto IL_001e;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Sign_m42EE1F0BC041AF14F89DED7F762BE996E2C50D8A_inline (float ___0_f, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float G_B3_0 = 0.0f;
	{
		float L_0 = ___0_f;
		if ((((float)L_0) >= ((float)(0.0f))))
		{
			goto IL_0010;
		}
	}
	{
		G_B3_0 = (-1.0f);
		goto IL_0015;
	}

IL_0010:
	{
		G_B3_0 = (1.0f);
	}

IL_0015:
	{
		V_0 = G_B3_0;
		goto IL_0018;
	}

IL_0018:
	{
		float L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m9262AB29E3E9CE94EF71051F38A28E82AEC73F90_inline (float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float L_0 = ___0_x;
		float L_1 = ___1_y;
		float L_2 = ___2_z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_3), L_0, L_1, L_2, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_3, (0.0174532924f), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_5;
		L_5 = Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E(L_4, NULL);
		V_0 = L_5;
		goto IL_001b;
	}

IL_001b:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5_inline (ProCamera2DCinematics_t9C51111549D25BBF7536A09BE0B2E4BBCB3D9EA3* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____isPlaying;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector2_get_magnitude_m5C59B4056420AEFDB291AD0914A3F675330A75CE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___y;
		float L_3 = __this->___y;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_4;
		L_4 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3))))));
		V_0 = ((float)L_4);
		goto IL_0026;
	}

IL_0026:
	{
		float L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Division_m57A2DCD71E0CE7420851D705D1951F9238902AAB_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_6), ((float)(L_1/L_2)), ((float)(L_4/L_5)), NULL);
		V_0 = L_6;
		goto IL_0019;
	}

IL_0019:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ((Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields*)il2cpp_codegen_static_fields_for(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Quaternion__ctor_m868FD60AA65DD5A8AC0C5DEB0608381A8D85FCD8_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		float L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	bool V_1 = false;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		float L_1;
		L_1 = Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline(L_0, NULL);
		V_0 = L_1;
		float L_2 = V_0;
		V_1 = (bool)((((float)L_2) > ((float)(9.99999975E-06f)))? 1 : 0);
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_001e;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_value;
		float L_5 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline(L_4, L_5, NULL);
		V_2 = L_6;
		goto IL_0026;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		V_2 = L_7;
		goto IL_0026;
	}

IL_0026:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = V_2;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_r;
		__this->___r = L_0;
		float L_1 = ___1_g;
		__this->___g = L_1;
		float L_2 = ___2_b;
		__this->___b = L_2;
		float L_3 = ___3_a;
		__this->___a = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___y;
		float L_3 = __this->___y;
		float L_4 = __this->___z;
		float L_5 = __this->___z;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3)))), ((float)il2cpp_codegen_multiply(L_4, L_5))));
		goto IL_002d;
	}

IL_002d:
	{
		float L_6 = V_0;
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))));
		goto IL_002d;
	}

IL_002d:
	{
		float L_12 = V_0;
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp_mEB9AEA827D27D20FCC787F7375156AF46BB12BBF_inline (float ___0_value, float ___1_min, float ___2_max, const RuntimeMethod* method) 
{
	bool V_0 = false;
	bool V_1 = false;
	float V_2 = 0.0f;
	{
		float L_0 = ___0_value;
		float L_1 = ___1_min;
		V_0 = (bool)((((float)L_0) < ((float)L_1))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_000e;
		}
	}
	{
		float L_3 = ___1_min;
		___0_value = L_3;
		goto IL_0019;
	}

IL_000e:
	{
		float L_4 = ___0_value;
		float L_5 = ___2_max;
		V_1 = (bool)((((float)L_4) > ((float)L_5))? 1 : 0);
		bool L_6 = V_1;
		if (!L_6)
		{
			goto IL_0019;
		}
	}
	{
		float L_7 = ___2_max;
		___0_value = L_7;
	}

IL_0019:
	{
		float L_8 = ___0_value;
		V_2 = L_8;
		goto IL_001d;
	}

IL_001d:
	{
		float L_9 = V_2;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_vector;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_vector;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_vector;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_vector;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___0_vector;
		float L_11 = L_10.___z;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_12;
		L_12 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))))));
		V_0 = ((float)L_12);
		goto IL_0034;
	}

IL_0034:
	{
		float L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)(L_1/L_2)), ((float)(L_4/L_5)), ((float)(L_7/L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
