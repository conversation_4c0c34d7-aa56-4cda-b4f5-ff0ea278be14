﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Event_get_rawType_mD7CD874F3C8DFD4DFB6237E79A7C3A484B33CE56 (void);
extern void Event_get_mousePosition_mD6D2DF45C75E6FADD415D27D0E93563DED37D9B9 (void);
extern void Event_set_mousePosition_m221CDC5C9556DE91E82242A693D9E14FAC371F38 (void);
extern void Event_get_delta_m1BBF28E2FC379EDD3907DC987E6BD7E6521D69A0 (void);
extern void Event_set_delta_mA4F7805B9B53B36C7DAA31735CC9097D363B9F9A (void);
extern void Event_get_pointerType_mFFB3FB3E83412151A66FEC136FA00EBDB563B94B (void);
extern void Event_get_button_m57F81B5CCB26866E776D0EBD1250C708A3565C08 (void);
extern void Event_get_modifiers_mD55E7CF06EB720434F0F174EA569B2A29792D39B (void);
extern void Event_set_modifiers_m879319643B5CD23F3223AB8E835C8ABCD3DA72FB (void);
extern void Event_get_pressure_m3E43BF333499DFDCFF2A36258BBC290DDD40D963 (void);
extern void Event_get_twist_m557A5139AD77A15D25598A3F83676E558D1202BF (void);
extern void Event_get_tilt_m5F37D44342F42D691336B23EB075171CFEE7C7A3 (void);
extern void Event_get_penStatus_m41CC65A15A4209D8168AE8BC64C691DE22F83611 (void);
extern void Event_get_clickCount_mEF418EB4A36318F07E5F3463E4E5E8A4C454DE7D (void);
extern void Event_get_character_m8F7A92E90EF65B9379C01432B42D6BF818C32A61 (void);
extern void Event_set_character_mA159F1742FD9EA968F32556C5FE1A2401069BAF5 (void);
extern void Event_get_keyCode_mADBB236A741F96D86E4A536E15FFECFD4C367B64 (void);
extern void Event_set_keyCode_m698D040F2EE0BE55B1B06A3FD865CC0A5D7B1168 (void);
extern void Event_get_displayIndex_m7DBF1B18DD9B5E5B4EEA979FCA87351E3E5B16C3 (void);
extern void Event_set_displayIndex_m8208F1B0471C0B45C0BB248F9A0178EB40FBE100 (void);
extern void Event_get_type_m8A825D6DA432B967DAA3E22E5C8571620A75F8A8 (void);
extern void Event_set_type_m16D35A8AF665F4A73A447F9EE7CA36963F34FEC2 (void);
extern void Event_get_commandName_m14F2015FA5A9050C3C42AF1BD9D0E85D4FF78C24 (void);
extern void Event_set_commandName_m8DA7262E1CD1005911EAB9777DE9FEC2D97504FA (void);
extern void Event_Internal_Use_m303C630AFC4EAE76036545C09C79729E90D81CB9 (void);
extern void Event_Internal_Create_m38519A1960401042CAB57086F9E038116B8D3EAF (void);
extern void Event_Internal_Destroy_m25BA236C0C66CB87A89B81336D7BFB55917127BB (void);
extern void Event_GetTypeForControl_mB5AFF2956E61972F0A1246416FCFE1C46F7E64D1 (void);
extern void Event_CopyFromPtr_mC78295EF5861558EC93D3F8691E2A8B50DE84E29 (void);
extern void Event_PopEvent_mC780BAA7CE4F0E75C8B5C7DC5EB430C278B0D0AE (void);
extern void Event_Internal_SetNativeEvent_mF0C015181EABFE56E2C90CD5C6DCA410C2C42746 (void);
extern void Event_Internal_MakeMasterEventCurrent_m67675F107F56ADDBCF72ECB4C3BE4DCE831C8214 (void);
extern void Event_GetDoubleClickTime_mF3D10CD927983547C6BF3479083B4155DE693826 (void);
extern void Event__ctor_m14342F32F62A39A8B8032286E2DCC07FEF72BFF4 (void);
extern void Event__ctor_mA5E77C0596952812A96703685523819CF50D71A0 (void);
extern void Event_Finalize_m0882CB2E5E0C20C5C9669518C4DB5D95F840DAB7 (void);
extern void Event_CopyFrom_m2F9B9704FBE156C5D58FF630F7968568C19821F5 (void);
extern void Event_get_shift_mB8409DA839B09DC6137848E131A6DBE70BB9E70A (void);
extern void Event_get_control_m1E363A7ABA4F2E8CF41C661A48D53D85D635D320 (void);
extern void Event_get_alt_m57F7F5C1F5FFCE43EFA6889F83CFA42DCA18A74B (void);
extern void Event_get_command_m202DE2CB0BE0AAB5CDFEC9DA1BBD3B51E8497547 (void);
extern void Event_get_current_mBD7135E10C392EAD61AC0A0D2489EF758C8A3FAD (void);
extern void Event_set_current_mDB5FE546AFA00DDF6CC23C106CE076EBEF36BCB3 (void);
extern void Event_get_isKey_mDA8FE1CC5E305BAF181E86A727173C9BE9A1B754 (void);
extern void Event_get_isMouse_mBD11F4FE2996DFAD2648C8A9648E301EDDA51D7A (void);
extern void Event_get_isDirectManipulationDevice_m9A72FB2DF7803E189857D24A65FB568B17533ED0 (void);
extern void Event_KeyboardEvent_m957733139998C86C7ECA28BA50863EB88B71418E (void);
extern void Event_GetHashCode_m9E93319C0E2A92678BC6B3B9A7B1758DBA605E6E (void);
extern void Event_Equals_mBA8BEAB37AE94F9B42F62D946DD61223E0F1258A (void);
extern void Event_ToString_mB30B330C86407E776E932EC18CF177A4066BA56B (void);
extern void Event_Use_mD77A166D8CFEC4997484C58BC55FEB2D288D3453 (void);
extern void Event_get_mousePosition_Injected_m003389887CF74AEA0E5FC70326E0BF873CDEDCE6 (void);
extern void Event_set_mousePosition_Injected_mC406AF97621061F7189B9AA9E4FEA7CD16C5C34B (void);
extern void Event_get_delta_Injected_mF0D15F34DC749A9AACD091795AE5DBC2609AE3AC (void);
extern void Event_set_delta_Injected_m9C70CF005D5B37C1B421C141A42BB53AA70E79B3 (void);
extern void Event_get_tilt_Injected_m0D3D0EAB424976D4B0FCFFDE4E0F6CF8184F3B09 (void);
extern void EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA (void);
extern void EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F (void);
extern void EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F (void);
extern void EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313 (void);
extern void EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE (void);
extern void EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6 (void);
extern void EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B (void);
extern void GUI_get_color_m15488B4AD785D10DEB5C66398D0FA9A0C0EA7ABB (void);
extern void GUI_set_color_mA44927D3F2ACB1E228815F10042A6F62B7645648 (void);
extern void GUI_get_backgroundColor_mCAA42085DAB8A1B71E2D0C6C8D86F93CD311FA33 (void);
extern void GUI_set_backgroundColor_m4ED80300A7DE3EAF923E3344E22D6682FA75B264 (void);
extern void GUI_get_contentColor_m32B15C8D6BEEFEBCE667ECD3CF664C83224F103F (void);
extern void GUI_set_contentColor_m3CDC4D626AC8B6D487AD19765D79C593B98AEF26 (void);
extern void GUI_get_changed_m3473B2964DCE8C2ADE081517093168C171BBE448 (void);
extern void GUI_set_changed_mBD91A44AFA77D2BF883B3150AF4AE6AC3ED121DC (void);
extern void GUI_get_enabled_m336E115A84DBD8D18A925D0755B51746B98B516D (void);
extern void GUI_set_enabled_mF2F99A6870ACAFAEFB5E8FF1B69C684951D390C9 (void);
extern void GUI_set_depth_m37E8C151AD6D1B28DDFABB64A0974398AA8EC44E (void);
extern void GUI_get_usePageScrollbars_m27B43460C2B19308AAB533181F7B4EE84E4DA797 (void);
extern void GUI_get_blendMaterial_m6139BEE5985EFE8E6C6CFB0A261E413BA12DA11E (void);
extern void GUI_get_blitMaterial_m5254BE88B8B1D1C182DCCED08D61F0C4663C0F2F (void);
extern void GUI_get_roundedRectMaterial_m0F58148D76BF7F9DA3D1AF004B95A7EB69FC19D6 (void);
extern void GUI_get_roundedRectWithColorPerBorderMaterial_m6795598BDAD6D7A1758386BE10D9718CEC35115E (void);
extern void GUI_GrabMouseControl_mA4B15F8FC1584E422AAA4FBAA2C8A25FCB70B62A (void);
extern void GUI_HasMouseControl_m336734E97742086851F3C78CC9DAB55508AA44FF (void);
extern void GUI_ReleaseMouseControl_m956B2CF27B6D82677D2960D310D92F043FCEC82B (void);
extern void GUI_InternalRepaintEditorWindow_m067D1FC98B8C7B30E0E6B28C38CA2EE78F96851E (void);
extern void GUI__cctor_m97D837BF457542B0F7308E8999670A46E465A9E3 (void);
extern void GUI_get_scrollTroughSide_mD6AEA9AFA867B71D484561D5BEBF3520321E5E3E (void);
extern void GUI_set_scrollTroughSide_m6E4ED984EDA808C922C062CA6FB6491D2CD28CCA (void);
extern void GUI_get_nextScrollStepTime_m9ECB2B2710D4233C7124D23F0DD995006913D70E (void);
extern void GUI_set_nextScrollStepTime_mA35BA69E3FDBC961E42F6C9D02BB4E8776926A09 (void);
extern void GUI_set_skin_mD51BAED314B39004AE3FDE74F9895CA19F3E40E5 (void);
extern void GUI_get_skin_m97EC9EB4628B311C0DB7DF9FB19FAD82D6790A1B (void);
extern void GUI_DoSetSkin_mF4C06A8BE59628B6514F7FBF9422214A48BE03B9 (void);
extern void GUI_get_matrix_m3CA02DED0598EE32BD9E66CA533A78EFB0A246FC (void);
extern void GUI_set_matrix_m7759FEC96FBCB97E02B1BA44D2EC1B3FEEFA257F (void);
extern void GUI_Label_m4A951E57C7DCCF95A0306240144CA2713F546526 (void);
extern void GUI_Label_m0D7BA53414421D71010DFF628EAA6CCCB3DE737E (void);
extern void GUI_Label_mFC6559DAC18FE889F1B94729AED3550374D18089 (void);
extern void GUI_DrawTexture_mEA112F138EB225F3722CFF9338DB4D14AAC8C7E6 (void);
extern void GUI_DrawTexture_mB96DA06923A0D6550CDBA2355A0BC11F27EEFD4F (void);
extern void GUI_DrawTexture_m05F173ECE6F2D78FFBDEB0052EB58AC2B5A7420E (void);
extern void GUI_DrawTexture_m7D84EE8CAF70725A19D590937A31B4592851A8AC (void);
extern void GUI_DrawTexture_m99A2F5B82FFEAF2A4E9B86DF603250E10C437578 (void);
extern void GUI_DrawTexture_m4B5BA823336A0967617C3181FDA1CDE98D309787 (void);
extern void GUI_DrawTexture_m71F10AF46E994867905A8D57D2AC61A6576AA8E8 (void);
extern void GUI_DrawTexture_m61984C14D59E7AA6BD4A1C7F0390B8E3D6913A1B (void);
extern void GUI_DrawTexture_m5062DEAA07FC3FF9E4F6FDB27D6C7715E1D3AEE3 (void);
extern void GUI_CalculateScaledTextureRects_mE981CDC4935FAFD23FE22405CF11AC79DEFBF55A (void);
extern void GUI_Box_mB47BC44807774B77DB8B2BB548D339036544ACC4 (void);
extern void GUI_Box_mA7CD625644B152E88F757E4B51F12A827BE49E57 (void);
extern void GUI_Box_m4A53BAE78DC7C6724F50E54D9BEB7135BAA0DA0C (void);
extern void GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B (void);
extern void GUI_Button_m62135816B7F4BB45759E10E953926E4E0F24D370 (void);
extern void GUI_Button_m4312FCE2CF33BCC9E4797A0CB27C0B158AF203A7 (void);
extern void GUI_Button_mC05C634998E83DB614858EC020F6A109AA782A93 (void);
extern void GUI_Button_mF539BB7C1C7D6C46E457F9A830A637D3D2EFDAF6 (void);
extern void GUI_PasswordFieldGetStrToShow_mDAE02834B02CF040DCD75BDBEF4EAAFDD4291CF9 (void);
extern void GUI_DoTextField_m0F51DFB1AA3D2DA7C8AA610CD944C2A6F8A4EF60 (void);
extern void GUI_DoTextField_mF1F1ACEEB9D68CD13475D22EFC59592AC98E342C (void);
extern void GUI_DoTextField_m65E9044C469743DABEFF0D45BF55D5E600F6B6B5 (void);
extern void GUI_HandleTextFieldEventForTouchscreen_mDA421BF6B3AA52547D0EE7F7CB9DF615ED49886C (void);
extern void GUI_HandleTextFieldEventForDesktop_mFCEC83B6B86B5A510181961406E4578F1B3CA268 (void);
extern void GUI_HandleTextFieldEventForDesktopWithForcedKeyboard_mC5D580E669A02A678D31F4A38DEC1656303B8BBA (void);
extern void GUI_DoControl_m2E99A053EADA967772D440EDDC745562BDC848D9 (void);
extern void GUI_DoLabel_mE43FD8B17DE5AF3B9E12E15B548CD6846F4AF27F (void);
extern void GUI_DoButton_m6B5D49C56FD43B570B43D9500AC5AFDE0533E99D (void);
extern void GUI_HorizontalSlider_mEED3CE859B1AF830E1851B3625AE127B4E6C5408 (void);
extern void GUI_Slider_mBFB296D4CDB39B49C191A93E00A0C5AD0255CCB5 (void);
extern void GUI_BeginGroup_mC984853EB22E39DD58DA1FDC3A6A8BB034B811C7 (void);
extern void GUI_BeginGroup_mAE18B263BF701C0C2DC412BB3F526BD147142241 (void);
extern void GUI_EndGroup_mE8C7A3FB87B0EAA3556AB16466D0D640BBEE1675 (void);
extern void GUI_get_scrollViewStates_m940A384A713B8A7DC67016D1588965A42E561773 (void);
extern void GUI_CallWindowDelegate_m3FC075A6C33D007CBDC6983CDD6515C246E35B3F (void);
extern void GUI_get_color_Injected_m7B9A31188627647FDD914FB8A83C32627769D1CA (void);
extern void GUI_set_color_Injected_mF82410FC38D4C12CEC8ADCC9CCCC00F12035CA12 (void);
extern void GUI_get_backgroundColor_Injected_m81488D0D17EB867EEA60685182EAD8E0BC7CFB1F (void);
extern void GUI_set_backgroundColor_Injected_m16FDF89F7678824BA547AEF70D4EC84615C7D6B8 (void);
extern void GUI_get_contentColor_Injected_mA592670CB3A23833ED6F6FA43D021CA049CB6FAC (void);
extern void GUI_set_contentColor_Injected_mE1EFDCAC30FF6CE60437BF1B8B04488C9A75E2C9 (void);
extern void WindowFunction__ctor_m31D7B6C221D9A078AE5C8BA7C3BC0FA406EA7B71 (void);
extern void WindowFunction_Invoke_m27ADD2F0F97D0149CE0B6F6452B3C23229D2CC85 (void);
extern void GUIClip_get_visibleRect_m93F10FF2376C3BBBF3562A67DD7E207240D2F611 (void);
extern void GUIClip_Internal_Push_m76819FE03A5080169157BA25B9182ADFDE3905F4 (void);
extern void GUIClip_Internal_Pop_m99B82F9D059E587FD37DEEB41385076E16162E62 (void);
extern void GUIClip_Internal_GetCount_m83C187F97642C73B9241C9A026CDA89A7A9EB8D1 (void);
extern void GUIClip_UnclipToWindow_Vector2_m40F9564F56FD494A8DC03C4A815C41BA405B5884 (void);
extern void GUIClip_GetMatrix_mABFDC4C3D2B71C84191EAA109A4373A1D75BD3E1 (void);
extern void GUIClip_SetMatrix_m2C4B22CA0D33E580CBD455CC8E5422C8FF229733 (void);
extern void GUIClip_Internal_PushParentClip_mDA817B810C6724A0F236C876C08CFB0EC64E78A8 (void);
extern void GUIClip_Internal_PushParentClip_mEAE43F73F48A4CD59FD9432B4F1E50124A0F3522 (void);
extern void GUIClip_Internal_PopParentClip_m7B43C8DD6186703019A5B7ADDC1FE48FB67BDEFA (void);
extern void GUIClip_Push_m78FFAE57A3F299C27A410834C1BE23539E284A60 (void);
extern void GUIClip_UnclipToWindow_mBAA52A9D4A4EA1EC7720E7064CAC9E6B75C524D8 (void);
extern void GUIClip_get_visibleRect_Injected_mBF3F116B530BCD6D5B3A5D110245691ADD4AA8BC (void);
extern void GUIClip_Internal_Push_Injected_m6BED9A38DF28718CE1059CC94E60269777410BBC (void);
extern void GUIClip_UnclipToWindow_Vector2_Injected_mB1FFA643AD2C4EACE9FABD41B32B094580D6BEEF (void);
extern void GUIClip_GetMatrix_Injected_mFAEC409FC44C49C7681DF684C954DF86AE076B76 (void);
extern void GUIClip_SetMatrix_Injected_m259A180FE5871D9D16330959A560EAC86E0224D0 (void);
extern void GUIClip_Internal_PushParentClip_Injected_m71572BEBE9BFFAA4D306958579D3B0B48411B87A (void);
extern void ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4 (void);
extern void ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D (void);
extern void GUIContent_get_text_mC6D7981351923AD7F802AC659314BA56DF7F3ED6 (void);
extern void GUIContent_set_text_m18A3EB5B4BD316561B3F4AB6BB3CC151684CE14F (void);
extern void GUIContent_set_image_mB91F27FCD27EDBA24794D52B7F3DF1CF4E878164 (void);
extern void GUIContent_get_tooltip_mC2D07D7B2884A5F5A56F84A7FE6BF39905AB15BD (void);
extern void GUIContent_set_tooltip_m72C6B6EA0C9FCA1544A7FCF6C78A93E55D8CB415 (void);
extern void GUIContent__ctor_m89AC53A7E9BF9EB9E70297353DEAA6FEC2C800AC (void);
extern void GUIContent__ctor_mD2BDF82C1E1F75DEEF36F2C8EDB60FFB49EE4DBC (void);
extern void GUIContent__ctor_m3FDFF98EA6ACDC116BCCA705EE8F8DEC09A4A0A7 (void);
extern void GUIContent__ctor_m798E35DEED8E153FF39445EBEB634F896F19DF19 (void);
extern void GUIContent_Temp_m4AE3B839AF38DD23ECC1D585C391E1CA43B8EA73 (void);
extern void GUIContent_Temp_m9057415F4E0A4DFCD6B9FB2DBC10B4A515DF8444 (void);
extern void GUIContent_ClearStaticCache_m36A399D55991F1B5B1C4A20DCDFF415B8636E934 (void);
extern void GUIContent_ToString_m9F42CA1D8DEFB446686D0010FF57B4F9B140BB9A (void);
extern void GUIContent__cctor_m1605F6A12B7BD089F1592F490DBF324ECEC3FE8C (void);
extern void GUILayout_Label_m1709C16A433383CCFC1FEA0E585E14CBD78CD94B (void);
extern void GUILayout_DoLabel_mB3819CFC26697E2721B76B03E8A6382C3BD0B572 (void);
extern void GUILayout_Button_m8CF27DB531C6A54FF0F7BD8CDE4FB5030B159E9E (void);
extern void GUILayout_DoButton_m5C440656DA589BD96635D6BB2D25D441EC1FA13B (void);
extern void GUILayout_TextField_mBDF7048805EC1B18C633CD17A7E071D26542288E (void);
extern void GUILayout_DoTextField_mCF612516F5261A38CAB5597109B4F0D24A801711 (void);
extern void GUILayout_Space_m9254FBF173F9260DDB6C83C0066447FC9D9CA597 (void);
extern void GUILayout_BeginHorizontal_m1BBD7EE29640BF48BED72DE582702809E6B22219 (void);
extern void GUILayout_BeginHorizontal_m345DE4B807BD1719D6D4D99697E583F2706830C6 (void);
extern void GUILayout_EndHorizontal_m694C622FEE40FFF0DD77EFFD026F899C193507C0 (void);
extern void GUILayout_BeginVertical_m1B2B474EF5A3CD257EC0474D17F78A149DC14D88 (void);
extern void GUILayout_BeginVertical_m48E21301AC4363B5FCDFFC654269E81C7C6E1204 (void);
extern void GUILayout_BeginVertical_m011D5556BD812DFF1620BFC648566CF89A09FD68 (void);
extern void GUILayout_EndVertical_m2D981AC3EA3F7273CBFEE6C30C14DC90044AE87C (void);
extern void GUILayout_BeginArea_m4D894562C97A0F6793450A0DF379B63F60121F64 (void);
extern void GUILayout_BeginArea_m6242362D4059133D9F749763CFAB63A2B61D8B77 (void);
extern void GUILayout_EndArea_m3A9C6B4D373E8A871A71E0D8D2D9249D9F62F079 (void);
extern void GUILayout_Width_m3FADF145F37481F9FEFF0E89E8A466CF5532DCE3 (void);
extern void GUILayout_MaxWidth_mC9E5A7877E53216E4DD1029BC6AF6D36255F1CE3 (void);
extern void GUILayout_Height_m5E1526C541663A21437ED06E233FDDA08A856B91 (void);
extern void GUILayoutOption__ctor_m4EF826EA43073869166C8D94A1D9EB7898ACC3AA (void);
extern void GUILayoutUtility_Internal_GetWindowRect_m4F0CEA512EAD2BF0BBA0218A10B9C820C24D44CE (void);
extern void GUILayoutUtility_Internal_MoveWindow_mAD1ECDE72F3573D2F71B43C5FB8F90C10919C6CF (void);
extern void GUILayoutUtility_get_unbalancedgroupscount_mF902A4C45A4817D8D05761E14033AC66BED4D2B5 (void);
extern void GUILayoutUtility_set_unbalancedgroupscount_mD2509D685C0DDB9ED9C800F34C0EAC0843C8C71B (void);
extern void GUILayoutUtility_GetLayoutCache_m6086C9523E16ECC3FF4613F5A2441351CF4B2878 (void);
extern void GUILayoutUtility_SelectIDList_m601F4AA990B7FD59A779F5375EC55ADDB86927A9 (void);
extern void GUILayoutUtility_RemoveSelectedIdList_m701496086D15B8BF1C936EB431AFBC6627A787FD (void);
extern void GUILayoutUtility_Begin_m701551F1F833A31A154BFFC9F6F3143A12A33061 (void);
extern void GUILayoutUtility_BeginContainer_m34C50FF74C76B91E32E1A3575ABC0AA0AE0F3DDB (void);
extern void GUILayoutUtility_BeginWindow_m99FBC28B305B9C0589BC73138073BE9420C977F5 (void);
extern void GUILayoutUtility_Layout_mBC6C938DC931B8CABC1FA6C33AA60ECFAC3D9B30 (void);
extern void GUILayoutUtility_LayoutFromEditorWindow_m0D41A3D7897D91D4420C722C47502FCBA0352804 (void);
extern void GUILayoutUtility_LayoutFromContainer_m81EC681FE0A88C36CCA8D4382043279F709EE59E (void);
extern void GUILayoutUtility_LayoutFreeGroup_m81D18A1401F6FF7EB4A3C1CC26D9BE80998BBF5C (void);
extern void GUILayoutUtility_LayoutSingleGroup_m95E3F31426ACA641C57016A1D1A058366A56AFE8 (void);
extern void GUILayoutUtility_CreateGUILayoutGroupInstanceOfType_m2F53981EB9DD3E591F4CD4AF4F6B6E9237E58F0A (void);
extern void GUILayoutUtility_BeginLayoutGroup_mE922EB7B3D05474F6259C238B7A8C2FFFE1DBC43 (void);
extern void GUILayoutUtility_EndLayoutGroup_m62EDDF35442EC5CE39A4DC11740B87DD45454C26 (void);
extern void GUILayoutUtility_BeginLayoutArea_mE8FF1BD8DA08B1F5D35F89B844E5FC05D10D40E1 (void);
extern void GUILayoutUtility_EndLayoutArea_mEB29D017C9B83031072AB9FB1D6BD0AFCE12F57C (void);
extern void GUILayoutUtility_GetRect_mD3E98D37BF22AD8CF97D7B607E7F11125C9A558A (void);
extern void GUILayoutUtility_DoGetRect_m5149CE27EE6C137479B6AE56A416CDC270A4E6EC (void);
extern void GUILayoutUtility_GetRect_mA52720BB9E74AEE8ABD39D20AD3CA1C23A9FE4DA (void);
extern void GUILayoutUtility_DoGetRect_m331109591122F160FEB7AFC177DB18DDB44D8C59 (void);
extern void GUILayoutUtility_get_spaceStyle_mC006DE36340B02EF0764873122DB9FAE793F7765 (void);
extern void GUILayoutUtility__cctor_m2CEDA9A8EB23B7D3A5A97825E6B192235954DC48 (void);
extern void GUILayoutUtility_Internal_GetWindowRect_Injected_m03328FF57858A53621C5907B345C56FA2C5AF0EC (void);
extern void GUILayoutUtility_Internal_MoveWindow_Injected_mDFDA2042DAFBDEBD108AC01F6F19E7D0F395B6A7 (void);
extern void LayoutCache_set_id_m532720FF0F65E8039E37D015910E2F1AE1C9F4FB (void);
extern void LayoutCache__ctor_m73B4DC62A0A7669976C8444DDB54EF8D55BF3E0B (void);
extern void LayoutCache_ResetCursor_m728841782E13F82B1AE96E40AF16D6C8EBE6D59A (void);
extern void GUISettings_Internal_GetCursorFlashSpeed_m5A85ED55BF5C4CF8EA0364EFB7279983EC098B92 (void);
extern void GUISettings_get_doubleClickSelectsWord_mC92A7F42A08C2C3477E95603B2EED4619B9E3290 (void);
extern void GUISettings_get_tripleClickSelectsLine_m9F5A93582F9BA64AA475A2BEB353A5D5AA7BCEED (void);
extern void GUISettings_get_cursorColor_m490EB7B6029BD2C4CD22F64DEF0B6C08618D7830 (void);
extern void GUISettings_get_cursorFlashSpeed_mC999C2599D804B274C8528F2414C4346A9A9D673 (void);
extern void GUISettings_get_selectionColor_mDF24274364732088DE57D112F91EAC70233D6EE5 (void);
extern void GUISettings__ctor_m4AA9AFBD94306E007937909CB7F542DF2E491404 (void);
extern void GUISkin__ctor_mAA94A46B37D9C2F70962435F250BBA202CD1EC7A (void);
extern void GUISkin_OnEnable_m5A7FE1F57C549711FCCC2DB0322F8667129AA0BF (void);
extern void GUISkin_CleanupRoots_mAD2E77BE9440832E8BC8CAA9C7F2D85C3D2F8B17 (void);
extern void GUISkin_get_font_m806CF702C59E43DF55BA441030A60F80E9D8CFD5 (void);
extern void GUISkin_set_font_mF98516DE4363C888D7215006D51BD527F3F9DDA9 (void);
extern void GUISkin_get_box_m21BE7FC56D903B95BAFAE8890425D330EA88D893 (void);
extern void GUISkin_set_box_m82E578044569D3831D103FFA1413D81DABF74711 (void);
extern void GUISkin_get_label_m99E1A8D6D8592F88F581437D24DB1EDE05C63E5E (void);
extern void GUISkin_set_label_m7E9E63BBA37F93D886F7E6AF70772ECD7894462B (void);
extern void GUISkin_get_textField_mC554496BAB959445F0CFA30BDC5736DC1F057D48 (void);
extern void GUISkin_set_textField_m4730F5B544F2A87AF3CA75A01FE845E5D40E06BE (void);
extern void GUISkin_get_textArea_m0ECBC9D126D930490F96E100B27F245E555EB7D1 (void);
extern void GUISkin_set_textArea_m916CC2135EE608D81035D3E96787735534DF4E9D (void);
extern void GUISkin_get_button_m51948EBD478CF9223522AD29B7FBD1BABAABE289 (void);
extern void GUISkin_set_button_m45F7F5CBF3E9286F4CD601AA92C0C3207C0BB373 (void);
extern void GUISkin_get_toggle_mD5F318C602494C478F09C2D48741EC7A9CF5B849 (void);
extern void GUISkin_set_toggle_mFE0DA0EC1F1952464B85894CCCFECFA5E0E0C57E (void);
extern void GUISkin_get_window_m760DAF129E72775DFD18CB71720AD306345E91C2 (void);
extern void GUISkin_set_window_mA74900E5D554578F3F45DD858B79C5A8FA4A6220 (void);
extern void GUISkin_get_horizontalSlider_mAA1753FEEDBA6E28A3A56C3E44A8F5B3D6C8336B (void);
extern void GUISkin_set_horizontalSlider_m8357A90F358C1A040308C8D0DEE363D3ABA71575 (void);
extern void GUISkin_get_horizontalSliderThumb_m9EE5EF8204397C2946D7F384AB7D8A17693837BD (void);
extern void GUISkin_set_horizontalSliderThumb_m1042BED23F10E28042D77D7E738F86C1FEDF460E (void);
extern void GUISkin_get_horizontalSliderThumbExtent_m6408F303B8932D6E74B307070689A96EA082D612 (void);
extern void GUISkin_set_horizontalSliderThumbExtent_m8F4C637DB7E25697AB463B9F2F8D50D8493609C1 (void);
extern void GUISkin_get_sliderMixed_mFD8CBA8BE229E299D63822AE3E632DABCC27FF61 (void);
extern void GUISkin_set_sliderMixed_m8A129FB05FAA0970C01A8C3DB14903E13F8E37B3 (void);
extern void GUISkin_get_verticalSlider_mB7EC86D11019F1892365E9C6F2A846A68879BBD5 (void);
extern void GUISkin_set_verticalSlider_m02D94C0BFF867BD8B1ECE05AB50F7F2475DF0E35 (void);
extern void GUISkin_get_verticalSliderThumb_m3D86347FFC94841C8B6CA94F9F946C76E96EBADB (void);
extern void GUISkin_set_verticalSliderThumb_mFBFA636B05068A0E7D24C8C3B06044AB2ACD4C58 (void);
extern void GUISkin_get_verticalSliderThumbExtent_m299DED8D10A1CE0F22B43BAF47D70DA1EB079AFA (void);
extern void GUISkin_set_verticalSliderThumbExtent_m3ECC754FC08BCFA5C3264A6B83C9EE280C1EFCDD (void);
extern void GUISkin_get_horizontalScrollbar_m945A39FBD098D3800A189FC34B9CE9E8AFF3AEEA (void);
extern void GUISkin_set_horizontalScrollbar_mF08764A78F23728E6FE157F08B9A0127157071FA (void);
extern void GUISkin_get_horizontalScrollbarThumb_m5011EED1650028044BCC7F6DE2829AC0243208BB (void);
extern void GUISkin_set_horizontalScrollbarThumb_mDDADEFFD5BF9B88AC4A37AEA13B6FCCC28A3F696 (void);
extern void GUISkin_get_horizontalScrollbarLeftButton_m4A6E58CF80A66F58CF5792B31D08A2D74BF40567 (void);
extern void GUISkin_set_horizontalScrollbarLeftButton_m3FDB02C1FDE47BCE92068EA21C531F1F6D667DBC (void);
extern void GUISkin_get_horizontalScrollbarRightButton_mADFCABC3339BE56E2BAD5443789D8D4FBDD73DAC (void);
extern void GUISkin_set_horizontalScrollbarRightButton_mE5ED9D2BB554FC29F6A69C81B9361A5E6E004CFD (void);
extern void GUISkin_get_verticalScrollbar_m600012E344D3EB4C687E8A4BE78CE33068374D2A (void);
extern void GUISkin_set_verticalScrollbar_m4F55D5B66DB408A5009FC00ABBB9AFFA0C65FFEC (void);
extern void GUISkin_get_verticalScrollbarThumb_m62663C3DDC40AC91FD4666FBF844DCD83DDA7DE6 (void);
extern void GUISkin_set_verticalScrollbarThumb_mECEC0DC79CCD9AABBF6CBA3CE5141C38699B5EC6 (void);
extern void GUISkin_get_verticalScrollbarUpButton_m0B5575CA6AFB1C74899BF931296EFC39B2C1A902 (void);
extern void GUISkin_set_verticalScrollbarUpButton_mF50F99BC770529789363EC9B1C37E610FF8A708C (void);
extern void GUISkin_get_verticalScrollbarDownButton_mFC75161EDB03597ECF09E189C8A57F0E64213E3D (void);
extern void GUISkin_set_verticalScrollbarDownButton_m37DD0E232BB98BD219494A297DDBE7620104D328 (void);
extern void GUISkin_get_scrollView_m5466CD77A4A7E01320DB0E0F57253D41226BB0B8 (void);
extern void GUISkin_set_scrollView_mF2D35906BC020D81F909E65B420494F254E4DC32 (void);
extern void GUISkin_get_customStyles_mAC8A1CFD5756E6C0D367E06B4BDC365E6F6BC39B (void);
extern void GUISkin_set_customStyles_mD22F50472DDB0A9770B18F0A15D3F73EEEC4A8B2 (void);
extern void GUISkin_get_settings_mCBAE5727D7774FAEE47CCC8B4C47AC321DDD85C2 (void);
extern void GUISkin_get_error_mB953A37C8F3296E529190A34E18506C735848C01 (void);
extern void GUISkin_Apply_mA85017BE8C994F6220112EE8D00D3C37C1FF2104 (void);
extern void GUISkin_BuildStyleCache_m8E99CC278C76A6DA63A24BFD2DE42AE313C0F7E1 (void);
extern void GUISkin_GetStyle_mF024BC5177A2AD477ACF44D87BE6A629C91562CA (void);
extern void GUISkin_FindStyle_mF82C37E2481D2B9E96C26EFE0353F8954F844FFE (void);
extern void GUISkin_MakeCurrent_mDB3BB1FBA5BD2FEDDA3F32F11170F47A6444AEED (void);
extern void GUISkin_GetEnumerator_mEC308E2DA9A94E09C622D13F82EB7ECCECF8AFF0 (void);
extern void SkinChangedDelegate__ctor_m20D33B3868351B98B708468F7A8192C1ACF85CD1 (void);
extern void SkinChangedDelegate_Invoke_mD14214487F9A0E4DD7EB7F97927D03EC8F1A3B4C (void);
extern void GUIStateObjects_GetStateObject_m2BB2858A7B432322CF1ECA3940D85BF809ADA4EC (void);
extern void GUIStateObjects__cctor_mDD043ED73553AD955E20FFD43933D1D124D6D3D1 (void);
extern void GUIStyleState_set_textColor_m5868D12858E6402247953BCCDDA7A543BE6084F1 (void);
extern void GUIStyleState_Init_m0D3428E2BA3343F8AC49253DE3AAC54EF07F4873 (void);
extern void GUIStyleState_Cleanup_mF244B2DAEE9DE90A300E6B7D78F9547BBBE59826 (void);
extern void GUIStyleState__ctor_mD47FE21F7FD8D786F7E8E4E8C3DCA224F9237AD7 (void);
extern void GUIStyleState__ctor_m74536B867B0F57F8A7DC74E78018830A948E4555 (void);
extern void GUIStyleState_GetGUIStyleState_m0B273F7909166249E3D98FA410C2D8A72091C7B1 (void);
extern void GUIStyleState_Finalize_m5CC6FBD8C44AF1091CACD6F7032E73B1114765B2 (void);
extern void GUIStyleState_set_textColor_Injected_m2E95B96544D89BEC498DF24CB036903535EA8184 (void);
extern void GUIStyle_get_rawName_m9C87EB1EA6CC5989EFF3567E85A2D0A3DF256782 (void);
extern void GUIStyle_set_rawName_mF8928B91294B5DA15AF365C760BB1437CF507ED6 (void);
extern void GUIStyle_get_font_mBD123E375D357B37F8E1303F288517FD883C1117 (void);
extern void GUIStyle_get_imagePosition_m339AA340B169230E9795B61BEE4BB1EDAD6D95B4 (void);
extern void GUIStyle_set_alignment_mEDC62A775C9551DBD1FEE4043F115E034EF12937 (void);
extern void GUIStyle_get_wordWrap_mD0046078E78B0F8F1988E055B7EEB261FE8C69AD (void);
extern void GUIStyle_get_contentOffset_m1585F4928435114551C27889DE159EEF55345C70 (void);
extern void GUIStyle_set_contentOffset_m550A9D4B3FA491B320F4B71F14C45A5785B18F24 (void);
extern void GUIStyle_get_fixedWidth_m9CB5B4E096287F75F4E4E3376590C7C085E28DE8 (void);
extern void GUIStyle_get_fixedHeight_m009155CF284509A87E6037D0A392A630FA728F7A (void);
extern void GUIStyle_get_stretchWidth_m528FFD3EB3104D0322F2EADBBE7DBFF3FB34CB37 (void);
extern void GUIStyle_set_stretchWidth_mA1880C36240B34EBAD585544CCE73224DA0B6A78 (void);
extern void GUIStyle_get_stretchHeight_m5ACA8F9CD25746932719C927159A105AADA5061F (void);
extern void GUIStyle_set_stretchHeight_m51C55ED43AA4EDE125E0C423FA0D301E81C09378 (void);
extern void GUIStyle_set_fontSize_m7F6DFD61AC55072C95DC3825B77FAE3F75F1CCFF (void);
extern void GUIStyle_set_fontStyle_m4166D61FBF25225F4A85BBEABCECE3F2DCEE714D (void);
extern void GUIStyle_set_Internal_clipOffset_mEAB08E971FED348348EEBACC2D4439473B930B9E (void);
extern void GUIStyle_Internal_Create_m2C5F872F6FE8C423759017DC72267D6AF637BC75 (void);
extern void GUIStyle_Internal_Destroy_mD93F2F454B69DB5C534AF9F4F6D847F955A39977 (void);
extern void GUIStyle_GetStyleStatePtr_m60D51351B040299578007102C3857E8E8F14FAFB (void);
extern void GUIStyle_GetRectOffsetPtr_mCABE2CEFE5CDB942D464051BF8B0E043BCC59593 (void);
extern void GUIStyle_Internal_GetLineHeight_m3A90D425C25B10618B8A3D95AEF72FCB1C574B07 (void);
extern void GUIStyle_Internal_Draw_mBEFC164F21949135F404FDA678F368FBA8074D50 (void);
extern void GUIStyle_Internal_Draw2_mD1050A7750AAAEEEEFD4EB6C8C8AFB0591B1221D (void);
extern void GUIStyle_Internal_DrawCursor_m016FAD8A76AF5146B0C684E73EDF61F0709FFAC8 (void);
extern void GUIStyle_Internal_DrawWithTextSelection_m446FACAD8160564B23C5080C0BB3103048F83878 (void);
extern void GUIStyle_Internal_GetCursorPixelPosition_m22C4D9AA182990942EA85B0EA834499EFA0CB0C4 (void);
extern void GUIStyle_Internal_GetCursorStringIndex_m88FFC09FCA6FD081C34ADC01F899D435AEFA2CC4 (void);
extern void GUIStyle_Internal_GetSelectedRenderedText_m3F9EF55E4958D2C9DE62AC723DBC99EBB80DD002 (void);
extern void GUIStyle_Internal_CalcSize_m6B1D90CF09404B4969678627BE86D43B41C5AF33 (void);
extern void GUIStyle_Internal_CalcSizeWithConstraints_m555CBD08EA22A9CB84A16BB8BEF95E8D25BF2617 (void);
extern void GUIStyle_Internal_CalcHeight_m12AD4C5012F9E237FAB309CC6C84D3CB9145FF76 (void);
extern void GUIStyle_Internal_CalcMinMaxWidth_mB0D00D2D7454F733458F3729E35FF22CE9FEDC58 (void);
extern void GUIStyle_SetMouseTooltip_mFF3E22C7330AE180E83AB2929049BCD87B13B21E (void);
extern void GUIStyle_IsTooltipActive_mAD93F97B98889CA47BF1305F3D4C87D5EE8DD777 (void);
extern void GUIStyle_Internal_GetCursorFlashOffset_m0CBC61945E3C828B42CE4651D5D04142E0CA7428 (void);
extern void GUIStyle_SetDefaultFont_mD6B98375749805CA5084CA8C5D6A1295359AE0E3 (void);
extern void GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9 (void);
extern void GUIStyle_Finalize_mFF6A6FBA538B711A6ED369DD83A41F25DE6EEE85 (void);
extern void GUIStyle_get_name_mDF9EF43C46A0B9431DAF4EB0CE1D18EA32E16B75 (void);
extern void GUIStyle_set_name_mE618266DC07236117AAE05FE8D2B14A595FCF020 (void);
extern void GUIStyle_get_normal_mDEA2808FBD692E505784BD9E521738B4321BCA8F (void);
extern void GUIStyle_get_margin_mD0AABA2CB3FB0CFC3C414635E6225D3003315D1B (void);
extern void GUIStyle_get_padding_m04E3210A51B2522158941AFA97ADC19C835987C2 (void);
extern void GUIStyle_get_lineHeight_mC814199D1ABA3CE38358BA70347562B0CDFEB96E (void);
extern void GUIStyle_Draw_m7B978F5F5B576810CF8546142D23FD9990E002D8 (void);
extern void GUIStyle_Draw_mA81B01AC68DF7F6948228AFA68A7126E838E49E9 (void);
extern void GUIStyle_Draw_m115BA19F2B86B0390964C5E0172BDF8970374BF9 (void);
extern void GUIStyle_Draw_mACFC9CE57BD530BB6A9592149DD95108A8014406 (void);
extern void GUIStyle_Draw_m3DBF8DC58719720455DFC818590D77752BA31008 (void);
extern void GUIStyle_DrawCursor_m6E56AA2C98FD6C2BE93F723BCAFC853D2C5E2B71 (void);
extern void GUIStyle_DrawWithTextSelection_m1F04940E605F072A86832E733D7BCD05077F0E71 (void);
extern void GUIStyle_DrawWithTextSelection_mC449ECFF67627B189F742FC7320ADFD5F7A9A540 (void);
extern void GUIStyle_DrawWithTextSelection_m7BFF3F02BD2B23164CF98DBD4E5FECB51283215E (void);
extern void GUIStyle_op_Implicit_m18FEE416A9FA577B09D98D80408008F34A5D9FE5 (void);
extern void GUIStyle_get_none_m808A9FE1F78920E4A29ED3484B99588B46D88938 (void);
extern void GUIStyle_GetCursorPixelPosition_m4FFBD3DC05CE503355DF01E57023AC349032CB2F (void);
extern void GUIStyle_GetCursorStringIndex_m9EFA2EC2CF6ACB5B4EAF3E9C4BC356980CBB4515 (void);
extern void GUIStyle_CalcSize_m3015BAC288A5D6D29C0596ECE8117C8F9DFF9A76 (void);
extern void GUIStyle_CalcSizeWithConstraints_m01ED0E843908709C7A316B83E4E10ABCECF1A8B1 (void);
extern void GUIStyle_CalcHeight_m57DA8F6020AE71B561ABCBCE74E0E58FD2ECC5E8 (void);
extern void GUIStyle_get_isHeightDependantOnWidth_mE18B09D8CD496F15F0EAB224020017BFF48065AF (void);
extern void GUIStyle_CalcMinMaxWidth_m6BBF836B9A9B2B4BA11DC448B03E441DEDC2CCA4 (void);
extern void GUIStyle_ToString_m41A8A58B4D9659047D06EF2A5AE5F170AE198ACF (void);
extern void GUIStyle__cctor_m4B955524A4DAEAAF103D78D9316756CEFA16FB62 (void);
extern void GUIStyle_get_contentOffset_Injected_mA08F10C2DF4530E0FB902E36AE327C1452686C9D (void);
extern void GUIStyle_set_contentOffset_Injected_mB71B7952884AEC19A3946ACD4F3601F07A5533F4 (void);
extern void GUIStyle_set_Internal_clipOffset_Injected_m0698799EBCFD9347BBC5D95FC9D15D8B17920709 (void);
extern void GUIStyle_Internal_Draw_Injected_mF4A2332005788106B28CB306FAFF530BE251E09B (void);
extern void GUIStyle_Internal_Draw2_Injected_m83867C172C18ED83724AA6600EDE59C55277A138 (void);
extern void GUIStyle_Internal_DrawCursor_Injected_m45E2755EF5415F0A401CE8A911364CB853BCA96F (void);
extern void GUIStyle_Internal_DrawWithTextSelection_Injected_mA31596E612AD06214B8E04210A969CC23F8E084F (void);
extern void GUIStyle_Internal_GetCursorPixelPosition_Injected_m9B676ED0A70FE6CC55EC8795CD7348406A4FD815 (void);
extern void GUIStyle_Internal_GetCursorStringIndex_Injected_m4C0A3DEF8B90D9A866378C24BB3F7E0169CA12C9 (void);
extern void GUIStyle_Internal_GetSelectedRenderedText_Injected_m1DE280FF00B670FB04D98786E87A7F18D72940E3 (void);
extern void GUIStyle_Internal_CalcSize_Injected_m19617B2C5FF35B1B10B9D31058ABC1EABD31FF48 (void);
extern void GUIStyle_Internal_CalcSizeWithConstraints_Injected_m0BAB7504DC082EAC9C5664166BD8B1DA3DEC0025 (void);
extern void GUIStyle_Internal_CalcMinMaxWidth_Injected_mEBCFBA4C8E76B115712AA308250F70CEACF1B844 (void);
extern void GUIStyle_SetMouseTooltip_Injected_m77EC0702533B68489605E0DE76A6761E1253CC71 (void);
extern void GUITargetAttribute_GetGUITargetAttrValue_mD0E7A4A7147F6B97077284408283EA380FE040B4 (void);
extern void GUIUtility_get_pixelsPerPoint_m13E69FE793E736FA60A61C6756F2FF57BA6C9F31 (void);
extern void GUIUtility_get_guiDepth_m011B188F7C41DAE079019E64BC064208E618F315 (void);
extern void GUIUtility_set_mouseUsed_mBD1FB685EF080F233A16BF558CE4703E68621E1C (void);
extern void GUIUtility_get_textFieldInput_mDB514BD41982E9A309A7E0297270162FA6918EBA (void);
extern void GUIUtility_set_textFieldInput_m3CDCEAC85B562998D5CBAF7ABF6EC1C92E9B1EEB (void);
extern void GUIUtility_get_systemCopyBuffer_m01E2DF71533C31A4C552B9177D7CBA0C6CA3FC2A (void);
extern void GUIUtility_set_systemCopyBuffer_mD14AE32BFEA4773BDC679205D470A228B8F225E8 (void);
extern void GUIUtility_Internal_GetControlID_m9836A3FD9B0629A36F356FD8D4606092B2E2AD21 (void);
extern void GUIUtility_GetControlID_m3AACC1B4BDE62E7C3E5D861A470351FA1BAA752E (void);
extern void GUIUtility_BeginContainerFromOwner_mA895E862C2444F93423836CE4B5F35E2F31B8B28 (void);
extern void GUIUtility_BeginContainer_m4A0F355072CE2DBCB50F706885EAAB70DB8C7115 (void);
extern void GUIUtility_Internal_EndContainer_mCE42BC4D58E684B724B58EC3C901E67BA62F1BF7 (void);
extern void GUIUtility_CheckForTabEvent_m6AC98E67A89330ACB330CBBC135E3DFBFCAC2C49 (void);
extern void GUIUtility_SetKeyboardControlToFirstControlId_m02DF215A0F07822021E17AF4153B4C31468287C0 (void);
extern void GUIUtility_SetKeyboardControlToLastControlId_mB7A3C208ADDF009FB9C3C522998459BCD9B107EB (void);
extern void GUIUtility_HasFocusableControls_mE149711C5695D4DB44940D8073487992F1ACB883 (void);
extern void GUIUtility_OwnsId_m46FE01F2CEF3A94173A1DB64A888E4DB1EBC74D2 (void);
extern void GUIUtility_AlignRectToDevice_mE651D8C8024AD7FF9C1773FA000A2626BC263B8C (void);
extern void GUIUtility_get_compositionString_mE06412C5CE41311C00BFC4028716D5F03EDD85E9 (void);
extern void GUIUtility_set_imeCompositionMode_mE5C0A2391D65DAC056B1752D78B5A832DCB314C7 (void);
extern void GUIUtility_set_compositionCursorPos_mECE1139A5660FFE152382DAB2DDBFADB96BB9644 (void);
extern void GUIUtility_Internal_GetHotControl_m8230315B3FECDB164C84AFC40C180C2C7B319892 (void);
extern void GUIUtility_Internal_GetKeyboardControl_mD0783552D4ACDA842F86F126C7A48ADC79340AB8 (void);
extern void GUIUtility_Internal_SetHotControl_m56F3F333B107EFD83C7F3D703DDA48C5A19BFCB8 (void);
extern void GUIUtility_Internal_SetKeyboardControl_mC8401D9C911D310EAA2284161264D2FC9D141418 (void);
extern void GUIUtility_Internal_GetDefaultSkin_m86F21D22A34DC2243194B8929A499FD98D26A234 (void);
extern void GUIUtility_Internal_ExitGUI_m5B145534F61B8CE2A2915A9297D0F25D771D4459 (void);
extern void GUIUtility_MarkGUIChanged_m43158D22AA065483FD91222B898772AEC06809A1 (void);
extern void GUIUtility_GetControlID_m4A403579ECC04C9F13A317078B55A30B366D77A4 (void);
extern void GUIUtility_GetControlID_m2E0F66C8714A84DD5E9BEF4B9B464DAF1C03A9F7 (void);
extern void GUIUtility_GetStateObject_m803B56DC7DF5396751B7367DB04072A6DCEEE616 (void);
extern void GUIUtility_set_guiIsExiting_m0DCDD09CD48330FD781C03D2EA20F973878A2BC5 (void);
extern void GUIUtility_get_hotControl_m6CD6AD33B46A9AFF2261E2C352DC7BAB4C20B026 (void);
extern void GUIUtility_set_hotControl_mFBC648186C83874DE776A508C420183ADB527E9A (void);
extern void GUIUtility_TakeCapture_mD8AB4A480269628E17877B77A94A6481EFC9763C (void);
extern void GUIUtility_RemoveCapture_m295E1BC4B7E1D471AF7C40E3B587B7D525E3D693 (void);
extern void GUIUtility_get_keyboardControl_mB0FAC848390B7F163CD2EE0A911FADD5CAD70B1E (void);
extern void GUIUtility_set_keyboardControl_m10F53FE5B292C2DC3C9A55CB504CC0DF36139465 (void);
extern void GUIUtility_HasKeyFocus_m6AD234443A7B2AB471E14BE141FC5E8ADD261A0F (void);
extern void GUIUtility_ExitGUI_m9B30B2DFC94CC1C04D1F78358D79E9DAC1231B03 (void);
extern void GUIUtility_GetDefaultSkin_m3275F31A9D5C3D90A1BCF5135F5B3968D6CD2C33 (void);
extern void GUIUtility_ProcessEvent_m88640934E0C2BFA9BAC544DD2A91112FE8227FE2 (void);
extern void GUIUtility_EndContainer_m19D0D5BA46EDAD7AF2D408A34D0141C5E481D963 (void);
extern void GUIUtility_BeginGUI_m05702C560EBBC0B0CA3AD4F1FFBB5BD070DA2E04 (void);
extern void GUIUtility_DestroyGUI_m5F94109C61BC0394F8936C899273093A6008702C (void);
extern void GUIUtility_EndGUI_mB34E82D4DD7A0AD22012DBAC207F605A68EA5E2E (void);
extern void GUIUtility_EndGUIFromException_m9C8B34B811C1E32C1BC818A57817FF5E117EC1B0 (void);
extern void GUIUtility_EndContainerGUIFromException_mC60505F763292A2C80F7FBC0644F3B4679414DEB (void);
extern void GUIUtility_ResetGlobalState_mD0A482A31337B6200F644995345CF56849913928 (void);
extern void GUIUtility_IsExitGUIException_mB887DAF961E8C1124916777B812FBF2324F5265F (void);
extern void GUIUtility_ShouldRethrowException_m60E879B4683840AAD5CD514E8C3BDDCC6403B652 (void);
extern void GUIUtility_CheckOnGUI_mD167632D5D038DF66CC97F231CD45736D1F556D6 (void);
extern void GUIUtility_RoundToPixelGrid_m0E594150154A6CCAD942F6B23179FB6886361105 (void);
extern void GUIUtility_AlignRectToDevice_mE788EB722671F5DC10F7ADB8CA1A3427749ECDD1 (void);
extern void GUIUtility_HitTest_m55D2F9EAC7EA99CA0C490546A6B45DA96F5AB3DA (void);
extern void GUIUtility_HitTest_m8C93A1BFB637176154C02F73038A98D1F616A7C2 (void);
extern void GUIUtility_HitTest_m0312C850D991342F3A7656A959C87466500F2987 (void);
extern void GUIUtility__cctor_mAA2103B5A88A5D093DB8AEDBE54C5E4757D84A87 (void);
extern void GUIUtility_Internal_GetControlID_Injected_m00F0DDAB73176CDD6EB5F19AA64511CF445E1249 (void);
extern void GUIUtility_AlignRectToDevice_Injected_mED42E3383D2A790E76602A5AB894DDE4850E43F1 (void);
extern void GUIUtility_set_compositionCursorPos_Injected_mF035733A0EF9A0258AB44982286A8FFFBF2B09A6 (void);
extern void ExitGUIException__ctor_m345D7AD70E401C1AFD46E537CDCEC0F1C8BA342B (void);
extern void ExitGUIException__ctor_mE93D467487F7F148547778DF06CF2BCD03472656 (void);
extern void GUILayoutEntry_get_style_mEFB6A8443849EC32BD84059C09632B53E44A5876 (void);
extern void GUILayoutEntry_set_style_m0A23F7EFF504A581FC6CA86EF3BE753F060AC48A (void);
extern void GUILayoutEntry_get_marginLeft_m3B362DA8241B4008C2A6CDA693295A609F765221 (void);
extern void GUILayoutEntry_get_marginRight_m032808DC8C04B31150407F3F61E71865C2636D7F (void);
extern void GUILayoutEntry_get_marginTop_m47BAB82D31A45E21F9AAB8229265788C0D19487C (void);
extern void GUILayoutEntry_get_marginBottom_m2BCCF0FC72E0230E155E7A26BA9FFD904AD4C221 (void);
extern void GUILayoutEntry_get_marginHorizontal_m9847FB7747542BB322195F9CF4B75F55339CD7B5 (void);
extern void GUILayoutEntry_get_marginVertical_mCD309A186E80B22E75DD8F15D2598B9B739C7AD3 (void);
extern void GUILayoutEntry__ctor_m011B3DA69713EEA6BD98D4056B5ADE01F237E5B2 (void);
extern void GUILayoutEntry__ctor_m9E77958057210F340E409F42DFCEFEF8539A5547 (void);
extern void GUILayoutEntry_CalcWidth_m77BB8C0413A27303E4E61CB53586FD4A825C5EF3 (void);
extern void GUILayoutEntry_CalcHeight_m295D607AB2FDD78D7C665BB3FB3A495E2E8CC0A6 (void);
extern void GUILayoutEntry_SetHorizontal_m268577E88A2AE5870C14EFDA9CB88C94CAC2ACE9 (void);
extern void GUILayoutEntry_SetVertical_mA20893626441C55001C1940C53A6A100DD22D61F (void);
extern void GUILayoutEntry_ApplyStyleSettings_m2D3679DAF547D104FE48E7D6D8E27B639F6A666B (void);
extern void GUILayoutEntry_ApplyOptions_mF024E6CEAAD97888AE293810E01F8431D79456A3 (void);
extern void GUILayoutEntry_ToString_mD3785AC5958EB56ECA6E5D325D166C5F5725E615 (void);
extern void GUILayoutEntry__cctor_mF6F64749802F89E5AA0A1458CE99CA5FC0D639C2 (void);
extern void GUIWordWrapSizer__ctor_m28C0BF2C7D0C5C71A47B8039DF939F954BD06785 (void);
extern void GUIWordWrapSizer_CalcWidth_mEB7A01D9C3EE00953A4EBC3F4A2B9EFA2BC81552 (void);
extern void GUIWordWrapSizer_CalcHeight_m8CD1B64A1632F1929EF0856E0BD091BAAEC411C4 (void);
extern void GUILayoutGroup_get_marginLeft_m343D82AA90154850B9B2A97B9E471D5235761EB3 (void);
extern void GUILayoutGroup_get_marginRight_m2710F9CCC1B6D67BC4F9D9487B082B7E143757D0 (void);
extern void GUILayoutGroup_get_marginTop_mA61C984665E93EE9E8670753AF919208528C4F87 (void);
extern void GUILayoutGroup_get_marginBottom_m1EC579493343750FB013A6F01AD84DFEC8D489BD (void);
extern void GUILayoutGroup__ctor_m2AA89FAB5BB5BA76F4059D106A59E346739755D8 (void);
extern void GUILayoutGroup_ApplyOptions_mD4C0BFAC7A90FB32BC6DC99ECA3EEA6C1C9396D2 (void);
extern void GUILayoutGroup_ApplyStyleSettings_m5A88CB0FC11FE81405684C3EFF7EF7DA974D2649 (void);
extern void GUILayoutGroup_ResetCursor_m58C36F1ABC54BE5EFC16D512318BED9EB8918127 (void);
extern void GUILayoutGroup_GetNext_m45FF6F2D555DE615B6C52335C68947898770EDC4 (void);
extern void GUILayoutGroup_Add_mCE459B14C2B364DF4B78DF95D26254B4B5FADD1F (void);
extern void GUILayoutGroup_CalcWidth_mFA744462378028538F1E3AAB39CB6AF0FBB1851B (void);
extern void GUILayoutGroup_SetHorizontal_m37D01CDDE4FAEDB20E0D469805EF96B878DFB5D5 (void);
extern void GUILayoutGroup_CalcHeight_mAA9676BD80BAFC48F515ACA00E83FB7E9EE1FC2A (void);
extern void GUILayoutGroup_SetVertical_m28ADC75A1C5148E22EDD149221535C4B97BC5FE2 (void);
extern void GUILayoutGroup_ToString_m7859D80D5D81B23684C4309DA0565D4CE1D2680C (void);
extern void GUILayoutGroup__cctor_m9214FACB657F5C28173EDCF59DAD85F14E7E2800 (void);
extern void GUIScrollGroup__ctor_m95351A883B27B71698A4B84815CEA687D109F3FB (void);
extern void GUIScrollGroup_CalcWidth_m6B927DBF94A8940301A9FB64190403E5667712CE (void);
extern void GUIScrollGroup_SetHorizontal_m31FCDD252E67D51FC954C8E2C358BA0EB3AD7601 (void);
extern void GUIScrollGroup_CalcHeight_mCB0CEC4871F6540145949E4CE8242172A75B2E5F (void);
extern void GUIScrollGroup_SetVertical_m8609CD909413A7364781818DDE37A314D8795FD6 (void);
extern void ObjectGUIState__ctor_mA9AB2887ABAF5102164545D7F0CE59BCF05618B4 (void);
extern void ObjectGUIState_Dispose_m156DC13F33DEFB261C8B13EB98A1A3782D182DE8 (void);
extern void ObjectGUIState_Finalize_m10310B7E07DB5215C7845BF0F770B587D4F4C1B8 (void);
extern void ObjectGUIState_Destroy_m316F4C75D0C8F18896A69BB9E39D90C0CDBE8726 (void);
extern void ObjectGUIState_Internal_Create_m22F3AED2A44D4D00B478C2626295D432F74383EA (void);
extern void ObjectGUIState_Internal_Destroy_m936A111D9F70932A3030FE851C9E3BD82FD1F425 (void);
extern void ScrollViewState__ctor_m9619262C4C72300A8B26011F627C68DF67425E53 (void);
extern void SliderState__ctor_m650A11534C71EF571FD631CC3E910B756A16889E (void);
extern void SliderHandler__ctor_mDCC4520E68A478FC28519FD5DB149EFFBB9C186A (void);
extern void SliderHandler_Handle_m9E9DBA6E45BA00A0D812C74BBB18DFBA0F923BFE (void);
extern void SliderHandler_OnMouseDown_m9DE4BD08EDF586656B93C94FDF4907D081BCA8B8 (void);
extern void SliderHandler_OnMouseDrag_mC985CE7801C44CC87A1B11ABF0E142E617356B9E (void);
extern void SliderHandler_OnMouseUp_m97A5FDF8EBADFE60EC0DE486127A7F4B9C7068DB (void);
extern void SliderHandler_OnRepaint_m5FAFC71FF2A246AC8336B0F400F35DF581AD54AF (void);
extern void SliderHandler_CurrentEventType_mC4756C3A92FC488ACF0C0148CFFA0F0239622F2A (void);
extern void SliderHandler_CurrentScrollTroughSide_m5E30388E1DE3C01FB968EFCC02215C14F301E1A2 (void);
extern void SliderHandler_IsEmptySlider_m8AD3CF68C310AEAAB0E59D54A29A094A285AA586 (void);
extern void SliderHandler_SupportsPageMovements_m1F5CDF7F988438DF502BC1312F23CC4164F97EA8 (void);
extern void SliderHandler_PageMovementValue_mA83FFA75AE8306757F98570BEDC2F307FBC96FE1 (void);
extern void SliderHandler_PageUpMovementBound_m82E469555A7A32E942A3B9DA1C16C6239151ED8A (void);
extern void SliderHandler_CurrentEvent_m5B1D7DFBE33DF06D2FE72D7BA64E997DB59ADE75 (void);
extern void SliderHandler_ValueForCurrentMousePosition_m089BEF0D4C4E82C086E3E49B871780AD6D6848F5 (void);
extern void SliderHandler_Clamp_mC73E2009637968EFFC5DEE94B8C48B8DF6442C5E (void);
extern void SliderHandler_ThumbSelectionRect_m182932776473DDB8076E858AC0580E931AE66EB8 (void);
extern void SliderHandler_StartDraggingWithValue_m580387DD28C5BC8EF389692BC4B0BDB402AD1F5C (void);
extern void SliderHandler_SliderState_m9E9EECE09E988B83FE3DF1F0AF9AFCF4AAF96D60 (void);
extern void SliderHandler_ThumbExtRect_m16D13E44222386A99F1E3F089A5F4DC2ED19DBC1 (void);
extern void SliderHandler_ThumbRect_mA297CC1978CFEEC1F01218A5C05766C9D0465C98 (void);
extern void SliderHandler_VerticalThumbRect_mC9C04B0479876CA886A4C6868FFD65168511CAFB (void);
extern void SliderHandler_HorizontalThumbRect_m6FADFA27C8510FDD5FC2EB30A738DD6569DFB0BA (void);
extern void SliderHandler_ClampedCurrentValue_m2312300551774A993735636675E23015737664EC (void);
extern void SliderHandler_MousePosition_m5E95F987E7F6CA9E517AA7B84328689A287965B6 (void);
extern void SliderHandler_ValuesPerPixel_mF654B0D4DDC55599E9B251A026727DAFF425E66F (void);
extern void SliderHandler_ThumbSize_m7A0F0323AF2FF51A3F35765438DDD1FBBAB63D47 (void);
extern void SliderHandler_MaxValue_m25803007A5DB5427855A607D85F0A68D5CCCB208 (void);
extern void SliderHandler_MinValue_m904DAD0D3F7BD5CD525BD520B82F60C3878FB25E (void);
extern void TextEditingUtilities_get_hasSelection_mB7272F47E994E6B88A2B0229BED793D4C5B23219 (void);
extern void TextEditingUtilities_set_revealCursor_m76B8081758CCD459072EAEF3B8FE3017A57735C2 (void);
extern void TextEditingUtilities_get_cursorIndex_m0D23C8510F3F2A20BBF5796F00CC36DA8BB32BD9 (void);
extern void TextEditingUtilities_set_cursorIndex_mED289FA84CA33C4A695463C856524B501A78FEF4 (void);
extern void TextEditingUtilities_get_selectIndex_m1331CF40E64C203B1713CFC2FEC5E0F30FFC737A (void);
extern void TextEditingUtilities_set_selectIndex_mFB98D1E5E2C236DEEE7A45619965502B73E0ADBA (void);
extern void TextEditingUtilities_get_text_mC8854D29B1F95E04E0FFB49F1F2327E77598EF8E (void);
extern void TextEditingUtilities_set_text_mC1FD19476AF4FA014E9DBA5A33C54A57E2FA70EC (void);
extern void TextEditingUtilities__ctor_m6503B88727D1F4008C31E4FB54F2153A44E99B07 (void);
extern void TextEditingUtilities_UpdateImeState_mC13FC46AB62C566576C0D653774896FFA438003B (void);
extern void TextEditingUtilities_ShouldUpdateImeWindowPosition_m58FC98A57B608095F3EB6688A0D95FA64B8444E1 (void);
extern void TextEditingUtilities_SetImeWindowPosition_m1DFAAA8DBA6A946B204470806FB968359BFB3C48 (void);
extern void TextEditingUtilities_GeneratePreviewString_mA97B83DDA33F11F6580B86F6F2C438F19018A037 (void);
extern void TextEditingUtilities_EnableCursorPreviewState_mD28C1FAC4AFFEE396D903D1EED90DF1F5BC6A85B (void);
extern void TextEditingUtilities_RestoreCursorState_m36D71121DFD69D0EB43FCF48E49E1F616F9346C4 (void);
extern void TextEditingUtilities_HandleKeyEvent_m155A0EFF2CC76B511BDA3405C68A48408C1C6256 (void);
extern void TextEditingUtilities_PerformOperation_m8DC34D9795E11260FA9F4C4961601B189F342820 (void);
extern void TextEditingUtilities_MapKey_mCC38540E032D8765E0E280B94072141E74FF43FF (void);
extern void TextEditingUtilities_InitKeyActions_m08B59B3C8199E8DA37622E85D269F9E848B06381 (void);
extern void TextEditingUtilities_DeleteLineBack_m34769E8A0D70CE5BDBA3B11DA506BC7CA859CCFA (void);
extern void TextEditingUtilities_DeleteWordBack_mC235A97BDFEE8CE4C993BCF02DF4E86AB4BB8F1D (void);
extern void TextEditingUtilities_DeleteWordForward_m7A29C8E5BD4E3F01F7A379C16D971BBF565E5CAA (void);
extern void TextEditingUtilities_Delete_mF34D04ACA64C871CDE0F5F5DD6CC36A0667926DD (void);
extern void TextEditingUtilities_Backspace_m08DB317F2AEDA35F6227D52FF92B3EE4F7AA5909 (void);
extern void TextEditingUtilities_DeleteSelection_mF7E0C7A8B7A8984A5DA2C55839BFE60E0A70B847 (void);
extern void TextEditingUtilities_ReplaceSelection_m49F49CDB5D91B695392E2CE1B7BDC5A46817BBCE (void);
extern void TextEditingUtilities_Insert_mC555975D4937CE0C4B68654520648A1C3F74C853 (void);
extern void TextEditingUtilities_CanPaste_m81E3C512EF04804A3594020C3CD084F5BD85B3E7 (void);
extern void TextEditingUtilities_Cut_m5E08F36BC2F88E0E55483A524E815A3EAA429D2B (void);
extern void TextEditingUtilities_Paste_m876D2AD7A881EAC57D762E15F9ACB1AC26B3C28C (void);
extern void TextEditingUtilities_ReplaceNewlinesWithSpaces_m368B355EF98969A0A9E527D67C43E173B3FCAC74 (void);
extern void TextEditingUtilities_OnBlur_mCD1823DED60BE96C25C0E31B8DEC5F8EB1ACFD13 (void);
extern void TextEditingUtilities_TouchScreenKeyboardShouldBeUsed_mAF399D5C01CD9A7B7F4E1C188792420AFBA99D53 (void);
extern void TextEditor_get_text_mB5A19231EF7159855775CF3E9C5BC5346156E168 (void);
extern void TextEditor_set_text_mB71257AAD99A56AD5EA96DB546B17296E60C4455 (void);
extern void TextEditor_get_position_m40763329A82988B1C5D5C1DA9919932061C99E13 (void);
extern void TextEditor_set_position_mDD8F5A0BFCE942F8D4403F78D3E1B0EF35D17EA0 (void);
extern void TextEditor_get_localPosition_mFC726E86A4A79A98813DB9591648E0D82049D01D (void);
extern void TextEditor_get_cursorIndex_m0954904B376E50D89A4CDD82EEE710544D6EF461 (void);
extern void TextEditor_set_cursorIndex_mF34C100A55F2767E46D07445B04B6DBEB77AF9A1 (void);
extern void TextEditor_get_selectIndex_m4DB0C8224B5C82B0F02FFF69E80D3FEA4202A020 (void);
extern void TextEditor_set_selectIndex_m782BBC95B43A71A1061060BF52959ADEE9AF27ED (void);
extern void TextEditor_ClearCursorPos_mAE2290DC256C2BB4F1E326187E0662F3BB42B1F6 (void);
extern void TextEditor__ctor_m4AEAC85E4950B709A35F26D1F0DAB3C9D35E3494 (void);
extern void TextEditor_OnFocus_mCD739D81E0F74A3E68A0BB861A3A3BD87DDBEE0A (void);
extern void TextEditor_OnLostFocus_mFDA430398601ABF5BBBF44D0A6CE969AFBED4FC9 (void);
extern void TextEditor_GrabGraphicalCursorPos_m74915B49D9B0D200367FD710A1321C0D2E54B1E4 (void);
extern void TextEditor_HandleKeyEvent_m2C8D0EA03A32518162947C87C4DF5A60C67BDE6D (void);
extern void TextEditor_HandleKeyEvent_m14D691B63637C1F4CFD0A96F7940C69A9CD6C658 (void);
extern void TextEditor_DeleteLineBack_m43927B9B9F8AD1CA54CED2C40571F190EBE9792D (void);
extern void TextEditor_DeleteWordBack_m9F0CDF4ADF1A86CB97BD8C60FD52031FCD24A210 (void);
extern void TextEditor_DeleteWordForward_mD81B94DA0DE3A3B9A212C3B6AF6C475B39E7A56D (void);
extern void TextEditor_Delete_mFE5E2A0C6230CA113C1C64C4F0F5F5D30DF16EEA (void);
extern void TextEditor_Backspace_m3D25240A83DA225BEDC8A5363CC83E9A2966169A (void);
extern void TextEditor_SelectAll_mDEBAABE01DF37B1EE8EFDE43E8036B5C2813C685 (void);
extern void TextEditor_SelectNone_m4EEF86ADCEEF1F445A57947AB7D58ECC0A334B86 (void);
extern void TextEditor_get_hasSelection_mD63A0ECF990D21515ABCAD26A7974B58A8CECCE9 (void);
extern void TextEditor_DeleteSelection_m520F49C6269E488DD60BBD4603DA869FC446A788 (void);
extern void TextEditor_ReplaceSelection_m7BBCC70F065AED2C5942127F95234C17897A70C1 (void);
extern void TextEditor_Insert_m7FE4F5EF50CDB90FCD47C93D399996A2149B54AD (void);
extern void TextEditor_MoveRight_m568871F86B97196C66A4ADDF335E0ECEBEE18DC1 (void);
extern void TextEditor_MoveLeft_m3784BAF8F1BF69781ECFA40D8DDAFA6EA9EC58C1 (void);
extern void TextEditor_MoveUp_mFCC668A7D89E092E588F92DA2FA2B4D03E7C921F (void);
extern void TextEditor_MoveDown_m6084C0F493B71485D0D0796D77B4F32F391C7571 (void);
extern void TextEditor_MoveLineStart_mCFB0865ABB2E2B6A6FC98F77FE6E69A8E2578ED0 (void);
extern void TextEditor_MoveLineEnd_m811BAE3ABB333A4CE56C6C80439111CC1FE4450F (void);
extern void TextEditor_MoveGraphicalLineStart_m0332C42BCF18CC2AE7024402CDFFD1F24210B3DD (void);
extern void TextEditor_MoveGraphicalLineEnd_m82D882096A9E72C9469F01F5E8882DBEA8DC2C0E (void);
extern void TextEditor_MoveTextStart_m59D0D8EADF0420DED887A9E6D9E780CBE1A87E16 (void);
extern void TextEditor_MoveTextEnd_m26A12A1C36B56C8D80B1E5C520EA38E670611028 (void);
extern void TextEditor_IndexOfEndOfLine_mE03CC016EBA2B7AFD72A204D23D64C9F686C057B (void);
extern void TextEditor_MoveParagraphForward_m5CD556511F7189813865732FDC28FB710ADA8572 (void);
extern void TextEditor_MoveParagraphBackward_mC64495DC5B520C2D279A03616D0ADDCE0718A510 (void);
extern void TextEditor_MoveCursorToPosition_m7E64149849945E081877617EED22BCBBA51CAC76 (void);
extern void TextEditor_MoveCursorToPosition_Internal_m7D1E68A7556DCCB8B6D557BF2E3B2894905B6037 (void);
extern void TextEditor_SelectToPosition_m406CAD9A7C9B9211A10DFB1FF6FB6E0CF4437ECA (void);
extern void TextEditor_SelectLeft_m7473E14B27D0D9503E88FE893B41CD52EA15A39B (void);
extern void TextEditor_SelectRight_mC374DDA6EF8A522FF0CF35393EE38C07154FC062 (void);
extern void TextEditor_SelectUp_mA28D8B3CB2012DE645CC3A027E1168AF2E9BCB3B (void);
extern void TextEditor_SelectDown_mC77A384914BA7F3CEC02C20D31CC28A3788B1C15 (void);
extern void TextEditor_SelectTextEnd_mDCF0E587F42EB91D85850AC5F840E089955D69F4 (void);
extern void TextEditor_SelectTextStart_m5FAFFEF24723CA29E6DA6631EE80EBF4ADE591C6 (void);
extern void TextEditor_MouseDragSelectsWholeWords_mAE66B48954FFFC0F439C4070ED3601CF611A8F3B (void);
extern void TextEditor_DblClickSnap_m6CF85AA1A22F59BFF0301F54815128CF5EBFD252 (void);
extern void TextEditor_GetGraphicalLineStart_m4503A00148DE73D825654C4DCBFD27E8234A957B (void);
extern void TextEditor_GetGraphicalLineEnd_m3396AC4E6D75FB0F8E8F99C91384064A32F0DF3F (void);
extern void TextEditor_FindNextSeperator_mE89483949A16CD41C7A7BDE7ACA89DBAF5039384 (void);
extern void TextEditor_FindPrevSeperator_m8619997F12F419286B495A9BA5078634373542CB (void);
extern void TextEditor_MoveWordRight_mB79E9C0C420ED29EB70CFAB49CBFE6C290ECD074 (void);
extern void TextEditor_MoveToStartOfNextWord_m8E6EA22B00CD272176D69786A716B5D125A026E2 (void);
extern void TextEditor_MoveToEndOfPreviousWord_m2CE9EBD0A0FB0CCBC93912FA856B7628C4B3C3FF (void);
extern void TextEditor_SelectToStartOfNextWord_mCC2D52A1AA807D2EDB985CE1FAB18B884A1E549A (void);
extern void TextEditor_SelectToEndOfPreviousWord_m5C182936491E07770967753F142C2CCC6BAABB5A (void);
extern void TextEditor_ClassifyChar_mC2104A64D197D5BE8FA3CB1CA12F0E6AFF50AC77 (void);
extern void TextEditor_FindStartOfNextWord_m07650DF8A35625ED2B3230B6B4C96C730F945B0A (void);
extern void TextEditor_FindEndOfPreviousWord_mFBDBEABAC6CFE72EF4ED33A3474EF3998E460C00 (void);
extern void TextEditor_MoveWordLeft_mFDC5DE936BBEF1CBAE440BD813107B04F20A2004 (void);
extern void TextEditor_SelectWordRight_m65AD5DB10CB51F517DA58E4BD5E11C3842ACC503 (void);
extern void TextEditor_SelectWordLeft_m3E721136E39E2CF679C08538DDD2FDE5D3D8F7E5 (void);
extern void TextEditor_ExpandSelectGraphicalLineStart_m04A641F4217F6F54795103B6819EADCB1AC0495F (void);
extern void TextEditor_ExpandSelectGraphicalLineEnd_m77DED153050FD67B42CA570497436192A3E5BA60 (void);
extern void TextEditor_SelectGraphicalLineStart_mDB2EDA9715BCF37692A79301F480C793D540E01A (void);
extern void TextEditor_SelectGraphicalLineEnd_m27A03A2BE9B63F0C294E986B4244942876EB2848 (void);
extern void TextEditor_SelectParagraphForward_m9531E6CCDFB591A90EC32464858B2280AD3F5772 (void);
extern void TextEditor_SelectParagraphBackward_m0430E7BC1725DB775D0D817B80C4C1232449AD79 (void);
extern void TextEditor_SelectCurrentWord_m9118CAE842D71A1AB19C90C94FC0ED4C32ABA99D (void);
extern void TextEditor_FindEndOfClassification_m9F20C27BA429FCCDDB9821EB9CE1E55535D44857 (void);
extern void TextEditor_SelectCurrentParagraph_m2D569FA93359557D691EB507471594473E419F0C (void);
extern void TextEditor_UpdateScrollOffsetIfNeeded_m5F969498D9A0DABC94D33D3F3E943C5D4009D5FD (void);
extern void TextEditor_UpdateScrollOffset_mD3F056830FF3FFC3461ED965EB0B7E306536FC3B (void);
extern void TextEditor_DrawCursor_m16C190E8A889D4CFBDD448A29CE2FDC503C10243 (void);
extern void TextEditor_PerformOperation_m9CC1732A34CF801A0348A4296BDBC9D015AB4014 (void);
extern void TextEditor_SaveBackup_m5DA3A7E39B3F28777DD53DC0934CAB46B9ED8151 (void);
extern void TextEditor_Cut_m3B9A748CFEF7633613107C8F4A1CF62255041BFB (void);
extern void TextEditor_Copy_m33D7D8DF6A4EE867CF02D15577E8A591C0027DCA (void);
extern void TextEditor_ReplaceNewlinesWithSpaces_m050CD5F1C45A59C776C840AC84A1CF1C4AECA47C (void);
extern void TextEditor_Paste_m1A9AEA3C543B2E7595070DA96D7DBE24066AC9E2 (void);
extern void TextEditor_MapKey_m911245BAA919A02A8FDFCC0998CB147A3EE0EC9B (void);
extern void TextEditor_InitKeyActions_m30295CE738738468794A7AE3338BE827B891A0DD (void);
extern void TextEditor_DetectFocusChange_mFE7D29EC9391792772129BD80FC236285218464B (void);
extern void TextEditor_OnDetectFocusChange_m70E412EF53A051067D33711E70BE73C76CB97168 (void);
extern void TextEditor_OnCursorIndexChange_m9B9C472B0F62917E96E5E27F15A76C9E4E493012 (void);
extern void TextEditor_OnSelectIndexChange_m99E1BBDFC6398F47F3170A6A46C5428F292FEE21 (void);
extern void TextEditor_ClampTextIndex_m08BC2F0E9A0599EE71C0632C61187F3F3EAAF4B6 (void);
extern void TextEditor_EnsureValidCodePointIndex_m9C20E36F766CF8DBD87A36606B1FAEED3BE42BB1 (void);
extern void TextEditor_IsValidCodePointIndex_m1D01E0B8AA575A60C985B9FC413ED3DDA4EE8097 (void);
extern void TextEditor_PreviousCodePointIndex_mFCCC1034ABF4773A7E1E121D14ACB948FCA116E0 (void);
extern void TextEditor_NextCodePointIndex_mF426772BB6B0CD7A3FC4042070C21902BF576B31 (void);
extern void TextSelectingUtilities_get_hasSelection_m86EA37D0A10EC2C4C1886C7E770DAB34DB8A66CD (void);
extern void TextSelectingUtilities_get_revealCursor_mD9502AE79AB9AC44496008153F267CBB4A9B3C16 (void);
extern void TextSelectingUtilities_set_revealCursor_m2A3BFE850A09B0716824E763373A521A24CC5F52 (void);
extern void TextSelectingUtilities_get_m_CharacterCount_m4C9189574E900CF75E41BB29861246D64A83CB0E (void);
extern void TextSelectingUtilities_get_characterCount_mF1A48644B9D24C0AE96B6259FDAFC87CB1BD5FEB (void);
extern void TextSelectingUtilities_get_m_TextElementInfos_mB87B7D1C40D71249B4E20DA1D7501194A2477368 (void);
extern void TextSelectingUtilities_get_cursorIndex_m89386F5B913CD481337E49A0635834F433B4AC5F (void);
extern void TextSelectingUtilities_set_cursorIndex_m0D3D5D519DA459906E983EABAA4AF692C4763269 (void);
extern void TextSelectingUtilities_SetCursorIndexWithoutNotify_mF240906A6FA8A38F5CB6AD1C5601265D05CCCAEE (void);
extern void TextSelectingUtilities_get_selectIndex_m1535CA4D982CC3A4C46E4491FA36E9514243CCB9 (void);
extern void TextSelectingUtilities_set_selectIndex_m1697EFBA1D0B91E0B20FD23B69A43D32D2110F10 (void);
extern void TextSelectingUtilities_SetSelectIndexWithoutNotify_m33DB6522D2FA6877E3B991CDFD4944836A5D77FF (void);
extern void TextSelectingUtilities_get_selectedText_m4A131331842BA17B453A09FE4663A75B7B356013 (void);
extern void TextSelectingUtilities__ctor_m0D593E63B3CFE982829CFF9C93C5858E27AB84AE (void);
extern void TextSelectingUtilities_HandleKeyEvent_mD6AD5FEF96C31860C66D49324F98BD7AB27AE551 (void);
extern void TextSelectingUtilities_PerformOperation_m3F865D868A3A9264B28FAABBFE92F654BF3706FC (void);
extern void TextSelectingUtilities_MapKey_mDCCB5F98583F4FC584FD97AF27D37F91A7C5048B (void);
extern void TextSelectingUtilities_InitKeyActions_m005DD393E320ADA5BEFC2AA357EE964A8AB6CCED (void);
extern void TextSelectingUtilities_ClearCursorPos_m743C82F3CC7576E5050B6FA23133EC3FA8E9ED9C (void);
extern void TextSelectingUtilities_OnFocus_mCAC979E4683D3A0B91C91FCC19516E5FEE605A9C (void);
extern void TextSelectingUtilities_SelectAll_m89B71F5AF97AC5848616468DEFCF062C26DF23FD (void);
extern void TextSelectingUtilities_SelectNone_m47791B4FBE066CCC974155E1BD9FE8ACCB48D21A (void);
extern void TextSelectingUtilities_SelectLeft_mB51FE46E45D1C077ACB44AC5E2BF63C52AC3727D (void);
extern void TextSelectingUtilities_SelectRight_mFC95A2800C1CBEC7606EA8901F75CC946ED3BCA8 (void);
extern void TextSelectingUtilities_SelectUp_m1D90105D04CF6CBAE84D059CEF0495FD2FF0C22A (void);
extern void TextSelectingUtilities_SelectDown_m8E2EE5EC95CE507A7814D0FAAD865557C2A191C1 (void);
extern void TextSelectingUtilities_SelectTextEnd_mFF22956A56670B41BEE1A4527A9057B63BE89927 (void);
extern void TextSelectingUtilities_SelectTextStart_mD4F085F9AF7C2441D60E54CDE64B96A6934D13A6 (void);
extern void TextSelectingUtilities_SelectToStartOfNextWord_mC53BBECD698C32BAD6B4856F52ACA9E9D59F3E52 (void);
extern void TextSelectingUtilities_SelectToEndOfPreviousWord_mAC43A04CA459FE506650A86B695900300E4B24D8 (void);
extern void TextSelectingUtilities_SelectWordRight_m75669B452A334F48FB1CE5E0AC6792862E706B5E (void);
extern void TextSelectingUtilities_SelectWordLeft_mC488E7BBD9E8187F426A2F1CA82C528D76F4FCCC (void);
extern void TextSelectingUtilities_SelectGraphicalLineStart_m87E74A85CBD46469B2D1263436D2BE2FE5ABDB38 (void);
extern void TextSelectingUtilities_SelectGraphicalLineEnd_m60A6059D86AEB922ED7829EAF9C4E53B800911B4 (void);
extern void TextSelectingUtilities_SelectParagraphForward_m64D82C33CE1B82DC1B994B6CA027D717AB4280EB (void);
extern void TextSelectingUtilities_SelectParagraphBackward_m77EE0A0A167E91E1850937F543DBA615DC6DE0A0 (void);
extern void TextSelectingUtilities_SelectCurrentWord_mCC2AC7DD6D2BA6D2DF3DD728D883FF0D6963A959 (void);
extern void TextSelectingUtilities_SelectCurrentParagraph_mBD0B848A023ED86697EBA135E81B59ACD13B2B7A (void);
extern void TextSelectingUtilities_MoveRight_m8F1910A2773A39EF5CE248349F5A6CD7166AB795 (void);
extern void TextSelectingUtilities_MoveLeft_m094C534A56FC3CDA9C2423E46D179F359693370E (void);
extern void TextSelectingUtilities_MoveUp_mF0F3EE17A2CB3C4C1AEB950E80A5237A55D2711D (void);
extern void TextSelectingUtilities_MoveDown_m7EF798D6A19267DE30ED50C66697F5BC8AB814B8 (void);
extern void TextSelectingUtilities_MoveLineStart_m561A829C19F6C50028473CD5F81C508F3EEFE276 (void);
extern void TextSelectingUtilities_MoveLineEnd_mB4BABB86B094C9B88DEF94E0392BAE4396283B61 (void);
extern void TextSelectingUtilities_MoveGraphicalLineStart_m1ECAAAF8A29D63C5D7E76170D3745E3EB9E2266F (void);
extern void TextSelectingUtilities_MoveGraphicalLineEnd_m8BB408E28EA20EB56531B1FFD417FE54296008BE (void);
extern void TextSelectingUtilities_MoveTextStart_m7A276F1B11A1DAF468AC84324E592005B5D47350 (void);
extern void TextSelectingUtilities_MoveTextEnd_mDA69E553CA7D50781E845169852F1A0059FF0EB9 (void);
extern void TextSelectingUtilities_MoveParagraphForward_m88210A22BC823945D6AA137D50603388233502EA (void);
extern void TextSelectingUtilities_MoveParagraphBackward_m087604CE592162192829ADB142B786B0C436A58C (void);
extern void TextSelectingUtilities_MoveWordRight_mDCDC1B673D599BB94C95D6DC364D8877E87E3383 (void);
extern void TextSelectingUtilities_MoveToStartOfNextWord_mD1CEFD9620822349FDE09237943F943EB512A8C2 (void);
extern void TextSelectingUtilities_MoveToEndOfPreviousWord_m5BE2565747FFC49AA41526504653159852FC50A7 (void);
extern void TextSelectingUtilities_MoveWordLeft_m7D8131CD2DF2DF180D3FC249E2F0E17E250E5D3D (void);
extern void TextSelectingUtilities_MouseDragSelectsWholeWords_mB586078A58B5D56A53138856AB8DE9BD33535CC1 (void);
extern void TextSelectingUtilities_ExpandSelectGraphicalLineStart_m5E109D0A6A12D3D2FDF09F3E1407EB0347F7C4EF (void);
extern void TextSelectingUtilities_ExpandSelectGraphicalLineEnd_mFCA738E71AADB3C1206F279BC2681CA906A36D74 (void);
extern void TextSelectingUtilities_DblClickSnap_m6472F8DA3F0FC46FF75FFB394B283F5E5EC834FA (void);
extern void TextSelectingUtilities_MoveCursorToPosition_Internal_mE4AEE1AA57B8CCBB371C24B4F4B1AA2FF89886FD (void);
extern void TextSelectingUtilities_SelectToPosition_m75C9B53E1227CF9D487D5C8D771F0D8ACFEDC2F8 (void);
extern void TextSelectingUtilities_FindNextSeperator_mCF332FBDFEA6BCB471EDF75D76A139A580DB0E2A (void);
extern void TextSelectingUtilities_FindPrevSeperator_mD0F56A4106D376F28D6A0E8E07D7D4DAB9FF8C7E (void);
extern void TextSelectingUtilities_FindStartOfNextWord_m3D436C6FB45B3574F6F436B7CBAD30F5372C6870 (void);
extern void TextSelectingUtilities_FindEndOfPreviousWord_mA7AF965D2F01728EE872F2CB3F03083ABA63D174 (void);
extern void TextSelectingUtilities_FindEndOfClassification_m134E7AB62A9E90595A3C6DCC1DD517515B85AB1A (void);
extern void TextSelectingUtilities_ClampTextIndex_m04DED78B3B466D3E5AF59442F736A822ABC8D7E0 (void);
extern void TextSelectingUtilities_EnsureValidCodePointIndex_m6D3C6E6936FCAB391B6B48D53DFA50D995523530 (void);
extern void TextSelectingUtilities_IsValidCodePointIndex_mB49A5FF3F3716818677CC13AE904ADF0034A07F5 (void);
extern void TextSelectingUtilities_IndexOfEndOfLine_m9D87F72DC289F7095E615D97D821BBCE8D2B978B (void);
extern void TextSelectingUtilities_PreviousCodePointIndex_mCE1A8A7E9180B182AEA4DB6562EBDE854A860C6D (void);
extern void TextSelectingUtilities_NextCodePointIndex_mA936059BE65DAABE0446AFB5425BB4EA83392607 (void);
extern void TextSelectingUtilities_GetGraphicalLineStart_mEDB6AF99C1BBE0A1180AEE4FE67DDC0A223BD218 (void);
extern void TextSelectingUtilities_GetGraphicalLineEnd_mD956DB2F4EC24F3DE89069232733376B615D3204 (void);
extern void TextSelectingUtilities_Copy_m69701E12FFE465B70E677DCCCCF3148873A5FE0A (void);
extern void TextSelectingUtilities_ClassifyChar_m887E3900015DB05F1EF0E5F4630748CCA5C3CD52 (void);
extern void U3CPrivateImplementationDetailsU3E_ComputeStringHash_m3791FADF6D0284BCC1AF6156A077038C2AA23055 (void);
static Il2CppMethodPointer s_methodPointers[730] = 
{
	Event_get_rawType_mD7CD874F3C8DFD4DFB6237E79A7C3A484B33CE56,
	Event_get_mousePosition_mD6D2DF45C75E6FADD415D27D0E93563DED37D9B9,
	Event_set_mousePosition_m221CDC5C9556DE91E82242A693D9E14FAC371F38,
	Event_get_delta_m1BBF28E2FC379EDD3907DC987E6BD7E6521D69A0,
	Event_set_delta_mA4F7805B9B53B36C7DAA31735CC9097D363B9F9A,
	Event_get_pointerType_mFFB3FB3E83412151A66FEC136FA00EBDB563B94B,
	Event_get_button_m57F81B5CCB26866E776D0EBD1250C708A3565C08,
	Event_get_modifiers_mD55E7CF06EB720434F0F174EA569B2A29792D39B,
	Event_set_modifiers_m879319643B5CD23F3223AB8E835C8ABCD3DA72FB,
	Event_get_pressure_m3E43BF333499DFDCFF2A36258BBC290DDD40D963,
	Event_get_twist_m557A5139AD77A15D25598A3F83676E558D1202BF,
	Event_get_tilt_m5F37D44342F42D691336B23EB075171CFEE7C7A3,
	Event_get_penStatus_m41CC65A15A4209D8168AE8BC64C691DE22F83611,
	Event_get_clickCount_mEF418EB4A36318F07E5F3463E4E5E8A4C454DE7D,
	Event_get_character_m8F7A92E90EF65B9379C01432B42D6BF818C32A61,
	Event_set_character_mA159F1742FD9EA968F32556C5FE1A2401069BAF5,
	Event_get_keyCode_mADBB236A741F96D86E4A536E15FFECFD4C367B64,
	Event_set_keyCode_m698D040F2EE0BE55B1B06A3FD865CC0A5D7B1168,
	Event_get_displayIndex_m7DBF1B18DD9B5E5B4EEA979FCA87351E3E5B16C3,
	Event_set_displayIndex_m8208F1B0471C0B45C0BB248F9A0178EB40FBE100,
	Event_get_type_m8A825D6DA432B967DAA3E22E5C8571620A75F8A8,
	Event_set_type_m16D35A8AF665F4A73A447F9EE7CA36963F34FEC2,
	Event_get_commandName_m14F2015FA5A9050C3C42AF1BD9D0E85D4FF78C24,
	Event_set_commandName_m8DA7262E1CD1005911EAB9777DE9FEC2D97504FA,
	Event_Internal_Use_m303C630AFC4EAE76036545C09C79729E90D81CB9,
	Event_Internal_Create_m38519A1960401042CAB57086F9E038116B8D3EAF,
	Event_Internal_Destroy_m25BA236C0C66CB87A89B81336D7BFB55917127BB,
	Event_GetTypeForControl_mB5AFF2956E61972F0A1246416FCFE1C46F7E64D1,
	Event_CopyFromPtr_mC78295EF5861558EC93D3F8691E2A8B50DE84E29,
	Event_PopEvent_mC780BAA7CE4F0E75C8B5C7DC5EB430C278B0D0AE,
	Event_Internal_SetNativeEvent_mF0C015181EABFE56E2C90CD5C6DCA410C2C42746,
	Event_Internal_MakeMasterEventCurrent_m67675F107F56ADDBCF72ECB4C3BE4DCE831C8214,
	Event_GetDoubleClickTime_mF3D10CD927983547C6BF3479083B4155DE693826,
	Event__ctor_m14342F32F62A39A8B8032286E2DCC07FEF72BFF4,
	Event__ctor_mA5E77C0596952812A96703685523819CF50D71A0,
	Event_Finalize_m0882CB2E5E0C20C5C9669518C4DB5D95F840DAB7,
	Event_CopyFrom_m2F9B9704FBE156C5D58FF630F7968568C19821F5,
	Event_get_shift_mB8409DA839B09DC6137848E131A6DBE70BB9E70A,
	Event_get_control_m1E363A7ABA4F2E8CF41C661A48D53D85D635D320,
	Event_get_alt_m57F7F5C1F5FFCE43EFA6889F83CFA42DCA18A74B,
	Event_get_command_m202DE2CB0BE0AAB5CDFEC9DA1BBD3B51E8497547,
	Event_get_current_mBD7135E10C392EAD61AC0A0D2489EF758C8A3FAD,
	Event_set_current_mDB5FE546AFA00DDF6CC23C106CE076EBEF36BCB3,
	Event_get_isKey_mDA8FE1CC5E305BAF181E86A727173C9BE9A1B754,
	Event_get_isMouse_mBD11F4FE2996DFAD2648C8A9648E301EDDA51D7A,
	Event_get_isDirectManipulationDevice_m9A72FB2DF7803E189857D24A65FB568B17533ED0,
	Event_KeyboardEvent_m957733139998C86C7ECA28BA50863EB88B71418E,
	Event_GetHashCode_m9E93319C0E2A92678BC6B3B9A7B1758DBA605E6E,
	Event_Equals_mBA8BEAB37AE94F9B42F62D946DD61223E0F1258A,
	Event_ToString_mB30B330C86407E776E932EC18CF177A4066BA56B,
	Event_Use_mD77A166D8CFEC4997484C58BC55FEB2D288D3453,
	Event_get_mousePosition_Injected_m003389887CF74AEA0E5FC70326E0BF873CDEDCE6,
	Event_set_mousePosition_Injected_mC406AF97621061F7189B9AA9E4FEA7CD16C5C34B,
	Event_get_delta_Injected_mF0D15F34DC749A9AACD091795AE5DBC2609AE3AC,
	Event_set_delta_Injected_m9C70CF005D5B37C1B421C141A42BB53AA70E79B3,
	Event_get_tilt_Injected_m0D3D0EAB424976D4B0FCFFDE4E0F6CF8184F3B09,
	EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA,
	EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F,
	EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F,
	EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313,
	EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE,
	EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6,
	EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B,
	GUI_get_color_m15488B4AD785D10DEB5C66398D0FA9A0C0EA7ABB,
	GUI_set_color_mA44927D3F2ACB1E228815F10042A6F62B7645648,
	GUI_get_backgroundColor_mCAA42085DAB8A1B71E2D0C6C8D86F93CD311FA33,
	GUI_set_backgroundColor_m4ED80300A7DE3EAF923E3344E22D6682FA75B264,
	GUI_get_contentColor_m32B15C8D6BEEFEBCE667ECD3CF664C83224F103F,
	GUI_set_contentColor_m3CDC4D626AC8B6D487AD19765D79C593B98AEF26,
	GUI_get_changed_m3473B2964DCE8C2ADE081517093168C171BBE448,
	GUI_set_changed_mBD91A44AFA77D2BF883B3150AF4AE6AC3ED121DC,
	GUI_get_enabled_m336E115A84DBD8D18A925D0755B51746B98B516D,
	GUI_set_enabled_mF2F99A6870ACAFAEFB5E8FF1B69C684951D390C9,
	GUI_set_depth_m37E8C151AD6D1B28DDFABB64A0974398AA8EC44E,
	GUI_get_usePageScrollbars_m27B43460C2B19308AAB533181F7B4EE84E4DA797,
	GUI_get_blendMaterial_m6139BEE5985EFE8E6C6CFB0A261E413BA12DA11E,
	GUI_get_blitMaterial_m5254BE88B8B1D1C182DCCED08D61F0C4663C0F2F,
	GUI_get_roundedRectMaterial_m0F58148D76BF7F9DA3D1AF004B95A7EB69FC19D6,
	GUI_get_roundedRectWithColorPerBorderMaterial_m6795598BDAD6D7A1758386BE10D9718CEC35115E,
	GUI_GrabMouseControl_mA4B15F8FC1584E422AAA4FBAA2C8A25FCB70B62A,
	GUI_HasMouseControl_m336734E97742086851F3C78CC9DAB55508AA44FF,
	GUI_ReleaseMouseControl_m956B2CF27B6D82677D2960D310D92F043FCEC82B,
	GUI_InternalRepaintEditorWindow_m067D1FC98B8C7B30E0E6B28C38CA2EE78F96851E,
	GUI__cctor_m97D837BF457542B0F7308E8999670A46E465A9E3,
	GUI_get_scrollTroughSide_mD6AEA9AFA867B71D484561D5BEBF3520321E5E3E,
	GUI_set_scrollTroughSide_m6E4ED984EDA808C922C062CA6FB6491D2CD28CCA,
	GUI_get_nextScrollStepTime_m9ECB2B2710D4233C7124D23F0DD995006913D70E,
	GUI_set_nextScrollStepTime_mA35BA69E3FDBC961E42F6C9D02BB4E8776926A09,
	GUI_set_skin_mD51BAED314B39004AE3FDE74F9895CA19F3E40E5,
	GUI_get_skin_m97EC9EB4628B311C0DB7DF9FB19FAD82D6790A1B,
	GUI_DoSetSkin_mF4C06A8BE59628B6514F7FBF9422214A48BE03B9,
	GUI_get_matrix_m3CA02DED0598EE32BD9E66CA533A78EFB0A246FC,
	GUI_set_matrix_m7759FEC96FBCB97E02B1BA44D2EC1B3FEEFA257F,
	GUI_Label_m4A951E57C7DCCF95A0306240144CA2713F546526,
	GUI_Label_m0D7BA53414421D71010DFF628EAA6CCCB3DE737E,
	GUI_Label_mFC6559DAC18FE889F1B94729AED3550374D18089,
	GUI_DrawTexture_mEA112F138EB225F3722CFF9338DB4D14AAC8C7E6,
	GUI_DrawTexture_mB96DA06923A0D6550CDBA2355A0BC11F27EEFD4F,
	GUI_DrawTexture_m05F173ECE6F2D78FFBDEB0052EB58AC2B5A7420E,
	GUI_DrawTexture_m7D84EE8CAF70725A19D590937A31B4592851A8AC,
	GUI_DrawTexture_m99A2F5B82FFEAF2A4E9B86DF603250E10C437578,
	GUI_DrawTexture_m4B5BA823336A0967617C3181FDA1CDE98D309787,
	GUI_DrawTexture_m71F10AF46E994867905A8D57D2AC61A6576AA8E8,
	GUI_DrawTexture_m61984C14D59E7AA6BD4A1C7F0390B8E3D6913A1B,
	GUI_DrawTexture_m5062DEAA07FC3FF9E4F6FDB27D6C7715E1D3AEE3,
	GUI_CalculateScaledTextureRects_mE981CDC4935FAFD23FE22405CF11AC79DEFBF55A,
	GUI_Box_mB47BC44807774B77DB8B2BB548D339036544ACC4,
	GUI_Box_mA7CD625644B152E88F757E4B51F12A827BE49E57,
	GUI_Box_m4A53BAE78DC7C6724F50E54D9BEB7135BAA0DA0C,
	GUI_Button_m26D18B144D3116398B9E9BECB0C4014F57DBE44B,
	GUI_Button_m62135816B7F4BB45759E10E953926E4E0F24D370,
	GUI_Button_m4312FCE2CF33BCC9E4797A0CB27C0B158AF203A7,
	GUI_Button_mC05C634998E83DB614858EC020F6A109AA782A93,
	GUI_Button_mF539BB7C1C7D6C46E457F9A830A637D3D2EFDAF6,
	GUI_PasswordFieldGetStrToShow_mDAE02834B02CF040DCD75BDBEF4EAAFDD4291CF9,
	GUI_DoTextField_m0F51DFB1AA3D2DA7C8AA610CD944C2A6F8A4EF60,
	GUI_DoTextField_mF1F1ACEEB9D68CD13475D22EFC59592AC98E342C,
	GUI_DoTextField_m65E9044C469743DABEFF0D45BF55D5E600F6B6B5,
	GUI_HandleTextFieldEventForTouchscreen_mDA421BF6B3AA52547D0EE7F7CB9DF615ED49886C,
	GUI_HandleTextFieldEventForDesktop_mFCEC83B6B86B5A510181961406E4578F1B3CA268,
	GUI_HandleTextFieldEventForDesktopWithForcedKeyboard_mC5D580E669A02A678D31F4A38DEC1656303B8BBA,
	GUI_DoControl_m2E99A053EADA967772D440EDDC745562BDC848D9,
	GUI_DoLabel_mE43FD8B17DE5AF3B9E12E15B548CD6846F4AF27F,
	GUI_DoButton_m6B5D49C56FD43B570B43D9500AC5AFDE0533E99D,
	GUI_HorizontalSlider_mEED3CE859B1AF830E1851B3625AE127B4E6C5408,
	GUI_Slider_mBFB296D4CDB39B49C191A93E00A0C5AD0255CCB5,
	GUI_BeginGroup_mC984853EB22E39DD58DA1FDC3A6A8BB034B811C7,
	GUI_BeginGroup_mAE18B263BF701C0C2DC412BB3F526BD147142241,
	GUI_EndGroup_mE8C7A3FB87B0EAA3556AB16466D0D640BBEE1675,
	GUI_get_scrollViewStates_m940A384A713B8A7DC67016D1588965A42E561773,
	GUI_CallWindowDelegate_m3FC075A6C33D007CBDC6983CDD6515C246E35B3F,
	GUI_get_color_Injected_m7B9A31188627647FDD914FB8A83C32627769D1CA,
	GUI_set_color_Injected_mF82410FC38D4C12CEC8ADCC9CCCC00F12035CA12,
	GUI_get_backgroundColor_Injected_m81488D0D17EB867EEA60685182EAD8E0BC7CFB1F,
	GUI_set_backgroundColor_Injected_m16FDF89F7678824BA547AEF70D4EC84615C7D6B8,
	GUI_get_contentColor_Injected_mA592670CB3A23833ED6F6FA43D021CA049CB6FAC,
	GUI_set_contentColor_Injected_mE1EFDCAC30FF6CE60437BF1B8B04488C9A75E2C9,
	WindowFunction__ctor_m31D7B6C221D9A078AE5C8BA7C3BC0FA406EA7B71,
	WindowFunction_Invoke_m27ADD2F0F97D0149CE0B6F6452B3C23229D2CC85,
	GUIClip_get_visibleRect_m93F10FF2376C3BBBF3562A67DD7E207240D2F611,
	GUIClip_Internal_Push_m76819FE03A5080169157BA25B9182ADFDE3905F4,
	GUIClip_Internal_Pop_m99B82F9D059E587FD37DEEB41385076E16162E62,
	GUIClip_Internal_GetCount_m83C187F97642C73B9241C9A026CDA89A7A9EB8D1,
	GUIClip_UnclipToWindow_Vector2_m40F9564F56FD494A8DC03C4A815C41BA405B5884,
	GUIClip_GetMatrix_mABFDC4C3D2B71C84191EAA109A4373A1D75BD3E1,
	GUIClip_SetMatrix_m2C4B22CA0D33E580CBD455CC8E5422C8FF229733,
	GUIClip_Internal_PushParentClip_mDA817B810C6724A0F236C876C08CFB0EC64E78A8,
	GUIClip_Internal_PushParentClip_mEAE43F73F48A4CD59FD9432B4F1E50124A0F3522,
	GUIClip_Internal_PopParentClip_m7B43C8DD6186703019A5B7ADDC1FE48FB67BDEFA,
	GUIClip_Push_m78FFAE57A3F299C27A410834C1BE23539E284A60,
	GUIClip_UnclipToWindow_mBAA52A9D4A4EA1EC7720E7064CAC9E6B75C524D8,
	GUIClip_get_visibleRect_Injected_mBF3F116B530BCD6D5B3A5D110245691ADD4AA8BC,
	GUIClip_Internal_Push_Injected_m6BED9A38DF28718CE1059CC94E60269777410BBC,
	GUIClip_UnclipToWindow_Vector2_Injected_mB1FFA643AD2C4EACE9FABD41B32B094580D6BEEF,
	GUIClip_GetMatrix_Injected_mFAEC409FC44C49C7681DF684C954DF86AE076B76,
	GUIClip_SetMatrix_Injected_m259A180FE5871D9D16330959A560EAC86E0224D0,
	GUIClip_Internal_PushParentClip_Injected_m71572BEBE9BFFAA4D306958579D3B0B48411B87A,
	ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4,
	ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D,
	GUIContent_get_text_mC6D7981351923AD7F802AC659314BA56DF7F3ED6,
	GUIContent_set_text_m18A3EB5B4BD316561B3F4AB6BB3CC151684CE14F,
	GUIContent_set_image_mB91F27FCD27EDBA24794D52B7F3DF1CF4E878164,
	GUIContent_get_tooltip_mC2D07D7B2884A5F5A56F84A7FE6BF39905AB15BD,
	GUIContent_set_tooltip_m72C6B6EA0C9FCA1544A7FCF6C78A93E55D8CB415,
	GUIContent__ctor_m89AC53A7E9BF9EB9E70297353DEAA6FEC2C800AC,
	GUIContent__ctor_mD2BDF82C1E1F75DEEF36F2C8EDB60FFB49EE4DBC,
	GUIContent__ctor_m3FDFF98EA6ACDC116BCCA705EE8F8DEC09A4A0A7,
	GUIContent__ctor_m798E35DEED8E153FF39445EBEB634F896F19DF19,
	GUIContent_Temp_m4AE3B839AF38DD23ECC1D585C391E1CA43B8EA73,
	GUIContent_Temp_m9057415F4E0A4DFCD6B9FB2DBC10B4A515DF8444,
	GUIContent_ClearStaticCache_m36A399D55991F1B5B1C4A20DCDFF415B8636E934,
	GUIContent_ToString_m9F42CA1D8DEFB446686D0010FF57B4F9B140BB9A,
	GUIContent__cctor_m1605F6A12B7BD089F1592F490DBF324ECEC3FE8C,
	GUILayout_Label_m1709C16A433383CCFC1FEA0E585E14CBD78CD94B,
	GUILayout_DoLabel_mB3819CFC26697E2721B76B03E8A6382C3BD0B572,
	GUILayout_Button_m8CF27DB531C6A54FF0F7BD8CDE4FB5030B159E9E,
	GUILayout_DoButton_m5C440656DA589BD96635D6BB2D25D441EC1FA13B,
	GUILayout_TextField_mBDF7048805EC1B18C633CD17A7E071D26542288E,
	GUILayout_DoTextField_mCF612516F5261A38CAB5597109B4F0D24A801711,
	GUILayout_Space_m9254FBF173F9260DDB6C83C0066447FC9D9CA597,
	GUILayout_BeginHorizontal_m1BBD7EE29640BF48BED72DE582702809E6B22219,
	GUILayout_BeginHorizontal_m345DE4B807BD1719D6D4D99697E583F2706830C6,
	GUILayout_EndHorizontal_m694C622FEE40FFF0DD77EFFD026F899C193507C0,
	GUILayout_BeginVertical_m1B2B474EF5A3CD257EC0474D17F78A149DC14D88,
	GUILayout_BeginVertical_m48E21301AC4363B5FCDFFC654269E81C7C6E1204,
	GUILayout_BeginVertical_m011D5556BD812DFF1620BFC648566CF89A09FD68,
	GUILayout_EndVertical_m2D981AC3EA3F7273CBFEE6C30C14DC90044AE87C,
	GUILayout_BeginArea_m4D894562C97A0F6793450A0DF379B63F60121F64,
	GUILayout_BeginArea_m6242362D4059133D9F749763CFAB63A2B61D8B77,
	GUILayout_EndArea_m3A9C6B4D373E8A871A71E0D8D2D9249D9F62F079,
	GUILayout_Width_m3FADF145F37481F9FEFF0E89E8A466CF5532DCE3,
	GUILayout_MaxWidth_mC9E5A7877E53216E4DD1029BC6AF6D36255F1CE3,
	GUILayout_Height_m5E1526C541663A21437ED06E233FDDA08A856B91,
	GUILayoutOption__ctor_m4EF826EA43073869166C8D94A1D9EB7898ACC3AA,
	GUILayoutUtility_Internal_GetWindowRect_m4F0CEA512EAD2BF0BBA0218A10B9C820C24D44CE,
	GUILayoutUtility_Internal_MoveWindow_mAD1ECDE72F3573D2F71B43C5FB8F90C10919C6CF,
	GUILayoutUtility_get_unbalancedgroupscount_mF902A4C45A4817D8D05761E14033AC66BED4D2B5,
	GUILayoutUtility_set_unbalancedgroupscount_mD2509D685C0DDB9ED9C800F34C0EAC0843C8C71B,
	GUILayoutUtility_GetLayoutCache_m6086C9523E16ECC3FF4613F5A2441351CF4B2878,
	GUILayoutUtility_SelectIDList_m601F4AA990B7FD59A779F5375EC55ADDB86927A9,
	GUILayoutUtility_RemoveSelectedIdList_m701496086D15B8BF1C936EB431AFBC6627A787FD,
	GUILayoutUtility_Begin_m701551F1F833A31A154BFFC9F6F3143A12A33061,
	GUILayoutUtility_BeginContainer_m34C50FF74C76B91E32E1A3575ABC0AA0AE0F3DDB,
	GUILayoutUtility_BeginWindow_m99FBC28B305B9C0589BC73138073BE9420C977F5,
	GUILayoutUtility_Layout_mBC6C938DC931B8CABC1FA6C33AA60ECFAC3D9B30,
	GUILayoutUtility_LayoutFromEditorWindow_m0D41A3D7897D91D4420C722C47502FCBA0352804,
	GUILayoutUtility_LayoutFromContainer_m81EC681FE0A88C36CCA8D4382043279F709EE59E,
	GUILayoutUtility_LayoutFreeGroup_m81D18A1401F6FF7EB4A3C1CC26D9BE80998BBF5C,
	GUILayoutUtility_LayoutSingleGroup_m95E3F31426ACA641C57016A1D1A058366A56AFE8,
	GUILayoutUtility_CreateGUILayoutGroupInstanceOfType_m2F53981EB9DD3E591F4CD4AF4F6B6E9237E58F0A,
	GUILayoutUtility_BeginLayoutGroup_mE922EB7B3D05474F6259C238B7A8C2FFFE1DBC43,
	GUILayoutUtility_EndLayoutGroup_m62EDDF35442EC5CE39A4DC11740B87DD45454C26,
	GUILayoutUtility_BeginLayoutArea_mE8FF1BD8DA08B1F5D35F89B844E5FC05D10D40E1,
	GUILayoutUtility_EndLayoutArea_mEB29D017C9B83031072AB9FB1D6BD0AFCE12F57C,
	GUILayoutUtility_GetRect_mD3E98D37BF22AD8CF97D7B607E7F11125C9A558A,
	GUILayoutUtility_DoGetRect_m5149CE27EE6C137479B6AE56A416CDC270A4E6EC,
	GUILayoutUtility_GetRect_mA52720BB9E74AEE8ABD39D20AD3CA1C23A9FE4DA,
	GUILayoutUtility_DoGetRect_m331109591122F160FEB7AFC177DB18DDB44D8C59,
	GUILayoutUtility_get_spaceStyle_mC006DE36340B02EF0764873122DB9FAE793F7765,
	GUILayoutUtility__cctor_m2CEDA9A8EB23B7D3A5A97825E6B192235954DC48,
	GUILayoutUtility_Internal_GetWindowRect_Injected_m03328FF57858A53621C5907B345C56FA2C5AF0EC,
	GUILayoutUtility_Internal_MoveWindow_Injected_mDFDA2042DAFBDEBD108AC01F6F19E7D0F395B6A7,
	LayoutCache_set_id_m532720FF0F65E8039E37D015910E2F1AE1C9F4FB,
	LayoutCache__ctor_m73B4DC62A0A7669976C8444DDB54EF8D55BF3E0B,
	LayoutCache_ResetCursor_m728841782E13F82B1AE96E40AF16D6C8EBE6D59A,
	GUISettings_Internal_GetCursorFlashSpeed_m5A85ED55BF5C4CF8EA0364EFB7279983EC098B92,
	GUISettings_get_doubleClickSelectsWord_mC92A7F42A08C2C3477E95603B2EED4619B9E3290,
	GUISettings_get_tripleClickSelectsLine_m9F5A93582F9BA64AA475A2BEB353A5D5AA7BCEED,
	GUISettings_get_cursorColor_m490EB7B6029BD2C4CD22F64DEF0B6C08618D7830,
	GUISettings_get_cursorFlashSpeed_mC999C2599D804B274C8528F2414C4346A9A9D673,
	GUISettings_get_selectionColor_mDF24274364732088DE57D112F91EAC70233D6EE5,
	GUISettings__ctor_m4AA9AFBD94306E007937909CB7F542DF2E491404,
	GUISkin__ctor_mAA94A46B37D9C2F70962435F250BBA202CD1EC7A,
	GUISkin_OnEnable_m5A7FE1F57C549711FCCC2DB0322F8667129AA0BF,
	GUISkin_CleanupRoots_mAD2E77BE9440832E8BC8CAA9C7F2D85C3D2F8B17,
	GUISkin_get_font_m806CF702C59E43DF55BA441030A60F80E9D8CFD5,
	GUISkin_set_font_mF98516DE4363C888D7215006D51BD527F3F9DDA9,
	GUISkin_get_box_m21BE7FC56D903B95BAFAE8890425D330EA88D893,
	GUISkin_set_box_m82E578044569D3831D103FFA1413D81DABF74711,
	GUISkin_get_label_m99E1A8D6D8592F88F581437D24DB1EDE05C63E5E,
	GUISkin_set_label_m7E9E63BBA37F93D886F7E6AF70772ECD7894462B,
	GUISkin_get_textField_mC554496BAB959445F0CFA30BDC5736DC1F057D48,
	GUISkin_set_textField_m4730F5B544F2A87AF3CA75A01FE845E5D40E06BE,
	GUISkin_get_textArea_m0ECBC9D126D930490F96E100B27F245E555EB7D1,
	GUISkin_set_textArea_m916CC2135EE608D81035D3E96787735534DF4E9D,
	GUISkin_get_button_m51948EBD478CF9223522AD29B7FBD1BABAABE289,
	GUISkin_set_button_m45F7F5CBF3E9286F4CD601AA92C0C3207C0BB373,
	GUISkin_get_toggle_mD5F318C602494C478F09C2D48741EC7A9CF5B849,
	GUISkin_set_toggle_mFE0DA0EC1F1952464B85894CCCFECFA5E0E0C57E,
	GUISkin_get_window_m760DAF129E72775DFD18CB71720AD306345E91C2,
	GUISkin_set_window_mA74900E5D554578F3F45DD858B79C5A8FA4A6220,
	GUISkin_get_horizontalSlider_mAA1753FEEDBA6E28A3A56C3E44A8F5B3D6C8336B,
	GUISkin_set_horizontalSlider_m8357A90F358C1A040308C8D0DEE363D3ABA71575,
	GUISkin_get_horizontalSliderThumb_m9EE5EF8204397C2946D7F384AB7D8A17693837BD,
	GUISkin_set_horizontalSliderThumb_m1042BED23F10E28042D77D7E738F86C1FEDF460E,
	GUISkin_get_horizontalSliderThumbExtent_m6408F303B8932D6E74B307070689A96EA082D612,
	GUISkin_set_horizontalSliderThumbExtent_m8F4C637DB7E25697AB463B9F2F8D50D8493609C1,
	GUISkin_get_sliderMixed_mFD8CBA8BE229E299D63822AE3E632DABCC27FF61,
	GUISkin_set_sliderMixed_m8A129FB05FAA0970C01A8C3DB14903E13F8E37B3,
	GUISkin_get_verticalSlider_mB7EC86D11019F1892365E9C6F2A846A68879BBD5,
	GUISkin_set_verticalSlider_m02D94C0BFF867BD8B1ECE05AB50F7F2475DF0E35,
	GUISkin_get_verticalSliderThumb_m3D86347FFC94841C8B6CA94F9F946C76E96EBADB,
	GUISkin_set_verticalSliderThumb_mFBFA636B05068A0E7D24C8C3B06044AB2ACD4C58,
	GUISkin_get_verticalSliderThumbExtent_m299DED8D10A1CE0F22B43BAF47D70DA1EB079AFA,
	GUISkin_set_verticalSliderThumbExtent_m3ECC754FC08BCFA5C3264A6B83C9EE280C1EFCDD,
	GUISkin_get_horizontalScrollbar_m945A39FBD098D3800A189FC34B9CE9E8AFF3AEEA,
	GUISkin_set_horizontalScrollbar_mF08764A78F23728E6FE157F08B9A0127157071FA,
	GUISkin_get_horizontalScrollbarThumb_m5011EED1650028044BCC7F6DE2829AC0243208BB,
	GUISkin_set_horizontalScrollbarThumb_mDDADEFFD5BF9B88AC4A37AEA13B6FCCC28A3F696,
	GUISkin_get_horizontalScrollbarLeftButton_m4A6E58CF80A66F58CF5792B31D08A2D74BF40567,
	GUISkin_set_horizontalScrollbarLeftButton_m3FDB02C1FDE47BCE92068EA21C531F1F6D667DBC,
	GUISkin_get_horizontalScrollbarRightButton_mADFCABC3339BE56E2BAD5443789D8D4FBDD73DAC,
	GUISkin_set_horizontalScrollbarRightButton_mE5ED9D2BB554FC29F6A69C81B9361A5E6E004CFD,
	GUISkin_get_verticalScrollbar_m600012E344D3EB4C687E8A4BE78CE33068374D2A,
	GUISkin_set_verticalScrollbar_m4F55D5B66DB408A5009FC00ABBB9AFFA0C65FFEC,
	GUISkin_get_verticalScrollbarThumb_m62663C3DDC40AC91FD4666FBF844DCD83DDA7DE6,
	GUISkin_set_verticalScrollbarThumb_mECEC0DC79CCD9AABBF6CBA3CE5141C38699B5EC6,
	GUISkin_get_verticalScrollbarUpButton_m0B5575CA6AFB1C74899BF931296EFC39B2C1A902,
	GUISkin_set_verticalScrollbarUpButton_mF50F99BC770529789363EC9B1C37E610FF8A708C,
	GUISkin_get_verticalScrollbarDownButton_mFC75161EDB03597ECF09E189C8A57F0E64213E3D,
	GUISkin_set_verticalScrollbarDownButton_m37DD0E232BB98BD219494A297DDBE7620104D328,
	GUISkin_get_scrollView_m5466CD77A4A7E01320DB0E0F57253D41226BB0B8,
	GUISkin_set_scrollView_mF2D35906BC020D81F909E65B420494F254E4DC32,
	GUISkin_get_customStyles_mAC8A1CFD5756E6C0D367E06B4BDC365E6F6BC39B,
	GUISkin_set_customStyles_mD22F50472DDB0A9770B18F0A15D3F73EEEC4A8B2,
	GUISkin_get_settings_mCBAE5727D7774FAEE47CCC8B4C47AC321DDD85C2,
	GUISkin_get_error_mB953A37C8F3296E529190A34E18506C735848C01,
	GUISkin_Apply_mA85017BE8C994F6220112EE8D00D3C37C1FF2104,
	GUISkin_BuildStyleCache_m8E99CC278C76A6DA63A24BFD2DE42AE313C0F7E1,
	GUISkin_GetStyle_mF024BC5177A2AD477ACF44D87BE6A629C91562CA,
	GUISkin_FindStyle_mF82C37E2481D2B9E96C26EFE0353F8954F844FFE,
	GUISkin_MakeCurrent_mDB3BB1FBA5BD2FEDDA3F32F11170F47A6444AEED,
	GUISkin_GetEnumerator_mEC308E2DA9A94E09C622D13F82EB7ECCECF8AFF0,
	SkinChangedDelegate__ctor_m20D33B3868351B98B708468F7A8192C1ACF85CD1,
	SkinChangedDelegate_Invoke_mD14214487F9A0E4DD7EB7F97927D03EC8F1A3B4C,
	GUIStateObjects_GetStateObject_m2BB2858A7B432322CF1ECA3940D85BF809ADA4EC,
	GUIStateObjects__cctor_mDD043ED73553AD955E20FFD43933D1D124D6D3D1,
	GUIStyleState_set_textColor_m5868D12858E6402247953BCCDDA7A543BE6084F1,
	GUIStyleState_Init_m0D3428E2BA3343F8AC49253DE3AAC54EF07F4873,
	GUIStyleState_Cleanup_mF244B2DAEE9DE90A300E6B7D78F9547BBBE59826,
	GUIStyleState__ctor_mD47FE21F7FD8D786F7E8E4E8C3DCA224F9237AD7,
	GUIStyleState__ctor_m74536B867B0F57F8A7DC74E78018830A948E4555,
	GUIStyleState_GetGUIStyleState_m0B273F7909166249E3D98FA410C2D8A72091C7B1,
	GUIStyleState_Finalize_m5CC6FBD8C44AF1091CACD6F7032E73B1114765B2,
	GUIStyleState_set_textColor_Injected_m2E95B96544D89BEC498DF24CB036903535EA8184,
	GUIStyle_get_rawName_m9C87EB1EA6CC5989EFF3567E85A2D0A3DF256782,
	GUIStyle_set_rawName_mF8928B91294B5DA15AF365C760BB1437CF507ED6,
	GUIStyle_get_font_mBD123E375D357B37F8E1303F288517FD883C1117,
	GUIStyle_get_imagePosition_m339AA340B169230E9795B61BEE4BB1EDAD6D95B4,
	GUIStyle_set_alignment_mEDC62A775C9551DBD1FEE4043F115E034EF12937,
	GUIStyle_get_wordWrap_mD0046078E78B0F8F1988E055B7EEB261FE8C69AD,
	GUIStyle_get_contentOffset_m1585F4928435114551C27889DE159EEF55345C70,
	GUIStyle_set_contentOffset_m550A9D4B3FA491B320F4B71F14C45A5785B18F24,
	GUIStyle_get_fixedWidth_m9CB5B4E096287F75F4E4E3376590C7C085E28DE8,
	GUIStyle_get_fixedHeight_m009155CF284509A87E6037D0A392A630FA728F7A,
	GUIStyle_get_stretchWidth_m528FFD3EB3104D0322F2EADBBE7DBFF3FB34CB37,
	GUIStyle_set_stretchWidth_mA1880C36240B34EBAD585544CCE73224DA0B6A78,
	GUIStyle_get_stretchHeight_m5ACA8F9CD25746932719C927159A105AADA5061F,
	GUIStyle_set_stretchHeight_m51C55ED43AA4EDE125E0C423FA0D301E81C09378,
	GUIStyle_set_fontSize_m7F6DFD61AC55072C95DC3825B77FAE3F75F1CCFF,
	GUIStyle_set_fontStyle_m4166D61FBF25225F4A85BBEABCECE3F2DCEE714D,
	GUIStyle_set_Internal_clipOffset_mEAB08E971FED348348EEBACC2D4439473B930B9E,
	GUIStyle_Internal_Create_m2C5F872F6FE8C423759017DC72267D6AF637BC75,
	GUIStyle_Internal_Destroy_mD93F2F454B69DB5C534AF9F4F6D847F955A39977,
	GUIStyle_GetStyleStatePtr_m60D51351B040299578007102C3857E8E8F14FAFB,
	GUIStyle_GetRectOffsetPtr_mCABE2CEFE5CDB942D464051BF8B0E043BCC59593,
	GUIStyle_Internal_GetLineHeight_m3A90D425C25B10618B8A3D95AEF72FCB1C574B07,
	GUIStyle_Internal_Draw_mBEFC164F21949135F404FDA678F368FBA8074D50,
	GUIStyle_Internal_Draw2_mD1050A7750AAAEEEEFD4EB6C8C8AFB0591B1221D,
	GUIStyle_Internal_DrawCursor_m016FAD8A76AF5146B0C684E73EDF61F0709FFAC8,
	GUIStyle_Internal_DrawWithTextSelection_m446FACAD8160564B23C5080C0BB3103048F83878,
	GUIStyle_Internal_GetCursorPixelPosition_m22C4D9AA182990942EA85B0EA834499EFA0CB0C4,
	GUIStyle_Internal_GetCursorStringIndex_m88FFC09FCA6FD081C34ADC01F899D435AEFA2CC4,
	GUIStyle_Internal_GetSelectedRenderedText_m3F9EF55E4958D2C9DE62AC723DBC99EBB80DD002,
	GUIStyle_Internal_CalcSize_m6B1D90CF09404B4969678627BE86D43B41C5AF33,
	GUIStyle_Internal_CalcSizeWithConstraints_m555CBD08EA22A9CB84A16BB8BEF95E8D25BF2617,
	GUIStyle_Internal_CalcHeight_m12AD4C5012F9E237FAB309CC6C84D3CB9145FF76,
	GUIStyle_Internal_CalcMinMaxWidth_mB0D00D2D7454F733458F3729E35FF22CE9FEDC58,
	GUIStyle_SetMouseTooltip_mFF3E22C7330AE180E83AB2929049BCD87B13B21E,
	GUIStyle_IsTooltipActive_mAD93F97B98889CA47BF1305F3D4C87D5EE8DD777,
	GUIStyle_Internal_GetCursorFlashOffset_m0CBC61945E3C828B42CE4651D5D04142E0CA7428,
	GUIStyle_SetDefaultFont_mD6B98375749805CA5084CA8C5D6A1295359AE0E3,
	GUIStyle__ctor_mE15E33802C5A2EA787E445A6D424813E1D5B75A9,
	GUIStyle_Finalize_mFF6A6FBA538B711A6ED369DD83A41F25DE6EEE85,
	GUIStyle_get_name_mDF9EF43C46A0B9431DAF4EB0CE1D18EA32E16B75,
	GUIStyle_set_name_mE618266DC07236117AAE05FE8D2B14A595FCF020,
	GUIStyle_get_normal_mDEA2808FBD692E505784BD9E521738B4321BCA8F,
	GUIStyle_get_margin_mD0AABA2CB3FB0CFC3C414635E6225D3003315D1B,
	GUIStyle_get_padding_m04E3210A51B2522158941AFA97ADC19C835987C2,
	GUIStyle_get_lineHeight_mC814199D1ABA3CE38358BA70347562B0CDFEB96E,
	GUIStyle_Draw_m7B978F5F5B576810CF8546142D23FD9990E002D8,
	GUIStyle_Draw_mA81B01AC68DF7F6948228AFA68A7126E838E49E9,
	GUIStyle_Draw_m115BA19F2B86B0390964C5E0172BDF8970374BF9,
	GUIStyle_Draw_mACFC9CE57BD530BB6A9592149DD95108A8014406,
	GUIStyle_Draw_m3DBF8DC58719720455DFC818590D77752BA31008,
	GUIStyle_DrawCursor_m6E56AA2C98FD6C2BE93F723BCAFC853D2C5E2B71,
	GUIStyle_DrawWithTextSelection_m1F04940E605F072A86832E733D7BCD05077F0E71,
	GUIStyle_DrawWithTextSelection_mC449ECFF67627B189F742FC7320ADFD5F7A9A540,
	GUIStyle_DrawWithTextSelection_m7BFF3F02BD2B23164CF98DBD4E5FECB51283215E,
	GUIStyle_op_Implicit_m18FEE416A9FA577B09D98D80408008F34A5D9FE5,
	GUIStyle_get_none_m808A9FE1F78920E4A29ED3484B99588B46D88938,
	GUIStyle_GetCursorPixelPosition_m4FFBD3DC05CE503355DF01E57023AC349032CB2F,
	GUIStyle_GetCursorStringIndex_m9EFA2EC2CF6ACB5B4EAF3E9C4BC356980CBB4515,
	GUIStyle_CalcSize_m3015BAC288A5D6D29C0596ECE8117C8F9DFF9A76,
	GUIStyle_CalcSizeWithConstraints_m01ED0E843908709C7A316B83E4E10ABCECF1A8B1,
	GUIStyle_CalcHeight_m57DA8F6020AE71B561ABCBCE74E0E58FD2ECC5E8,
	GUIStyle_get_isHeightDependantOnWidth_mE18B09D8CD496F15F0EAB224020017BFF48065AF,
	GUIStyle_CalcMinMaxWidth_m6BBF836B9A9B2B4BA11DC448B03E441DEDC2CCA4,
	GUIStyle_ToString_m41A8A58B4D9659047D06EF2A5AE5F170AE198ACF,
	GUIStyle__cctor_m4B955524A4DAEAAF103D78D9316756CEFA16FB62,
	GUIStyle_get_contentOffset_Injected_mA08F10C2DF4530E0FB902E36AE327C1452686C9D,
	GUIStyle_set_contentOffset_Injected_mB71B7952884AEC19A3946ACD4F3601F07A5533F4,
	GUIStyle_set_Internal_clipOffset_Injected_m0698799EBCFD9347BBC5D95FC9D15D8B17920709,
	GUIStyle_Internal_Draw_Injected_mF4A2332005788106B28CB306FAFF530BE251E09B,
	GUIStyle_Internal_Draw2_Injected_m83867C172C18ED83724AA6600EDE59C55277A138,
	GUIStyle_Internal_DrawCursor_Injected_m45E2755EF5415F0A401CE8A911364CB853BCA96F,
	GUIStyle_Internal_DrawWithTextSelection_Injected_mA31596E612AD06214B8E04210A969CC23F8E084F,
	GUIStyle_Internal_GetCursorPixelPosition_Injected_m9B676ED0A70FE6CC55EC8795CD7348406A4FD815,
	GUIStyle_Internal_GetCursorStringIndex_Injected_m4C0A3DEF8B90D9A866378C24BB3F7E0169CA12C9,
	GUIStyle_Internal_GetSelectedRenderedText_Injected_m1DE280FF00B670FB04D98786E87A7F18D72940E3,
	GUIStyle_Internal_CalcSize_Injected_m19617B2C5FF35B1B10B9D31058ABC1EABD31FF48,
	GUIStyle_Internal_CalcSizeWithConstraints_Injected_m0BAB7504DC082EAC9C5664166BD8B1DA3DEC0025,
	GUIStyle_Internal_CalcMinMaxWidth_Injected_mEBCFBA4C8E76B115712AA308250F70CEACF1B844,
	GUIStyle_SetMouseTooltip_Injected_m77EC0702533B68489605E0DE76A6761E1253CC71,
	GUITargetAttribute_GetGUITargetAttrValue_mD0E7A4A7147F6B97077284408283EA380FE040B4,
	GUIUtility_get_pixelsPerPoint_m13E69FE793E736FA60A61C6756F2FF57BA6C9F31,
	GUIUtility_get_guiDepth_m011B188F7C41DAE079019E64BC064208E618F315,
	GUIUtility_set_mouseUsed_mBD1FB685EF080F233A16BF558CE4703E68621E1C,
	GUIUtility_get_textFieldInput_mDB514BD41982E9A309A7E0297270162FA6918EBA,
	GUIUtility_set_textFieldInput_m3CDCEAC85B562998D5CBAF7ABF6EC1C92E9B1EEB,
	GUIUtility_get_systemCopyBuffer_m01E2DF71533C31A4C552B9177D7CBA0C6CA3FC2A,
	GUIUtility_set_systemCopyBuffer_mD14AE32BFEA4773BDC679205D470A228B8F225E8,
	GUIUtility_Internal_GetControlID_m9836A3FD9B0629A36F356FD8D4606092B2E2AD21,
	GUIUtility_GetControlID_m3AACC1B4BDE62E7C3E5D861A470351FA1BAA752E,
	GUIUtility_BeginContainerFromOwner_mA895E862C2444F93423836CE4B5F35E2F31B8B28,
	GUIUtility_BeginContainer_m4A0F355072CE2DBCB50F706885EAAB70DB8C7115,
	GUIUtility_Internal_EndContainer_mCE42BC4D58E684B724B58EC3C901E67BA62F1BF7,
	GUIUtility_CheckForTabEvent_m6AC98E67A89330ACB330CBBC135E3DFBFCAC2C49,
	GUIUtility_SetKeyboardControlToFirstControlId_m02DF215A0F07822021E17AF4153B4C31468287C0,
	GUIUtility_SetKeyboardControlToLastControlId_mB7A3C208ADDF009FB9C3C522998459BCD9B107EB,
	GUIUtility_HasFocusableControls_mE149711C5695D4DB44940D8073487992F1ACB883,
	GUIUtility_OwnsId_m46FE01F2CEF3A94173A1DB64A888E4DB1EBC74D2,
	GUIUtility_AlignRectToDevice_mE651D8C8024AD7FF9C1773FA000A2626BC263B8C,
	GUIUtility_get_compositionString_mE06412C5CE41311C00BFC4028716D5F03EDD85E9,
	GUIUtility_set_imeCompositionMode_mE5C0A2391D65DAC056B1752D78B5A832DCB314C7,
	GUIUtility_set_compositionCursorPos_mECE1139A5660FFE152382DAB2DDBFADB96BB9644,
	GUIUtility_Internal_GetHotControl_m8230315B3FECDB164C84AFC40C180C2C7B319892,
	GUIUtility_Internal_GetKeyboardControl_mD0783552D4ACDA842F86F126C7A48ADC79340AB8,
	GUIUtility_Internal_SetHotControl_m56F3F333B107EFD83C7F3D703DDA48C5A19BFCB8,
	GUIUtility_Internal_SetKeyboardControl_mC8401D9C911D310EAA2284161264D2FC9D141418,
	GUIUtility_Internal_GetDefaultSkin_m86F21D22A34DC2243194B8929A499FD98D26A234,
	GUIUtility_Internal_ExitGUI_m5B145534F61B8CE2A2915A9297D0F25D771D4459,
	GUIUtility_MarkGUIChanged_m43158D22AA065483FD91222B898772AEC06809A1,
	GUIUtility_GetControlID_m4A403579ECC04C9F13A317078B55A30B366D77A4,
	GUIUtility_GetControlID_m2E0F66C8714A84DD5E9BEF4B9B464DAF1C03A9F7,
	GUIUtility_GetStateObject_m803B56DC7DF5396751B7367DB04072A6DCEEE616,
	GUIUtility_set_guiIsExiting_m0DCDD09CD48330FD781C03D2EA20F973878A2BC5,
	GUIUtility_get_hotControl_m6CD6AD33B46A9AFF2261E2C352DC7BAB4C20B026,
	GUIUtility_set_hotControl_mFBC648186C83874DE776A508C420183ADB527E9A,
	GUIUtility_TakeCapture_mD8AB4A480269628E17877B77A94A6481EFC9763C,
	GUIUtility_RemoveCapture_m295E1BC4B7E1D471AF7C40E3B587B7D525E3D693,
	GUIUtility_get_keyboardControl_mB0FAC848390B7F163CD2EE0A911FADD5CAD70B1E,
	GUIUtility_set_keyboardControl_m10F53FE5B292C2DC3C9A55CB504CC0DF36139465,
	GUIUtility_HasKeyFocus_m6AD234443A7B2AB471E14BE141FC5E8ADD261A0F,
	GUIUtility_ExitGUI_m9B30B2DFC94CC1C04D1F78358D79E9DAC1231B03,
	GUIUtility_GetDefaultSkin_m3275F31A9D5C3D90A1BCF5135F5B3968D6CD2C33,
	GUIUtility_ProcessEvent_m88640934E0C2BFA9BAC544DD2A91112FE8227FE2,
	GUIUtility_EndContainer_m19D0D5BA46EDAD7AF2D408A34D0141C5E481D963,
	GUIUtility_BeginGUI_m05702C560EBBC0B0CA3AD4F1FFBB5BD070DA2E04,
	GUIUtility_DestroyGUI_m5F94109C61BC0394F8936C899273093A6008702C,
	GUIUtility_EndGUI_mB34E82D4DD7A0AD22012DBAC207F605A68EA5E2E,
	GUIUtility_EndGUIFromException_m9C8B34B811C1E32C1BC818A57817FF5E117EC1B0,
	GUIUtility_EndContainerGUIFromException_mC60505F763292A2C80F7FBC0644F3B4679414DEB,
	GUIUtility_ResetGlobalState_mD0A482A31337B6200F644995345CF56849913928,
	GUIUtility_IsExitGUIException_mB887DAF961E8C1124916777B812FBF2324F5265F,
	GUIUtility_ShouldRethrowException_m60E879B4683840AAD5CD514E8C3BDDCC6403B652,
	GUIUtility_CheckOnGUI_mD167632D5D038DF66CC97F231CD45736D1F556D6,
	GUIUtility_RoundToPixelGrid_m0E594150154A6CCAD942F6B23179FB6886361105,
	GUIUtility_AlignRectToDevice_mE788EB722671F5DC10F7ADB8CA1A3427749ECDD1,
	GUIUtility_HitTest_m55D2F9EAC7EA99CA0C490546A6B45DA96F5AB3DA,
	GUIUtility_HitTest_m8C93A1BFB637176154C02F73038A98D1F616A7C2,
	GUIUtility_HitTest_m0312C850D991342F3A7656A959C87466500F2987,
	GUIUtility__cctor_mAA2103B5A88A5D093DB8AEDBE54C5E4757D84A87,
	GUIUtility_Internal_GetControlID_Injected_m00F0DDAB73176CDD6EB5F19AA64511CF445E1249,
	GUIUtility_AlignRectToDevice_Injected_mED42E3383D2A790E76602A5AB894DDE4850E43F1,
	GUIUtility_set_compositionCursorPos_Injected_mF035733A0EF9A0258AB44982286A8FFFBF2B09A6,
	ExitGUIException__ctor_m345D7AD70E401C1AFD46E537CDCEC0F1C8BA342B,
	ExitGUIException__ctor_mE93D467487F7F148547778DF06CF2BCD03472656,
	GUILayoutEntry_get_style_mEFB6A8443849EC32BD84059C09632B53E44A5876,
	GUILayoutEntry_set_style_m0A23F7EFF504A581FC6CA86EF3BE753F060AC48A,
	GUILayoutEntry_get_marginLeft_m3B362DA8241B4008C2A6CDA693295A609F765221,
	GUILayoutEntry_get_marginRight_m032808DC8C04B31150407F3F61E71865C2636D7F,
	GUILayoutEntry_get_marginTop_m47BAB82D31A45E21F9AAB8229265788C0D19487C,
	GUILayoutEntry_get_marginBottom_m2BCCF0FC72E0230E155E7A26BA9FFD904AD4C221,
	GUILayoutEntry_get_marginHorizontal_m9847FB7747542BB322195F9CF4B75F55339CD7B5,
	GUILayoutEntry_get_marginVertical_mCD309A186E80B22E75DD8F15D2598B9B739C7AD3,
	GUILayoutEntry__ctor_m011B3DA69713EEA6BD98D4056B5ADE01F237E5B2,
	GUILayoutEntry__ctor_m9E77958057210F340E409F42DFCEFEF8539A5547,
	GUILayoutEntry_CalcWidth_m77BB8C0413A27303E4E61CB53586FD4A825C5EF3,
	GUILayoutEntry_CalcHeight_m295D607AB2FDD78D7C665BB3FB3A495E2E8CC0A6,
	GUILayoutEntry_SetHorizontal_m268577E88A2AE5870C14EFDA9CB88C94CAC2ACE9,
	GUILayoutEntry_SetVertical_mA20893626441C55001C1940C53A6A100DD22D61F,
	GUILayoutEntry_ApplyStyleSettings_m2D3679DAF547D104FE48E7D6D8E27B639F6A666B,
	GUILayoutEntry_ApplyOptions_mF024E6CEAAD97888AE293810E01F8431D79456A3,
	GUILayoutEntry_ToString_mD3785AC5958EB56ECA6E5D325D166C5F5725E615,
	GUILayoutEntry__cctor_mF6F64749802F89E5AA0A1458CE99CA5FC0D639C2,
	GUIWordWrapSizer__ctor_m28C0BF2C7D0C5C71A47B8039DF939F954BD06785,
	GUIWordWrapSizer_CalcWidth_mEB7A01D9C3EE00953A4EBC3F4A2B9EFA2BC81552,
	GUIWordWrapSizer_CalcHeight_m8CD1B64A1632F1929EF0856E0BD091BAAEC411C4,
	GUILayoutGroup_get_marginLeft_m343D82AA90154850B9B2A97B9E471D5235761EB3,
	GUILayoutGroup_get_marginRight_m2710F9CCC1B6D67BC4F9D9487B082B7E143757D0,
	GUILayoutGroup_get_marginTop_mA61C984665E93EE9E8670753AF919208528C4F87,
	GUILayoutGroup_get_marginBottom_m1EC579493343750FB013A6F01AD84DFEC8D489BD,
	GUILayoutGroup__ctor_m2AA89FAB5BB5BA76F4059D106A59E346739755D8,
	GUILayoutGroup_ApplyOptions_mD4C0BFAC7A90FB32BC6DC99ECA3EEA6C1C9396D2,
	GUILayoutGroup_ApplyStyleSettings_m5A88CB0FC11FE81405684C3EFF7EF7DA974D2649,
	GUILayoutGroup_ResetCursor_m58C36F1ABC54BE5EFC16D512318BED9EB8918127,
	GUILayoutGroup_GetNext_m45FF6F2D555DE615B6C52335C68947898770EDC4,
	GUILayoutGroup_Add_mCE459B14C2B364DF4B78DF95D26254B4B5FADD1F,
	GUILayoutGroup_CalcWidth_mFA744462378028538F1E3AAB39CB6AF0FBB1851B,
	GUILayoutGroup_SetHorizontal_m37D01CDDE4FAEDB20E0D469805EF96B878DFB5D5,
	GUILayoutGroup_CalcHeight_mAA9676BD80BAFC48F515ACA00E83FB7E9EE1FC2A,
	GUILayoutGroup_SetVertical_m28ADC75A1C5148E22EDD149221535C4B97BC5FE2,
	GUILayoutGroup_ToString_m7859D80D5D81B23684C4309DA0565D4CE1D2680C,
	GUILayoutGroup__cctor_m9214FACB657F5C28173EDCF59DAD85F14E7E2800,
	GUIScrollGroup__ctor_m95351A883B27B71698A4B84815CEA687D109F3FB,
	GUIScrollGroup_CalcWidth_m6B927DBF94A8940301A9FB64190403E5667712CE,
	GUIScrollGroup_SetHorizontal_m31FCDD252E67D51FC954C8E2C358BA0EB3AD7601,
	GUIScrollGroup_CalcHeight_mCB0CEC4871F6540145949E4CE8242172A75B2E5F,
	GUIScrollGroup_SetVertical_m8609CD909413A7364781818DDE37A314D8795FD6,
	ObjectGUIState__ctor_mA9AB2887ABAF5102164545D7F0CE59BCF05618B4,
	ObjectGUIState_Dispose_m156DC13F33DEFB261C8B13EB98A1A3782D182DE8,
	ObjectGUIState_Finalize_m10310B7E07DB5215C7845BF0F770B587D4F4C1B8,
	ObjectGUIState_Destroy_m316F4C75D0C8F18896A69BB9E39D90C0CDBE8726,
	ObjectGUIState_Internal_Create_m22F3AED2A44D4D00B478C2626295D432F74383EA,
	ObjectGUIState_Internal_Destroy_m936A111D9F70932A3030FE851C9E3BD82FD1F425,
	ScrollViewState__ctor_m9619262C4C72300A8B26011F627C68DF67425E53,
	SliderState__ctor_m650A11534C71EF571FD631CC3E910B756A16889E,
	SliderHandler__ctor_mDCC4520E68A478FC28519FD5DB149EFFBB9C186A,
	SliderHandler_Handle_m9E9DBA6E45BA00A0D812C74BBB18DFBA0F923BFE,
	SliderHandler_OnMouseDown_m9DE4BD08EDF586656B93C94FDF4907D081BCA8B8,
	SliderHandler_OnMouseDrag_mC985CE7801C44CC87A1B11ABF0E142E617356B9E,
	SliderHandler_OnMouseUp_m97A5FDF8EBADFE60EC0DE486127A7F4B9C7068DB,
	SliderHandler_OnRepaint_m5FAFC71FF2A246AC8336B0F400F35DF581AD54AF,
	SliderHandler_CurrentEventType_mC4756C3A92FC488ACF0C0148CFFA0F0239622F2A,
	SliderHandler_CurrentScrollTroughSide_m5E30388E1DE3C01FB968EFCC02215C14F301E1A2,
	SliderHandler_IsEmptySlider_m8AD3CF68C310AEAAB0E59D54A29A094A285AA586,
	SliderHandler_SupportsPageMovements_m1F5CDF7F988438DF502BC1312F23CC4164F97EA8,
	SliderHandler_PageMovementValue_mA83FFA75AE8306757F98570BEDC2F307FBC96FE1,
	SliderHandler_PageUpMovementBound_m82E469555A7A32E942A3B9DA1C16C6239151ED8A,
	SliderHandler_CurrentEvent_m5B1D7DFBE33DF06D2FE72D7BA64E997DB59ADE75,
	SliderHandler_ValueForCurrentMousePosition_m089BEF0D4C4E82C086E3E49B871780AD6D6848F5,
	SliderHandler_Clamp_mC73E2009637968EFFC5DEE94B8C48B8DF6442C5E,
	SliderHandler_ThumbSelectionRect_m182932776473DDB8076E858AC0580E931AE66EB8,
	SliderHandler_StartDraggingWithValue_m580387DD28C5BC8EF389692BC4B0BDB402AD1F5C,
	SliderHandler_SliderState_m9E9EECE09E988B83FE3DF1F0AF9AFCF4AAF96D60,
	SliderHandler_ThumbExtRect_m16D13E44222386A99F1E3F089A5F4DC2ED19DBC1,
	SliderHandler_ThumbRect_mA297CC1978CFEEC1F01218A5C05766C9D0465C98,
	SliderHandler_VerticalThumbRect_mC9C04B0479876CA886A4C6868FFD65168511CAFB,
	SliderHandler_HorizontalThumbRect_m6FADFA27C8510FDD5FC2EB30A738DD6569DFB0BA,
	SliderHandler_ClampedCurrentValue_m2312300551774A993735636675E23015737664EC,
	SliderHandler_MousePosition_m5E95F987E7F6CA9E517AA7B84328689A287965B6,
	SliderHandler_ValuesPerPixel_mF654B0D4DDC55599E9B251A026727DAFF425E66F,
	SliderHandler_ThumbSize_m7A0F0323AF2FF51A3F35765438DDD1FBBAB63D47,
	SliderHandler_MaxValue_m25803007A5DB5427855A607D85F0A68D5CCCB208,
	SliderHandler_MinValue_m904DAD0D3F7BD5CD525BD520B82F60C3878FB25E,
	TextEditingUtilities_get_hasSelection_mB7272F47E994E6B88A2B0229BED793D4C5B23219,
	TextEditingUtilities_set_revealCursor_m76B8081758CCD459072EAEF3B8FE3017A57735C2,
	TextEditingUtilities_get_cursorIndex_m0D23C8510F3F2A20BBF5796F00CC36DA8BB32BD9,
	TextEditingUtilities_set_cursorIndex_mED289FA84CA33C4A695463C856524B501A78FEF4,
	TextEditingUtilities_get_selectIndex_m1331CF40E64C203B1713CFC2FEC5E0F30FFC737A,
	TextEditingUtilities_set_selectIndex_mFB98D1E5E2C236DEEE7A45619965502B73E0ADBA,
	TextEditingUtilities_get_text_mC8854D29B1F95E04E0FFB49F1F2327E77598EF8E,
	TextEditingUtilities_set_text_mC1FD19476AF4FA014E9DBA5A33C54A57E2FA70EC,
	TextEditingUtilities__ctor_m6503B88727D1F4008C31E4FB54F2153A44E99B07,
	TextEditingUtilities_UpdateImeState_mC13FC46AB62C566576C0D653774896FFA438003B,
	TextEditingUtilities_ShouldUpdateImeWindowPosition_m58FC98A57B608095F3EB6688A0D95FA64B8444E1,
	TextEditingUtilities_SetImeWindowPosition_m1DFAAA8DBA6A946B204470806FB968359BFB3C48,
	TextEditingUtilities_GeneratePreviewString_mA97B83DDA33F11F6580B86F6F2C438F19018A037,
	TextEditingUtilities_EnableCursorPreviewState_mD28C1FAC4AFFEE396D903D1EED90DF1F5BC6A85B,
	TextEditingUtilities_RestoreCursorState_m36D71121DFD69D0EB43FCF48E49E1F616F9346C4,
	TextEditingUtilities_HandleKeyEvent_m155A0EFF2CC76B511BDA3405C68A48408C1C6256,
	TextEditingUtilities_PerformOperation_m8DC34D9795E11260FA9F4C4961601B189F342820,
	TextEditingUtilities_MapKey_mCC38540E032D8765E0E280B94072141E74FF43FF,
	TextEditingUtilities_InitKeyActions_m08B59B3C8199E8DA37622E85D269F9E848B06381,
	TextEditingUtilities_DeleteLineBack_m34769E8A0D70CE5BDBA3B11DA506BC7CA859CCFA,
	TextEditingUtilities_DeleteWordBack_mC235A97BDFEE8CE4C993BCF02DF4E86AB4BB8F1D,
	TextEditingUtilities_DeleteWordForward_m7A29C8E5BD4E3F01F7A379C16D971BBF565E5CAA,
	TextEditingUtilities_Delete_mF34D04ACA64C871CDE0F5F5DD6CC36A0667926DD,
	TextEditingUtilities_Backspace_m08DB317F2AEDA35F6227D52FF92B3EE4F7AA5909,
	TextEditingUtilities_DeleteSelection_mF7E0C7A8B7A8984A5DA2C55839BFE60E0A70B847,
	TextEditingUtilities_ReplaceSelection_m49F49CDB5D91B695392E2CE1B7BDC5A46817BBCE,
	TextEditingUtilities_Insert_mC555975D4937CE0C4B68654520648A1C3F74C853,
	TextEditingUtilities_CanPaste_m81E3C512EF04804A3594020C3CD084F5BD85B3E7,
	TextEditingUtilities_Cut_m5E08F36BC2F88E0E55483A524E815A3EAA429D2B,
	TextEditingUtilities_Paste_m876D2AD7A881EAC57D762E15F9ACB1AC26B3C28C,
	TextEditingUtilities_ReplaceNewlinesWithSpaces_m368B355EF98969A0A9E527D67C43E173B3FCAC74,
	TextEditingUtilities_OnBlur_mCD1823DED60BE96C25C0E31B8DEC5F8EB1ACFD13,
	TextEditingUtilities_TouchScreenKeyboardShouldBeUsed_mAF399D5C01CD9A7B7F4E1C188792420AFBA99D53,
	TextEditor_get_text_mB5A19231EF7159855775CF3E9C5BC5346156E168,
	TextEditor_set_text_mB71257AAD99A56AD5EA96DB546B17296E60C4455,
	TextEditor_get_position_m40763329A82988B1C5D5C1DA9919932061C99E13,
	TextEditor_set_position_mDD8F5A0BFCE942F8D4403F78D3E1B0EF35D17EA0,
	TextEditor_get_localPosition_mFC726E86A4A79A98813DB9591648E0D82049D01D,
	TextEditor_get_cursorIndex_m0954904B376E50D89A4CDD82EEE710544D6EF461,
	TextEditor_set_cursorIndex_mF34C100A55F2767E46D07445B04B6DBEB77AF9A1,
	TextEditor_get_selectIndex_m4DB0C8224B5C82B0F02FFF69E80D3FEA4202A020,
	TextEditor_set_selectIndex_m782BBC95B43A71A1061060BF52959ADEE9AF27ED,
	TextEditor_ClearCursorPos_mAE2290DC256C2BB4F1E326187E0662F3BB42B1F6,
	TextEditor__ctor_m4AEAC85E4950B709A35F26D1F0DAB3C9D35E3494,
	TextEditor_OnFocus_mCD739D81E0F74A3E68A0BB861A3A3BD87DDBEE0A,
	TextEditor_OnLostFocus_mFDA430398601ABF5BBBF44D0A6CE969AFBED4FC9,
	TextEditor_GrabGraphicalCursorPos_m74915B49D9B0D200367FD710A1321C0D2E54B1E4,
	TextEditor_HandleKeyEvent_m2C8D0EA03A32518162947C87C4DF5A60C67BDE6D,
	TextEditor_HandleKeyEvent_m14D691B63637C1F4CFD0A96F7940C69A9CD6C658,
	TextEditor_DeleteLineBack_m43927B9B9F8AD1CA54CED2C40571F190EBE9792D,
	TextEditor_DeleteWordBack_m9F0CDF4ADF1A86CB97BD8C60FD52031FCD24A210,
	TextEditor_DeleteWordForward_mD81B94DA0DE3A3B9A212C3B6AF6C475B39E7A56D,
	TextEditor_Delete_mFE5E2A0C6230CA113C1C64C4F0F5F5D30DF16EEA,
	TextEditor_Backspace_m3D25240A83DA225BEDC8A5363CC83E9A2966169A,
	TextEditor_SelectAll_mDEBAABE01DF37B1EE8EFDE43E8036B5C2813C685,
	TextEditor_SelectNone_m4EEF86ADCEEF1F445A57947AB7D58ECC0A334B86,
	TextEditor_get_hasSelection_mD63A0ECF990D21515ABCAD26A7974B58A8CECCE9,
	TextEditor_DeleteSelection_m520F49C6269E488DD60BBD4603DA869FC446A788,
	TextEditor_ReplaceSelection_m7BBCC70F065AED2C5942127F95234C17897A70C1,
	TextEditor_Insert_m7FE4F5EF50CDB90FCD47C93D399996A2149B54AD,
	TextEditor_MoveRight_m568871F86B97196C66A4ADDF335E0ECEBEE18DC1,
	TextEditor_MoveLeft_m3784BAF8F1BF69781ECFA40D8DDAFA6EA9EC58C1,
	TextEditor_MoveUp_mFCC668A7D89E092E588F92DA2FA2B4D03E7C921F,
	TextEditor_MoveDown_m6084C0F493B71485D0D0796D77B4F32F391C7571,
	TextEditor_MoveLineStart_mCFB0865ABB2E2B6A6FC98F77FE6E69A8E2578ED0,
	TextEditor_MoveLineEnd_m811BAE3ABB333A4CE56C6C80439111CC1FE4450F,
	TextEditor_MoveGraphicalLineStart_m0332C42BCF18CC2AE7024402CDFFD1F24210B3DD,
	TextEditor_MoveGraphicalLineEnd_m82D882096A9E72C9469F01F5E8882DBEA8DC2C0E,
	TextEditor_MoveTextStart_m59D0D8EADF0420DED887A9E6D9E780CBE1A87E16,
	TextEditor_MoveTextEnd_m26A12A1C36B56C8D80B1E5C520EA38E670611028,
	TextEditor_IndexOfEndOfLine_mE03CC016EBA2B7AFD72A204D23D64C9F686C057B,
	TextEditor_MoveParagraphForward_m5CD556511F7189813865732FDC28FB710ADA8572,
	TextEditor_MoveParagraphBackward_mC64495DC5B520C2D279A03616D0ADDCE0718A510,
	TextEditor_MoveCursorToPosition_m7E64149849945E081877617EED22BCBBA51CAC76,
	TextEditor_MoveCursorToPosition_Internal_m7D1E68A7556DCCB8B6D557BF2E3B2894905B6037,
	TextEditor_SelectToPosition_m406CAD9A7C9B9211A10DFB1FF6FB6E0CF4437ECA,
	TextEditor_SelectLeft_m7473E14B27D0D9503E88FE893B41CD52EA15A39B,
	TextEditor_SelectRight_mC374DDA6EF8A522FF0CF35393EE38C07154FC062,
	TextEditor_SelectUp_mA28D8B3CB2012DE645CC3A027E1168AF2E9BCB3B,
	TextEditor_SelectDown_mC77A384914BA7F3CEC02C20D31CC28A3788B1C15,
	TextEditor_SelectTextEnd_mDCF0E587F42EB91D85850AC5F840E089955D69F4,
	TextEditor_SelectTextStart_m5FAFFEF24723CA29E6DA6631EE80EBF4ADE591C6,
	TextEditor_MouseDragSelectsWholeWords_mAE66B48954FFFC0F439C4070ED3601CF611A8F3B,
	TextEditor_DblClickSnap_m6CF85AA1A22F59BFF0301F54815128CF5EBFD252,
	TextEditor_GetGraphicalLineStart_m4503A00148DE73D825654C4DCBFD27E8234A957B,
	TextEditor_GetGraphicalLineEnd_m3396AC4E6D75FB0F8E8F99C91384064A32F0DF3F,
	TextEditor_FindNextSeperator_mE89483949A16CD41C7A7BDE7ACA89DBAF5039384,
	TextEditor_FindPrevSeperator_m8619997F12F419286B495A9BA5078634373542CB,
	TextEditor_MoveWordRight_mB79E9C0C420ED29EB70CFAB49CBFE6C290ECD074,
	TextEditor_MoveToStartOfNextWord_m8E6EA22B00CD272176D69786A716B5D125A026E2,
	TextEditor_MoveToEndOfPreviousWord_m2CE9EBD0A0FB0CCBC93912FA856B7628C4B3C3FF,
	TextEditor_SelectToStartOfNextWord_mCC2D52A1AA807D2EDB985CE1FAB18B884A1E549A,
	TextEditor_SelectToEndOfPreviousWord_m5C182936491E07770967753F142C2CCC6BAABB5A,
	TextEditor_ClassifyChar_mC2104A64D197D5BE8FA3CB1CA12F0E6AFF50AC77,
	TextEditor_FindStartOfNextWord_m07650DF8A35625ED2B3230B6B4C96C730F945B0A,
	TextEditor_FindEndOfPreviousWord_mFBDBEABAC6CFE72EF4ED33A3474EF3998E460C00,
	TextEditor_MoveWordLeft_mFDC5DE936BBEF1CBAE440BD813107B04F20A2004,
	TextEditor_SelectWordRight_m65AD5DB10CB51F517DA58E4BD5E11C3842ACC503,
	TextEditor_SelectWordLeft_m3E721136E39E2CF679C08538DDD2FDE5D3D8F7E5,
	TextEditor_ExpandSelectGraphicalLineStart_m04A641F4217F6F54795103B6819EADCB1AC0495F,
	TextEditor_ExpandSelectGraphicalLineEnd_m77DED153050FD67B42CA570497436192A3E5BA60,
	TextEditor_SelectGraphicalLineStart_mDB2EDA9715BCF37692A79301F480C793D540E01A,
	TextEditor_SelectGraphicalLineEnd_m27A03A2BE9B63F0C294E986B4244942876EB2848,
	TextEditor_SelectParagraphForward_m9531E6CCDFB591A90EC32464858B2280AD3F5772,
	TextEditor_SelectParagraphBackward_m0430E7BC1725DB775D0D817B80C4C1232449AD79,
	TextEditor_SelectCurrentWord_m9118CAE842D71A1AB19C90C94FC0ED4C32ABA99D,
	TextEditor_FindEndOfClassification_m9F20C27BA429FCCDDB9821EB9CE1E55535D44857,
	TextEditor_SelectCurrentParagraph_m2D569FA93359557D691EB507471594473E419F0C,
	TextEditor_UpdateScrollOffsetIfNeeded_m5F969498D9A0DABC94D33D3F3E943C5D4009D5FD,
	TextEditor_UpdateScrollOffset_mD3F056830FF3FFC3461ED965EB0B7E306536FC3B,
	TextEditor_DrawCursor_m16C190E8A889D4CFBDD448A29CE2FDC503C10243,
	TextEditor_PerformOperation_m9CC1732A34CF801A0348A4296BDBC9D015AB4014,
	TextEditor_SaveBackup_m5DA3A7E39B3F28777DD53DC0934CAB46B9ED8151,
	TextEditor_Cut_m3B9A748CFEF7633613107C8F4A1CF62255041BFB,
	TextEditor_Copy_m33D7D8DF6A4EE867CF02D15577E8A591C0027DCA,
	TextEditor_ReplaceNewlinesWithSpaces_m050CD5F1C45A59C776C840AC84A1CF1C4AECA47C,
	TextEditor_Paste_m1A9AEA3C543B2E7595070DA96D7DBE24066AC9E2,
	TextEditor_MapKey_m911245BAA919A02A8FDFCC0998CB147A3EE0EC9B,
	TextEditor_InitKeyActions_m30295CE738738468794A7AE3338BE827B891A0DD,
	TextEditor_DetectFocusChange_mFE7D29EC9391792772129BD80FC236285218464B,
	TextEditor_OnDetectFocusChange_m70E412EF53A051067D33711E70BE73C76CB97168,
	TextEditor_OnCursorIndexChange_m9B9C472B0F62917E96E5E27F15A76C9E4E493012,
	TextEditor_OnSelectIndexChange_m99E1BBDFC6398F47F3170A6A46C5428F292FEE21,
	TextEditor_ClampTextIndex_m08BC2F0E9A0599EE71C0632C61187F3F3EAAF4B6,
	TextEditor_EnsureValidCodePointIndex_m9C20E36F766CF8DBD87A36606B1FAEED3BE42BB1,
	TextEditor_IsValidCodePointIndex_m1D01E0B8AA575A60C985B9FC413ED3DDA4EE8097,
	TextEditor_PreviousCodePointIndex_mFCCC1034ABF4773A7E1E121D14ACB948FCA116E0,
	TextEditor_NextCodePointIndex_mF426772BB6B0CD7A3FC4042070C21902BF576B31,
	TextSelectingUtilities_get_hasSelection_m86EA37D0A10EC2C4C1886C7E770DAB34DB8A66CD,
	TextSelectingUtilities_get_revealCursor_mD9502AE79AB9AC44496008153F267CBB4A9B3C16,
	TextSelectingUtilities_set_revealCursor_m2A3BFE850A09B0716824E763373A521A24CC5F52,
	TextSelectingUtilities_get_m_CharacterCount_m4C9189574E900CF75E41BB29861246D64A83CB0E,
	TextSelectingUtilities_get_characterCount_mF1A48644B9D24C0AE96B6259FDAFC87CB1BD5FEB,
	TextSelectingUtilities_get_m_TextElementInfos_mB87B7D1C40D71249B4E20DA1D7501194A2477368,
	TextSelectingUtilities_get_cursorIndex_m89386F5B913CD481337E49A0635834F433B4AC5F,
	TextSelectingUtilities_set_cursorIndex_m0D3D5D519DA459906E983EABAA4AF692C4763269,
	TextSelectingUtilities_SetCursorIndexWithoutNotify_mF240906A6FA8A38F5CB6AD1C5601265D05CCCAEE,
	TextSelectingUtilities_get_selectIndex_m1535CA4D982CC3A4C46E4491FA36E9514243CCB9,
	TextSelectingUtilities_set_selectIndex_m1697EFBA1D0B91E0B20FD23B69A43D32D2110F10,
	TextSelectingUtilities_SetSelectIndexWithoutNotify_m33DB6522D2FA6877E3B991CDFD4944836A5D77FF,
	TextSelectingUtilities_get_selectedText_m4A131331842BA17B453A09FE4663A75B7B356013,
	TextSelectingUtilities__ctor_m0D593E63B3CFE982829CFF9C93C5858E27AB84AE,
	TextSelectingUtilities_HandleKeyEvent_mD6AD5FEF96C31860C66D49324F98BD7AB27AE551,
	TextSelectingUtilities_PerformOperation_m3F865D868A3A9264B28FAABBFE92F654BF3706FC,
	TextSelectingUtilities_MapKey_mDCCB5F98583F4FC584FD97AF27D37F91A7C5048B,
	TextSelectingUtilities_InitKeyActions_m005DD393E320ADA5BEFC2AA357EE964A8AB6CCED,
	TextSelectingUtilities_ClearCursorPos_m743C82F3CC7576E5050B6FA23133EC3FA8E9ED9C,
	TextSelectingUtilities_OnFocus_mCAC979E4683D3A0B91C91FCC19516E5FEE605A9C,
	TextSelectingUtilities_SelectAll_m89B71F5AF97AC5848616468DEFCF062C26DF23FD,
	TextSelectingUtilities_SelectNone_m47791B4FBE066CCC974155E1BD9FE8ACCB48D21A,
	TextSelectingUtilities_SelectLeft_mB51FE46E45D1C077ACB44AC5E2BF63C52AC3727D,
	TextSelectingUtilities_SelectRight_mFC95A2800C1CBEC7606EA8901F75CC946ED3BCA8,
	TextSelectingUtilities_SelectUp_m1D90105D04CF6CBAE84D059CEF0495FD2FF0C22A,
	TextSelectingUtilities_SelectDown_m8E2EE5EC95CE507A7814D0FAAD865557C2A191C1,
	TextSelectingUtilities_SelectTextEnd_mFF22956A56670B41BEE1A4527A9057B63BE89927,
	TextSelectingUtilities_SelectTextStart_mD4F085F9AF7C2441D60E54CDE64B96A6934D13A6,
	TextSelectingUtilities_SelectToStartOfNextWord_mC53BBECD698C32BAD6B4856F52ACA9E9D59F3E52,
	TextSelectingUtilities_SelectToEndOfPreviousWord_mAC43A04CA459FE506650A86B695900300E4B24D8,
	TextSelectingUtilities_SelectWordRight_m75669B452A334F48FB1CE5E0AC6792862E706B5E,
	TextSelectingUtilities_SelectWordLeft_mC488E7BBD9E8187F426A2F1CA82C528D76F4FCCC,
	TextSelectingUtilities_SelectGraphicalLineStart_m87E74A85CBD46469B2D1263436D2BE2FE5ABDB38,
	TextSelectingUtilities_SelectGraphicalLineEnd_m60A6059D86AEB922ED7829EAF9C4E53B800911B4,
	TextSelectingUtilities_SelectParagraphForward_m64D82C33CE1B82DC1B994B6CA027D717AB4280EB,
	TextSelectingUtilities_SelectParagraphBackward_m77EE0A0A167E91E1850937F543DBA615DC6DE0A0,
	TextSelectingUtilities_SelectCurrentWord_mCC2AC7DD6D2BA6D2DF3DD728D883FF0D6963A959,
	TextSelectingUtilities_SelectCurrentParagraph_mBD0B848A023ED86697EBA135E81B59ACD13B2B7A,
	TextSelectingUtilities_MoveRight_m8F1910A2773A39EF5CE248349F5A6CD7166AB795,
	TextSelectingUtilities_MoveLeft_m094C534A56FC3CDA9C2423E46D179F359693370E,
	TextSelectingUtilities_MoveUp_mF0F3EE17A2CB3C4C1AEB950E80A5237A55D2711D,
	TextSelectingUtilities_MoveDown_m7EF798D6A19267DE30ED50C66697F5BC8AB814B8,
	TextSelectingUtilities_MoveLineStart_m561A829C19F6C50028473CD5F81C508F3EEFE276,
	TextSelectingUtilities_MoveLineEnd_mB4BABB86B094C9B88DEF94E0392BAE4396283B61,
	TextSelectingUtilities_MoveGraphicalLineStart_m1ECAAAF8A29D63C5D7E76170D3745E3EB9E2266F,
	TextSelectingUtilities_MoveGraphicalLineEnd_m8BB408E28EA20EB56531B1FFD417FE54296008BE,
	TextSelectingUtilities_MoveTextStart_m7A276F1B11A1DAF468AC84324E592005B5D47350,
	TextSelectingUtilities_MoveTextEnd_mDA69E553CA7D50781E845169852F1A0059FF0EB9,
	TextSelectingUtilities_MoveParagraphForward_m88210A22BC823945D6AA137D50603388233502EA,
	TextSelectingUtilities_MoveParagraphBackward_m087604CE592162192829ADB142B786B0C436A58C,
	TextSelectingUtilities_MoveWordRight_mDCDC1B673D599BB94C95D6DC364D8877E87E3383,
	TextSelectingUtilities_MoveToStartOfNextWord_mD1CEFD9620822349FDE09237943F943EB512A8C2,
	TextSelectingUtilities_MoveToEndOfPreviousWord_m5BE2565747FFC49AA41526504653159852FC50A7,
	TextSelectingUtilities_MoveWordLeft_m7D8131CD2DF2DF180D3FC249E2F0E17E250E5D3D,
	TextSelectingUtilities_MouseDragSelectsWholeWords_mB586078A58B5D56A53138856AB8DE9BD33535CC1,
	TextSelectingUtilities_ExpandSelectGraphicalLineStart_m5E109D0A6A12D3D2FDF09F3E1407EB0347F7C4EF,
	TextSelectingUtilities_ExpandSelectGraphicalLineEnd_mFCA738E71AADB3C1206F279BC2681CA906A36D74,
	TextSelectingUtilities_DblClickSnap_m6472F8DA3F0FC46FF75FFB394B283F5E5EC834FA,
	TextSelectingUtilities_MoveCursorToPosition_Internal_mE4AEE1AA57B8CCBB371C24B4F4B1AA2FF89886FD,
	TextSelectingUtilities_SelectToPosition_m75C9B53E1227CF9D487D5C8D771F0D8ACFEDC2F8,
	TextSelectingUtilities_FindNextSeperator_mCF332FBDFEA6BCB471EDF75D76A139A580DB0E2A,
	TextSelectingUtilities_FindPrevSeperator_mD0F56A4106D376F28D6A0E8E07D7D4DAB9FF8C7E,
	TextSelectingUtilities_FindStartOfNextWord_m3D436C6FB45B3574F6F436B7CBAD30F5372C6870,
	TextSelectingUtilities_FindEndOfPreviousWord_mA7AF965D2F01728EE872F2CB3F03083ABA63D174,
	TextSelectingUtilities_FindEndOfClassification_m134E7AB62A9E90595A3C6DCC1DD517515B85AB1A,
	TextSelectingUtilities_ClampTextIndex_m04DED78B3B466D3E5AF59442F736A822ABC8D7E0,
	TextSelectingUtilities_EnsureValidCodePointIndex_m6D3C6E6936FCAB391B6B48D53DFA50D995523530,
	TextSelectingUtilities_IsValidCodePointIndex_mB49A5FF3F3716818677CC13AE904ADF0034A07F5,
	TextSelectingUtilities_IndexOfEndOfLine_m9D87F72DC289F7095E615D97D821BBCE8D2B978B,
	TextSelectingUtilities_PreviousCodePointIndex_mCE1A8A7E9180B182AEA4DB6562EBDE854A860C6D,
	TextSelectingUtilities_NextCodePointIndex_mA936059BE65DAABE0446AFB5425BB4EA83392607,
	TextSelectingUtilities_GetGraphicalLineStart_mEDB6AF99C1BBE0A1180AEE4FE67DDC0A223BD218,
	TextSelectingUtilities_GetGraphicalLineEnd_mD956DB2F4EC24F3DE89069232733376B615D3204,
	TextSelectingUtilities_Copy_m69701E12FFE465B70E677DCCCCF3148873A5FE0A,
	TextSelectingUtilities_ClassifyChar_m887E3900015DB05F1EF0E5F4630748CCA5C3CD52,
	U3CPrivateImplementationDetailsU3E_ComputeStringHash_m3791FADF6D0284BCC1AF6156A077038C2AA23055,
};
extern void EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA_AdjustorThunk (void);
extern void EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F_AdjustorThunk (void);
extern void EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F_AdjustorThunk (void);
extern void EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313_AdjustorThunk (void);
extern void EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE_AdjustorThunk (void);
extern void EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6_AdjustorThunk (void);
extern void EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B_AdjustorThunk (void);
extern void ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4_AdjustorThunk (void);
extern void ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D_AdjustorThunk (void);
extern void SliderHandler__ctor_mDCC4520E68A478FC28519FD5DB149EFFBB9C186A_AdjustorThunk (void);
extern void SliderHandler_Handle_m9E9DBA6E45BA00A0D812C74BBB18DFBA0F923BFE_AdjustorThunk (void);
extern void SliderHandler_OnMouseDown_m9DE4BD08EDF586656B93C94FDF4907D081BCA8B8_AdjustorThunk (void);
extern void SliderHandler_OnMouseDrag_mC985CE7801C44CC87A1B11ABF0E142E617356B9E_AdjustorThunk (void);
extern void SliderHandler_OnMouseUp_m97A5FDF8EBADFE60EC0DE486127A7F4B9C7068DB_AdjustorThunk (void);
extern void SliderHandler_OnRepaint_m5FAFC71FF2A246AC8336B0F400F35DF581AD54AF_AdjustorThunk (void);
extern void SliderHandler_CurrentEventType_mC4756C3A92FC488ACF0C0148CFFA0F0239622F2A_AdjustorThunk (void);
extern void SliderHandler_CurrentScrollTroughSide_m5E30388E1DE3C01FB968EFCC02215C14F301E1A2_AdjustorThunk (void);
extern void SliderHandler_IsEmptySlider_m8AD3CF68C310AEAAB0E59D54A29A094A285AA586_AdjustorThunk (void);
extern void SliderHandler_SupportsPageMovements_m1F5CDF7F988438DF502BC1312F23CC4164F97EA8_AdjustorThunk (void);
extern void SliderHandler_PageMovementValue_mA83FFA75AE8306757F98570BEDC2F307FBC96FE1_AdjustorThunk (void);
extern void SliderHandler_PageUpMovementBound_m82E469555A7A32E942A3B9DA1C16C6239151ED8A_AdjustorThunk (void);
extern void SliderHandler_CurrentEvent_m5B1D7DFBE33DF06D2FE72D7BA64E997DB59ADE75_AdjustorThunk (void);
extern void SliderHandler_ValueForCurrentMousePosition_m089BEF0D4C4E82C086E3E49B871780AD6D6848F5_AdjustorThunk (void);
extern void SliderHandler_Clamp_mC73E2009637968EFFC5DEE94B8C48B8DF6442C5E_AdjustorThunk (void);
extern void SliderHandler_ThumbSelectionRect_m182932776473DDB8076E858AC0580E931AE66EB8_AdjustorThunk (void);
extern void SliderHandler_StartDraggingWithValue_m580387DD28C5BC8EF389692BC4B0BDB402AD1F5C_AdjustorThunk (void);
extern void SliderHandler_SliderState_m9E9EECE09E988B83FE3DF1F0AF9AFCF4AAF96D60_AdjustorThunk (void);
extern void SliderHandler_ThumbExtRect_m16D13E44222386A99F1E3F089A5F4DC2ED19DBC1_AdjustorThunk (void);
extern void SliderHandler_ThumbRect_mA297CC1978CFEEC1F01218A5C05766C9D0465C98_AdjustorThunk (void);
extern void SliderHandler_VerticalThumbRect_mC9C04B0479876CA886A4C6868FFD65168511CAFB_AdjustorThunk (void);
extern void SliderHandler_HorizontalThumbRect_m6FADFA27C8510FDD5FC2EB30A738DD6569DFB0BA_AdjustorThunk (void);
extern void SliderHandler_ClampedCurrentValue_m2312300551774A993735636675E23015737664EC_AdjustorThunk (void);
extern void SliderHandler_MousePosition_m5E95F987E7F6CA9E517AA7B84328689A287965B6_AdjustorThunk (void);
extern void SliderHandler_ValuesPerPixel_mF654B0D4DDC55599E9B251A026727DAFF425E66F_AdjustorThunk (void);
extern void SliderHandler_ThumbSize_m7A0F0323AF2FF51A3F35765438DDD1FBBAB63D47_AdjustorThunk (void);
extern void SliderHandler_MaxValue_m25803007A5DB5427855A607D85F0A68D5CCCB208_AdjustorThunk (void);
extern void SliderHandler_MinValue_m904DAD0D3F7BD5CD525BD520B82F60C3878FB25E_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[37] = 
{
	{ 0x06000039, EventInterests_get_wantsMouseMove_m4CE6AE73062DE1E37A138ED365FE4D8C7894B9AA_AdjustorThunk },
	{ 0x0600003A, EventInterests_set_wantsMouseMove_mFEA33E053185D63A19F60AA69E385C05CE795F0F_AdjustorThunk },
	{ 0x0600003B, EventInterests_get_wantsMouseEnterLeaveWindow_m5CC6DB8DAF1DEB0F7E8878B96A856F540E66840F_AdjustorThunk },
	{ 0x0600003C, EventInterests_set_wantsMouseEnterLeaveWindow_m5D73B54F5855E5BB5FE54AA2366A83A33982D313_AdjustorThunk },
	{ 0x0600003D, EventInterests_get_wantsLessLayoutEvents_m1BC017D5AC484596A2A9B05BF592B65CE2A00CDE_AdjustorThunk },
	{ 0x0600003E, EventInterests_WantsEvent_mD34E2AD1F937EE03C9C29882672F400AD3C3E5B6_AdjustorThunk },
	{ 0x0600003F, EventInterests_WantsLayoutPass_m403675D6BA834A05764A2C2558ECBCE90C8D066B_AdjustorThunk },
	{ 0x0600009E, ParentClipScope__ctor_m5251E311D308625C438134442CA69D75E872DCD4_AdjustorThunk },
	{ 0x0600009F, ParentClipScope_Dispose_m39F5E11A8E9346D5ADE850A5A600A675589E786D_AdjustorThunk },
	{ 0x060001F3, SliderHandler__ctor_mDCC4520E68A478FC28519FD5DB149EFFBB9C186A_AdjustorThunk },
	{ 0x060001F4, SliderHandler_Handle_m9E9DBA6E45BA00A0D812C74BBB18DFBA0F923BFE_AdjustorThunk },
	{ 0x060001F5, SliderHandler_OnMouseDown_m9DE4BD08EDF586656B93C94FDF4907D081BCA8B8_AdjustorThunk },
	{ 0x060001F6, SliderHandler_OnMouseDrag_mC985CE7801C44CC87A1B11ABF0E142E617356B9E_AdjustorThunk },
	{ 0x060001F7, SliderHandler_OnMouseUp_m97A5FDF8EBADFE60EC0DE486127A7F4B9C7068DB_AdjustorThunk },
	{ 0x060001F8, SliderHandler_OnRepaint_m5FAFC71FF2A246AC8336B0F400F35DF581AD54AF_AdjustorThunk },
	{ 0x060001F9, SliderHandler_CurrentEventType_mC4756C3A92FC488ACF0C0148CFFA0F0239622F2A_AdjustorThunk },
	{ 0x060001FA, SliderHandler_CurrentScrollTroughSide_m5E30388E1DE3C01FB968EFCC02215C14F301E1A2_AdjustorThunk },
	{ 0x060001FB, SliderHandler_IsEmptySlider_m8AD3CF68C310AEAAB0E59D54A29A094A285AA586_AdjustorThunk },
	{ 0x060001FC, SliderHandler_SupportsPageMovements_m1F5CDF7F988438DF502BC1312F23CC4164F97EA8_AdjustorThunk },
	{ 0x060001FD, SliderHandler_PageMovementValue_mA83FFA75AE8306757F98570BEDC2F307FBC96FE1_AdjustorThunk },
	{ 0x060001FE, SliderHandler_PageUpMovementBound_m82E469555A7A32E942A3B9DA1C16C6239151ED8A_AdjustorThunk },
	{ 0x060001FF, SliderHandler_CurrentEvent_m5B1D7DFBE33DF06D2FE72D7BA64E997DB59ADE75_AdjustorThunk },
	{ 0x06000200, SliderHandler_ValueForCurrentMousePosition_m089BEF0D4C4E82C086E3E49B871780AD6D6848F5_AdjustorThunk },
	{ 0x06000201, SliderHandler_Clamp_mC73E2009637968EFFC5DEE94B8C48B8DF6442C5E_AdjustorThunk },
	{ 0x06000202, SliderHandler_ThumbSelectionRect_m182932776473DDB8076E858AC0580E931AE66EB8_AdjustorThunk },
	{ 0x06000203, SliderHandler_StartDraggingWithValue_m580387DD28C5BC8EF389692BC4B0BDB402AD1F5C_AdjustorThunk },
	{ 0x06000204, SliderHandler_SliderState_m9E9EECE09E988B83FE3DF1F0AF9AFCF4AAF96D60_AdjustorThunk },
	{ 0x06000205, SliderHandler_ThumbExtRect_m16D13E44222386A99F1E3F089A5F4DC2ED19DBC1_AdjustorThunk },
	{ 0x06000206, SliderHandler_ThumbRect_mA297CC1978CFEEC1F01218A5C05766C9D0465C98_AdjustorThunk },
	{ 0x06000207, SliderHandler_VerticalThumbRect_mC9C04B0479876CA886A4C6868FFD65168511CAFB_AdjustorThunk },
	{ 0x06000208, SliderHandler_HorizontalThumbRect_m6FADFA27C8510FDD5FC2EB30A738DD6569DFB0BA_AdjustorThunk },
	{ 0x06000209, SliderHandler_ClampedCurrentValue_m2312300551774A993735636675E23015737664EC_AdjustorThunk },
	{ 0x0600020A, SliderHandler_MousePosition_m5E95F987E7F6CA9E517AA7B84328689A287965B6_AdjustorThunk },
	{ 0x0600020B, SliderHandler_ValuesPerPixel_mF654B0D4DDC55599E9B251A026727DAFF425E66F_AdjustorThunk },
	{ 0x0600020C, SliderHandler_ThumbSize_m7A0F0323AF2FF51A3F35765438DDD1FBBAB63D47_AdjustorThunk },
	{ 0x0600020D, SliderHandler_MaxValue_m25803007A5DB5427855A607D85F0A68D5CCCB208_AdjustorThunk },
	{ 0x0600020E, SliderHandler_MinValue_m904DAD0D3F7BD5CD525BD520B82F60C3878FB25E_AdjustorThunk },
};
static const int32_t s_InvokerIndices[730] = 
{
	6957,
	7109,
	5913,
	7109,
	5913,
	6957,
	6957,
	6957,
	5774,
	7043,
	7043,
	7109,
	6957,
	6957,
	7105,
	5909,
	6957,
	5774,
	6957,
	5774,
	6957,
	5774,
	6992,
	5806,
	7120,
	9970,
	10290,
	4900,
	5777,
	9834,
	10290,
	10288,
	10412,
	7120,
	5774,
	7120,
	5806,
	6887,
	6887,
	6887,
	6887,
	10420,
	10293,
	6887,
	6887,
	6887,
	10054,
	6957,
	4261,
	6992,
	7120,
	5689,
	5689,
	5689,
	5689,
	5689,
	6887,
	5703,
	6887,
	5703,
	6887,
	4231,
	4231,
	10400,
	10286,
	10400,
	10286,
	10400,
	10286,
	10397,
	10284,
	10397,
	10284,
	10288,
	10397,
	10420,
	10420,
	10420,
	10420,
	10288,
	9831,
	10455,
	10455,
	10455,
	10412,
	10288,
	10404,
	10287,
	10293,
	10420,
	10293,
	10418,
	10292,
	9645,
	8984,
	8984,
	9645,
	8983,
	8427,
	7926,
	7333,
	7334,
	7335,
	7292,
	7249,
	7577,
	9645,
	8984,
	8984,
	9146,
	8550,
	8550,
	8550,
	7996,
	9387,
	7531,
	7404,
	7332,
	7291,
	7404,
	7331,
	7428,
	8984,
	7996,
	8311,
	7261,
	8984,
	8428,
	10455,
	10420,
	7324,
	10282,
	10282,
	10282,
	10282,
	10282,
	10282,
	3361,
	5774,
	10432,
	8429,
	10455,
	10412,
	10264,
	10418,
	10292,
	9590,
	8933,
	10455,
	8429,
	10264,
	10282,
	8329,
	9553,
	10282,
	10282,
	8853,
	3336,
	7120,
	6992,
	5806,
	5806,
	6992,
	5806,
	7120,
	5806,
	1909,
	5806,
	10054,
	10054,
	10455,
	6992,
	10455,
	9630,
	8965,
	9127,
	8541,
	8732,
	7753,
	10300,
	10293,
	8965,
	10455,
	10293,
	9630,
	8965,
	10455,
	10296,
	8984,
	10455,
	10063,
	10063,
	10063,
	3140,
	10080,
	9579,
	10412,
	10288,
	9356,
	9356,
	9573,
	10288,
	10293,
	8912,
	10455,
	10455,
	9650,
	10293,
	10293,
	10054,
	8737,
	10455,
	9381,
	10455,
	8781,
	8781,
	8304,
	7485,
	10420,
	10455,
	9572,
	9572,
	5774,
	5774,
	7120,
	10440,
	6887,
	6887,
	6890,
	7043,
	6890,
	7120,
	7120,
	7120,
	10455,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	10420,
	7120,
	7120,
	5181,
	5181,
	7120,
	6992,
	3361,
	7120,
	9376,
	10455,
	5706,
	10414,
	7120,
	7120,
	3361,
	9378,
	7120,
	5689,
	6992,
	5806,
	6992,
	6957,
	5774,
	6887,
	7109,
	5913,
	7043,
	7043,
	6887,
	5703,
	6887,
	5703,
	5774,
	5774,
	5913,
	9973,
	10290,
	5117,
	5117,
	10109,
	394,
	1349,
	1350,
	37,
	1710,
	1569,
	1146,
	5367,
	2753,
	2720,
	5367,
	9632,
	9834,
	10440,
	10293,
	7120,
	7120,
	6992,
	5806,
	6992,
	6992,
	6992,
	7043,
	394,
	1940,
	1349,
	710,
	247,
	1351,
	156,
	395,
	711,
	10054,
	10420,
	1710,
	1569,
	5367,
	2753,
	2720,
	6887,
	1865,
	6992,
	10455,
	5689,
	5689,
	5689,
	323,
	1188,
	1187,
	22,
	1187,
	1537,
	1103,
	3346,
	1865,
	3346,
	9617,
	9282,
	10440,
	10412,
	10284,
	10397,
	10284,
	10420,
	10293,
	8605,
	8605,
	10293,
	10293,
	10455,
	9942,
	10455,
	10455,
	10397,
	9831,
	8782,
	10420,
	10288,
	10303,
	10412,
	10412,
	10288,
	10288,
	10051,
	10455,
	10455,
	9938,
	9270,
	9376,
	10284,
	10412,
	10288,
	10455,
	10455,
	10412,
	10288,
	9831,
	10455,
	10420,
	8909,
	10455,
	8907,
	10288,
	10288,
	9834,
	9834,
	10455,
	9834,
	9834,
	10455,
	10112,
	10082,
	8552,
	8551,
	9146,
	10455,
	8603,
	8328,
	10282,
	7120,
	5806,
	6992,
	5806,
	6957,
	6957,
	6957,
	6957,
	6957,
	6957,
	715,
	399,
	7120,
	7120,
	3410,
	3410,
	5806,
	5806,
	6992,
	10455,
	1909,
	7120,
	7120,
	6957,
	6957,
	6957,
	6957,
	7120,
	5806,
	5806,
	7120,
	6992,
	5806,
	7120,
	3410,
	7120,
	3410,
	6992,
	10455,
	7120,
	7120,
	3410,
	7120,
	3410,
	7120,
	7120,
	7120,
	7120,
	10414,
	10290,
	7120,
	7120,
	57,
	7043,
	7043,
	7043,
	7043,
	7043,
	6957,
	6957,
	6887,
	6887,
	7043,
	7043,
	6992,
	7043,
	5265,
	7017,
	5851,
	6992,
	7017,
	7017,
	7017,
	7017,
	7043,
	7043,
	7043,
	7043,
	7043,
	7043,
	6887,
	5703,
	6957,
	5774,
	6957,
	5774,
	6992,
	5806,
	1909,
	6887,
	6887,
	5913,
	5169,
	7120,
	7120,
	4261,
	5774,
	9623,
	7120,
	6887,
	6887,
	6887,
	6887,
	6887,
	6887,
	5806,
	5909,
	6887,
	6887,
	6887,
	10054,
	7120,
	6887,
	6992,
	5806,
	7017,
	5831,
	7017,
	6957,
	5774,
	6957,
	5774,
	7120,
	7120,
	7120,
	7120,
	7120,
	4261,
	2196,
	6887,
	6887,
	6887,
	6887,
	6887,
	7120,
	7120,
	6887,
	6887,
	5806,
	5909,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	4900,
	7120,
	7120,
	5913,
	3439,
	5913,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	5703,
	5703,
	4900,
	4900,
	4900,
	4900,
	7120,
	7120,
	7120,
	7120,
	7120,
	4900,
	4900,
	4900,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	2445,
	7120,
	5806,
	7120,
	5806,
	2150,
	7120,
	6887,
	7120,
	10054,
	6887,
	9623,
	7120,
	7120,
	7120,
	7120,
	7120,
	5689,
	5689,
	4231,
	4900,
	4900,
	6887,
	6887,
	5703,
	6957,
	6957,
	6992,
	6957,
	5774,
	5774,
	6957,
	5774,
	5774,
	6992,
	5806,
	4261,
	4231,
	9623,
	7120,
	7120,
	5703,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	5703,
	7120,
	7120,
	5703,
	3439,
	5913,
	4900,
	4900,
	4900,
	4900,
	2445,
	4900,
	4900,
	4231,
	4900,
	4900,
	4900,
	4900,
	4900,
	7120,
	4900,
	10238,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule = 
{
	"UnityEngine.IMGUIModule.dll",
	730,
	s_methodPointers,
	37,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
