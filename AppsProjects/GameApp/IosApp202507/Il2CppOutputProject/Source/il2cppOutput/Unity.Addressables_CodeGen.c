﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PackedPlayModeBuildLogs_get_RuntimeBuildLogs_m2A62CE1FAC6164FD6B0D89993B38230C9D8256E9 (void);
extern void PackedPlayModeBuildLogs_set_RuntimeBuildLogs_mD62DB1198BDD40392771BA8702C5E44BF4B2E44F (void);
extern void PackedPlayModeBuildLogs__ctor_mCA7D3DF16DA06CC7A356906C32B466EAA5D57189 (void);
extern void RuntimeBuildLog__ctor_m29FC0C974B2C6D702AF74C393BF2640D0836A7DB (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9FCEB3415C007CA3CC22DE687734735C857E2E8C (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m95195BF4B962DEB33556FA9BD52A00EB38035E22 (void);
extern void AssetReferenceUIRestriction_ValidateAsset_mC395728636B5AE4C49922728EA9F6E59626A3A59 (void);
extern void AssetReferenceUIRestriction_ValidateAsset_m3AD169DF63426909A45850B40AC1C601536CE2E2 (void);
extern void AssetReferenceUIRestriction__ctor_m068B89DD8634990FD707FB05B33B258D10E1C8E7 (void);
extern void AssetReferenceUILabelRestriction__ctor_m2C0E08EF97801C19BCDB2C1D56660099038445C7 (void);
extern void AssetReferenceUILabelRestriction_ValidateAsset_m13B6237658C7E47DB0A4F9D8BC6DC44CB5A9C2F6 (void);
extern void AssetReferenceUILabelRestriction_ValidateAsset_mBEC28A0381E9B3778E661CF722A2A7646B4A04F3 (void);
extern void AssetReferenceUILabelRestriction_ToString_mFB39115AFCF55E3CF99F3399ED3BBF2FE1C299D3 (void);
extern void InitalizationObjectsOperation_Init_m5C261C641269B76CEAA9774EF4A621EA846EEB5D (void);
extern void InitalizationObjectsOperation_get_DebugName_m851C38457F31826B52E531C6E783121AF5B6BD5C (void);
extern void InitalizationObjectsOperation_LogRuntimeWarnings_m00AE676D1FCCD48675884FE9DD20F5D1F684AEDA (void);
extern void InitalizationObjectsOperation_InvokeWaitForCompletion_mE8FAF7DB4D7D10F22C4FC6DF3A26CAA0ADC6D5FD (void);
extern void InitalizationObjectsOperation_Execute_mBD7C50B85F47EE0269BBBE721B5195D4AAC1A3ED (void);
extern void InitalizationObjectsOperation__ctor_m1C106B8E7787E7E178E17DBFDD984EEE88C13A33 (void);
extern void InitalizationObjectsOperation_U3CExecuteU3Eb__8_0_m1AA291DA4F801F4D169E0D3BABF6501F7265E51B (void);
extern void ResourceLocatorInfo_get_Locator_m8CA415524E1D9121B15A313510B1CFF6C13ED324 (void);
extern void ResourceLocatorInfo_set_Locator_m8EC314A008CEC95E8764F39D8C8BADE84786B9F3 (void);
extern void ResourceLocatorInfo_get_LocalHash_mBB53B74BC6AA216279BE59C16D0B97DA26E47827 (void);
extern void ResourceLocatorInfo_set_LocalHash_mBCB7185F4FCA467938538C04AE287EFE4CBF8F2B (void);
extern void ResourceLocatorInfo_get_CatalogLocation_m9E793031F8D1D962FCB2D9B1DC032F182B7A608B (void);
extern void ResourceLocatorInfo_set_CatalogLocation_m386014DD0243D531197FEED38F599BCF02E68199 (void);
extern void ResourceLocatorInfo_get_ContentUpdateAvailable_m8F272A4A122D091D0AF9C97720AECD6B5D86CF43 (void);
extern void ResourceLocatorInfo_set_ContentUpdateAvailable_m373A4DF73C2A079484DDEA80D9E5AF2BD3526680 (void);
extern void ResourceLocatorInfo__ctor_m31FE77DF3F969F0924E4AD5589C33199DE7C6DDC (void);
extern void ResourceLocatorInfo_get_HashLocation_m6524A33D7BDC4E82BD6BD6D0EE08C67DD2790321 (void);
extern void ResourceLocatorInfo_get_CanUpdateContent_mA52B33646AA6A40B017A8205378534CCE5E01CB7 (void);
extern void ResourceLocatorInfo_UpdateContent_m69FD26B59EA444D13933A720E24DB884F948037F (void);
extern void InvalidKeyException_get_Key_m04CFA5445F268B1738B83E149DA2A560A789A291 (void);
extern void InvalidKeyException_set_Key_mCDC4F50DA79B43EE22E693752C230A727E520F1A (void);
extern void InvalidKeyException_get_Type_mFCF9566F7A5F7ECEF09376CD0D95CC78ACE44633 (void);
extern void InvalidKeyException_set_Type_m95D438D9D2D16C60E66334D606F8CCE73928A0CB (void);
extern void InvalidKeyException_get_MergeMode_m3B874B38F69384DAF973604A6946E0F21AE1360F (void);
extern void InvalidKeyException__ctor_m21E9020F3B24FBB0AF0AE05C0AE4763C16DE5323 (void);
extern void InvalidKeyException__ctor_mA75E071E74953129BAA2A83B92185C62931D173E (void);
extern void InvalidKeyException__ctor_mB9F03E732D5DA73676D6DF8EC03DEA22200B7BA3 (void);
extern void InvalidKeyException__ctor_m90E9CC579C3E857C03D9C00002CBF62B53C28850 (void);
extern void InvalidKeyException__ctor_m8D3B34B5E8910C77D93F429D76917D7338AF9E56 (void);
extern void InvalidKeyException__ctor_m96AB14996875BE89CC68F16D0ED9F9BA7BA98D8C (void);
extern void InvalidKeyException__ctor_mEF237FAA1A8213257158AF80BC52D1CCFF54EB94 (void);
extern void InvalidKeyException__ctor_m077154786074EF704E2A7016F4201686C8D1C20D (void);
extern void InvalidKeyException__ctor_m396FDC2F7F898D5AB463DD235713201A9FCFFFFD (void);
extern void InvalidKeyException_get_Message_mDB7371B5C9392DA9F8B939BC774A21B5EE372112 (void);
extern void InvalidKeyException_GetMessageForSingleKey_m5374F963B1E10C773466862AD10ECF4FF3759BB7 (void);
extern void InvalidKeyException_GetNotFoundMessage_mF3931E9199699200E317ABC1D926D9BA0829FA1C (void);
extern void InvalidKeyException_GetTypeNotAssignableMessage_m4419D1ABD095B7BE90D5D3284B3824AAC710BE11 (void);
extern void InvalidKeyException_GetMultipleAssignableTypesMessage_m71D5012299FCBBC8AA7D403C82FB923C67BC7CDC (void);
extern void InvalidKeyException_GetMessageforMergeKeys_mA2BBC8F4622CAACCB2A3B5CC42CDAF38AE061397 (void);
extern void InvalidKeyException_GetTypesForKey_m9DC047CFA2835A0E6CD415A8A6D42C9DD8F91F4F (void);
extern void InvalidKeyException_GetTypeToKeys_m6B2D3ECD7BE86E4A2D5820995E129AA401E7F0E1 (void);
extern void InvalidKeyException_GetCSVString_m8BEE349B248FE9D98DCD0AE3493E8E1D2E2AFE88 (void);
extern void Addressables_get_m_Addressables_mCBFD180C3722D8CE681814A67B652682C6710BEE (void);
extern void Addressables_get_Version_m5859B74ECD99A1FE471001736E4F0DBBB31CD586 (void);
extern void Addressables_get_ResourceManager_m480AE4DB9D40EABEF022FDAEA00A043DA52D1D5E (void);
extern void Addressables_get_Instance_m3E7F02F47DF6F68E8BC755D62A17A523817E19DA (void);
extern void Addressables_get_InstanceProvider_mF41503CC8D9E35608FD5B4CFEA318CEF9B2DEE06 (void);
extern void Addressables_ResolveInternalId_mF1D3A8B02C0D6741CB72AD8A55D5E82A36AA1782 (void);
extern void Addressables_get_InternalIdTransformFunc_m38C63DD89DD67E32EF724311E423C9E7F02ED910 (void);
extern void Addressables_set_InternalIdTransformFunc_m4C5D3FAF0A0A2D5D5A9B8D42301DB46D192CF173 (void);
extern void Addressables_get_WebRequestOverride_mDD30550FA978B31E56769281D177E128C8472BE8 (void);
extern void Addressables_set_WebRequestOverride_m88489CF0DEBBBBF186D016ED942FDD7C4F0A6D54 (void);
extern void Addressables_get_StreamingAssetsSubFolder_m515E605E96FA0C0AD73FD588743C61D323922632 (void);
extern void Addressables_get_BuildPath_m14B123D92F2BC1DE452782033BEA8D35F2B24604 (void);
extern void Addressables_get_PlayerBuildDataPath_m6C6803DDA5F78720729534974D363E2CCE98C046 (void);
extern void Addressables_get_RuntimePath_m8FC9F9D2B59FBCD4992E5C00A83B7516946BEF03 (void);
extern void Addressables_get_ResourceLocators_m6926D30F20D53A9A04E3A886BC5CD030A2A487ED (void);
extern void Addressables_InternalSafeSerializationLog_m70DC8762251BF472BCCB1827B9BA00503197AA2E (void);
extern void Addressables_InternalSafeSerializationLogFormat_m7061A5C16C5D0AA42717380C4EC9BFBE6E983114 (void);
extern void Addressables_Log_m672F41B5DCED757A773E01FE0E1B7EA54772B519 (void);
extern void Addressables_LogFormat_m5F59C1A02A958337C738D89A7D8B2A9F86C42B46 (void);
extern void Addressables_LogWarning_mE6B724D5D8608F3B426E8CFB94917FE28D6B851B (void);
extern void Addressables_LogWarningFormat_m113FDAC9E0B6D6D25774FBE6AD2E1BA82DCC5AE9 (void);
extern void Addressables_LogError_m14F9E54E8D0491FFE0254C08E7433BCB7D3119B6 (void);
extern void Addressables_LogException_m51128068141C2820273A09C8AD12787648078AB4 (void);
extern void Addressables_LogException_m4441DD24B424FDFEA229D4DA747A55E9E931A802 (void);
extern void Addressables_LogErrorFormat_m37C249A86F953A975C66573FA9A33F7C35F5D647 (void);
extern void Addressables_Initialize_m372DCB61F63896256F2B5453AFF7E6D24FA815AB (void);
extern void Addressables_InitializeAsync_m8F30FEFAF5514C2D0F978D4D92F09C6F575F80F3 (void);
extern void Addressables_InitializeAsync_m7EE86C523A74F3B7B5D36FE791DEC30F6DD4768F (void);
extern void Addressables_LoadContentCatalog_m12ACE159108BECD255B46D4E50F730E026D706DD (void);
extern void Addressables_LoadContentCatalogAsync_m1C496ADD1D371499DACA8A236FC11BBFB0AF622D (void);
extern void Addressables_LoadContentCatalogAsync_m5BA614644CBA0609A389D7F65705CA14CFD77B83 (void);
extern void Addressables_get_InitializationOperation_m1F6013D1B5884F567757B7A4F6AE4B02755CA68A (void);
extern void Addressables_LoadResourceLocations_m59705C66A7E5F19C5706C6BA716CC6BDBC6CC2CA (void);
extern void Addressables_LoadResourceLocationsAsync_mA31184C114F4B16056AB81F904DE573ED7A3AED7 (void);
extern void Addressables_LoadResourceLocationsAsync_mA7BC8A23176D4E1BAF0279763F81FEAB57F2BE43 (void);
extern void Addressables_LoadResourceLocations_mD486B5801D9DB46C4BEDF9746DF414E57B2CEE9B (void);
extern void Addressables_LoadResourceLocationsAsync_m96BF35184F12370BCD9A304065F42D080D7EE18B (void);
extern void Addressables_Release_m0A186637EB825A9C793E94DD9D5B00842EC67589 (void);
extern void Addressables_ReleaseInstance_m6673D687FF759A40D9351A6EE9666689A8770B3F (void);
extern void Addressables_ReleaseInstance_mDD4BA6F39D5557A7A9D3AB637B0B8D6A904DF34E (void);
extern void Addressables_ReleaseInstance_mE67A4267C9B7968F47C07233214F1AB9C57D5887 (void);
extern void Addressables_GetDownloadSize_mFDD7A4E47AC1FB3F9C0B884A4C5D7A5C6ABF53F5 (void);
extern void Addressables_GetDownloadSizeAsync_mF0E34AF916F5F61E1325863FC3960CFC1BB3E928 (void);
extern void Addressables_GetDownloadSizeAsync_m058D2B896EEB943A183AEE1E74D9428602F1D49B (void);
extern void Addressables_GetDownloadSizeAsync_mBD39CF5009539BE90B91E7EDB94C48E61EB2902D (void);
extern void Addressables_GetDownloadSizeAsync_m21569D24012E62C0D5B34AB060D8E4CEDAE4C427 (void);
extern void Addressables_DownloadDependencies_m48EBB3DC4C16A627ED197A7F0EE1581FD64E4C92 (void);
extern void Addressables_DownloadDependenciesAsync_mD5916DDBF0EE6DE8A5AC1B9D12B6DBF233B49D96 (void);
extern void Addressables_DownloadDependenciesAsync_m891471C5E090DEF0F7ABBBFD43D3EA99351891F7 (void);
extern void Addressables_DownloadDependenciesAsync_mCAFF6DC0D7CF7DFDAC352F516937B9C88FBB62E7 (void);
extern void Addressables_DownloadDependenciesAsync_m540DC6897FCC7409CC56B9BA77D6F91DFC7ECD98 (void);
extern void Addressables_ClearDependencyCacheAsync_mC0BD38E99D05EFDA61056DF039F4BF865653C465 (void);
extern void Addressables_ClearDependencyCacheAsync_mB47FBCDACD6C1D7537095E9F86A07F9F03E1D485 (void);
extern void Addressables_ClearDependencyCacheAsync_mE4FBB0C0A47CF1FF4244EC6466D2A667E92A2B79 (void);
extern void Addressables_ClearDependencyCacheAsync_mD82D927AB5AE8FCB74A7E510AFD6EB4E6A541AC1 (void);
extern void Addressables_ClearDependencyCacheAsync_m7831A86E7316252FFF70020877E57F9E923DEB7D (void);
extern void Addressables_ClearDependencyCacheAsync_m72FC60DEDA6EACD28042F011BC5A62B03BD8D619 (void);
extern void Addressables_ClearDependencyCacheAsync_m1B0922223364DB3C90A6756A03985D9962536DE7 (void);
extern void Addressables_ClearDependencyCacheAsync_m9E9CF3BB16ECB75D11C7CDCECDB9EF5648B56052 (void);
extern void Addressables_ClearDependencyCacheAsync_m2F2CBBE98127892DCE7339F15225EB62D6397A75 (void);
extern void Addressables_ClearDependencyCacheAsync_m6B4E01BA5DA3909306298321F481C5574A52B59B (void);
extern void Addressables_GetLocatorInfo_mE6E37BAB67195DD7E46010724F9B7DE02694DA27 (void);
extern void Addressables_GetLocatorInfo_m73EB1B8EFE6EE8D5E5331DA4E74007C9E2B79B97 (void);
extern void Addressables_Instantiate_m6ACDEA015DBD9C043D337BFCF56A61833689E3B9 (void);
extern void Addressables_Instantiate_mB733B72A1A18ECD75980D65A249A90D04EF15EE0 (void);
extern void Addressables_Instantiate_m4A8A87FD9A0C57570ABDB2F018FF30B025409EF6 (void);
extern void Addressables_Instantiate_m3BA65CA58E9A7417A43E4F760784ECC83ACB3D92 (void);
extern void Addressables_Instantiate_m853749C8B171C4D5E780C1560630324A6ACC8EE3 (void);
extern void Addressables_Instantiate_m05E5ADC46320370CCFA5D7D0EF8BC36BEBBCBEF8 (void);
extern void Addressables_InstantiateAsync_m61AA97078A68E75BDC0A85A21A6C4A9441CCF3D4 (void);
extern void Addressables_InstantiateAsync_m053C0AD711DC77C488635CA05493239D10F38CE5 (void);
extern void Addressables_InstantiateAsync_mD10FF052D569CA9E7027857932564E8630F9574E (void);
extern void Addressables_InstantiateAsync_m3B65AC3D763A0021C90B0B215FC11A6E1F437477 (void);
extern void Addressables_InstantiateAsync_m6378634E7D2CC4A036C4930884B7EEF3D1DB0496 (void);
extern void Addressables_InstantiateAsync_mD8D8FDC62D11C8D6B332F604D5D33AD920C8C511 (void);
extern void Addressables_LoadScene_mF4C8412BAB025C874A40464A42815EFD47A8DF05 (void);
extern void Addressables_LoadScene_m0DC9457A0F985FC5526E5387FF475C35FA85E27D (void);
extern void Addressables_LoadSceneAsync_m3868019E92C7809B7352837DB3D2D81088DA6134 (void);
extern void Addressables_LoadSceneAsync_m95B2F95F4D52B29ACE1AE76FF2B930766DA04AF1 (void);
extern void Addressables_LoadSceneAsync_m4F7123C212FBF24C8DDAED2102E3B67D7ADD65A4 (void);
extern void Addressables_LoadSceneAsync_m36AB6EA45E88D830500F1B47FACF4BB6701DF4B0 (void);
extern void Addressables_UnloadScene_m661FF1AEBA03DFC66FA01694C424798AF9C8FDA8 (void);
extern void Addressables_UnloadScene_m7AA8C415D40D5EEDBE6CD3E32221C32C4549C8AC (void);
extern void Addressables_UnloadScene_m10E336597465DCBA695F407959FE9130147179F1 (void);
extern void Addressables_UnloadScene_mA03FC15C8A53E6163BEBA7271F93519E42E87D35 (void);
extern void Addressables_UnloadSceneAsync_mFD6664CE234291B2F69459F282BE5FF4B85646BA (void);
extern void Addressables_UnloadSceneAsync_m60FB61C85398F746E3A2977BDCD88C7DD2AC026A (void);
extern void Addressables_UnloadSceneAsync_m8CD5516D612F97860F6D3AD2054176BE90445FF2 (void);
extern void Addressables_UnloadSceneAsync_mD68AD0B6FF8E9570DBB6AFDD146107C705049F5D (void);
extern void Addressables_UnloadSceneAsync_m01ABFEE8878DE15A676284B2E1C998DE23EA8AA7 (void);
extern void Addressables_CheckForCatalogUpdates_m4AB4B9D717C9BB941E9379B105A3479457D95A1D (void);
extern void Addressables_UpdateCatalogs_m7AEECFD1B41CD4A0EB2AF0AB3A9589564F0C3024 (void);
extern void Addressables_UpdateCatalogs_mC1709A955E2BC1C4371BF7DF91C1DDFF0B7BE9A4 (void);
extern void Addressables_AddResourceLocator_m80AAFA9FADC9637D28B4CFF7508A17A2BFC71F82 (void);
extern void Addressables_RemoveResourceLocator_m755F8FFE85FD38CEB18543A7ACBB241653180A01 (void);
extern void Addressables_ClearResourceLocators_mEDA2320D42AC72BB2DD48560EA97D8EF84E60E09 (void);
extern void Addressables_CleanBundleCache_mD40E5A18BFD8ABECE6E8D5EBB4FEE6A5F51C80ED (void);
extern void Addressables__cctor_m9269D2DB2C8115DAF077A0A3801015469F9825BE (void);
extern void AddressablesImpl_get_InstanceProvider_m8BC14A958EB91A2BD3A686758670C728190A6090 (void);
extern void AddressablesImpl_set_InstanceProvider_mA92B463DD7BCDB14B2EDCD84F58D7F595CC5A01F (void);
extern void AddressablesImpl_get_ResourceManager_mFCBBB9F321655F40BDD1CAF07194C1AA40BCB656 (void);
extern void AddressablesImpl_get_CatalogRequestsTimeout_mA809B3948354BE139452696AEC4D97E4D1E2E4FB (void);
extern void AddressablesImpl_set_CatalogRequestsTimeout_mBD266E78770ADCFBEFFADF14AA524B67CE2222DD (void);
extern void AddressablesImpl_get_SceneOperationCount_m0E34C64E4228D8AEF0FE3C98A663784CA111BC55 (void);
extern void AddressablesImpl_get_TrackedHandleCount_m8E6536E52B6A517110CC75974832B6B7DB72FE15 (void);
extern void AddressablesImpl__ctor_m0D145411AE0D75A5FD395FACBE97807535D02974 (void);
extern void AddressablesImpl_ReleaseSceneManagerOperation_mABA795B6F5262B1A77B81E817798F8966C462A04 (void);
extern void AddressablesImpl_get_InternalIdTransformFunc_m805CE26548AF0BE138B2212784F882066B3F1E71 (void);
extern void AddressablesImpl_set_InternalIdTransformFunc_m832753A5BBAAA21DF25F69A464E3B4C6DB38DCD1 (void);
extern void AddressablesImpl_get_WebRequestOverride_mCDA70F560071BBABA265369E3192DAA9E36264E5 (void);
extern void AddressablesImpl_set_WebRequestOverride_mAF441D4DF4914BA54E1BAC5E2D5E54187CCD1216 (void);
extern void AddressablesImpl_get_ChainOperation_mD1E214493952096323F333E3315296E79C812DCE (void);
extern void AddressablesImpl_get_ShouldChainRequest_m7B0C3F2F8AC53881B87AB144601A4E80DE69560D (void);
extern void AddressablesImpl_OnSceneUnloaded_m3BE985BC72C4FC0E4046123CB977B7E6AACCF4C8 (void);
extern void AddressablesImpl_get_StreamingAssetsSubFolder_m836CFC1375369C5895DEA6E1D779DC09A466C31B (void);
extern void AddressablesImpl_get_BuildPath_m6621C8C65DAEF38F23E826AFA7E51B05FEAA924C (void);
extern void AddressablesImpl_get_PlayerBuildDataPath_m29B43E1D45B38754C648ABECDB7C9783E12CFD8B (void);
extern void AddressablesImpl_get_RuntimePath_m516C45F2BF5F5622F31FC11CD967DDDD02CCBE6A (void);
extern void AddressablesImpl_Log_mFD5E4D7EF8166751055599E2D73C60181CB5EAD2 (void);
extern void AddressablesImpl_LogFormat_mB535C0BB3A5E30C93E8FF6DBECB0B4C756B09661 (void);
extern void AddressablesImpl_LogWarning_m1A027072AF03D4E499CF922978CBED57344AAF8A (void);
extern void AddressablesImpl_LogWarningFormat_mE3F74A1473E680C02FAB802B2FBA8779BBA4ECC2 (void);
extern void AddressablesImpl_LogError_m52A2B1A6E3B166A075222CE0D00EB3124ECCB0D0 (void);
extern void AddressablesImpl_LogException_m8F960C2ADDA73A9E3F99D14AEB0BF77209E0966E (void);
extern void AddressablesImpl_LogException_m7C420869EAA7619FC909037D84E5E7A93BEB7A3B (void);
extern void AddressablesImpl_LogErrorFormat_m9CA5AD09EC3AAE49EC71065499CEE0ED3E5F1B05 (void);
extern void AddressablesImpl_ResolveInternalId_m858249BA7A7A6C41684815D2A09618EEC81F48CC (void);
extern void AddressablesImpl_get_ResourceLocators_m8E2213BC51CCDBF257902E4E17E0C9385457288F (void);
extern void AddressablesImpl_AddResourceLocator_m4B7B6E7D82E6F6D3B11BC865C417A5C4F891A0FE (void);
extern void AddressablesImpl_RemoveResourceLocator_m3648D6A6C2FDA74776BD0EAB41E370D06EC952A5 (void);
extern void AddressablesImpl_ClearResourceLocators_m1DAD782DEEAD2D25F026BB3305CD88A82C34FF8E (void);
extern void AddressablesImpl_GetResourceLocations_m51B702F089F2E776BFB4D3C0D3C0CF982B1C77F1 (void);
extern void AddressablesImpl_GetResourceLocations_m868373B7918DBF99851290C662E7A92E06EEB982 (void);
extern void AddressablesImpl_InitializeAsync_mA3C3DC099508914EF7A101A6DD6D8D5F9B6D1245 (void);
extern void AddressablesImpl_InitializeAsync_m39F307E3349132C26AEA3CDB5711109DB64AD358 (void);
extern void AddressablesImpl_InitializeAsync_m686DE491E4A5336A025D165F1D83D73A4DA23B66 (void);
extern void AddressablesImpl_QueueEditorUpdateIfNeeded_mD562431EC2529EC5EE6946FF1BB69723987A7086 (void);
extern void AddressablesImpl_LoadContentCatalogAsync_m3F26BD7F722C8D44FC6231023F98619B7523FA23 (void);
extern void AddressablesImpl_TrackHandle_m83943CB8288691A45B0C30A6A0DD1237CFE93A95 (void);
extern void AddressablesImpl_TrackHandle_m0B6C04AB3AB14BA9A1986DD7A14B25311A25C1C7 (void);
extern void AddressablesImpl_ClearTrackHandles_m015173AECE6C5AEFACE8B99ACDFDB7A3B35F1FA7 (void);
extern void AddressablesImpl_LoadResourceLocationsWithChain_mD0C9A4B25EF3217C5BBBC0C3B95BBFA1E006516F (void);
extern void AddressablesImpl_LoadResourceLocationsAsync_m2B02B077B4C9EBF5AF34FFE2C7066A94CD1D7DC4 (void);
extern void AddressablesImpl_LoadResourceLocationsWithChain_mBE480C20C62FC3427B07DBF7B81998D81ED87083 (void);
extern void AddressablesImpl_LoadResourceLocationsAsync_m507C5B218CA7202B33C9E3C16AA442FD9E2F0A16 (void);
extern void AddressablesImpl_OnHandleDestroyed_m48205B3E1BBA2C490C9737E3EF61F2CC0294CD43 (void);
extern void AddressablesImpl_OnSceneHandleCompleted_m542A4475E4DA62372E5F423BF70A0363F141EA87 (void);
extern void AddressablesImpl_OnHandleCompleted_m0F6517BB79B2FFEF7D3DCB8D5B19D1DEDE90B38E (void);
extern void AddressablesImpl_Release_mBCA4FCEFB7E34BD7DA17F90CC86028197383A61D (void);
extern void AddressablesImpl_GetDownloadSizeWithChain_m8C34CD7F772F35D787FE002E6B2063816C5F5F7F (void);
extern void AddressablesImpl_ComputeCatalogSizeWithChain_m169C765E42B01EA37A4236314BE55543E708B460 (void);
extern void AddressablesImpl_IsCatalogCached_mE931FAC878D0C8FCD9BBE298826D7D776EE23810 (void);
extern void AddressablesImpl_GetRemoteCatalogHeaderSize_m0F2AD3A7A76933B73985021C2AD92081599B4718 (void);
extern void AddressablesImpl_GetDownloadSizeWithChain_mCD6CABEBE8D17E5F80956CA61431B2969EEB659D (void);
extern void AddressablesImpl_GetDownloadSizeAsync_mCD8F00A23FB807951242D109791248C542EE9B76 (void);
extern void AddressablesImpl_GetDownloadSizeAsync_mF7C1FB7C9793E9A7715EF4838E1E098C39903DB0 (void);
extern void AddressablesImpl_DownloadDependenciesAsyncWithChain_m82EA58A0C022B1FF208798D5D3E981764E3077CC (void);
extern void AddressablesImpl_WrapAsDownloadLocations_mFE727D4AE96D0080C8DBF546FCEBCA20D2272401 (void);
extern void AddressablesImpl_GatherDependenciesFromLocations_mC7A83A12F11693B271885BFCA7B2869C7B817E88 (void);
extern void AddressablesImpl_DownloadDependenciesAsync_m4E27BE7C9A8C259B1AD4A7CF11765924981E8997 (void);
extern void AddressablesImpl_DownloadDependenciesAsyncWithChain_m5867093606DB38893E91DE7996F837BCD83792AB (void);
extern void AddressablesImpl_DownloadDependenciesAsync_m10CBB4F9F01BE24C4FA47D900F61D1BEABD4DEE4 (void);
extern void AddressablesImpl_DownloadDependenciesAsyncWithChain_m00678A3E449AA0DF2BD3B6225CABA77527A012E3 (void);
extern void AddressablesImpl_DownloadDependenciesAsync_mC64850B023B6B832EFD3C644A2A06CE4FC71DE2F (void);
extern void AddressablesImpl_ClearDependencyCacheForKey_m26E7292DBBAB525BC1665E129684F51A39E5A8CB (void);
extern void AddressablesImpl_ClearDependencyCacheAsync_mE7ACB0D3E62DDD07F64712F3AD798D68D6775F73 (void);
extern void AddressablesImpl_ClearDependencyCacheAsync_m0DB5056B4986B2A50A970C6B9E462A7D93261BB6 (void);
extern void AddressablesImpl_ClearDependencyCacheAsync_m283EC8640789B6FFC2EC7600903163356ED64396 (void);
extern void AddressablesImpl_InstantiateAsync_mFA8C8388EB69536C452ED0B70A14C00BCA3BA095 (void);
extern void AddressablesImpl_InstantiateAsync_m408055C93BD550F7359348E077C8B9FF3471B381 (void);
extern void AddressablesImpl_InstantiateAsync_mD834F9EFCC94F9FB72BD2BC3EAE53BD7F9861E58 (void);
extern void AddressablesImpl_InstantiateAsync_mEC6599097225FEA1388691B7B7AA1309A81F39ED (void);
extern void AddressablesImpl_InstantiateWithChain_m2CB2C73F5F8ED56745C79E4CF30337556D5FCB6B (void);
extern void AddressablesImpl_InstantiateAsync_mEE0922EE21FCF112AB428A526D228BFFBDD810AD (void);
extern void AddressablesImpl_InstantiateWithChain_m518DA48F0A30FC8D22B73227313CD328E13E2102 (void);
extern void AddressablesImpl_InstantiateAsync_m9C8C0A5C1240DA8367D5D1D675372AF193ED1DB4 (void);
extern void AddressablesImpl_ReleaseInstance_mE61D6A077166382F5DC59DC3D3DFA6C90270705D (void);
extern void AddressablesImpl_LoadSceneWithChain_m6580B1C924CC4BDA2600CD49F6D198949E1594F6 (void);
extern void AddressablesImpl_LoadSceneWithChain_m34FFD23F3C24F5790457810FBE80FB73250FDED8 (void);
extern void AddressablesImpl_LoadSceneAsync_m3733A0B410868ABB911F915D83D31D7B07BF0BD0 (void);
extern void AddressablesImpl_LoadSceneAsync_m6E60A77B0D23933801B938CCDB5CC48B2ACF0F24 (void);
extern void AddressablesImpl_LoadSceneAsync_mDB5B0182F0A5A489B9A411CC4CE8E0AA5B961F0C (void);
extern void AddressablesImpl_LoadSceneAsync_mCB03AE1B2FF7EEBD52E9D1E9440B9932F701A1D6 (void);
extern void AddressablesImpl_LoadSceneAsync_m3C85CAE0B3A111A496E86A67352F6D069C2E3039 (void);
extern void AddressablesImpl_UnloadSceneAsync_mBADAC031E335590330C3DDE567EA61F15CF4FDFF (void);
extern void AddressablesImpl_UnloadSceneAsync_m37210AD09729FEAF3F075450BCC9A7C38B6A488C (void);
extern void AddressablesImpl_UnloadSceneAsync_mBD793D435D1FFC95B2F575701E2B05F78D67C4C3 (void);
extern void AddressablesImpl_CreateUnloadSceneWithChain_m4773DCFDCD481D0677ED8738A34DDF1C2E97964F (void);
extern void AddressablesImpl_CreateUnloadSceneWithChain_m217E2D006EAAEA5FE9140909C3E4D823A11ED527 (void);
extern void AddressablesImpl_InternalUnloadScene_mFFCE1581450DEFA20442388F93A496C50C2C0B71 (void);
extern void AddressablesImpl_EvaluateKey_mF8F0DBA33B0A60464585E2422A6D054E34E8B4F8 (void);
extern void AddressablesImpl_CheckForCatalogUpdates_mCBEC83E0DC72938D3F55F6E3B5B193B921E3619F (void);
extern void AddressablesImpl_CheckForCatalogUpdatesWithChain_m4A4C8F003EB53481A634BE4995016490E447886C (void);
extern void AddressablesImpl_GetLocatorInfo_mC638F2D008FE42A18F3672B57EECFFF4E1177E9E (void);
extern void AddressablesImpl_get_CatalogsWithAvailableUpdates_m9E268E83C0B9766C88A75CC22422AE06F780B464 (void);
extern void AddressablesImpl_UpdateCatalogs_mB39266B1E19DD7179946990544FDBC123D6F2F23 (void);
extern void AddressablesImpl_Equals_m9C9049FC1B9E31A96E7440E849688849815B79AA (void);
extern void AddressablesImpl_GetHashCode_m9AFBC9F6CD9EEB911131180B201E42969DF95DA7 (void);
extern void AddressablesImpl_CleanBundleCache_mBF5BFA48E1ADC3D8175D6002B4805DF80F324F43 (void);
extern void AddressablesImpl_CleanBundleCache_mF07D7181620D08FB994A5E05A15032D58BAB2261 (void);
extern void AddressablesImpl_CleanBundleCacheWithChain_m8DAC379852F84E5A3BC326DA1788BE326B5ABA08 (void);
extern void AddressablesImpl_CleanBundleCacheWithChain_m1D5DAC06240014BB573DDFEBF73C144259BFD787 (void);
extern void AddressablesImpl_U3CTrackHandleU3Eb__73_0_m505860096F2742FCB0EC2C7AA5EF036A77890B68 (void);
extern void AddressablesImpl_U3CGetRemoteCatalogHeaderSizeU3Eb__102_0_m52CF758D484BBEE4162EB560D7F23A8C2F59F05C (void);
extern void LoadResourceLocationKeyOp_get_DebugName_m7182C31D9604CBB0162CA85BCA00F146B604C0BD (void);
extern void LoadResourceLocationKeyOp_Init_mC9E0250DEE601A85A904837AB8B7DFDE38768889 (void);
extern void LoadResourceLocationKeyOp_InvokeWaitForCompletion_m8C6DCBB4BB0EFAE3A7D4B8B98C67649881DB2250 (void);
extern void LoadResourceLocationKeyOp_Execute_mAFA1E3E5CF26E7DDFFD39CC87023F80796B2AFD2 (void);
extern void LoadResourceLocationKeyOp__ctor_mB595D261116AA11C4A07330E3E86560D05394044 (void);
extern void LoadResourceLocationKeysOp_get_DebugName_m4C1800E01BE2301FD440FD23835F102F78978C15 (void);
extern void LoadResourceLocationKeysOp_Init_m0B0FB4F0FEF6A6CE880EE39AB8A55A2FA4403D03 (void);
extern void LoadResourceLocationKeysOp_Execute_m45739EC09ED6412F383E7B03C7CBD2C152357234 (void);
extern void LoadResourceLocationKeysOp_InvokeWaitForCompletion_mE0E2D9BB217B3F830A6D56E3BBE07D862567822D (void);
extern void LoadResourceLocationKeysOp__ctor_mB930530F598048D7253C4F9346DD6F7C55FB8C56 (void);
extern void U3CU3Ec__cctor_m65F36F3B1EDBCF7D5815B0324971AFDAAD1A1654 (void);
extern void U3CU3Ec__ctor_m174D0B02F3D309C783651F872C869FA2462C9D57 (void);
extern void U3CU3Ec_U3Cget_ResourceLocatorsU3Eb__59_0_mF1BED58AE9127242AA85FA2279E67E46F1A0CDEF (void);
extern void U3CU3Ec_U3Cget_CatalogsWithAvailableUpdatesU3Eb__146_0_mBFE07768CE72A2DC93CBE46D96B65B9E9DC621C5 (void);
extern void U3CU3Ec_U3Cget_CatalogsWithAvailableUpdatesU3Eb__146_1_m00AF67900E74CA442591411209B38CFCD9E3B0AF (void);
extern void U3CU3Ec_U3CCleanBundleCacheU3Eb__150_0_m358FDE260FA8DCEE33099E88A7C72765E7484237 (void);
extern void U3CU3Ec__DisplayClass100_0__ctor_m684C2E526C7F6056C1B6FA138CBB33C2FA4AD350 (void);
extern void U3CU3Ec__DisplayClass100_0_U3CComputeCatalogSizeWithChainU3Eb__0_mCBB70B9839F6C073CA118F6F9162A258C7CC0DF6 (void);
extern void U3CU3Ec__DisplayClass103_0__ctor_m1D354FB7E66A6F2EA3EBCF482674343D34538159 (void);
extern void U3CU3Ec__DisplayClass103_0_U3CGetDownloadSizeWithChainU3Eb__0_m950A041EC832A778DC5B8A3CB31679398FC4F62C (void);
extern void U3CU3Ec__DisplayClass106_0__ctor_mBBF922B20F9286E096B0E40D15A48EC7DA1BD464 (void);
extern void U3CU3Ec__DisplayClass106_0_U3CDownloadDependenciesAsyncWithChainU3Eb__0_m3E0BA0E2E942629B40D9F833D0FA7197ACF73E64 (void);
extern void U3CU3Ec__DisplayClass110_0__ctor_m1F7F7D26903507C157E56FDFE5D54607F59A140C (void);
extern void U3CU3Ec__DisplayClass110_0_U3CDownloadDependenciesAsyncWithChainU3Eb__0_mFBA6719807263C72ED3BFCD8D2E8FE48D107391D (void);
extern void U3CU3Ec__DisplayClass112_0__ctor_m65B0C78E971B8B62537C7292661A6FFBDC875B21 (void);
extern void U3CU3Ec__DisplayClass112_0_U3CDownloadDependenciesAsyncWithChainU3Eb__0_m1D29DA8A2C7159E318E681849678324C764850BE (void);
extern void U3CU3Ec__DisplayClass116_0__ctor_m6BD9C0B7BEDBC4FD2BF7C94CDFA61E2385D3C389 (void);
extern void U3CU3Ec__DisplayClass116_0_U3CClearDependencyCacheAsyncU3Eb__0_mD7057AA8D1546A397FE4F2DF6235D208C4EDDF23 (void);
extern void U3CU3Ec__DisplayClass117_0__ctor_mA28CA9FC30616995A19CEA1A0B26C1139BFF813E (void);
extern void U3CU3Ec__DisplayClass117_0_U3CClearDependencyCacheAsyncU3Eb__0_m34E148D13E7CE7678D20730A7B46752CBB8E7BD1 (void);
extern void U3CU3Ec__DisplayClass118_0__ctor_mFED3F459A43F9D6AF58C0A8DFB6D667438DB4C61 (void);
extern void U3CU3Ec__DisplayClass118_0_U3CClearDependencyCacheAsyncU3Eb__0_m603B8B15F4605959C0ACCB1AA5E4A2C7A59C5426 (void);
extern void U3CU3Ec__DisplayClass123_0__ctor_mE4C1E9D92F36E8942ECED0219DFE2216AEF649A0 (void);
extern void U3CU3Ec__DisplayClass123_0_U3CInstantiateWithChainU3Eb__0_m2DFD778DD90EE1C46AC6DCFE9DB6E755CD8F193E (void);
extern void U3CU3Ec__DisplayClass125_0__ctor_m5775CE749F80AE36E0889989C3993B0AF81BD50F (void);
extern void U3CU3Ec__DisplayClass125_0_U3CInstantiateWithChainU3Eb__0_m8B6E24922C4D8097AE7FE3526B637B3F9E00854D (void);
extern void U3CU3Ec__DisplayClass128_0__ctor_mDE52D65C4530B329961C6DF540EDA6C5B66A204B (void);
extern void U3CU3Ec__DisplayClass128_0_U3CLoadSceneWithChainU3Eb__0_mB0C5168A3DD03396D0156AAF6E1C552DEB3A5307 (void);
extern void U3CU3Ec__DisplayClass129_0__ctor_m690CE513F27DC3315914A7D1D11FAE64BD220725 (void);
extern void U3CU3Ec__DisplayClass129_0_U3CLoadSceneWithChainU3Eb__0_mE92E7D69BA593931E23732EA3F3E4CFAB17194ED (void);
extern void U3CU3Ec__DisplayClass138_0__ctor_m354E2F6C07374E6AE11A9F87FF59CF27235B3485 (void);
extern void U3CU3Ec__DisplayClass138_0_U3CCreateUnloadSceneWithChainU3Eb__0_m444850DE80B083E0C15CAFBCE5163DC7FAB0A177 (void);
extern void U3CU3Ec__DisplayClass139_0__ctor_mAE4DEB73391DF8A15D7B3E2518F35EE23ADC6626 (void);
extern void U3CU3Ec__DisplayClass139_0_U3CCreateUnloadSceneWithChainU3Eb__0_m57B6218F34E4A89B44DBE91B3D88BA715D443E30 (void);
extern void U3CU3Ec__DisplayClass143_0__ctor_mC0E6F10EFDE96ACF18F39401A69CD9D9E153D2F6 (void);
extern void U3CU3Ec__DisplayClass143_0_U3CCheckForCatalogUpdatesWithChainU3Eb__0_m982B61590FDE9CD99A70B6659C1A057DCF87C98B (void);
extern void U3CU3Ec__DisplayClass147_0__ctor_m70885B58326BD0FF5BE810516766B9AF4ABD6ED3 (void);
extern void U3CU3Ec__DisplayClass147_0_U3CUpdateCatalogsU3Eb__0_mAAEB96AABA4C132DE6BFBF790CD7AD534350CD9C (void);
extern void U3CU3Ec__DisplayClass152_0__ctor_mC3B74ECDD1409A2C16FD416349F34EE92AE0895B (void);
extern void U3CU3Ec__DisplayClass152_0_U3CCleanBundleCacheWithChainU3Eb__0_m1C8339C21E05486F523C425F615248D9600A4881 (void);
extern void U3CU3Ec__DisplayClass153_0__ctor_mA53062822C78034E8105AFEFF147F3A32D028E07 (void);
extern void U3CU3Ec__DisplayClass153_0_U3CCleanBundleCacheWithChainU3Eb__0_m08B8620435E94C68C9B3F14B1667FCF9EEADC11F (void);
extern void U3CU3Ec__DisplayClass61_0__ctor_m54906041791B9BD4DD7137E8042FC7EFAF082DD4 (void);
extern void U3CU3Ec__DisplayClass61_0_U3CRemoveResourceLocatorU3Eb__0_mD8B0008E5181881BD56E79E53B362255378468AF (void);
extern void U3CU3Ec__DisplayClass72_0__ctor_m571A9A00E8C38F2CF5491E783F8BD737561587CF (void);
extern void U3CU3Ec__DisplayClass72_0_U3CLoadContentCatalogAsyncU3Eb__0_m3185C31DC80EB494BF66753EE0B44CBD8C6851C2 (void);
extern void U3CU3Ec__DisplayClass83_0__ctor_mDD6F3FCC7B2EAC2546437094AA8CD187057E4F49 (void);
extern void U3CU3Ec__DisplayClass83_0_U3CLoadResourceLocationsWithChainU3Eb__0_mE9A8D713FC4E61BC01B0799BBD61721026033A67 (void);
extern void U3CU3Ec__DisplayClass85_0__ctor_m6F49FA14D34D371984A637BEFAFBCC4C221F3C82 (void);
extern void U3CU3Ec__DisplayClass85_0_U3CLoadResourceLocationsWithChainU3Eb__0_mF16389453D43CB0754F35C68E6FD956AFC1B8735 (void);
extern void U3CU3Ec__DisplayClass99_0__ctor_m65D4E527C4C59C76237B590810A89986C3B6F52A (void);
extern void U3CU3Ec__DisplayClass99_0_U3CGetDownloadSizeWithChainU3Eb__0_mF5018219FDED956758ED4E6B0ECEE6F1E7FB0F01 (void);
extern void AssetLabelReference_get_labelString_mB279E3D5A71CEAB12320C34D42F6A8693446F457 (void);
extern void AssetLabelReference_set_labelString_mC49D18432DACA47F342CE3D233F1AFC7561792FE (void);
extern void AssetLabelReference_get_RuntimeKey_m93CE4F4594DD522C0A89B9069C49108EA6E7CA38 (void);
extern void AssetLabelReference_RuntimeKeyIsValid_m82C616599AD12D7D943E75D0B516F07B1A0F3DAA (void);
extern void AssetLabelReference_GetHashCode_m63A7B342B3FFEE89919B13047314E29FFCEA323A (void);
extern void AssetLabelReference__ctor_m98399A6E27E40BD44A990540BBCC00CC2FAB8D6F (void);
extern void AssetReferenceGameObject__ctor_m06F030B2E5586975C190F18A13FB37C298987BB5 (void);
extern void AssetReferenceTexture__ctor_m6D8A78D21DE7A7A902A51DC16F8EA7162679A1DF (void);
extern void AssetReferenceTexture2D__ctor_mC08F14BA36A9EE52E4F21CDF429881B6C5A450CB (void);
extern void AssetReferenceTexture3D__ctor_m8AAC6DEA3754402CDF06323A3C7517341BC0D5F8 (void);
extern void AssetReferenceSprite__ctor_mDC0E86A070B7808FA3CA6DF2EBBD4E3725905BE2 (void);
extern void AssetReferenceSprite_ValidateAsset_mF5856555E8A3156E9A11905129EBD34EDB93107A (void);
extern void AssetReferenceAtlasedSprite__ctor_m1831E591FBA26F58A5B09C22C729E187A8B1EE12 (void);
extern void AssetReferenceAtlasedSprite_ValidateAsset_mB3E5C678C3241C501306B6E796FEFA0723EB5979 (void);
extern void AssetReferenceAtlasedSprite_ValidateAsset_m48DE085EB5AA8AE65BAE45675359F7A01CFBACEC (void);
extern void AssetReference_get_OperationHandle_mA4ACC3916FDA7DFC1E8C4B771B68241E049AEAE7 (void);
extern void AssetReference_set_OperationHandle_m9A1C8675CD0978DDB20CFA77E82609436B525CEA (void);
extern void AssetReference_get_RuntimeKey_mB52D387E5244EAD047745D2A8904DA0D9C864F74 (void);
extern void AssetReference_get_AssetGUID_m74C838B7F893C1B3230C6ED65EF5B23BC6DBD4F9 (void);
extern void AssetReference_get_SubObjectName_m0B0C2FA0541A14BB11C760D4DC64FEB83D0798B0 (void);
extern void AssetReference_set_SubObjectName_mF779DF6D4BE9BA7DB4F9685F3B46F02F83BE7012 (void);
extern void AssetReference_get_SubOjbectType_mC090A0EE55581CADB950F2884C1C69553D36885C (void);
extern void AssetReference_IsValid_m3A1FA94BA7D6573485F6D3120740EC6CDF4BE4D9 (void);
extern void AssetReference_get_IsDone_m95CA175979A05DB8FA809157093ACEEED5ED45FD (void);
extern void AssetReference__ctor_m1B9255F08152E57E683A1C4DB603BF2C75605BAB (void);
extern void AssetReference__ctor_m239611FF89EAB0B2AC82E7E3303166B910F69C8B (void);
extern void AssetReference_get_Asset_mB104EEC61E7E58914D43CFBAAFF518979E27F12B (void);
extern void AssetReference_ToString_mBBCBADD628EC8D46171956F79B4F869DE8688FCE (void);
extern void AssetReference_LoadScene_mB6C819789F857CD630CACC5F8F22598C94D06BCC (void);
extern void AssetReference_Instantiate_mF5626C68E0B70B0468ABD1B481BDEDBEEEBD01C6 (void);
extern void AssetReference_Instantiate_m39937255B168D7849CC7CE05262630041298A94C (void);
extern void AssetReference_LoadSceneAsync_m07D39AC840AD18BAE80FB8050CFA7F3D5D452B7C (void);
extern void AssetReference_UnLoadScene_mEC0A04AE8FCE47164961111A9A59E8E0FF7F333B (void);
extern void AssetReference_InstantiateAsync_m4C9966DA2ECA2BFBF5520C5C7831A2778394A1AE (void);
extern void AssetReference_InstantiateAsync_mBE3A36E01A59222911FF2A8D354C32D4188EFCE5 (void);
extern void AssetReference_RuntimeKeyIsValid_m72A4C6B49D06D3985FEDE36CB19667FE8E23BDF5 (void);
extern void AssetReference_ReleaseAsset_mB83E9D70CEB0042D15EA5BC9B00E245EF6765A02 (void);
extern void AssetReference_ReleaseInstance_m14337B1684BF39CE7E5C2B31AF7FBBB9CCAFD164 (void);
extern void AssetReference_ValidateAsset_mCA00616B7F960333A13C518E0E388B1D1D09B1A5 (void);
extern void AssetReference_ValidateAsset_m509B132545E3C9FA5526F013F5D7A77986D141CA (void);
extern void CheckCatalogsOperation__ctor_m219855D6B8101674B79BE71D2BB298AE35CAD656 (void);
extern void CheckCatalogsOperation_Start_m33EFCDB1C93D3349DCDA41708DF3D753AB820CFD (void);
extern void CheckCatalogsOperation_InvokeWaitForCompletion_m994BDCA439BEBF467344D6EA6007AE051E9A9309 (void);
extern void CheckCatalogsOperation_Destroy_mBCF867F11D72E29998EA013BC63433EA8DD9E000 (void);
extern void CheckCatalogsOperation_GetDependencies_mBDFDEE060B2A746F85610F32776E8916195D214D (void);
extern void CheckCatalogsOperation_ProcessDependentOpResults_m6782EC6BB33564D5AA2D5D5B59246DD409FDD382 (void);
extern void CheckCatalogsOperation_Execute_mDDD66461E88E6B57525CCB4A1B293FF8CAF4E13E (void);
extern void U3CU3Ec__cctor_mC1BAC0157CA2916216A98CCB39AD8D02F35A95C6 (void);
extern void U3CU3Ec__ctor_mFDACDE60C12E191E91BB59108F0CCFE6361BCD39 (void);
extern void U3CU3Ec_U3CStartU3Eb__5_0_m62ABBDF4E546D441FE3FC90FA66E63A22B2B9B36 (void);
extern void CleanBundleCacheOperation__ctor_m00C16277A8C859B463590EC27DAD938B788EA5DA (void);
extern void CleanBundleCacheOperation_Start_m56A1365CE9BA04E28BE051AF0E991C97B6EF735A (void);
extern void CleanBundleCacheOperation_CompleteInternal_mAAFBE0F89A6C33907B32C44E5D97AC7F8CD58D0F (void);
extern void CleanBundleCacheOperation_InvokeWaitForCompletion_m1C18F200ED096D4E538CA37231A8960F37D25D17 (void);
extern void CleanBundleCacheOperation_Destroy_m1BD6BBA1B13AB4E54109432A548504792B561F3E (void);
extern void CleanBundleCacheOperation_GetDependencies_mEDA53E9E18ADA55C196CF1496913026C3CA0C9F9 (void);
extern void CleanBundleCacheOperation_Execute_mB7D5A06CB45135400A0A93E39F3CF27BF6A5D24F (void);
extern void CleanBundleCacheOperation_UnityEngine_ResourceManagement_IUpdateReceiver_Update_mB772F83D47A57F362C6448745297CD3D5C4597FB (void);
extern void CleanBundleCacheOperation_RemoveCacheEntries_mF5384D24FAD20F730A5CB97470ED078B5F702752 (void);
extern void CleanBundleCacheOperation_DetermineCacheDirsNotInUse_mADF2AC1F2761135BDF6D3996D2689A10E5034764 (void);
extern void CleanBundleCacheOperation_DetermineCacheDirsNotInUse_m68FEAC51EE1B3C73D746AD9526AECB8109D01B60 (void);
extern void CleanBundleCacheOperation_GetCacheDirsInUse_m5DAFD43E40E32558662FF5BCF05375F16ACB9E7E (void);
extern void UpdateCatalogsOperation__ctor_mC24059B34401DCEDEAC6EF7AE232397A4B8F97B6 (void);
extern void UpdateCatalogsOperation_Start_mC3FB55D3ABBD31F4C10073A294CFB2A0B1E200A7 (void);
extern void UpdateCatalogsOperation_InvokeWaitForCompletion_m53FBDFEC0AFFD17C12A118EC2EAAF52C83B6AA45 (void);
extern void UpdateCatalogsOperation_Destroy_m29BDCE763155716CF97F6BA9C1FCC28703F35377 (void);
extern void UpdateCatalogsOperation_GetDependencies_m159515E7C3E3738EE2B3D4CBEA5C755A34ADCE93 (void);
extern void UpdateCatalogsOperation_Execute_mA269F67EA0C49DF42FC4E2C755C2114C305F24DF (void);
extern void UpdateCatalogsOperation_OnCleanCacheCompleted_mBAAD03085035B2DCF2781FE69D13ED5A9243604C (void);
extern void U3CU3Ec__cctor_mE9D554A2F26607B4DEBFD9E7D83BF6D58526FA0F (void);
extern void U3CU3Ec__ctor_mD572CF2016ADF8E07CDBFF03927C35D1589ADFF3 (void);
extern void U3CU3Ec_U3CStartU3Eb__6_0_m7F9D05BAB1E9E02C53B91F2D8714A907ECD64F22 (void);
extern void U3CU3Ec__DisplayClass11_0__ctor_mC0990EDA323280DF0279337D541A34BD249E427A (void);
extern void U3CU3Ec__DisplayClass11_0_U3COnCleanCacheCompletedU3Eb__0_m521279AD645B82BD7399DF0A599B94A3475C08FB (void);
extern void DynamicResourceLocator_get_LocatorId_mAB65647CC9D934BD2F7DF410B1B5B62E2FC113F9 (void);
extern void DynamicResourceLocator_get_Keys_mC69ACA592238DF646AABFFEDC3232E0238659DEE (void);
extern void DynamicResourceLocator_get_AtlasSpriteProviderId_m8B5E40CB4D132AFFB7A73BB55121DCEF3F27C591 (void);
extern void DynamicResourceLocator_get_AllLocations_m9AF7B214A74721B121556644581EB1EF52C54939 (void);
extern void DynamicResourceLocator__ctor_m0591C466CEE70375622E314B0A644494DFB3B615 (void);
extern void DynamicResourceLocator_Locate_mFB3C48C9716AC403E0D482E0F2E6D11E8FA54519 (void);
extern void DynamicResourceLocator_CreateDynamicLocations_m00AA7E8B7DB23159E543F9D288D5F25292188A81 (void);
extern void PlatformMappingService_GetAddressablesPlatformInternal_m77DF962D5E753412EBFE0EEAD80B0DCA87B896D1 (void);
extern void PlatformMappingService_GetAddressablesPlatformPathInternal_m795F15662AB5950097F146356608A9D397E0047C (void);
extern void PlatformMappingService_GetPlatform_mA135E54C1F93EBB9F404805EC704928C1CFC774C (void);
extern void PlatformMappingService_GetPlatformPathSubFolder_m24731077366BDFD49B87AEC3EA16E3F78CB4D1EA (void);
extern void PlatformMappingService__ctor_m9106177388112C338144141438C768C36795F0B1 (void);
extern void PlatformMappingService__cctor_m1F8CB5C9210FA5166C0D72DAD4C0754EDA94902D (void);
extern void DiagnosticInfo_CreateEvent_mFC695681560C9459FBFA471C90094E3325FF4CC9 (void);
extern void DiagnosticInfo__ctor_mBE6F113C30B6B595260A1C58D68976F755711235 (void);
extern void ResourceManagerDiagnostics__ctor_mF72CD3FA03BE7F127EF30342D928FB4BF5B3C16B (void);
extern void ResourceManagerDiagnostics_SumDependencyNameHashCodes_m6A0C79690FEF8395EB97EBA054D7152FB475409A (void);
extern void ResourceManagerDiagnostics_CalculateHashCode_mD0A63E93BBA660EDA64D00077B2D256777FCCC0E (void);
extern void ResourceManagerDiagnostics_CalculateCompletedOperationHashcode_m030791457AE2071C4AB4758161B8A0482C4B0C68 (void);
extern void ResourceManagerDiagnostics_GenerateCompletedOperationDisplayName_m5BC3877EE31B4E3A13ED7C899B8446ACD0C7A2A8 (void);
extern void ResourceManagerDiagnostics_OnResourceManagerDiagnosticEvent_mFC37FE3507A4A3911B6B12F48A5C64B1963953C3 (void);
extern void ResourceManagerDiagnostics_Dispose_m74764DF40B3F376B67D29D552615E1D6726B90E7 (void);
extern void SerializationUtilities_ReadInt32FromByteArray_m496E2FB4F19B471BBBEED40A07DB83E98B56C09C (void);
extern void SerializationUtilities_WriteInt32ToByteArray_mE7ED9541AE3C4A60B8036D009AC2FEDE63D18DCB (void);
extern void SerializationUtilities_ReadObjectFromByteArray_m30E1BCA3DA1047C02BB01E9CB6A6D615713396FD (void);
extern void SerializationUtilities_WriteObjectToByteList_mF3E744C1A7B1A5973EDD0710D3E31C7128D6089A (void);
extern void ContentCatalogProvider__ctor_m97F166F23702191D7D16AA3D9BF89E1FF790F450 (void);
extern void ContentCatalogProvider_Release_m03BB273F5882D9F3A99FC146308180CA301E3A01 (void);
extern void ContentCatalogProvider_Provide_mA1F3CB11945DB4A494B241DE33F1A14F72C2561F (void);
extern void InternalOp_Start_m13B3B4C3CA844D0F8C15D7A6934E223FC21C00EB (void);
extern void InternalOp_WaitForCompletionCallback_mCA7721866E3D80E9DFB782A57D3BF7E0416D9FB2 (void);
extern void InternalOp_Release_m19233D2953DE23EDD4FEF4316DA41FBE215029AF (void);
extern void InternalOp_CanLoadCatalogFromBundle_m29E9F085D94C616A0C063064B3D64662BC64F768 (void);
extern void InternalOp_LoadCatalog_m4BD81C370F6AE19DEF2E8EED967842FA9AD1CF9B (void);
extern void InternalOp_CatalogLoadOpCompleteCallback_m7ABE16B5B9550F66BEA06C239B7658EDBEB108DA (void);
extern void InternalOp_GetTransformedInternalId_m470174337BBF1E1F783418CF80AB6A55794E099B (void);
extern void InternalOp_DetermineIdToLoad_m7A9ED93D1D000457E22F5B71B11E7CD0337AED43 (void);
extern void InternalOp_OnCatalogLoaded_m329BB3AFD87FFA454AFB357E8B8677CE2986E192 (void);
extern void InternalOp__ctor_m0BCBDE9F053C63B431C706CC0B02DEB82B88070C (void);
extern void InternalOp_U3CLoadCatalogU3Eb__14_0_m5CBB4FD815E005B54023B6F1372C54DDB9D4FE25 (void);
extern void BundledCatalog_add_OnLoaded_m33CD2D9BBFC438E3FE4229FC61270ABE1757BAA3 (void);
extern void BundledCatalog_remove_OnLoaded_m68F435DDCFB6D3D2CA28503F9B5B11B9063BC324 (void);
extern void BundledCatalog_get_OpInProgress_m8D73BFA5A4DC5B0467301A23B98B42D73BF6F069 (void);
extern void BundledCatalog_get_OpIsSuccess_m14D5377F83708F645CEED98F1AEC0AED746F075B (void);
extern void BundledCatalog__ctor_m6D89F5B7F2AD9A45FEC08F39153F95E0BA8ADD99 (void);
extern void BundledCatalog_Finalize_mB263EF63BE9F8833EA015FF783D59B881AE3A8B2 (void);
extern void BundledCatalog_Unload_m03E13520B4FA682A239FDC52C34E847C96262762 (void);
extern void BundledCatalog_LoadCatalogFromBundleAsync_m3ECD92F9B736CBD9E74B484C62741B6279741D39 (void);
extern void BundledCatalog_WebRequestOperationCompleted_mBA5F7FFA2C52CCADC3CCAB283CDA984BB96EDE30 (void);
extern void BundledCatalog_LoadTextAssetRequestComplete_m547E724B6A69F92A8A7FC289F35A7B582485D1F8 (void);
extern void BundledCatalog_WaitForCompletion_mB920DFE0296D0BFD0E4383811C5F6F952DCF0AA8 (void);
extern void BundledCatalog_U3CLoadCatalogFromBundleAsyncU3Eb__19_1_mC6ADD115296A511E303F04EE7DED7537B3291239 (void);
extern void BundledCatalog_U3CLoadCatalogFromBundleAsyncU3Eb__19_0_m49D30A2381B93A4822469FF39F342C47B9CFD7C7 (void);
extern void ContentCatalogDataEntry_get_InternalId_m3F4EEE94BF527908FA3203BA2648D8C30C32CF57 (void);
extern void ContentCatalogDataEntry_set_InternalId_mD4CD4C7C630C0FCAC3DC69AA0B72F11815931D9C (void);
extern void ContentCatalogDataEntry_get_Provider_m7E80375AA50EFCCEE644C57EA3CAD397D9EA41D3 (void);
extern void ContentCatalogDataEntry_set_Provider_m4DA1BFAE05E7333FF77B05DFB5B868D66CD6C211 (void);
extern void ContentCatalogDataEntry_get_Keys_mF7D1C60109A7164A11E8852A8AE2C880695E5A72 (void);
extern void ContentCatalogDataEntry_set_Keys_m3B3D7369ABC77240E00702F613E8B2036D1F4E03 (void);
extern void ContentCatalogDataEntry_get_Dependencies_m7F9D277A5F94B4DDD6D9699DD922F0E6307B89D6 (void);
extern void ContentCatalogDataEntry_set_Dependencies_m94C0577CEEDF603C199EC6154D54E68FA02391C9 (void);
extern void ContentCatalogDataEntry_get_Data_mAE907BFC6C5931BD529AF26D5098FEDA8CE6C530 (void);
extern void ContentCatalogDataEntry_set_Data_m46804BE456882181345F9E3CA0DFE14866BFDDDE (void);
extern void ContentCatalogDataEntry_get_ResourceType_m4C186DC3D2E71A1B31EA3F2D838B99D7B93A26AA (void);
extern void ContentCatalogDataEntry_set_ResourceType_m2769BE1342590A10AC25393432902F574945E80A (void);
extern void ContentCatalogDataEntry__ctor_m7E597E37E0A6865690293668BE6642107DCF32E5 (void);
extern void ContentCatalogData_get_BuildResultHash_m7B85D5A4EFD5A5D139846113A80F34FEE779AA36 (void);
extern void ContentCatalogData_set_BuildResultHash_m84BA4F042281CA62FA5290FB5BB8B46045E5BDC7 (void);
extern void ContentCatalogData_get_ProviderId_mE5ACA3F702D77AB7EA704C2AD3B823CE138F509A (void);
extern void ContentCatalogData_set_ProviderId_mF6488FAAFF42ADA8950C20C861EC20740567542F (void);
extern void ContentCatalogData_get_InstanceProviderData_m834DC403FD021DEA0A45877A6E748A31AC70F7DA (void);
extern void ContentCatalogData_set_InstanceProviderData_m2697DD7FE45C17FE22551B2AC29444B4DBB60A52 (void);
extern void ContentCatalogData_get_SceneProviderData_mAFA60EDC86ADDBBBB71A8614C48279788C932A93 (void);
extern void ContentCatalogData_set_SceneProviderData_m6D182FC50E0B3DF1B924A5C999578A1913731514 (void);
extern void ContentCatalogData_get_ResourceProviderData_mE21C6A8378AADCED75EC268B0D38F9A8BB081BA8 (void);
extern void ContentCatalogData_set_ResourceProviderData_mDFFB84F925F604978E61B1205B4FC294C7268D30 (void);
extern void ContentCatalogData__ctor_m20EC611AE9393E249FE606E4915B5C048CAE31C4 (void);
extern void ContentCatalogData__ctor_mBC68CA5D564E32298FDB12BD15A9C10661140545 (void);
extern void ContentCatalogData_get_ProviderIds_mAAE0AD6E3E0F688745840BFED0ACE50A49B8069A (void);
extern void ContentCatalogData_get_InternalIds_m491D748768D65E49929740F4149234D091DB546E (void);
extern void ContentCatalogData_LoadFromFile_m5FA1E4EBDE4F6F451584BF82723B27582C7B57FB (void);
extern void ContentCatalogData_SaveToFile_m554EE39C1C253C8EB5A0D09DBF549C0DDF473082 (void);
extern void ContentCatalogData_CleanData_m7061F20894338BA105E325243D68BBB255132AF6 (void);
extern void ContentCatalogData_CreateCustomLocator_m4E0907DB7C5658416BC3041EC0AADE21845FFD55 (void);
extern void ContentCatalogData_CreateLocator_mC6077733917E0FC434D7106651CC47F81ADED66D (void);
extern void ContentCatalogData_GetData_m8662F99E11CB70C8FA0056ECE51E54FEFAB86C28 (void);
extern void ContentCatalogData_ExpandInternalId_m87F4A2AC028351BBD339C68F57E1A0FA59A089B6 (void);
extern void ContentCatalogData__cctor_mF3B52D5CF1094462A536044FF82ABAE4166B37BA (void);
extern void CompactLocation_get_InternalId_mC16C70D4E5014EA89BFD62107807F062FA47EF62 (void);
extern void CompactLocation_get_ProviderId_m335FE90B0A5CACDAB78DC9096E429E1261CD0B08 (void);
extern void CompactLocation_get_Dependencies_m6CCB51754B3A64596D953E3E6081AE52F228478C (void);
extern void CompactLocation_get_HasDependencies_m5141EE22AA14F4EDE9FC5AEE2B93EC5C7D33F92D (void);
extern void CompactLocation_get_DependencyHashCode_mFA406D583779A37511AD8CAB4A100EB13B239B53 (void);
extern void CompactLocation_get_Data_mF3DD6A57FD955EB7030532C5B03DCB0EDE447F91 (void);
extern void CompactLocation_get_PrimaryKey_m498C50CEB2A12D81A5E7B9ECEC6B04220938B011 (void);
extern void CompactLocation_set_PrimaryKey_m6C38429E9C6C0A7D292FD70C1BA039BDE698DFE0 (void);
extern void CompactLocation_get_ResourceType_m6F2534DD17152B770E8CD7B63180887C82CE9949 (void);
extern void CompactLocation_ToString_mACFF7E9338D13EDE4F72E377171A31B25D038B37 (void);
extern void CompactLocation_Hash_m272067C4BD7CD3F4F4F82E1094911E6232F1F009 (void);
extern void CompactLocation__ctor_mE8FACA0177302132CC346946EAD2A1A8BBC456EE (void);
extern void U3CU3Ec__cctor_mF174F75E5DFA61491D02ECC68A29996CF3073129 (void);
extern void U3CU3Ec__ctor_m9FD30C948C1A8BCCB8A93A8371B0155069609418 (void);
extern void U3CU3Ec_U3CGetDataU3Eb__48_0_m9B05C8EAA4FF6D551D1FE4E9990950A34EA38050 (void);
extern void LegacyResourcesLocator_Locate_m2495A14BFAF91A5CE077BAC06D5E5FACC3DCF5BC (void);
extern void LegacyResourcesLocator_get_Keys_m732337A80862621F7F54ACB4F128B0C740C13FBE (void);
extern void LegacyResourcesLocator_get_LocatorId_mA8745A21B0FC9A071B8BB3D41ABA92C1C7F70939 (void);
extern void LegacyResourcesLocator__ctor_m9761FCA7DB2E3D21BC5424FB13B832F60B4C2E92 (void);
extern void ResourceLocationData_get_Keys_mED03309C4C351194C81A3B4F85FD9BC87C402B05 (void);
extern void ResourceLocationData_get_InternalId_m543A0A16BCEE568C722BEF8ACB80CE996D2318F0 (void);
extern void ResourceLocationData_get_Provider_m5F914FDA29E77390D629ADBF4565EAAEDCB083F4 (void);
extern void ResourceLocationData_get_Dependencies_m0A25C9674BE13EA817A38332A7B90B648BC050FF (void);
extern void ResourceLocationData_get_ResourceType_mA94B078186E27CA215B166AA51142198B4EA3465 (void);
extern void ResourceLocationData_get_Data_m19B678392C47B3FB539F9C9F592C027F86D578B3 (void);
extern void ResourceLocationData_set_Data_mF06608062093D635426FB2CCF31D4BB36E2CB8C9 (void);
extern void ResourceLocationData__ctor_mC6BC93B21101353EAD420971B75E3FE6F97CAA75 (void);
extern void ResourceLocationMap__ctor_m2208B4BDD1EEABBD0AA58DA7B7B44CADDF0A17B5 (void);
extern void ResourceLocationMap_get_LocatorId_m904C44B5DDCF105BB9036C05A9579E33FC827161 (void);
extern void ResourceLocationMap_set_LocatorId_m0D6156C92CFBACF321BD856D34D3D86E3350FF2C (void);
extern void ResourceLocationMap__ctor_m5D6BEA9C6AF5B3E8085CD0592EAB92876C08931F (void);
extern void ResourceLocationMap_get_Locations_mDBC0190E559C406A8C9413E195A60656ED3D136E (void);
extern void ResourceLocationMap_get_Keys_mA2893DB96A26617347DB6EBB0EF97C9A6E60AF2D (void);
extern void ResourceLocationMap_Locate_mFBB5443BD10920BF4246F53CE7E0ABFB0D846BCF (void);
extern void ResourceLocationMap_Add_m659847BEA0D3FCC8A1707E47B227916447F980D0 (void);
extern void ResourceLocationMap_Add_mE919B9E341B6569C97F055A04E9A7976F20C5EBF (void);
extern void AddressablesRuntimeProperties_GetAssemblies_mD3E86E3C70FDD0D914C58CDF9D36F9A0279FF2CB (void);
extern void AddressablesRuntimeProperties_GetCachedValueCount_m6C3B071C3D61842E7264C23CAE977A6D7DB913ED (void);
extern void AddressablesRuntimeProperties_SetPropertyValue_mEE8B3F7DA4D54817B8AAAB8AAE46C99556753860 (void);
extern void AddressablesRuntimeProperties_ClearCachedPropertyValues_m0680783D9B1FAC77D8DB7C3565AD6719D2225B7B (void);
extern void AddressablesRuntimeProperties_EvaluateProperty_m4771C62AACC00C3C8C843AE347FC7276A63FFC28 (void);
extern void AddressablesRuntimeProperties_EvaluateString_m8D5439F7D5FD76E58D04D7F3DAB6E0FEF2D84901 (void);
extern void AddressablesRuntimeProperties_EvaluateString_mEC744A949D466FC39CACF0D72F044B40FD2D32C8 (void);
extern void AddressablesRuntimeProperties__cctor_m39EBA6D1E9D2EF7A914A4F048C207E302D637DD5 (void);
extern void CacheInitialization_Initialize_m573FF8030EBE8494895369624E59BCF2E5587EC2 (void);
extern void CacheInitialization_InitializeAsync_mD4E515BFF3E9E7C15994F7DD0E9C2FC35BB2A03F (void);
extern void CacheInitialization_get_RootPath_mC0ED23627FA9364B14F21131343C3D462A0F1393 (void);
extern void CacheInitialization__ctor_m875CB892A6E780D9927D6EA3F26FD6E10AF67429 (void);
extern void CacheInitOp_Init_mE5D39CE4E05CE455C48F54DACCC81B1A97907992 (void);
extern void CacheInitOp_InvokeWaitForCompletion_m60660E8DA579346A0A5A92B63957C83A8CD615CF (void);
extern void CacheInitOp_Update_mE6A0E0BBEF6E35D4C41BF437A599449E9AE2A564 (void);
extern void CacheInitOp_Execute_m7BD406672C3DC5993F90F40E325E908AFB56E944 (void);
extern void CacheInitOp__ctor_mA335CEDF22E8A3160E91A9C52FCE445108000E33 (void);
extern void U3CU3Ec__DisplayClass1_0__ctor_m5506F595D9A82AA404F753B6F48697B823FA81A5 (void);
extern void U3CU3Ec__DisplayClass1_0_U3CInitializeAsyncU3Eb__0_mFA267E258EDF125EE896F4A77337B41BFEF7A97F (void);
extern void CacheInitializationData_get_CompressionEnabled_m24C73459490E5D94810D0E19BE6A7B8A2FFD6917 (void);
extern void CacheInitializationData_set_CompressionEnabled_m5E1F359471C1A7535170C34CA3388AA1553C3382 (void);
extern void CacheInitializationData_get_CacheDirectoryOverride_m658B7B84EA37378E31D653E199EA4022DF28D231 (void);
extern void CacheInitializationData_set_CacheDirectoryOverride_m6A2D6B65EB78B7D317EA23D60F8849DD790CC501 (void);
extern void CacheInitializationData_get_ExpirationDelay_m7C37593CC0F38201D39A330795826973324C08E2 (void);
extern void CacheInitializationData_set_ExpirationDelay_m8EA2722465904AE00A5DE1ED20835DA8A2F27360 (void);
extern void CacheInitializationData_get_LimitCacheSize_mD6433457DD34055C9E911FDBC09B877E20E8C51F (void);
extern void CacheInitializationData_set_LimitCacheSize_m9F28D622360651446B472B24EB4DE6EDAC85665C (void);
extern void CacheInitializationData_get_MaximumCacheSize_m31E6D8FA717BF8BFD25B0F4ED90F1C9C2C2CE6AF (void);
extern void CacheInitializationData_set_MaximumCacheSize_m088E7F125A1B3390E6A0E25533F43F4DEED60081 (void);
extern void CacheInitializationData__ctor_m395841C0A1C6C82528AC4360452F7B646ACB769B (void);
extern void InitializationOperation__ctor_mA2A202C201ED8F364F6FFEC174EAE51C2E6A6434 (void);
extern void InitializationOperation_get_Progress_mFB29BE9EEA230CDD4E515DF73E8F0E60732BE80D (void);
extern void InitializationOperation_get_DebugName_m590B65F43134C5390756001BBD04D1DE8F410674 (void);
extern void InitializationOperation_CreateInitializationOperation_m33F7993FE97B0A04012A9DAA81AB2ECD2EDC766A (void);
extern void InitializationOperation_InvokeWaitForCompletion_m650DCC2A0243E4F11D688AAF34DBD8A05E6C0D43 (void);
extern void InitializationOperation_Execute_mEB9FE18E7E3C2F24F88AD53F3C6DAA03C66C67D9 (void);
extern void InitializationOperation_LoadProvider_m5EFEC1E24E8D12BD71D95BBB7B799C0BCE667527 (void);
extern void InitializationOperation_OnCatalogDataLoaded_mDC85CF69F44EFB5D9A383C9AEF77D59C0FF699D9 (void);
extern void InitializationOperation_LoadContentCatalog_m96F29CCE7193CB68E454EE7474581ACDD3459A23 (void);
extern void InitializationOperation_LoadContentCatalog_m2B1E267F7B46BBE94489B678020E88C081A9B373 (void);
extern void InitializationOperation_LoadContentCatalogInternal_mB8879495162C6FDA165CB2571DABBA643C257D4D (void);
extern void InitializationOperation_LoadOpComplete_m7411329487C62A65E027BE07B055CF290C09C009 (void);
extern void U3CU3Ec__cctor_mE9258D312AFF75B5AE671A1B5B0B352B98A96F29 (void);
extern void U3CU3Ec__ctor_m400FA93F30A0788073EEF09EFDA850B0DD08B1D1 (void);
extern void U3CU3Ec_U3CExecuteU3Eb__13_0_mAED31DB7FABB1ABC307A8C6610EC50871F29A002 (void);
extern void U3CU3Ec__DisplayClass16_0__ctor_m8DD3D41CB6ADB76E0EA33F4AF0F24D8492D77AB6 (void);
extern void U3CU3Ec__DisplayClass16_0_U3CLoadContentCatalogU3Eb__0_m66DB2E022694F69F39CCFFC3154CB5AB5976645F (void);
extern void U3CU3Ec__DisplayClass18_0__ctor_m99303C3B088C7B4052A6F8AA53CAA1BC21EB1D5A (void);
extern void U3CU3Ec__DisplayClass18_0_U3CLoadContentCatalogInternalU3Eb__0_m8E4BA6A17387BF7F02B23A139D7E8A5C782D43BA (void);
extern void ResourceManagerRuntimeData_get_BuildTarget_mB64F1595BD714F5D79138ED161B4F05D3A0FE121 (void);
extern void ResourceManagerRuntimeData_set_BuildTarget_m0958B3EC4A849CEB55CD6E94277F093AE824B83A (void);
extern void ResourceManagerRuntimeData_get_SettingsHash_m05B636625500B0CADC8C1116A2C880DD90BD9D46 (void);
extern void ResourceManagerRuntimeData_set_SettingsHash_mC0115B10FB9904CB21A385433EEBA3E8AB47A74A (void);
extern void ResourceManagerRuntimeData_get_CatalogLocations_m6BDA2FA48B12635AD67A780C5E197C02BB411172 (void);
extern void ResourceManagerRuntimeData_get_ProfileEvents_mD05FEC5FBD8C8A04EB35B153297CA9FE0A92220B (void);
extern void ResourceManagerRuntimeData_set_ProfileEvents_m41B9D67AD80113BC75407C5432F45E6BC8CD7551 (void);
extern void ResourceManagerRuntimeData_get_LogResourceManagerExceptions_m421154EADF056D9461BFAE4D8C98B3818457BF2C (void);
extern void ResourceManagerRuntimeData_set_LogResourceManagerExceptions_m815094B1BF1DDA5C333ED3F3333E8D074C4396B4 (void);
extern void ResourceManagerRuntimeData_get_InitializationObjects_m0C2D008C79BD52C0F9895F093E4AFD3AFDB9E014 (void);
extern void ResourceManagerRuntimeData_get_DisableCatalogUpdateOnStartup_m2EB745669A9AC1D07E47D345DE2EF87442D56907 (void);
extern void ResourceManagerRuntimeData_set_DisableCatalogUpdateOnStartup_m43EF818B02F4E5A00C57BA9BF19BCFF68779834D (void);
extern void ResourceManagerRuntimeData_get_IsLocalCatalogInBundle_m5D312C49BFCB754034EC7BBFD1EAF7BF1AF6B312 (void);
extern void ResourceManagerRuntimeData_set_IsLocalCatalogInBundle_m6931A3CBF1E368065CC96972855EA73BFD7E8D56 (void);
extern void ResourceManagerRuntimeData_get_CertificateHandlerType_m1194D6694DE8A1FBBF29D5025C2E9C9DB1FD941F (void);
extern void ResourceManagerRuntimeData_set_CertificateHandlerType_m3276FCE0F13D2E6C72B7DBDFDDE1B6339A9D633F (void);
extern void ResourceManagerRuntimeData_get_AddressablesVersion_m0B7B76DF108ABD855470460DF6D862480D1A00D7 (void);
extern void ResourceManagerRuntimeData_set_AddressablesVersion_m77531E4740B232F4052281D8E0E061F8B1E04556 (void);
extern void ResourceManagerRuntimeData_get_MaxConcurrentWebRequests_m1CF41EB91ABB3B99C338122E3507D3D196A838F0 (void);
extern void ResourceManagerRuntimeData_set_MaxConcurrentWebRequests_m197F95E0235FC2BE257A01E923013464687EB8B9 (void);
extern void ResourceManagerRuntimeData_get_CatalogRequestsTimeout_mED5A093F40FE50DF355C12FE18D6FCFB2AC993D1 (void);
extern void ResourceManagerRuntimeData_set_CatalogRequestsTimeout_mF4DBF74FCF5A3AC5D15D257666EEC56771B5B097 (void);
extern void ResourceManagerRuntimeData__ctor_m5AD4B872EB21CB8C9613B008ADA0A55980AB4B75 (void);
static Il2CppMethodPointer s_methodPointers[654] = 
{
	PackedPlayModeBuildLogs_get_RuntimeBuildLogs_m2A62CE1FAC6164FD6B0D89993B38230C9D8256E9,
	PackedPlayModeBuildLogs_set_RuntimeBuildLogs_mD62DB1198BDD40392771BA8702C5E44BF4B2E44F,
	PackedPlayModeBuildLogs__ctor_mCA7D3DF16DA06CC7A356906C32B466EAA5D57189,
	RuntimeBuildLog__ctor_m29FC0C974B2C6D702AF74C393BF2640D0836A7DB,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9FCEB3415C007CA3CC22DE687734735C857E2E8C,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m95195BF4B962DEB33556FA9BD52A00EB38035E22,
	AssetReferenceUIRestriction_ValidateAsset_mC395728636B5AE4C49922728EA9F6E59626A3A59,
	AssetReferenceUIRestriction_ValidateAsset_m3AD169DF63426909A45850B40AC1C601536CE2E2,
	AssetReferenceUIRestriction__ctor_m068B89DD8634990FD707FB05B33B258D10E1C8E7,
	AssetReferenceUILabelRestriction__ctor_m2C0E08EF97801C19BCDB2C1D56660099038445C7,
	AssetReferenceUILabelRestriction_ValidateAsset_m13B6237658C7E47DB0A4F9D8BC6DC44CB5A9C2F6,
	AssetReferenceUILabelRestriction_ValidateAsset_mBEC28A0381E9B3778E661CF722A2A7646B4A04F3,
	AssetReferenceUILabelRestriction_ToString_mFB39115AFCF55E3CF99F3399ED3BBF2FE1C299D3,
	InitalizationObjectsOperation_Init_m5C261C641269B76CEAA9774EF4A621EA846EEB5D,
	InitalizationObjectsOperation_get_DebugName_m851C38457F31826B52E531C6E783121AF5B6BD5C,
	InitalizationObjectsOperation_LogRuntimeWarnings_m00AE676D1FCCD48675884FE9DD20F5D1F684AEDA,
	InitalizationObjectsOperation_InvokeWaitForCompletion_mE8FAF7DB4D7D10F22C4FC6DF3A26CAA0ADC6D5FD,
	InitalizationObjectsOperation_Execute_mBD7C50B85F47EE0269BBBE721B5195D4AAC1A3ED,
	InitalizationObjectsOperation__ctor_m1C106B8E7787E7E178E17DBFDD984EEE88C13A33,
	InitalizationObjectsOperation_U3CExecuteU3Eb__8_0_m1AA291DA4F801F4D169E0D3BABF6501F7265E51B,
	ResourceLocatorInfo_get_Locator_m8CA415524E1D9121B15A313510B1CFF6C13ED324,
	ResourceLocatorInfo_set_Locator_m8EC314A008CEC95E8764F39D8C8BADE84786B9F3,
	ResourceLocatorInfo_get_LocalHash_mBB53B74BC6AA216279BE59C16D0B97DA26E47827,
	ResourceLocatorInfo_set_LocalHash_mBCB7185F4FCA467938538C04AE287EFE4CBF8F2B,
	ResourceLocatorInfo_get_CatalogLocation_m9E793031F8D1D962FCB2D9B1DC032F182B7A608B,
	ResourceLocatorInfo_set_CatalogLocation_m386014DD0243D531197FEED38F599BCF02E68199,
	ResourceLocatorInfo_get_ContentUpdateAvailable_m8F272A4A122D091D0AF9C97720AECD6B5D86CF43,
	ResourceLocatorInfo_set_ContentUpdateAvailable_m373A4DF73C2A079484DDEA80D9E5AF2BD3526680,
	ResourceLocatorInfo__ctor_m31FE77DF3F969F0924E4AD5589C33199DE7C6DDC,
	ResourceLocatorInfo_get_HashLocation_m6524A33D7BDC4E82BD6BD6D0EE08C67DD2790321,
	ResourceLocatorInfo_get_CanUpdateContent_mA52B33646AA6A40B017A8205378534CCE5E01CB7,
	ResourceLocatorInfo_UpdateContent_m69FD26B59EA444D13933A720E24DB884F948037F,
	InvalidKeyException_get_Key_m04CFA5445F268B1738B83E149DA2A560A789A291,
	InvalidKeyException_set_Key_mCDC4F50DA79B43EE22E693752C230A727E520F1A,
	InvalidKeyException_get_Type_mFCF9566F7A5F7ECEF09376CD0D95CC78ACE44633,
	InvalidKeyException_set_Type_m95D438D9D2D16C60E66334D606F8CCE73928A0CB,
	InvalidKeyException_get_MergeMode_m3B874B38F69384DAF973604A6946E0F21AE1360F,
	InvalidKeyException__ctor_m21E9020F3B24FBB0AF0AE05C0AE4763C16DE5323,
	InvalidKeyException__ctor_mA75E071E74953129BAA2A83B92185C62931D173E,
	InvalidKeyException__ctor_mB9F03E732D5DA73676D6DF8EC03DEA22200B7BA3,
	InvalidKeyException__ctor_m90E9CC579C3E857C03D9C00002CBF62B53C28850,
	InvalidKeyException__ctor_m8D3B34B5E8910C77D93F429D76917D7338AF9E56,
	InvalidKeyException__ctor_m96AB14996875BE89CC68F16D0ED9F9BA7BA98D8C,
	InvalidKeyException__ctor_mEF237FAA1A8213257158AF80BC52D1CCFF54EB94,
	InvalidKeyException__ctor_m077154786074EF704E2A7016F4201686C8D1C20D,
	InvalidKeyException__ctor_m396FDC2F7F898D5AB463DD235713201A9FCFFFFD,
	InvalidKeyException_get_Message_mDB7371B5C9392DA9F8B939BC774A21B5EE372112,
	InvalidKeyException_GetMessageForSingleKey_m5374F963B1E10C773466862AD10ECF4FF3759BB7,
	InvalidKeyException_GetNotFoundMessage_mF3931E9199699200E317ABC1D926D9BA0829FA1C,
	InvalidKeyException_GetTypeNotAssignableMessage_m4419D1ABD095B7BE90D5D3284B3824AAC710BE11,
	InvalidKeyException_GetMultipleAssignableTypesMessage_m71D5012299FCBBC8AA7D403C82FB923C67BC7CDC,
	InvalidKeyException_GetMessageforMergeKeys_mA2BBC8F4622CAACCB2A3B5CC42CDAF38AE061397,
	InvalidKeyException_GetTypesForKey_m9DC047CFA2835A0E6CD415A8A6D42C9DD8F91F4F,
	InvalidKeyException_GetTypeToKeys_m6B2D3ECD7BE86E4A2D5820995E129AA401E7F0E1,
	InvalidKeyException_GetCSVString_m8BEE349B248FE9D98DCD0AE3493E8E1D2E2AFE88,
	Addressables_get_m_Addressables_mCBFD180C3722D8CE681814A67B652682C6710BEE,
	Addressables_get_Version_m5859B74ECD99A1FE471001736E4F0DBBB31CD586,
	Addressables_get_ResourceManager_m480AE4DB9D40EABEF022FDAEA00A043DA52D1D5E,
	Addressables_get_Instance_m3E7F02F47DF6F68E8BC755D62A17A523817E19DA,
	Addressables_get_InstanceProvider_mF41503CC8D9E35608FD5B4CFEA318CEF9B2DEE06,
	Addressables_ResolveInternalId_mF1D3A8B02C0D6741CB72AD8A55D5E82A36AA1782,
	Addressables_get_InternalIdTransformFunc_m38C63DD89DD67E32EF724311E423C9E7F02ED910,
	Addressables_set_InternalIdTransformFunc_m4C5D3FAF0A0A2D5D5A9B8D42301DB46D192CF173,
	Addressables_get_WebRequestOverride_mDD30550FA978B31E56769281D177E128C8472BE8,
	Addressables_set_WebRequestOverride_m88489CF0DEBBBBF186D016ED942FDD7C4F0A6D54,
	Addressables_get_StreamingAssetsSubFolder_m515E605E96FA0C0AD73FD588743C61D323922632,
	Addressables_get_BuildPath_m14B123D92F2BC1DE452782033BEA8D35F2B24604,
	Addressables_get_PlayerBuildDataPath_m6C6803DDA5F78720729534974D363E2CCE98C046,
	Addressables_get_RuntimePath_m8FC9F9D2B59FBCD4992E5C00A83B7516946BEF03,
	Addressables_get_ResourceLocators_m6926D30F20D53A9A04E3A886BC5CD030A2A487ED,
	Addressables_InternalSafeSerializationLog_m70DC8762251BF472BCCB1827B9BA00503197AA2E,
	Addressables_InternalSafeSerializationLogFormat_m7061A5C16C5D0AA42717380C4EC9BFBE6E983114,
	Addressables_Log_m672F41B5DCED757A773E01FE0E1B7EA54772B519,
	Addressables_LogFormat_m5F59C1A02A958337C738D89A7D8B2A9F86C42B46,
	Addressables_LogWarning_mE6B724D5D8608F3B426E8CFB94917FE28D6B851B,
	Addressables_LogWarningFormat_m113FDAC9E0B6D6D25774FBE6AD2E1BA82DCC5AE9,
	Addressables_LogError_m14F9E54E8D0491FFE0254C08E7433BCB7D3119B6,
	Addressables_LogException_m51128068141C2820273A09C8AD12787648078AB4,
	Addressables_LogException_m4441DD24B424FDFEA229D4DA747A55E9E931A802,
	Addressables_LogErrorFormat_m37C249A86F953A975C66573FA9A33F7C35F5D647,
	Addressables_Initialize_m372DCB61F63896256F2B5453AFF7E6D24FA815AB,
	Addressables_InitializeAsync_m8F30FEFAF5514C2D0F978D4D92F09C6F575F80F3,
	Addressables_InitializeAsync_m7EE86C523A74F3B7B5D36FE791DEC30F6DD4768F,
	Addressables_LoadContentCatalog_m12ACE159108BECD255B46D4E50F730E026D706DD,
	Addressables_LoadContentCatalogAsync_m1C496ADD1D371499DACA8A236FC11BBFB0AF622D,
	Addressables_LoadContentCatalogAsync_m5BA614644CBA0609A389D7F65705CA14CFD77B83,
	Addressables_get_InitializationOperation_m1F6013D1B5884F567757B7A4F6AE4B02755CA68A,
	NULL,
	NULL,
	NULL,
	NULL,
	Addressables_LoadResourceLocations_m59705C66A7E5F19C5706C6BA716CC6BDBC6CC2CA,
	Addressables_LoadResourceLocationsAsync_mA31184C114F4B16056AB81F904DE573ED7A3AED7,
	Addressables_LoadResourceLocationsAsync_mA7BC8A23176D4E1BAF0279763F81FEAB57F2BE43,
	Addressables_LoadResourceLocations_mD486B5801D9DB46C4BEDF9746DF414E57B2CEE9B,
	Addressables_LoadResourceLocationsAsync_m96BF35184F12370BCD9A304065F42D080D7EE18B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Addressables_Release_m0A186637EB825A9C793E94DD9D5B00842EC67589,
	Addressables_ReleaseInstance_m6673D687FF759A40D9351A6EE9666689A8770B3F,
	Addressables_ReleaseInstance_mDD4BA6F39D5557A7A9D3AB637B0B8D6A904DF34E,
	Addressables_ReleaseInstance_mE67A4267C9B7968F47C07233214F1AB9C57D5887,
	Addressables_GetDownloadSize_mFDD7A4E47AC1FB3F9C0B884A4C5D7A5C6ABF53F5,
	Addressables_GetDownloadSizeAsync_mF0E34AF916F5F61E1325863FC3960CFC1BB3E928,
	Addressables_GetDownloadSizeAsync_m058D2B896EEB943A183AEE1E74D9428602F1D49B,
	Addressables_GetDownloadSizeAsync_mBD39CF5009539BE90B91E7EDB94C48E61EB2902D,
	Addressables_GetDownloadSizeAsync_m21569D24012E62C0D5B34AB060D8E4CEDAE4C427,
	Addressables_DownloadDependencies_m48EBB3DC4C16A627ED197A7F0EE1581FD64E4C92,
	Addressables_DownloadDependenciesAsync_mD5916DDBF0EE6DE8A5AC1B9D12B6DBF233B49D96,
	Addressables_DownloadDependenciesAsync_m891471C5E090DEF0F7ABBBFD43D3EA99351891F7,
	Addressables_DownloadDependenciesAsync_mCAFF6DC0D7CF7DFDAC352F516937B9C88FBB62E7,
	Addressables_DownloadDependenciesAsync_m540DC6897FCC7409CC56B9BA77D6F91DFC7ECD98,
	Addressables_ClearDependencyCacheAsync_mC0BD38E99D05EFDA61056DF039F4BF865653C465,
	Addressables_ClearDependencyCacheAsync_mB47FBCDACD6C1D7537095E9F86A07F9F03E1D485,
	Addressables_ClearDependencyCacheAsync_mE4FBB0C0A47CF1FF4244EC6466D2A667E92A2B79,
	Addressables_ClearDependencyCacheAsync_mD82D927AB5AE8FCB74A7E510AFD6EB4E6A541AC1,
	Addressables_ClearDependencyCacheAsync_m7831A86E7316252FFF70020877E57F9E923DEB7D,
	Addressables_ClearDependencyCacheAsync_m72FC60DEDA6EACD28042F011BC5A62B03BD8D619,
	Addressables_ClearDependencyCacheAsync_m1B0922223364DB3C90A6756A03985D9962536DE7,
	Addressables_ClearDependencyCacheAsync_m9E9CF3BB16ECB75D11C7CDCECDB9EF5648B56052,
	Addressables_ClearDependencyCacheAsync_m2F2CBBE98127892DCE7339F15225EB62D6397A75,
	Addressables_ClearDependencyCacheAsync_m6B4E01BA5DA3909306298321F481C5574A52B59B,
	Addressables_GetLocatorInfo_mE6E37BAB67195DD7E46010724F9B7DE02694DA27,
	Addressables_GetLocatorInfo_m73EB1B8EFE6EE8D5E5331DA4E74007C9E2B79B97,
	Addressables_Instantiate_m6ACDEA015DBD9C043D337BFCF56A61833689E3B9,
	Addressables_Instantiate_mB733B72A1A18ECD75980D65A249A90D04EF15EE0,
	Addressables_Instantiate_m4A8A87FD9A0C57570ABDB2F018FF30B025409EF6,
	Addressables_Instantiate_m3BA65CA58E9A7417A43E4F760784ECC83ACB3D92,
	Addressables_Instantiate_m853749C8B171C4D5E780C1560630324A6ACC8EE3,
	Addressables_Instantiate_m05E5ADC46320370CCFA5D7D0EF8BC36BEBBCBEF8,
	Addressables_InstantiateAsync_m61AA97078A68E75BDC0A85A21A6C4A9441CCF3D4,
	Addressables_InstantiateAsync_m053C0AD711DC77C488635CA05493239D10F38CE5,
	Addressables_InstantiateAsync_mD10FF052D569CA9E7027857932564E8630F9574E,
	Addressables_InstantiateAsync_m3B65AC3D763A0021C90B0B215FC11A6E1F437477,
	Addressables_InstantiateAsync_m6378634E7D2CC4A036C4930884B7EEF3D1DB0496,
	Addressables_InstantiateAsync_mD8D8FDC62D11C8D6B332F604D5D33AD920C8C511,
	Addressables_LoadScene_mF4C8412BAB025C874A40464A42815EFD47A8DF05,
	Addressables_LoadScene_m0DC9457A0F985FC5526E5387FF475C35FA85E27D,
	Addressables_LoadSceneAsync_m3868019E92C7809B7352837DB3D2D81088DA6134,
	Addressables_LoadSceneAsync_m95B2F95F4D52B29ACE1AE76FF2B930766DA04AF1,
	Addressables_LoadSceneAsync_m4F7123C212FBF24C8DDAED2102E3B67D7ADD65A4,
	Addressables_LoadSceneAsync_m36AB6EA45E88D830500F1B47FACF4BB6701DF4B0,
	Addressables_UnloadScene_m661FF1AEBA03DFC66FA01694C424798AF9C8FDA8,
	Addressables_UnloadScene_m7AA8C415D40D5EEDBE6CD3E32221C32C4549C8AC,
	Addressables_UnloadScene_m10E336597465DCBA695F407959FE9130147179F1,
	Addressables_UnloadScene_mA03FC15C8A53E6163BEBA7271F93519E42E87D35,
	Addressables_UnloadSceneAsync_mFD6664CE234291B2F69459F282BE5FF4B85646BA,
	Addressables_UnloadSceneAsync_m60FB61C85398F746E3A2977BDCD88C7DD2AC026A,
	Addressables_UnloadSceneAsync_m8CD5516D612F97860F6D3AD2054176BE90445FF2,
	Addressables_UnloadSceneAsync_mD68AD0B6FF8E9570DBB6AFDD146107C705049F5D,
	Addressables_UnloadSceneAsync_m01ABFEE8878DE15A676284B2E1C998DE23EA8AA7,
	Addressables_CheckForCatalogUpdates_m4AB4B9D717C9BB941E9379B105A3479457D95A1D,
	Addressables_UpdateCatalogs_m7AEECFD1B41CD4A0EB2AF0AB3A9589564F0C3024,
	Addressables_UpdateCatalogs_mC1709A955E2BC1C4371BF7DF91C1DDFF0B7BE9A4,
	Addressables_AddResourceLocator_m80AAFA9FADC9637D28B4CFF7508A17A2BFC71F82,
	Addressables_RemoveResourceLocator_m755F8FFE85FD38CEB18543A7ACBB241653180A01,
	Addressables_ClearResourceLocators_mEDA2320D42AC72BB2DD48560EA97D8EF84E60E09,
	Addressables_CleanBundleCache_mD40E5A18BFD8ABECE6E8D5EBB4FEE6A5F51C80ED,
	NULL,
	NULL,
	NULL,
	Addressables__cctor_m9269D2DB2C8115DAF077A0A3801015469F9825BE,
	AddressablesImpl_get_InstanceProvider_m8BC14A958EB91A2BD3A686758670C728190A6090,
	AddressablesImpl_set_InstanceProvider_mA92B463DD7BCDB14B2EDCD84F58D7F595CC5A01F,
	AddressablesImpl_get_ResourceManager_mFCBBB9F321655F40BDD1CAF07194C1AA40BCB656,
	AddressablesImpl_get_CatalogRequestsTimeout_mA809B3948354BE139452696AEC4D97E4D1E2E4FB,
	AddressablesImpl_set_CatalogRequestsTimeout_mBD266E78770ADCFBEFFADF14AA524B67CE2222DD,
	AddressablesImpl_get_SceneOperationCount_m0E34C64E4228D8AEF0FE3C98A663784CA111BC55,
	AddressablesImpl_get_TrackedHandleCount_m8E6536E52B6A517110CC75974832B6B7DB72FE15,
	AddressablesImpl__ctor_m0D145411AE0D75A5FD395FACBE97807535D02974,
	AddressablesImpl_ReleaseSceneManagerOperation_mABA795B6F5262B1A77B81E817798F8966C462A04,
	AddressablesImpl_get_InternalIdTransformFunc_m805CE26548AF0BE138B2212784F882066B3F1E71,
	AddressablesImpl_set_InternalIdTransformFunc_m832753A5BBAAA21DF25F69A464E3B4C6DB38DCD1,
	AddressablesImpl_get_WebRequestOverride_mCDA70F560071BBABA265369E3192DAA9E36264E5,
	AddressablesImpl_set_WebRequestOverride_mAF441D4DF4914BA54E1BAC5E2D5E54187CCD1216,
	AddressablesImpl_get_ChainOperation_mD1E214493952096323F333E3315296E79C812DCE,
	AddressablesImpl_get_ShouldChainRequest_m7B0C3F2F8AC53881B87AB144601A4E80DE69560D,
	AddressablesImpl_OnSceneUnloaded_m3BE985BC72C4FC0E4046123CB977B7E6AACCF4C8,
	AddressablesImpl_get_StreamingAssetsSubFolder_m836CFC1375369C5895DEA6E1D779DC09A466C31B,
	AddressablesImpl_get_BuildPath_m6621C8C65DAEF38F23E826AFA7E51B05FEAA924C,
	AddressablesImpl_get_PlayerBuildDataPath_m29B43E1D45B38754C648ABECDB7C9783E12CFD8B,
	AddressablesImpl_get_RuntimePath_m516C45F2BF5F5622F31FC11CD967DDDD02CCBE6A,
	AddressablesImpl_Log_mFD5E4D7EF8166751055599E2D73C60181CB5EAD2,
	AddressablesImpl_LogFormat_mB535C0BB3A5E30C93E8FF6DBECB0B4C756B09661,
	AddressablesImpl_LogWarning_m1A027072AF03D4E499CF922978CBED57344AAF8A,
	AddressablesImpl_LogWarningFormat_mE3F74A1473E680C02FAB802B2FBA8779BBA4ECC2,
	AddressablesImpl_LogError_m52A2B1A6E3B166A075222CE0D00EB3124ECCB0D0,
	AddressablesImpl_LogException_m8F960C2ADDA73A9E3F99D14AEB0BF77209E0966E,
	AddressablesImpl_LogException_m7C420869EAA7619FC909037D84E5E7A93BEB7A3B,
	AddressablesImpl_LogErrorFormat_m9CA5AD09EC3AAE49EC71065499CEE0ED3E5F1B05,
	AddressablesImpl_ResolveInternalId_m858249BA7A7A6C41684815D2A09618EEC81F48CC,
	AddressablesImpl_get_ResourceLocators_m8E2213BC51CCDBF257902E4E17E0C9385457288F,
	AddressablesImpl_AddResourceLocator_m4B7B6E7D82E6F6D3B11BC865C417A5C4F891A0FE,
	AddressablesImpl_RemoveResourceLocator_m3648D6A6C2FDA74776BD0EAB41E370D06EC952A5,
	AddressablesImpl_ClearResourceLocators_m1DAD782DEEAD2D25F026BB3305CD88A82C34FF8E,
	AddressablesImpl_GetResourceLocations_m51B702F089F2E776BFB4D3C0D3C0CF982B1C77F1,
	AddressablesImpl_GetResourceLocations_m868373B7918DBF99851290C662E7A92E06EEB982,
	AddressablesImpl_InitializeAsync_mA3C3DC099508914EF7A101A6DD6D8D5F9B6D1245,
	AddressablesImpl_InitializeAsync_m39F307E3349132C26AEA3CDB5711109DB64AD358,
	AddressablesImpl_InitializeAsync_m686DE491E4A5336A025D165F1D83D73A4DA23B66,
	NULL,
	NULL,
	NULL,
	AddressablesImpl_QueueEditorUpdateIfNeeded_mD562431EC2529EC5EE6946FF1BB69723987A7086,
	AddressablesImpl_LoadContentCatalogAsync_m3F26BD7F722C8D44FC6231023F98619B7523FA23,
	AddressablesImpl_TrackHandle_m83943CB8288691A45B0C30A6A0DD1237CFE93A95,
	NULL,
	AddressablesImpl_TrackHandle_m0B6C04AB3AB14BA9A1986DD7A14B25311A25C1C7,
	AddressablesImpl_ClearTrackHandles_m015173AECE6C5AEFACE8B99ACDFDB7A3B35F1FA7,
	NULL,
	NULL,
	NULL,
	NULL,
	AddressablesImpl_LoadResourceLocationsWithChain_mD0C9A4B25EF3217C5BBBC0C3B95BBFA1E006516F,
	AddressablesImpl_LoadResourceLocationsAsync_m2B02B077B4C9EBF5AF34FFE2C7066A94CD1D7DC4,
	AddressablesImpl_LoadResourceLocationsWithChain_mBE480C20C62FC3427B07DBF7B81998D81ED87083,
	AddressablesImpl_LoadResourceLocationsAsync_m507C5B218CA7202B33C9E3C16AA442FD9E2F0A16,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AddressablesImpl_OnHandleDestroyed_m48205B3E1BBA2C490C9737E3EF61F2CC0294CD43,
	AddressablesImpl_OnSceneHandleCompleted_m542A4475E4DA62372E5F423BF70A0363F141EA87,
	AddressablesImpl_OnHandleCompleted_m0F6517BB79B2FFEF7D3DCB8D5B19D1DEDE90B38E,
	NULL,
	NULL,
	AddressablesImpl_Release_mBCA4FCEFB7E34BD7DA17F90CC86028197383A61D,
	AddressablesImpl_GetDownloadSizeWithChain_m8C34CD7F772F35D787FE002E6B2063816C5F5F7F,
	AddressablesImpl_ComputeCatalogSizeWithChain_m169C765E42B01EA37A4236314BE55543E708B460,
	AddressablesImpl_IsCatalogCached_mE931FAC878D0C8FCD9BBE298826D7D776EE23810,
	AddressablesImpl_GetRemoteCatalogHeaderSize_m0F2AD3A7A76933B73985021C2AD92081599B4718,
	AddressablesImpl_GetDownloadSizeWithChain_mCD6CABEBE8D17E5F80956CA61431B2969EEB659D,
	AddressablesImpl_GetDownloadSizeAsync_mCD8F00A23FB807951242D109791248C542EE9B76,
	AddressablesImpl_GetDownloadSizeAsync_mF7C1FB7C9793E9A7715EF4838E1E098C39903DB0,
	AddressablesImpl_DownloadDependenciesAsyncWithChain_m82EA58A0C022B1FF208798D5D3E981764E3077CC,
	AddressablesImpl_WrapAsDownloadLocations_mFE727D4AE96D0080C8DBF546FCEBCA20D2272401,
	AddressablesImpl_GatherDependenciesFromLocations_mC7A83A12F11693B271885BFCA7B2869C7B817E88,
	AddressablesImpl_DownloadDependenciesAsync_m4E27BE7C9A8C259B1AD4A7CF11765924981E8997,
	AddressablesImpl_DownloadDependenciesAsyncWithChain_m5867093606DB38893E91DE7996F837BCD83792AB,
	AddressablesImpl_DownloadDependenciesAsync_m10CBB4F9F01BE24C4FA47D900F61D1BEABD4DEE4,
	AddressablesImpl_DownloadDependenciesAsyncWithChain_m00678A3E449AA0DF2BD3B6225CABA77527A012E3,
	AddressablesImpl_DownloadDependenciesAsync_mC64850B023B6B832EFD3C644A2A06CE4FC71DE2F,
	AddressablesImpl_ClearDependencyCacheForKey_m26E7292DBBAB525BC1665E129684F51A39E5A8CB,
	NULL,
	AddressablesImpl_ClearDependencyCacheAsync_mE7ACB0D3E62DDD07F64712F3AD798D68D6775F73,
	AddressablesImpl_ClearDependencyCacheAsync_m0DB5056B4986B2A50A970C6B9E462A7D93261BB6,
	AddressablesImpl_ClearDependencyCacheAsync_m283EC8640789B6FFC2EC7600903163356ED64396,
	AddressablesImpl_InstantiateAsync_mFA8C8388EB69536C452ED0B70A14C00BCA3BA095,
	AddressablesImpl_InstantiateAsync_m408055C93BD550F7359348E077C8B9FF3471B381,
	AddressablesImpl_InstantiateAsync_mD834F9EFCC94F9FB72BD2BC3EAE53BD7F9861E58,
	AddressablesImpl_InstantiateAsync_mEC6599097225FEA1388691B7B7AA1309A81F39ED,
	AddressablesImpl_InstantiateWithChain_m2CB2C73F5F8ED56745C79E4CF30337556D5FCB6B,
	AddressablesImpl_InstantiateAsync_mEE0922EE21FCF112AB428A526D228BFFBDD810AD,
	AddressablesImpl_InstantiateWithChain_m518DA48F0A30FC8D22B73227313CD328E13E2102,
	AddressablesImpl_InstantiateAsync_m9C8C0A5C1240DA8367D5D1D675372AF193ED1DB4,
	AddressablesImpl_ReleaseInstance_mE61D6A077166382F5DC59DC3D3DFA6C90270705D,
	AddressablesImpl_LoadSceneWithChain_m6580B1C924CC4BDA2600CD49F6D198949E1594F6,
	AddressablesImpl_LoadSceneWithChain_m34FFD23F3C24F5790457810FBE80FB73250FDED8,
	AddressablesImpl_LoadSceneAsync_m3733A0B410868ABB911F915D83D31D7B07BF0BD0,
	AddressablesImpl_LoadSceneAsync_m6E60A77B0D23933801B938CCDB5CC48B2ACF0F24,
	AddressablesImpl_LoadSceneAsync_mDB5B0182F0A5A489B9A411CC4CE8E0AA5B961F0C,
	AddressablesImpl_LoadSceneAsync_mCB03AE1B2FF7EEBD52E9D1E9440B9932F701A1D6,
	AddressablesImpl_LoadSceneAsync_m3C85CAE0B3A111A496E86A67352F6D069C2E3039,
	AddressablesImpl_UnloadSceneAsync_mBADAC031E335590330C3DDE567EA61F15CF4FDFF,
	AddressablesImpl_UnloadSceneAsync_m37210AD09729FEAF3F075450BCC9A7C38B6A488C,
	AddressablesImpl_UnloadSceneAsync_mBD793D435D1FFC95B2F575701E2B05F78D67C4C3,
	AddressablesImpl_CreateUnloadSceneWithChain_m4773DCFDCD481D0677ED8738A34DDF1C2E97964F,
	AddressablesImpl_CreateUnloadSceneWithChain_m217E2D006EAAEA5FE9140909C3E4D823A11ED527,
	AddressablesImpl_InternalUnloadScene_mFFCE1581450DEFA20442388F93A496C50C2C0B71,
	AddressablesImpl_EvaluateKey_mF8F0DBA33B0A60464585E2422A6D054E34E8B4F8,
	AddressablesImpl_CheckForCatalogUpdates_mCBEC83E0DC72938D3F55F6E3B5B193B921E3619F,
	AddressablesImpl_CheckForCatalogUpdatesWithChain_m4A4C8F003EB53481A634BE4995016490E447886C,
	AddressablesImpl_GetLocatorInfo_mC638F2D008FE42A18F3672B57EECFFF4E1177E9E,
	AddressablesImpl_get_CatalogsWithAvailableUpdates_m9E268E83C0B9766C88A75CC22422AE06F780B464,
	AddressablesImpl_UpdateCatalogs_mB39266B1E19DD7179946990544FDBC123D6F2F23,
	AddressablesImpl_Equals_m9C9049FC1B9E31A96E7440E849688849815B79AA,
	AddressablesImpl_GetHashCode_m9AFBC9F6CD9EEB911131180B201E42969DF95DA7,
	AddressablesImpl_CleanBundleCache_mBF5BFA48E1ADC3D8175D6002B4805DF80F324F43,
	AddressablesImpl_CleanBundleCache_mF07D7181620D08FB994A5E05A15032D58BAB2261,
	AddressablesImpl_CleanBundleCacheWithChain_m8DAC379852F84E5A3BC326DA1788BE326B5ABA08,
	AddressablesImpl_CleanBundleCacheWithChain_m1D5DAC06240014BB573DDFEBF73C144259BFD787,
	AddressablesImpl_U3CTrackHandleU3Eb__73_0_m505860096F2742FCB0EC2C7AA5EF036A77890B68,
	AddressablesImpl_U3CGetRemoteCatalogHeaderSizeU3Eb__102_0_m52CF758D484BBEE4162EB560D7F23A8C2F59F05C,
	LoadResourceLocationKeyOp_get_DebugName_m7182C31D9604CBB0162CA85BCA00F146B604C0BD,
	LoadResourceLocationKeyOp_Init_mC9E0250DEE601A85A904837AB8B7DFDE38768889,
	LoadResourceLocationKeyOp_InvokeWaitForCompletion_m8C6DCBB4BB0EFAE3A7D4B8B98C67649881DB2250,
	LoadResourceLocationKeyOp_Execute_mAFA1E3E5CF26E7DDFFD39CC87023F80796B2AFD2,
	LoadResourceLocationKeyOp__ctor_mB595D261116AA11C4A07330E3E86560D05394044,
	LoadResourceLocationKeysOp_get_DebugName_m4C1800E01BE2301FD440FD23835F102F78978C15,
	LoadResourceLocationKeysOp_Init_m0B0FB4F0FEF6A6CE880EE39AB8A55A2FA4403D03,
	LoadResourceLocationKeysOp_Execute_m45739EC09ED6412F383E7B03C7CBD2C152357234,
	LoadResourceLocationKeysOp_InvokeWaitForCompletion_mE0E2D9BB217B3F830A6D56E3BBE07D862567822D,
	LoadResourceLocationKeysOp__ctor_mB930530F598048D7253C4F9346DD6F7C55FB8C56,
	U3CU3Ec__cctor_m65F36F3B1EDBCF7D5815B0324971AFDAAD1A1654,
	U3CU3Ec__ctor_m174D0B02F3D309C783651F872C869FA2462C9D57,
	U3CU3Ec_U3Cget_ResourceLocatorsU3Eb__59_0_mF1BED58AE9127242AA85FA2279E67E46F1A0CDEF,
	U3CU3Ec_U3Cget_CatalogsWithAvailableUpdatesU3Eb__146_0_mBFE07768CE72A2DC93CBE46D96B65B9E9DC621C5,
	U3CU3Ec_U3Cget_CatalogsWithAvailableUpdatesU3Eb__146_1_m00AF67900E74CA442591411209B38CFCD9E3B0AF,
	U3CU3Ec_U3CCleanBundleCacheU3Eb__150_0_m358FDE260FA8DCEE33099E88A7C72765E7484237,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass100_0__ctor_m684C2E526C7F6056C1B6FA138CBB33C2FA4AD350,
	U3CU3Ec__DisplayClass100_0_U3CComputeCatalogSizeWithChainU3Eb__0_mCBB70B9839F6C073CA118F6F9162A258C7CC0DF6,
	U3CU3Ec__DisplayClass103_0__ctor_m1D354FB7E66A6F2EA3EBCF482674343D34538159,
	U3CU3Ec__DisplayClass103_0_U3CGetDownloadSizeWithChainU3Eb__0_m950A041EC832A778DC5B8A3CB31679398FC4F62C,
	U3CU3Ec__DisplayClass106_0__ctor_mBBF922B20F9286E096B0E40D15A48EC7DA1BD464,
	U3CU3Ec__DisplayClass106_0_U3CDownloadDependenciesAsyncWithChainU3Eb__0_m3E0BA0E2E942629B40D9F833D0FA7197ACF73E64,
	U3CU3Ec__DisplayClass110_0__ctor_m1F7F7D26903507C157E56FDFE5D54607F59A140C,
	U3CU3Ec__DisplayClass110_0_U3CDownloadDependenciesAsyncWithChainU3Eb__0_mFBA6719807263C72ED3BFCD8D2E8FE48D107391D,
	U3CU3Ec__DisplayClass112_0__ctor_m65B0C78E971B8B62537C7292661A6FFBDC875B21,
	U3CU3Ec__DisplayClass112_0_U3CDownloadDependenciesAsyncWithChainU3Eb__0_m1D29DA8A2C7159E318E681849678324C764850BE,
	U3CU3Ec__DisplayClass116_0__ctor_m6BD9C0B7BEDBC4FD2BF7C94CDFA61E2385D3C389,
	U3CU3Ec__DisplayClass116_0_U3CClearDependencyCacheAsyncU3Eb__0_mD7057AA8D1546A397FE4F2DF6235D208C4EDDF23,
	U3CU3Ec__DisplayClass117_0__ctor_mA28CA9FC30616995A19CEA1A0B26C1139BFF813E,
	U3CU3Ec__DisplayClass117_0_U3CClearDependencyCacheAsyncU3Eb__0_m34E148D13E7CE7678D20730A7B46752CBB8E7BD1,
	U3CU3Ec__DisplayClass118_0__ctor_mFED3F459A43F9D6AF58C0A8DFB6D667438DB4C61,
	U3CU3Ec__DisplayClass118_0_U3CClearDependencyCacheAsyncU3Eb__0_m603B8B15F4605959C0ACCB1AA5E4A2C7A59C5426,
	U3CU3Ec__DisplayClass123_0__ctor_mE4C1E9D92F36E8942ECED0219DFE2216AEF649A0,
	U3CU3Ec__DisplayClass123_0_U3CInstantiateWithChainU3Eb__0_m2DFD778DD90EE1C46AC6DCFE9DB6E755CD8F193E,
	U3CU3Ec__DisplayClass125_0__ctor_m5775CE749F80AE36E0889989C3993B0AF81BD50F,
	U3CU3Ec__DisplayClass125_0_U3CInstantiateWithChainU3Eb__0_m8B6E24922C4D8097AE7FE3526B637B3F9E00854D,
	U3CU3Ec__DisplayClass128_0__ctor_mDE52D65C4530B329961C6DF540EDA6C5B66A204B,
	U3CU3Ec__DisplayClass128_0_U3CLoadSceneWithChainU3Eb__0_mB0C5168A3DD03396D0156AAF6E1C552DEB3A5307,
	U3CU3Ec__DisplayClass129_0__ctor_m690CE513F27DC3315914A7D1D11FAE64BD220725,
	U3CU3Ec__DisplayClass129_0_U3CLoadSceneWithChainU3Eb__0_mE92E7D69BA593931E23732EA3F3E4CFAB17194ED,
	U3CU3Ec__DisplayClass138_0__ctor_m354E2F6C07374E6AE11A9F87FF59CF27235B3485,
	U3CU3Ec__DisplayClass138_0_U3CCreateUnloadSceneWithChainU3Eb__0_m444850DE80B083E0C15CAFBCE5163DC7FAB0A177,
	U3CU3Ec__DisplayClass139_0__ctor_mAE4DEB73391DF8A15D7B3E2518F35EE23ADC6626,
	U3CU3Ec__DisplayClass139_0_U3CCreateUnloadSceneWithChainU3Eb__0_m57B6218F34E4A89B44DBE91B3D88BA715D443E30,
	U3CU3Ec__DisplayClass143_0__ctor_mC0E6F10EFDE96ACF18F39401A69CD9D9E153D2F6,
	U3CU3Ec__DisplayClass143_0_U3CCheckForCatalogUpdatesWithChainU3Eb__0_m982B61590FDE9CD99A70B6659C1A057DCF87C98B,
	U3CU3Ec__DisplayClass147_0__ctor_m70885B58326BD0FF5BE810516766B9AF4ABD6ED3,
	U3CU3Ec__DisplayClass147_0_U3CUpdateCatalogsU3Eb__0_mAAEB96AABA4C132DE6BFBF790CD7AD534350CD9C,
	U3CU3Ec__DisplayClass152_0__ctor_mC3B74ECDD1409A2C16FD416349F34EE92AE0895B,
	U3CU3Ec__DisplayClass152_0_U3CCleanBundleCacheWithChainU3Eb__0_m1C8339C21E05486F523C425F615248D9600A4881,
	U3CU3Ec__DisplayClass153_0__ctor_mA53062822C78034E8105AFEFF147F3A32D028E07,
	U3CU3Ec__DisplayClass153_0_U3CCleanBundleCacheWithChainU3Eb__0_m08B8620435E94C68C9B3F14B1667FCF9EEADC11F,
	U3CU3Ec__DisplayClass61_0__ctor_m54906041791B9BD4DD7137E8042FC7EFAF082DD4,
	U3CU3Ec__DisplayClass61_0_U3CRemoveResourceLocatorU3Eb__0_mD8B0008E5181881BD56E79E53B362255378468AF,
	U3CU3Ec__DisplayClass72_0__ctor_m571A9A00E8C38F2CF5491E783F8BD737561587CF,
	U3CU3Ec__DisplayClass72_0_U3CLoadContentCatalogAsyncU3Eb__0_m3185C31DC80EB494BF66753EE0B44CBD8C6851C2,
	NULL,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass83_0__ctor_mDD6F3FCC7B2EAC2546437094AA8CD187057E4F49,
	U3CU3Ec__DisplayClass83_0_U3CLoadResourceLocationsWithChainU3Eb__0_mE9A8D713FC4E61BC01B0799BBD61721026033A67,
	U3CU3Ec__DisplayClass85_0__ctor_m6F49FA14D34D371984A637BEFAFBCC4C221F3C82,
	U3CU3Ec__DisplayClass85_0_U3CLoadResourceLocationsWithChainU3Eb__0_mF16389453D43CB0754F35C68E6FD956AFC1B8735,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass99_0__ctor_m65D4E527C4C59C76237B590810A89986C3B6F52A,
	U3CU3Ec__DisplayClass99_0_U3CGetDownloadSizeWithChainU3Eb__0_mF5018219FDED956758ED4E6B0ECEE6F1E7FB0F01,
	AssetLabelReference_get_labelString_mB279E3D5A71CEAB12320C34D42F6A8693446F457,
	AssetLabelReference_set_labelString_mC49D18432DACA47F342CE3D233F1AFC7561792FE,
	AssetLabelReference_get_RuntimeKey_m93CE4F4594DD522C0A89B9069C49108EA6E7CA38,
	AssetLabelReference_RuntimeKeyIsValid_m82C616599AD12D7D943E75D0B516F07B1A0F3DAA,
	AssetLabelReference_GetHashCode_m63A7B342B3FFEE89919B13047314E29FFCEA323A,
	AssetLabelReference__ctor_m98399A6E27E40BD44A990540BBCC00CC2FAB8D6F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AssetReferenceGameObject__ctor_m06F030B2E5586975C190F18A13FB37C298987BB5,
	AssetReferenceTexture__ctor_m6D8A78D21DE7A7A902A51DC16F8EA7162679A1DF,
	AssetReferenceTexture2D__ctor_mC08F14BA36A9EE52E4F21CDF429881B6C5A450CB,
	AssetReferenceTexture3D__ctor_m8AAC6DEA3754402CDF06323A3C7517341BC0D5F8,
	AssetReferenceSprite__ctor_mDC0E86A070B7808FA3CA6DF2EBBD4E3725905BE2,
	AssetReferenceSprite_ValidateAsset_mF5856555E8A3156E9A11905129EBD34EDB93107A,
	AssetReferenceAtlasedSprite__ctor_m1831E591FBA26F58A5B09C22C729E187A8B1EE12,
	AssetReferenceAtlasedSprite_ValidateAsset_mB3E5C678C3241C501306B6E796FEFA0723EB5979,
	AssetReferenceAtlasedSprite_ValidateAsset_m48DE085EB5AA8AE65BAE45675359F7A01CFBACEC,
	AssetReference_get_OperationHandle_mA4ACC3916FDA7DFC1E8C4B771B68241E049AEAE7,
	AssetReference_set_OperationHandle_m9A1C8675CD0978DDB20CFA77E82609436B525CEA,
	AssetReference_get_RuntimeKey_mB52D387E5244EAD047745D2A8904DA0D9C864F74,
	AssetReference_get_AssetGUID_m74C838B7F893C1B3230C6ED65EF5B23BC6DBD4F9,
	AssetReference_get_SubObjectName_m0B0C2FA0541A14BB11C760D4DC64FEB83D0798B0,
	AssetReference_set_SubObjectName_mF779DF6D4BE9BA7DB4F9685F3B46F02F83BE7012,
	AssetReference_get_SubOjbectType_mC090A0EE55581CADB950F2884C1C69553D36885C,
	AssetReference_IsValid_m3A1FA94BA7D6573485F6D3120740EC6CDF4BE4D9,
	AssetReference_get_IsDone_m95CA175979A05DB8FA809157093ACEEED5ED45FD,
	AssetReference__ctor_m1B9255F08152E57E683A1C4DB603BF2C75605BAB,
	AssetReference__ctor_m239611FF89EAB0B2AC82E7E3303166B910F69C8B,
	AssetReference_get_Asset_mB104EEC61E7E58914D43CFBAAFF518979E27F12B,
	AssetReference_ToString_mBBCBADD628EC8D46171956F79B4F869DE8688FCE,
	NULL,
	NULL,
	AssetReference_LoadScene_mB6C819789F857CD630CACC5F8F22598C94D06BCC,
	AssetReference_Instantiate_mF5626C68E0B70B0468ABD1B481BDEDBEEEBD01C6,
	AssetReference_Instantiate_m39937255B168D7849CC7CE05262630041298A94C,
	NULL,
	AssetReference_LoadSceneAsync_m07D39AC840AD18BAE80FB8050CFA7F3D5D452B7C,
	AssetReference_UnLoadScene_mEC0A04AE8FCE47164961111A9A59E8E0FF7F333B,
	AssetReference_InstantiateAsync_m4C9966DA2ECA2BFBF5520C5C7831A2778394A1AE,
	AssetReference_InstantiateAsync_mBE3A36E01A59222911FF2A8D354C32D4188EFCE5,
	AssetReference_RuntimeKeyIsValid_m72A4C6B49D06D3985FEDE36CB19667FE8E23BDF5,
	AssetReference_ReleaseAsset_mB83E9D70CEB0042D15EA5BC9B00E245EF6765A02,
	AssetReference_ReleaseInstance_m14337B1684BF39CE7E5C2B31AF7FBBB9CCAFD164,
	AssetReference_ValidateAsset_mCA00616B7F960333A13C518E0E388B1D1D09B1A5,
	AssetReference_ValidateAsset_m509B132545E3C9FA5526F013F5D7A77986D141CA,
	NULL,
	NULL,
	CheckCatalogsOperation__ctor_m219855D6B8101674B79BE71D2BB298AE35CAD656,
	CheckCatalogsOperation_Start_m33EFCDB1C93D3349DCDA41708DF3D753AB820CFD,
	CheckCatalogsOperation_InvokeWaitForCompletion_m994BDCA439BEBF467344D6EA6007AE051E9A9309,
	CheckCatalogsOperation_Destroy_mBCF867F11D72E29998EA013BC63433EA8DD9E000,
	CheckCatalogsOperation_GetDependencies_mBDFDEE060B2A746F85610F32776E8916195D214D,
	CheckCatalogsOperation_ProcessDependentOpResults_m6782EC6BB33564D5AA2D5D5B59246DD409FDD382,
	CheckCatalogsOperation_Execute_mDDD66461E88E6B57525CCB4A1B293FF8CAF4E13E,
	U3CU3Ec__cctor_mC1BAC0157CA2916216A98CCB39AD8D02F35A95C6,
	U3CU3Ec__ctor_mFDACDE60C12E191E91BB59108F0CCFE6361BCD39,
	U3CU3Ec_U3CStartU3Eb__5_0_m62ABBDF4E546D441FE3FC90FA66E63A22B2B9B36,
	CleanBundleCacheOperation__ctor_m00C16277A8C859B463590EC27DAD938B788EA5DA,
	CleanBundleCacheOperation_Start_m56A1365CE9BA04E28BE051AF0E991C97B6EF735A,
	CleanBundleCacheOperation_CompleteInternal_mAAFBE0F89A6C33907B32C44E5D97AC7F8CD58D0F,
	CleanBundleCacheOperation_InvokeWaitForCompletion_m1C18F200ED096D4E538CA37231A8960F37D25D17,
	CleanBundleCacheOperation_Destroy_m1BD6BBA1B13AB4E54109432A548504792B561F3E,
	CleanBundleCacheOperation_GetDependencies_mEDA53E9E18ADA55C196CF1496913026C3CA0C9F9,
	CleanBundleCacheOperation_Execute_mB7D5A06CB45135400A0A93E39F3CF27BF6A5D24F,
	CleanBundleCacheOperation_UnityEngine_ResourceManagement_IUpdateReceiver_Update_mB772F83D47A57F362C6448745297CD3D5C4597FB,
	CleanBundleCacheOperation_RemoveCacheEntries_mF5384D24FAD20F730A5CB97470ED078B5F702752,
	CleanBundleCacheOperation_DetermineCacheDirsNotInUse_mADF2AC1F2761135BDF6D3996D2689A10E5034764,
	CleanBundleCacheOperation_DetermineCacheDirsNotInUse_m68FEAC51EE1B3C73D746AD9526AECB8109D01B60,
	CleanBundleCacheOperation_GetCacheDirsInUse_m5DAFD43E40E32558662FF5BCF05375F16ACB9E7E,
	UpdateCatalogsOperation__ctor_mC24059B34401DCEDEAC6EF7AE232397A4B8F97B6,
	UpdateCatalogsOperation_Start_mC3FB55D3ABBD31F4C10073A294CFB2A0B1E200A7,
	UpdateCatalogsOperation_InvokeWaitForCompletion_m53FBDFEC0AFFD17C12A118EC2EAAF52C83B6AA45,
	UpdateCatalogsOperation_Destroy_m29BDCE763155716CF97F6BA9C1FCC28703F35377,
	UpdateCatalogsOperation_GetDependencies_m159515E7C3E3738EE2B3D4CBEA5C755A34ADCE93,
	UpdateCatalogsOperation_Execute_mA269F67EA0C49DF42FC4E2C755C2114C305F24DF,
	UpdateCatalogsOperation_OnCleanCacheCompleted_mBAAD03085035B2DCF2781FE69D13ED5A9243604C,
	U3CU3Ec__cctor_mE9D554A2F26607B4DEBFD9E7D83BF6D58526FA0F,
	U3CU3Ec__ctor_mD572CF2016ADF8E07CDBFF03927C35D1589ADFF3,
	U3CU3Ec_U3CStartU3Eb__6_0_m7F9D05BAB1E9E02C53B91F2D8714A907ECD64F22,
	U3CU3Ec__DisplayClass11_0__ctor_mC0990EDA323280DF0279337D541A34BD249E427A,
	U3CU3Ec__DisplayClass11_0_U3COnCleanCacheCompletedU3Eb__0_m521279AD645B82BD7399DF0A599B94A3475C08FB,
	DynamicResourceLocator_get_LocatorId_mAB65647CC9D934BD2F7DF410B1B5B62E2FC113F9,
	DynamicResourceLocator_get_Keys_mC69ACA592238DF646AABFFEDC3232E0238659DEE,
	DynamicResourceLocator_get_AtlasSpriteProviderId_m8B5E40CB4D132AFFB7A73BB55121DCEF3F27C591,
	DynamicResourceLocator_get_AllLocations_m9AF7B214A74721B121556644581EB1EF52C54939,
	DynamicResourceLocator__ctor_m0591C466CEE70375622E314B0A644494DFB3B615,
	DynamicResourceLocator_Locate_mFB3C48C9716AC403E0D482E0F2E6D11E8FA54519,
	DynamicResourceLocator_CreateDynamicLocations_m00AA7E8B7DB23159E543F9D288D5F25292188A81,
	PlatformMappingService_GetAddressablesPlatformInternal_m77DF962D5E753412EBFE0EEAD80B0DCA87B896D1,
	PlatformMappingService_GetAddressablesPlatformPathInternal_m795F15662AB5950097F146356608A9D397E0047C,
	PlatformMappingService_GetPlatform_mA135E54C1F93EBB9F404805EC704928C1CFC774C,
	PlatformMappingService_GetPlatformPathSubFolder_m24731077366BDFD49B87AEC3EA16E3F78CB4D1EA,
	PlatformMappingService__ctor_m9106177388112C338144141438C768C36795F0B1,
	PlatformMappingService__cctor_m1F8CB5C9210FA5166C0D72DAD4C0754EDA94902D,
	DiagnosticInfo_CreateEvent_mFC695681560C9459FBFA471C90094E3325FF4CC9,
	DiagnosticInfo__ctor_mBE6F113C30B6B595260A1C58D68976F755711235,
	ResourceManagerDiagnostics__ctor_mF72CD3FA03BE7F127EF30342D928FB4BF5B3C16B,
	ResourceManagerDiagnostics_SumDependencyNameHashCodes_m6A0C79690FEF8395EB97EBA054D7152FB475409A,
	ResourceManagerDiagnostics_CalculateHashCode_mD0A63E93BBA660EDA64D00077B2D256777FCCC0E,
	ResourceManagerDiagnostics_CalculateCompletedOperationHashcode_m030791457AE2071C4AB4758161B8A0482C4B0C68,
	ResourceManagerDiagnostics_GenerateCompletedOperationDisplayName_m5BC3877EE31B4E3A13ED7C899B8446ACD0C7A2A8,
	ResourceManagerDiagnostics_OnResourceManagerDiagnosticEvent_mFC37FE3507A4A3911B6B12F48A5C64B1963953C3,
	ResourceManagerDiagnostics_Dispose_m74764DF40B3F376B67D29D552615E1D6726B90E7,
	SerializationUtilities_ReadInt32FromByteArray_m496E2FB4F19B471BBBEED40A07DB83E98B56C09C,
	SerializationUtilities_WriteInt32ToByteArray_mE7ED9541AE3C4A60B8036D009AC2FEDE63D18DCB,
	SerializationUtilities_ReadObjectFromByteArray_m30E1BCA3DA1047C02BB01E9CB6A6D615713396FD,
	SerializationUtilities_WriteObjectToByteList_mF3E744C1A7B1A5973EDD0710D3E31C7128D6089A,
	ContentCatalogProvider__ctor_m97F166F23702191D7D16AA3D9BF89E1FF790F450,
	ContentCatalogProvider_Release_m03BB273F5882D9F3A99FC146308180CA301E3A01,
	ContentCatalogProvider_Provide_mA1F3CB11945DB4A494B241DE33F1A14F72C2561F,
	InternalOp_Start_m13B3B4C3CA844D0F8C15D7A6934E223FC21C00EB,
	InternalOp_WaitForCompletionCallback_mCA7721866E3D80E9DFB782A57D3BF7E0416D9FB2,
	InternalOp_Release_m19233D2953DE23EDD4FEF4316DA41FBE215029AF,
	InternalOp_CanLoadCatalogFromBundle_m29E9F085D94C616A0C063064B3D64662BC64F768,
	InternalOp_LoadCatalog_m4BD81C370F6AE19DEF2E8EED967842FA9AD1CF9B,
	InternalOp_CatalogLoadOpCompleteCallback_m7ABE16B5B9550F66BEA06C239B7658EDBEB108DA,
	InternalOp_GetTransformedInternalId_m470174337BBF1E1F783418CF80AB6A55794E099B,
	InternalOp_DetermineIdToLoad_m7A9ED93D1D000457E22F5B71B11E7CD0337AED43,
	InternalOp_OnCatalogLoaded_m329BB3AFD87FFA454AFB357E8B8677CE2986E192,
	InternalOp__ctor_m0BCBDE9F053C63B431C706CC0B02DEB82B88070C,
	InternalOp_U3CLoadCatalogU3Eb__14_0_m5CBB4FD815E005B54023B6F1372C54DDB9D4FE25,
	BundledCatalog_add_OnLoaded_m33CD2D9BBFC438E3FE4229FC61270ABE1757BAA3,
	BundledCatalog_remove_OnLoaded_m68F435DDCFB6D3D2CA28503F9B5B11B9063BC324,
	BundledCatalog_get_OpInProgress_m8D73BFA5A4DC5B0467301A23B98B42D73BF6F069,
	BundledCatalog_get_OpIsSuccess_m14D5377F83708F645CEED98F1AEC0AED746F075B,
	BundledCatalog__ctor_m6D89F5B7F2AD9A45FEC08F39153F95E0BA8ADD99,
	BundledCatalog_Finalize_mB263EF63BE9F8833EA015FF783D59B881AE3A8B2,
	BundledCatalog_Unload_m03E13520B4FA682A239FDC52C34E847C96262762,
	BundledCatalog_LoadCatalogFromBundleAsync_m3ECD92F9B736CBD9E74B484C62741B6279741D39,
	BundledCatalog_WebRequestOperationCompleted_mBA5F7FFA2C52CCADC3CCAB283CDA984BB96EDE30,
	BundledCatalog_LoadTextAssetRequestComplete_m547E724B6A69F92A8A7FC289F35A7B582485D1F8,
	BundledCatalog_WaitForCompletion_mB920DFE0296D0BFD0E4383811C5F6F952DCF0AA8,
	BundledCatalog_U3CLoadCatalogFromBundleAsyncU3Eb__19_1_mC6ADD115296A511E303F04EE7DED7537B3291239,
	BundledCatalog_U3CLoadCatalogFromBundleAsyncU3Eb__19_0_m49D30A2381B93A4822469FF39F342C47B9CFD7C7,
	ContentCatalogDataEntry_get_InternalId_m3F4EEE94BF527908FA3203BA2648D8C30C32CF57,
	ContentCatalogDataEntry_set_InternalId_mD4CD4C7C630C0FCAC3DC69AA0B72F11815931D9C,
	ContentCatalogDataEntry_get_Provider_m7E80375AA50EFCCEE644C57EA3CAD397D9EA41D3,
	ContentCatalogDataEntry_set_Provider_m4DA1BFAE05E7333FF77B05DFB5B868D66CD6C211,
	ContentCatalogDataEntry_get_Keys_mF7D1C60109A7164A11E8852A8AE2C880695E5A72,
	ContentCatalogDataEntry_set_Keys_m3B3D7369ABC77240E00702F613E8B2036D1F4E03,
	ContentCatalogDataEntry_get_Dependencies_m7F9D277A5F94B4DDD6D9699DD922F0E6307B89D6,
	ContentCatalogDataEntry_set_Dependencies_m94C0577CEEDF603C199EC6154D54E68FA02391C9,
	ContentCatalogDataEntry_get_Data_mAE907BFC6C5931BD529AF26D5098FEDA8CE6C530,
	ContentCatalogDataEntry_set_Data_m46804BE456882181345F9E3CA0DFE14866BFDDDE,
	ContentCatalogDataEntry_get_ResourceType_m4C186DC3D2E71A1B31EA3F2D838B99D7B93A26AA,
	ContentCatalogDataEntry_set_ResourceType_m2769BE1342590A10AC25393432902F574945E80A,
	ContentCatalogDataEntry__ctor_m7E597E37E0A6865690293668BE6642107DCF32E5,
	ContentCatalogData_get_BuildResultHash_m7B85D5A4EFD5A5D139846113A80F34FEE779AA36,
	ContentCatalogData_set_BuildResultHash_m84BA4F042281CA62FA5290FB5BB8B46045E5BDC7,
	ContentCatalogData_get_ProviderId_mE5ACA3F702D77AB7EA704C2AD3B823CE138F509A,
	ContentCatalogData_set_ProviderId_mF6488FAAFF42ADA8950C20C861EC20740567542F,
	ContentCatalogData_get_InstanceProviderData_m834DC403FD021DEA0A45877A6E748A31AC70F7DA,
	ContentCatalogData_set_InstanceProviderData_m2697DD7FE45C17FE22551B2AC29444B4DBB60A52,
	ContentCatalogData_get_SceneProviderData_mAFA60EDC86ADDBBBB71A8614C48279788C932A93,
	ContentCatalogData_set_SceneProviderData_m6D182FC50E0B3DF1B924A5C999578A1913731514,
	ContentCatalogData_get_ResourceProviderData_mE21C6A8378AADCED75EC268B0D38F9A8BB081BA8,
	ContentCatalogData_set_ResourceProviderData_mDFFB84F925F604978E61B1205B4FC294C7268D30,
	ContentCatalogData__ctor_m20EC611AE9393E249FE606E4915B5C048CAE31C4,
	ContentCatalogData__ctor_mBC68CA5D564E32298FDB12BD15A9C10661140545,
	ContentCatalogData_get_ProviderIds_mAAE0AD6E3E0F688745840BFED0ACE50A49B8069A,
	ContentCatalogData_get_InternalIds_m491D748768D65E49929740F4149234D091DB546E,
	ContentCatalogData_LoadFromFile_m5FA1E4EBDE4F6F451584BF82723B27582C7B57FB,
	ContentCatalogData_SaveToFile_m554EE39C1C253C8EB5A0D09DBF549C0DDF473082,
	ContentCatalogData_CleanData_m7061F20894338BA105E325243D68BBB255132AF6,
	ContentCatalogData_CreateCustomLocator_m4E0907DB7C5658416BC3041EC0AADE21845FFD55,
	ContentCatalogData_CreateLocator_mC6077733917E0FC434D7106651CC47F81ADED66D,
	ContentCatalogData_GetData_m8662F99E11CB70C8FA0056ECE51E54FEFAB86C28,
	ContentCatalogData_ExpandInternalId_m87F4A2AC028351BBD339C68F57E1A0FA59A089B6,
	ContentCatalogData__cctor_mF3B52D5CF1094462A536044FF82ABAE4166B37BA,
	CompactLocation_get_InternalId_mC16C70D4E5014EA89BFD62107807F062FA47EF62,
	CompactLocation_get_ProviderId_m335FE90B0A5CACDAB78DC9096E429E1261CD0B08,
	CompactLocation_get_Dependencies_m6CCB51754B3A64596D953E3E6081AE52F228478C,
	CompactLocation_get_HasDependencies_m5141EE22AA14F4EDE9FC5AEE2B93EC5C7D33F92D,
	CompactLocation_get_DependencyHashCode_mFA406D583779A37511AD8CAB4A100EB13B239B53,
	CompactLocation_get_Data_mF3DD6A57FD955EB7030532C5B03DCB0EDE447F91,
	CompactLocation_get_PrimaryKey_m498C50CEB2A12D81A5E7B9ECEC6B04220938B011,
	CompactLocation_set_PrimaryKey_m6C38429E9C6C0A7D292FD70C1BA039BDE698DFE0,
	CompactLocation_get_ResourceType_m6F2534DD17152B770E8CD7B63180887C82CE9949,
	CompactLocation_ToString_mACFF7E9338D13EDE4F72E377171A31B25D038B37,
	CompactLocation_Hash_m272067C4BD7CD3F4F4F82E1094911E6232F1F009,
	CompactLocation__ctor_mE8FACA0177302132CC346946EAD2A1A8BBC456EE,
	U3CU3Ec__cctor_mF174F75E5DFA61491D02ECC68A29996CF3073129,
	U3CU3Ec__ctor_m9FD30C948C1A8BCCB8A93A8371B0155069609418,
	U3CU3Ec_U3CGetDataU3Eb__48_0_m9B05C8EAA4FF6D551D1FE4E9990950A34EA38050,
	NULL,
	NULL,
	NULL,
	LegacyResourcesLocator_Locate_m2495A14BFAF91A5CE077BAC06D5E5FACC3DCF5BC,
	LegacyResourcesLocator_get_Keys_m732337A80862621F7F54ACB4F128B0C740C13FBE,
	LegacyResourcesLocator_get_LocatorId_mA8745A21B0FC9A071B8BB3D41ABA92C1C7F70939,
	LegacyResourcesLocator__ctor_m9761FCA7DB2E3D21BC5424FB13B832F60B4C2E92,
	ResourceLocationData_get_Keys_mED03309C4C351194C81A3B4F85FD9BC87C402B05,
	ResourceLocationData_get_InternalId_m543A0A16BCEE568C722BEF8ACB80CE996D2318F0,
	ResourceLocationData_get_Provider_m5F914FDA29E77390D629ADBF4565EAAEDCB083F4,
	ResourceLocationData_get_Dependencies_m0A25C9674BE13EA817A38332A7B90B648BC050FF,
	ResourceLocationData_get_ResourceType_mA94B078186E27CA215B166AA51142198B4EA3465,
	ResourceLocationData_get_Data_m19B678392C47B3FB539F9C9F592C027F86D578B3,
	ResourceLocationData_set_Data_mF06608062093D635426FB2CCF31D4BB36E2CB8C9,
	ResourceLocationData__ctor_mC6BC93B21101353EAD420971B75E3FE6F97CAA75,
	ResourceLocationMap__ctor_m2208B4BDD1EEABBD0AA58DA7B7B44CADDF0A17B5,
	ResourceLocationMap_get_LocatorId_m904C44B5DDCF105BB9036C05A9579E33FC827161,
	ResourceLocationMap_set_LocatorId_m0D6156C92CFBACF321BD856D34D3D86E3350FF2C,
	ResourceLocationMap__ctor_m5D6BEA9C6AF5B3E8085CD0592EAB92876C08931F,
	ResourceLocationMap_get_Locations_mDBC0190E559C406A8C9413E195A60656ED3D136E,
	ResourceLocationMap_get_Keys_mA2893DB96A26617347DB6EBB0EF97C9A6E60AF2D,
	ResourceLocationMap_Locate_mFBB5443BD10920BF4246F53CE7E0ABFB0D846BCF,
	ResourceLocationMap_Add_m659847BEA0D3FCC8A1707E47B227916447F980D0,
	ResourceLocationMap_Add_mE919B9E341B6569C97F055A04E9A7976F20C5EBF,
	AddressablesRuntimeProperties_GetAssemblies_mD3E86E3C70FDD0D914C58CDF9D36F9A0279FF2CB,
	AddressablesRuntimeProperties_GetCachedValueCount_m6C3B071C3D61842E7264C23CAE977A6D7DB913ED,
	AddressablesRuntimeProperties_SetPropertyValue_mEE8B3F7DA4D54817B8AAAB8AAE46C99556753860,
	AddressablesRuntimeProperties_ClearCachedPropertyValues_m0680783D9B1FAC77D8DB7C3565AD6719D2225B7B,
	AddressablesRuntimeProperties_EvaluateProperty_m4771C62AACC00C3C8C843AE347FC7276A63FFC28,
	AddressablesRuntimeProperties_EvaluateString_m8D5439F7D5FD76E58D04D7F3DAB6E0FEF2D84901,
	AddressablesRuntimeProperties_EvaluateString_mEC744A949D466FC39CACF0D72F044B40FD2D32C8,
	AddressablesRuntimeProperties__cctor_m39EBA6D1E9D2EF7A914A4F048C207E302D637DD5,
	CacheInitialization_Initialize_m573FF8030EBE8494895369624E59BCF2E5587EC2,
	CacheInitialization_InitializeAsync_mD4E515BFF3E9E7C15994F7DD0E9C2FC35BB2A03F,
	CacheInitialization_get_RootPath_mC0ED23627FA9364B14F21131343C3D462A0F1393,
	CacheInitialization__ctor_m875CB892A6E780D9927D6EA3F26FD6E10AF67429,
	CacheInitOp_Init_mE5D39CE4E05CE455C48F54DACCC81B1A97907992,
	CacheInitOp_InvokeWaitForCompletion_m60660E8DA579346A0A5A92B63957C83A8CD615CF,
	CacheInitOp_Update_mE6A0E0BBEF6E35D4C41BF437A599449E9AE2A564,
	CacheInitOp_Execute_m7BD406672C3DC5993F90F40E325E908AFB56E944,
	CacheInitOp__ctor_mA335CEDF22E8A3160E91A9C52FCE445108000E33,
	U3CU3Ec__DisplayClass1_0__ctor_m5506F595D9A82AA404F753B6F48697B823FA81A5,
	U3CU3Ec__DisplayClass1_0_U3CInitializeAsyncU3Eb__0_mFA267E258EDF125EE896F4A77337B41BFEF7A97F,
	CacheInitializationData_get_CompressionEnabled_m24C73459490E5D94810D0E19BE6A7B8A2FFD6917,
	CacheInitializationData_set_CompressionEnabled_m5E1F359471C1A7535170C34CA3388AA1553C3382,
	CacheInitializationData_get_CacheDirectoryOverride_m658B7B84EA37378E31D653E199EA4022DF28D231,
	CacheInitializationData_set_CacheDirectoryOverride_m6A2D6B65EB78B7D317EA23D60F8849DD790CC501,
	CacheInitializationData_get_ExpirationDelay_m7C37593CC0F38201D39A330795826973324C08E2,
	CacheInitializationData_set_ExpirationDelay_m8EA2722465904AE00A5DE1ED20835DA8A2F27360,
	CacheInitializationData_get_LimitCacheSize_mD6433457DD34055C9E911FDBC09B877E20E8C51F,
	CacheInitializationData_set_LimitCacheSize_m9F28D622360651446B472B24EB4DE6EDAC85665C,
	CacheInitializationData_get_MaximumCacheSize_m31E6D8FA717BF8BFD25B0F4ED90F1C9C2C2CE6AF,
	CacheInitializationData_set_MaximumCacheSize_m088E7F125A1B3390E6A0E25533F43F4DEED60081,
	CacheInitializationData__ctor_m395841C0A1C6C82528AC4360452F7B646ACB769B,
	InitializationOperation__ctor_mA2A202C201ED8F364F6FFEC174EAE51C2E6A6434,
	InitializationOperation_get_Progress_mFB29BE9EEA230CDD4E515DF73E8F0E60732BE80D,
	InitializationOperation_get_DebugName_m590B65F43134C5390756001BBD04D1DE8F410674,
	InitializationOperation_CreateInitializationOperation_m33F7993FE97B0A04012A9DAA81AB2ECD2EDC766A,
	InitializationOperation_InvokeWaitForCompletion_m650DCC2A0243E4F11D688AAF34DBD8A05E6C0D43,
	InitializationOperation_Execute_mEB9FE18E7E3C2F24F88AD53F3C6DAA03C66C67D9,
	InitializationOperation_LoadProvider_m5EFEC1E24E8D12BD71D95BBB7B799C0BCE667527,
	InitializationOperation_OnCatalogDataLoaded_mDC85CF69F44EFB5D9A383C9AEF77D59C0FF699D9,
	InitializationOperation_LoadContentCatalog_m96F29CCE7193CB68E454EE7474581ACDD3459A23,
	InitializationOperation_LoadContentCatalog_m2B1E267F7B46BBE94489B678020E88C081A9B373,
	InitializationOperation_LoadContentCatalogInternal_mB8879495162C6FDA165CB2571DABBA643C257D4D,
	InitializationOperation_LoadOpComplete_m7411329487C62A65E027BE07B055CF290C09C009,
	U3CU3Ec__cctor_mE9258D312AFF75B5AE671A1B5B0B352B98A96F29,
	U3CU3Ec__ctor_m400FA93F30A0788073EEF09EFDA850B0DD08B1D1,
	U3CU3Ec_U3CExecuteU3Eb__13_0_mAED31DB7FABB1ABC307A8C6610EC50871F29A002,
	U3CU3Ec__DisplayClass16_0__ctor_m8DD3D41CB6ADB76E0EA33F4AF0F24D8492D77AB6,
	U3CU3Ec__DisplayClass16_0_U3CLoadContentCatalogU3Eb__0_m66DB2E022694F69F39CCFFC3154CB5AB5976645F,
	U3CU3Ec__DisplayClass18_0__ctor_m99303C3B088C7B4052A6F8AA53CAA1BC21EB1D5A,
	U3CU3Ec__DisplayClass18_0_U3CLoadContentCatalogInternalU3Eb__0_m8E4BA6A17387BF7F02B23A139D7E8A5C782D43BA,
	ResourceManagerRuntimeData_get_BuildTarget_mB64F1595BD714F5D79138ED161B4F05D3A0FE121,
	ResourceManagerRuntimeData_set_BuildTarget_m0958B3EC4A849CEB55CD6E94277F093AE824B83A,
	ResourceManagerRuntimeData_get_SettingsHash_m05B636625500B0CADC8C1116A2C880DD90BD9D46,
	ResourceManagerRuntimeData_set_SettingsHash_mC0115B10FB9904CB21A385433EEBA3E8AB47A74A,
	ResourceManagerRuntimeData_get_CatalogLocations_m6BDA2FA48B12635AD67A780C5E197C02BB411172,
	ResourceManagerRuntimeData_get_ProfileEvents_mD05FEC5FBD8C8A04EB35B153297CA9FE0A92220B,
	ResourceManagerRuntimeData_set_ProfileEvents_m41B9D67AD80113BC75407C5432F45E6BC8CD7551,
	ResourceManagerRuntimeData_get_LogResourceManagerExceptions_m421154EADF056D9461BFAE4D8C98B3818457BF2C,
	ResourceManagerRuntimeData_set_LogResourceManagerExceptions_m815094B1BF1DDA5C333ED3F3333E8D074C4396B4,
	ResourceManagerRuntimeData_get_InitializationObjects_m0C2D008C79BD52C0F9895F093E4AFD3AFDB9E014,
	ResourceManagerRuntimeData_get_DisableCatalogUpdateOnStartup_m2EB745669A9AC1D07E47D345DE2EF87442D56907,
	ResourceManagerRuntimeData_set_DisableCatalogUpdateOnStartup_m43EF818B02F4E5A00C57BA9BF19BCFF68779834D,
	ResourceManagerRuntimeData_get_IsLocalCatalogInBundle_m5D312C49BFCB754034EC7BBFD1EAF7BF1AF6B312,
	ResourceManagerRuntimeData_set_IsLocalCatalogInBundle_m6931A3CBF1E368065CC96972855EA73BFD7E8D56,
	ResourceManagerRuntimeData_get_CertificateHandlerType_m1194D6694DE8A1FBBF29D5025C2E9C9DB1FD941F,
	ResourceManagerRuntimeData_set_CertificateHandlerType_m3276FCE0F13D2E6C72B7DBDFDDE1B6339A9D633F,
	ResourceManagerRuntimeData_get_AddressablesVersion_m0B7B76DF108ABD855470460DF6D862480D1A00D7,
	ResourceManagerRuntimeData_set_AddressablesVersion_m77531E4740B232F4052281D8E0E061F8B1E04556,
	ResourceManagerRuntimeData_get_MaxConcurrentWebRequests_m1CF41EB91ABB3B99C338122E3507D3D196A838F0,
	ResourceManagerRuntimeData_set_MaxConcurrentWebRequests_m197F95E0235FC2BE257A01E923013464687EB8B9,
	ResourceManagerRuntimeData_get_CatalogRequestsTimeout_mED5A093F40FE50DF355C12FE18D6FCFB2AC993D1,
	ResourceManagerRuntimeData_set_CatalogRequestsTimeout_mF4DBF74FCF5A3AC5D15D257666EEC56771B5B097,
	ResourceManagerRuntimeData__ctor_m5AD4B872EB21CB8C9613B008ADA0A55980AB4B75,
};
extern void RuntimeBuildLog__ctor_m29FC0C974B2C6D702AF74C393BF2640D0836A7DB_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[1] = 
{
	{ 0x06000004, RuntimeBuildLog__ctor_m29FC0C974B2C6D702AF74C393BF2640D0836A7DB_AdjustorThunk },
};
static const int32_t s_InvokerIndices[654] = 
{
	6992,
	5806,
	7120,
	3140,
	10467,
	7120,
	4261,
	4261,
	7120,
	5806,
	4261,
	4261,
	6992,
	2771,
	6992,
	4261,
	6887,
	7120,
	7120,
	5391,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6887,
	5703,
	1909,
	6992,
	6887,
	1909,
	6992,
	5806,
	6992,
	5806,
	6753,
	5806,
	3363,
	1909,
	1907,
	1322,
	7120,
	5806,
	3363,
	3371,
	6992,
	5181,
	5181,
	2682,
	2682,
	5181,
	5181,
	2202,
	1630,
	10420,
	10420,
	10420,
	10420,
	10420,
	10054,
	10420,
	10293,
	10420,
	10293,
	10420,
	10420,
	10420,
	10420,
	10420,
	9623,
	8954,
	10293,
	9630,
	10293,
	9630,
	10293,
	9563,
	10293,
	9630,
	10354,
	10354,
	9672,
	9010,
	9010,
	8445,
	10354,
	0,
	0,
	0,
	0,
	8440,
	8440,
	8440,
	9006,
	9006,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10283,
	9834,
	9823,
	9819,
	9673,
	9673,
	9673,
	9673,
	9673,
	9803,
	9036,
	9036,
	8489,
	8489,
	10293,
	10293,
	10293,
	10293,
	10293,
	9009,
	9009,
	9009,
	9009,
	9009,
	10054,
	10054,
	7933,
	7533,
	7933,
	7533,
	8444,
	8444,
	7933,
	7533,
	7933,
	7533,
	8444,
	8444,
	7936,
	7936,
	7936,
	7937,
	7936,
	7937,
	9013,
	9012,
	9011,
	8447,
	8449,
	8448,
	9013,
	9012,
	9011,
	9670,
	9008,
	8443,
	8965,
	10293,
	10455,
	9671,
	0,
	0,
	0,
	10455,
	6992,
	5806,
	6992,
	6957,
	5774,
	6957,
	6957,
	5806,
	7120,
	6992,
	5806,
	6992,
	5806,
	6877,
	6887,
	5845,
	6992,
	6992,
	6992,
	6992,
	5806,
	3363,
	5806,
	3363,
	5806,
	2803,
	5806,
	3363,
	5181,
	6992,
	1909,
	5806,
	7120,
	1485,
	773,
	1400,
	6186,
	3494,
	0,
	0,
	0,
	7120,
	1399,
	3504,
	0,
	3820,
	7120,
	0,
	0,
	0,
	0,
	724,
	1389,
	1388,
	1998,
	0,
	0,
	0,
	0,
	0,
	0,
	5693,
	5693,
	5693,
	0,
	0,
	5693,
	2008,
	3499,
	2200,
	3499,
	2008,
	3499,
	3499,
	1415,
	10293,
	10054,
	2056,
	1415,
	2056,
	741,
	1416,
	4261,
	0,
	2005,
	2005,
	2005,
	730,
	408,
	730,
	408,
	729,
	1396,
	729,
	1396,
	4261,
	410,
	409,
	3506,
	412,
	3506,
	411,
	412,
	1407,
	1404,
	1403,
	1404,
	1403,
	1403,
	5181,
	3487,
	3487,
	5181,
	6992,
	1394,
	2202,
	4927,
	2005,
	2001,
	2001,
	2005,
	5397,
	3497,
	6992,
	1909,
	6887,
	7120,
	7120,
	6992,
	1327,
	7120,
	6887,
	7120,
	10455,
	7120,
	5181,
	4261,
	5181,
	5181,
	0,
	0,
	0,
	7120,
	3498,
	7120,
	3498,
	7120,
	3481,
	7120,
	3481,
	7120,
	3481,
	7120,
	3490,
	7120,
	3490,
	7120,
	3490,
	7120,
	3491,
	7120,
	3491,
	7120,
	3505,
	7120,
	3505,
	7120,
	3505,
	7120,
	3504,
	7120,
	3486,
	7120,
	3485,
	7120,
	3490,
	7120,
	3490,
	7120,
	4261,
	7120,
	3493,
	0,
	0,
	0,
	0,
	7120,
	3482,
	7120,
	3482,
	0,
	0,
	0,
	0,
	0,
	0,
	7120,
	3498,
	6992,
	5806,
	6992,
	6887,
	6957,
	7120,
	0,
	0,
	0,
	0,
	0,
	5806,
	5806,
	5806,
	5806,
	5806,
	4261,
	5806,
	4261,
	4261,
	6877,
	5693,
	6992,
	6992,
	6992,
	5806,
	6992,
	6887,
	6887,
	7120,
	5806,
	6992,
	6992,
	0,
	0,
	6189,
	1398,
	2006,
	0,
	1405,
	6189,
	1398,
	2006,
	6887,
	7120,
	5806,
	4261,
	4261,
	0,
	0,
	5806,
	3488,
	6887,
	7120,
	5806,
	7765,
	7120,
	10455,
	7120,
	4261,
	3349,
	3489,
	1768,
	6887,
	7120,
	5806,
	7120,
	5851,
	7120,
	5806,
	5806,
	5181,
	5806,
	2000,
	6887,
	7120,
	5806,
	7120,
	2770,
	10455,
	7120,
	4261,
	7120,
	5392,
	6992,
	6992,
	6992,
	6992,
	5806,
	1485,
	696,
	9938,
	10051,
	10412,
	10420,
	7120,
	10455,
	794,
	7120,
	5806,
	4841,
	4841,
	4841,
	5168,
	5970,
	7120,
	9281,
	8623,
	9376,
	9282,
	5806,
	3363,
	5822,
	1935,
	6887,
	7120,
	2202,
	3349,
	5393,
	5181,
	1627,
	5806,
	7120,
	5806,
	5806,
	5806,
	6887,
	6887,
	3358,
	7120,
	7120,
	7120,
	5806,
	5806,
	6887,
	5806,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	6992,
	5806,
	381,
	6992,
	5806,
	6992,
	5806,
	6993,
	5807,
	6993,
	5807,
	6992,
	5806,
	5806,
	7120,
	6992,
	6992,
	9376,
	5806,
	7120,
	2682,
	5181,
	6992,
	9381,
	10455,
	6992,
	6992,
	6992,
	6887,
	6957,
	6992,
	6992,
	5806,
	6992,
	6992,
	4927,
	147,
	10455,
	7120,
	5181,
	0,
	0,
	0,
	1485,
	6992,
	6992,
	7120,
	6992,
	6992,
	6992,
	6992,
	6992,
	6992,
	5806,
	696,
	3358,
	6992,
	5806,
	3363,
	6992,
	6992,
	1485,
	3363,
	3363,
	10420,
	10412,
	9630,
	10455,
	10054,
	10054,
	8277,
	10455,
	2202,
	1395,
	10420,
	7120,
	5806,
	6887,
	5851,
	7120,
	7120,
	7120,
	6887,
	6887,
	5703,
	6992,
	5806,
	6957,
	5774,
	6887,
	5703,
	6958,
	5775,
	7120,
	5806,
	7043,
	6992,
	8446,
	6887,
	7120,
	8970,
	7934,
	7935,
	1401,
	731,
	618,
	10455,
	7120,
	4261,
	7120,
	3492,
	7120,
	5394,
	6992,
	5806,
	6992,
	5806,
	6992,
	6887,
	5703,
	6887,
	5703,
	6992,
	6887,
	5703,
	6887,
	5703,
	6992,
	5806,
	6992,
	5806,
	6957,
	5774,
	6957,
	5774,
	7120,
};
static const Il2CppTokenRangePair s_rgctxIndices[47] = 
{
	{ 0x02000011, { 135, 3 } },
	{ 0x02000026, { 138, 3 } },
	{ 0x02000027, { 141, 3 } },
	{ 0x0200002A, { 144, 4 } },
	{ 0x0200002B, { 148, 4 } },
	{ 0x0200002C, { 152, 4 } },
	{ 0x0200002F, { 156, 5 } },
	{ 0x06000058, { 0, 2 } },
	{ 0x06000059, { 2, 2 } },
	{ 0x0600005A, { 4, 2 } },
	{ 0x0600005B, { 6, 2 } },
	{ 0x06000061, { 8, 3 } },
	{ 0x06000062, { 11, 3 } },
	{ 0x06000063, { 14, 3 } },
	{ 0x06000064, { 17, 3 } },
	{ 0x06000065, { 20, 3 } },
	{ 0x06000066, { 23, 3 } },
	{ 0x06000067, { 26, 3 } },
	{ 0x06000068, { 29, 3 } },
	{ 0x06000069, { 32, 3 } },
	{ 0x0600006A, { 35, 3 } },
	{ 0x0600006B, { 38, 3 } },
	{ 0x0600006C, { 41, 2 } },
	{ 0x0600006D, { 43, 3 } },
	{ 0x060000AA, { 46, 1 } },
	{ 0x060000AB, { 47, 1 } },
	{ 0x060000AC, { 48, 1 } },
	{ 0x060000D4, { 49, 1 } },
	{ 0x060000D5, { 50, 1 } },
	{ 0x060000D6, { 51, 1 } },
	{ 0x060000DA, { 52, 3 } },
	{ 0x060000DD, { 55, 4 } },
	{ 0x060000DE, { 59, 7 } },
	{ 0x060000DF, { 66, 7 } },
	{ 0x060000E0, { 73, 7 } },
	{ 0x060000E5, { 80, 5 } },
	{ 0x060000E6, { 85, 8 } },
	{ 0x060000E7, { 93, 8 } },
	{ 0x060000E8, { 101, 8 } },
	{ 0x060000E9, { 109, 8 } },
	{ 0x060000EA, { 117, 8 } },
	{ 0x060000EE, { 125, 1 } },
	{ 0x060000EF, { 126, 3 } },
	{ 0x06000101, { 129, 6 } },
	{ 0x06000195, { 161, 3 } },
	{ 0x06000196, { 164, 2 } },
	{ 0x0600019A, { 166, 4 } },
};
extern const uint32_t g_rgctx_Addressables_LoadAssetAsync_TisTObject_t177DC59CF57C0D53B2F274DC2112DEE7259A23E3_mF2A3B85F3C419864E02D38C4310472170AF36ADB;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t2091F6034A6FCE715F87CA0C486B019754DE15B7;
extern const uint32_t g_rgctx_Addressables_LoadAssetAsync_TisTObject_tBFA2C7423F67CCB7C9C9C8C7E647A0057B55F0EB_mCFC8DB385048C7202921F117F893718F59150340;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t6EC2432F017B331EDC95CC166BB5F8C11AAADF62;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_tD2373F647EE9B0AA6520FC2026DC856E1E2A0209_mFF22F065AF21F82B4D575B6172CF846942363DE9;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t584D0B6E4BA5B34B447925E68D9E104E0FDFF962;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_tC9776890BCD712E103DFF8BCC7477061A08AD7F2_m44E27F5DE374DC420CD27E3E4FFE664E036BA914;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t307948A7DDB879C6A522CE3795B2486E18C99C72;
extern const uint32_t g_rgctx_Action_1_t11858D5761CEE0B850C720DFB0ACFECA027C32A4;
extern const uint32_t g_rgctx_Addressables_LoadAssetsAsync_TisTObject_t46D67E6CC10B2B05E78D1F8ACB6576D081BD240D_m6C54495967A1C484EE84B8E1D616F64425D05FB6;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tB8D91DB36DB9A53D730A7B7302026B523D2DA4B6;
extern const uint32_t g_rgctx_Action_1_tD3910385A8B6580A7025551C8E47ECFA9609BD23;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t12A906795D1F22C873F8A30194F9BE89DCA895D8_m9389F948819866F23FA6EB2BA240D8817739F879;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tB6BF8A000B359543BC9C1A40DA3431F93987FA70;
extern const uint32_t g_rgctx_Action_1_tB0C6FFAAB87C1F73E2375DB6E06AF03F05B16536;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tEB99174DCA18293E591DFCB67294BD0086359065_m97A5CED51925248BF882A27B2B2BA0108D1D34A8;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t78FB4D6C4AE1930E9D0D35509D4810E4D8A76B65;
extern const uint32_t g_rgctx_Action_1_tB3F81023DCC9B53453184F344B8CB4C4B8B90FBF;
extern const uint32_t g_rgctx_Addressables_LoadAssetsAsync_TisTObject_t3C4954BDC1A5A2EE014A3561985BD1E237E0EC82_mF209B71DC2B4F65FD54680084B16487F30F90ACF;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tE1E2127CAD5F4AB351B75D83FA5854ED75AAE8A2;
extern const uint32_t g_rgctx_Action_1_t95BF7CF4FE0B3F323187BA983E033BB67AFF585F;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t2BF75A45D2D36D4865607DA5FC836172D010AF39_mBF9CB632D3BAD39702B847125D65C9D7335754DD;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tCB7004E6722EFD91154AD95C238D8D332CBD9815;
extern const uint32_t g_rgctx_Action_1_t2898EE39E7C0C74FFE3616858015EE8A4E4FC0EE;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t2936A8AF7EF6A40ABCA10852B284ACDD4183B4A5_mB310FAADFA18C72FB64115CF1B8A25E87C317544;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t0674595D97D764A138C5F02A05ADDB624E0C812F;
extern const uint32_t g_rgctx_Action_1_t9EBBDA3F64F3ABB6DB5C9286483893FF6A458841;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t191D76D95E0F660E7F8DE70F39202FAB2061C013_m5FB8FFA35F038F1FA477322978BA68A404D1D890;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tA87B98453C30200D82E386D21C4F1E013425C114;
extern const uint32_t g_rgctx_Action_1_t2EA23868735B2C8BC5BA038EA9D464A2AC00EA83;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t477E393168078F9089C3C0EB372584969A08D09F_mCE6D48DA617116CA434DE12ED78A6022659D22A9;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tF2A60CD3C72BDAE30C921188C5AFB7FF0C4C823E;
extern const uint32_t g_rgctx_Action_1_t9997200FA02E0446655BD9B62EC13898D22F955B;
extern const uint32_t g_rgctx_Addressables_LoadAssetsAsync_TisTObject_t184AE07321097D25AC2CF47744972363BF6F7DAE_m555041BE7ABD018926F29265176BBAB018C9C5F2;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tA5A42F791A2360DCF0BFCCA8DA62CD84C2FE12A7;
extern const uint32_t g_rgctx_Action_1_t1F4C94FEA60084E0F457F488D0C73CA9F58243CC;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tE44BFCA5ED06EA11BE6CA922C606E7D2684CAB42_m27BE6DA496B33CA7F810E4F43FB1DFC62F7B6B3E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tB624CE5547C4C4807BCDE40BD2B17097B19C6FB0;
extern const uint32_t g_rgctx_Action_1_t603C8A88650B578932B65463A38A091A8B29B595;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tAC4FF711D61C8BBFFA0E84331BA8D1933C176DA7_m3F039E287364B83B354AB0754D7378827FDDDE69;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t35C437489DD5915ECFB7DDAF56E0F380078FF8F1;
extern const uint32_t g_rgctx_TObject_t00FDC168632015F31412F5A2E9F58D594B4646DB;
extern const uint32_t g_rgctx_AddressablesImpl_Release_TisTObject_t00FDC168632015F31412F5A2E9F58D594B4646DB_mFE06CDAE8B6DDE001BA002D799022FC896ED979A;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t31F89A5814AD3DD937BDD99AD4FB156E2E68FF4F;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Release_mF53EAB51AB4DEB8AE4F42552D2794CFDED25F6EF;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t31F89A5814AD3DD937BDD99AD4FB156E2E68FF4F;
extern const uint32_t g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tB85E5566418F03D7879AA7C57AB9AF9634C80789_m1946B432DD92759A9D22543B9F4CA7685B689E03;
extern const uint32_t g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tF2F1D02C48102240E54E55BEF24B87E5F67AFE45_mBC4BCF1113B6B6167BD264A2D9BBDAACBA8CAABE;
extern const uint32_t g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tF7A518D0F449B81B7D0CC7BACFC295D3C967A17A_m0B7B19C7B79D2AFC73AABD1D15AD867C79302408;
extern const uint32_t g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_t7EA7AEDEB7A20E1ECF5F7281A4D0440903EF7C93_m80017C248809A4E19457E70276B42F577BB2A992;
extern const uint32_t g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tDB845FE2584EB07D6E83684FD20ECED78BEF8875_m2B853AF7F19E41D34F6EDA8CB4EDC9775CA861AE;
extern const uint32_t g_rgctx_T_tC2910AB26617D6C33D1AE8E697E002AA4D2A6F2E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t601AFD21F40BE3262224D983DF7B9FBFD4AA25AC;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_add_CompletedTypeless_m8FC274E029D8A6A09B3025BB711843F753A6CAD9;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t601AFD21F40BE3262224D983DF7B9FBFD4AA25AC;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetWithChain_TisTObject_t25812EB46FC6CA78E96373AE343B8ECF6BE99066_mC10CE34CA6D6B6754AF29648536DBAE2533C4443;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t21230FEED6CEEAA2582D44D69360D1088882A48A;
extern const uint32_t g_rgctx_AddressablesImpl_TrackHandle_TisTObject_t25812EB46FC6CA78E96373AE343B8ECF6BE99066_m3B6059B77B65F1070D610880DB7A23439DC7B741;
extern const uint32_t g_rgctx_ResourceManager_ProvideResource_TisTObject_t25812EB46FC6CA78E96373AE343B8ECF6BE99066_m28418ED3F9186A20656B7D7B290C7CD6237CEF02;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass78_0_1_tB6BB7E9B29F0DA98831D713000684C371426B729;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass78_0_1__ctor_m1511DD62AB4F9D50E1F6D04AF197B057F6DACA93;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass78_0_1_U3CLoadAssetWithChainU3Eb__0_m9E2BCAFD6A1B868A86F25F26B9738ACC1D6D8189;
extern const uint32_t g_rgctx_Func_2_t4552A6F0F1C35C8AE642D0AE111035B632C18FB8;
extern const uint32_t g_rgctx_Func_2__ctor_m9BA5CE9378F8E3F0FC9730395A369C7719BB7F80;
extern const uint32_t g_rgctx_ResourceManager_CreateChainOperation_TisTObject_tB23896CE6A3BD20CA1337B2D4C3EB83FCC9120E8_mB367B9B7787E6C2B48CA5ED2CF38C9A115A09DE4;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tF54925803D45878BF81F330005037E4047F293D7;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass79_0_1_tE6FC2D5B530EE439B64BC758FA690FBADB82F487;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass79_0_1__ctor_mE73C10472B67DB08A9356B23FB00AE3E0BC25945;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass79_0_1_U3CLoadAssetWithChainU3Eb__0_m8B518AAA45B2CA95C4F9D2DE18AB6FBD4045CED7;
extern const uint32_t g_rgctx_Func_2_t6961B74AD1DB9DF61B78642A1E7E51A636B95232;
extern const uint32_t g_rgctx_Func_2__ctor_mAD52CAD2F6931D6350F99FCB545C4E9B3B7F935F;
extern const uint32_t g_rgctx_ResourceManager_CreateChainOperation_TisTObject_tE63E9FA7B6EE6805E71EEC79B904518AD71C8B53_mF5DAFC874B3C112D113503A84761D1A174E16FE4;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t720B551BDBD0006E30B5C4C5B47E4C7B08B77A80;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetWithChain_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_m02D2D60AB3AF13C57F5EF71431D8D70567FEE1C5;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t5FF1A15052824BD770C59C6D6F7332A4BF2BA968;
extern const uint32_t g_rgctx_AddressablesImpl_TrackHandle_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_m9BE1B86C2836147053D7DAFDEA56809E424F4D29;
extern const uint32_t g_rgctx_TObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363;
extern const uint32_t g_rgctx_ResourceManager_ProvideResource_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_mD4C857293BA8070F74B580B61D151C2FDF1734EC;
extern const uint32_t g_rgctx_TObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperationWithException_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_m30CC3E73B06ED9E4898A6EDE0E8F787BEA752704;
extern const uint32_t g_rgctx_Action_1_t9972FB6408E3964D31917587638AD0D83877E3EC;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsWithChain_TisTObject_t229A8C5CFB730ABC04A02FB31C45C03D38D679E7_mFDACB4E482BB4440B34F1A2123F45A441853061B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t462DBF99749452B1B768C3529D46F2BB2D9AD573;
extern const uint32_t g_rgctx_AddressablesImpl_TrackHandle_TisIList_1_t92EFB6C2ABB70D599B9ECD7862A011DF66FD9D49_m3192C6BA1120B085BF74C0B4863DCBB0A29D28B7;
extern const uint32_t g_rgctx_ResourceManager_ProvideResources_TisTObject_t229A8C5CFB730ABC04A02FB31C45C03D38D679E7_mC994FE197A8FA888B6D99734D87E03C2EA196615;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass88_0_1_t6CD1D32BD8FFCE9993A9C3CCE5638BFE2CDFE5E9;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass88_0_1__ctor_m70D3E5E8BA8E8B6EA18338B4DCBBF06669C0A9E8;
extern const uint32_t g_rgctx_Action_1_tBE92AD202CB3822F11A17177D5FB0BA4104D3DDF;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass88_0_1_U3CLoadAssetsWithChainU3Eb__0_m04720D4F12E06C638EB74E6569A5CAC9CEF00E0F;
extern const uint32_t g_rgctx_Func_2_t56EADA2C4CC536BD5E1811B384B0E905E5EAF73A;
extern const uint32_t g_rgctx_Func_2__ctor_m2D563884E113DD38DDB7CA631CDFC89D5C3050B9;
extern const uint32_t g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t59023A908D5E0AD73BDBFBC75B521B5A85F63A5A_mFE7DE981C84B2D3EEB1C4207EDAB06089A7C9802;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tD41773C7CA0D3D6FA072AB0C314F687BFB3B8089;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass89_0_1_t51956D20D7DCC209FED4CF46FE33BC5C9E3E0D09;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass89_0_1__ctor_mF14C077ABEB1167AFA5F836D53717E55E278E5BB;
extern const uint32_t g_rgctx_Action_1_tEFA7B38998A8A37D61BC1D3B7CE4B9DEF029DE5D;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass89_0_1_U3CLoadAssetsWithChainU3Eb__0_m55C92DBB0000B39563EFEA6220F49DDB841FDAF0;
extern const uint32_t g_rgctx_Func_2_t8843F03F0B085F7BE2B1189840CE4D2F22D702AF;
extern const uint32_t g_rgctx_Func_2__ctor_m4EB9BBD17D77E28C4057C981F2534A3A531A55A3;
extern const uint32_t g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t46E9712EF2E30F08D2A6F6B144782074A611A374_m060CD3D1C7CAC067A6577509BAC0E5430D63F5EB;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t8400E96BB273A54C51D2C91CFCB37AD751568C06;
extern const uint32_t g_rgctx_Action_1_t5BF39666DB0D780734534AFC316033E08F68FBCF;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsWithChain_TisTObject_t1070783765A76781FB11BEC87FBB5E87746365A6_mD15A94BE85642AE977A7965F38476136F1ED914B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t7F90B447768A501AB6B527ED04910688A4E6983D;
extern const uint32_t g_rgctx_AddressablesImpl_TrackHandle_TisIList_1_t7BE8D5B63B873CB9F8B31EFD1F7E6A5842CF9F43_m4FA768DB5B58B4A5C497BC5376D2C68D49939B4D;
extern const uint32_t g_rgctx_TObject_t1070783765A76781FB11BEC87FBB5E87746365A6;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperationWithException_TisIList_1_t7BE8D5B63B873CB9F8B31EFD1F7E6A5842CF9F43_m54CBADA41372519143EB3D7ECCB3CF9CAB56F61A;
extern const uint32_t g_rgctx_IList_1_t7BE8D5B63B873CB9F8B31EFD1F7E6A5842CF9F43;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t1070783765A76781FB11BEC87FBB5E87746365A6_m0EC911ACBBB9B921A80CFC673CE5829C45624B2B;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass91_0_1_t3873F4D81CC935D7C5929AAAD491BB08F466444E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass91_0_1__ctor_m454BC2D93D0EC75A2C08F5435F6E8E72FA653AB3;
extern const uint32_t g_rgctx_Action_1_t9C4124AED933BF36DEAC6109D3252B9578B4C280;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass91_0_1_U3CLoadAssetsWithChainU3Eb__0_m71E2A0F3D90821279C24396635EA3CABBBBFF34F;
extern const uint32_t g_rgctx_Func_2_t3E6597C5D96435782D9620478696B659D85FB5A4;
extern const uint32_t g_rgctx_Func_2__ctor_m3B37A5A40C0DDB8CC2B3FA1E2BAC19D9A62ABD39;
extern const uint32_t g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t406609552E680D14BE0B532C39367988A6BDAF28_mF2053E6F8BC5897086DD382BB733B6054BCA9CCA;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tA61CBA6748897026E2D3EAFB8418D40DDAFF65D4;
extern const uint32_t g_rgctx_Action_1_t198C1E98FDB6A8FBC8A2E49D34C2A936EF8D34D0;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsWithChain_TisTObject_t8DDFF8BAB2663CE90EC5B8D8FA022DFAC43488FD_mAB0D8699F3A213AC931F1C5384E1845210725EAD;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t5D85335786ED31D6478FDDAC788AEBEC885C660F;
extern const uint32_t g_rgctx_AddressablesImpl_TrackHandle_TisIList_1_tB3E004E00A129D6C3E20168829D6E81A81CE6A19_m2F2FA12EE611B90656954DFCCA3AEC1E8D776430;
extern const uint32_t g_rgctx_TObject_t8DDFF8BAB2663CE90EC5B8D8FA022DFAC43488FD;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperationWithException_TisIList_1_tB3E004E00A129D6C3E20168829D6E81A81CE6A19_m0BBA5C2B6D0F14C7522B4350B0C7E29BC3ED0724;
extern const uint32_t g_rgctx_IList_1_tB3E004E00A129D6C3E20168829D6E81A81CE6A19;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t8DDFF8BAB2663CE90EC5B8D8FA022DFAC43488FD_m49E656ADB8C5B7E17D1F9AEAC9CB39DE1B0B2392;
extern const uint32_t g_rgctx_TObject_t75E8B112770CB79459859F4ADF3B25094E02B874;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t5A5F5CF35ED3515FA19142CFE8F795248C01A040;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_mF71F652E8022A65B907B28A06015E09F15718A6C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t5A5F5CF35ED3515FA19142CFE8F795248C01A040;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t815714C45597852648518C57F10991307473C82B;
extern const uint32_t g_rgctx_U3CU3Ec__115_1_t244548CC8F8DCD2C2EAA66B72B1386F4BD00903D;
extern const uint32_t g_rgctx_U3CU3Ec__115_1_t244548CC8F8DCD2C2EAA66B72B1386F4BD00903D;
extern const uint32_t g_rgctx_U3CU3Ec__115_1_U3CAutoReleaseHandleOnTypelessCompletionU3Eb__115_0_mC377C679092AEBC1B633751BB2688A02E513815F;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_add_CompletedTypeless_mD0349B08C063353EDF0412D9869E7C416EFA6E46;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t815714C45597852648518C57F10991307473C82B;
extern const uint32_t g_rgctx_U3CU3Ec__115_1_tAB11FA575BFB174EC317DA600D5C04D391D99C4C;
extern const uint32_t g_rgctx_U3CU3Ec__115_1__ctor_m576B595FCF6CFEFE240CD89C9FEC37B5A1D077EC;
extern const uint32_t g_rgctx_U3CU3Ec__115_1_tAB11FA575BFB174EC317DA600D5C04D391D99C4C;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass78_0_1_t912A50E91B1D8BDA2A963C0758DFEDB5372B1886;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_tBE0362ACE5A257F63CE57B39F2B9EDE1D8946928_m553693CB16EB669E2F44A501A30DF45938D985D8;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t6D0D23069641D8368768DB5D17E627C9B97E5CB0;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass79_0_1_tA1C55CF411C4614D3590BFDFB09E7751AB30B836;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_t916BC20E79E6632ECB30FA61062A600ED07B962E_m11A577F3AA03B3F9C46606FA86649181A3FF1BF6;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t067D1A55CDC5681EED53C29F1328BA92652AB2A2;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass88_0_1_tB8D6EE613557F1B8E96E9F86227B2D767AA356C9;
extern const uint32_t g_rgctx_Action_1_tBE148041DE06B1734725C16AA5EAFCCD7649BEA9;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tD424FEC722DF4F2325F8944800702D604C22CD99_m6FE7DD6F0BDC673D2A07F431A3B8894A60AF1397;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t4FC9F84A403F2D56AF25C8FE3F71933976E20114;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass89_0_1_tB963553CCF16525D7CDF97A663D8AF9CFEADF509;
extern const uint32_t g_rgctx_Action_1_t88147053C6F0578F79F179F3DA6A52893DC54934;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t3956A45282C819BB2EF04DEB61F2A48D71612C8E_mA148078A900EA0036AEB03960A6CE9A75D3ECA92;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tEC02A91F8BD9C4D043B6C3EB04B187DD848EF816;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass91_0_1_tF9743D98FFBCF327AB6D96434E51D640BC51A631;
extern const uint32_t g_rgctx_Action_1_t289DB0097FFA0115585FAD81FFADB7BE0C06604C;
extern const uint32_t g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t366D809791FA1513F433822A7B2E8EC03156AC4A_m4AA344F16BDC48FB77274814D3425695B9311B05;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t34EEB84C3663F1AFA3E7910AAC7E10DC97D445DE;
extern const uint32_t g_rgctx_AssetReferenceT_1_t68138FEB5AE3FA2B84967F0CBC4D7A6508326643;
extern const uint32_t g_rgctx_AssetReferenceT_1_LoadAssetAsync_m482374F3BD1898016A65A00499B1826BBFC1C21E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tD6A6A991F5E65961307D4487931E4693C9495E91;
extern const uint32_t g_rgctx_AssetReference_LoadAssetAsync_TisTObject_t4FA98A908960430AEA27200AAD43192E7F8D3211_m7697163C09CAC6F7D5D9DFA9EB4BB52BE6C41A0F;
extern const uint32_t g_rgctx_TObject_t4FA98A908960430AEA27200AAD43192E7F8D3211;
extern const uint32_t g_rgctx_T_t561DC3B36142EE989A1F5D2A107EA3128493272A;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperation_TisT_t561DC3B36142EE989A1F5D2A107EA3128493272A_mB9B48ECC729342203A18BB9601028C6C2D82A7AD;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tEFAFC46D1BF00984F3625945C35BE54C606EE28C;
extern const uint32_t g_rgctx_AssetReference_LoadAssetAsync_TisTObject_tB183741CC1DE5D3EF4874329425CCE0FAE8043DE_m8558E525EE1E78C7779E5F6E2D15D5B34A6D2D82;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tC892198BADAFECD672A6A61C375B5F30EEE3C6D7;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t2ECE65F58A05C2E7D4B4EFC31F3EB68C4581B88E;
extern const uint32_t g_rgctx_Addressables_LoadAssetAsync_TisTObject_tD2E24E2A28CC2396831C2A3612FFFE0B84801EB5_mBC53A8F23829DF623FCAD5C7DD44F2C00D5AA36E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m880860E2091721AE6005FD9E7D0A81274C4A7146;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t2ECE65F58A05C2E7D4B4EFC31F3EB68C4581B88E;
static const Il2CppRGCTXDefinition s_rgctxValues[170] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Addressables_LoadAssetAsync_TisTObject_t177DC59CF57C0D53B2F274DC2112DEE7259A23E3_mF2A3B85F3C419864E02D38C4310472170AF36ADB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t2091F6034A6FCE715F87CA0C486B019754DE15B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Addressables_LoadAssetAsync_TisTObject_tBFA2C7423F67CCB7C9C9C8C7E647A0057B55F0EB_mCFC8DB385048C7202921F117F893718F59150340 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t6EC2432F017B331EDC95CC166BB5F8C11AAADF62 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_tD2373F647EE9B0AA6520FC2026DC856E1E2A0209_mFF22F065AF21F82B4D575B6172CF846942363DE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t584D0B6E4BA5B34B447925E68D9E104E0FDFF962 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_tC9776890BCD712E103DFF8BCC7477061A08AD7F2_m44E27F5DE374DC420CD27E3E4FFE664E036BA914 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t307948A7DDB879C6A522CE3795B2486E18C99C72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t11858D5761CEE0B850C720DFB0ACFECA027C32A4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Addressables_LoadAssetsAsync_TisTObject_t46D67E6CC10B2B05E78D1F8ACB6576D081BD240D_m6C54495967A1C484EE84B8E1D616F64425D05FB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tB8D91DB36DB9A53D730A7B7302026B523D2DA4B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tD3910385A8B6580A7025551C8E47ECFA9609BD23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t12A906795D1F22C873F8A30194F9BE89DCA895D8_m9389F948819866F23FA6EB2BA240D8817739F879 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tB6BF8A000B359543BC9C1A40DA3431F93987FA70 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tB0C6FFAAB87C1F73E2375DB6E06AF03F05B16536 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tEB99174DCA18293E591DFCB67294BD0086359065_m97A5CED51925248BF882A27B2B2BA0108D1D34A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t78FB4D6C4AE1930E9D0D35509D4810E4D8A76B65 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tB3F81023DCC9B53453184F344B8CB4C4B8B90FBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Addressables_LoadAssetsAsync_TisTObject_t3C4954BDC1A5A2EE014A3561985BD1E237E0EC82_mF209B71DC2B4F65FD54680084B16487F30F90ACF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tE1E2127CAD5F4AB351B75D83FA5854ED75AAE8A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t95BF7CF4FE0B3F323187BA983E033BB67AFF585F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t2BF75A45D2D36D4865607DA5FC836172D010AF39_mBF9CB632D3BAD39702B847125D65C9D7335754DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tCB7004E6722EFD91154AD95C238D8D332CBD9815 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t2898EE39E7C0C74FFE3616858015EE8A4E4FC0EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t2936A8AF7EF6A40ABCA10852B284ACDD4183B4A5_mB310FAADFA18C72FB64115CF1B8A25E87C317544 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t0674595D97D764A138C5F02A05ADDB624E0C812F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t9EBBDA3F64F3ABB6DB5C9286483893FF6A458841 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t191D76D95E0F660E7F8DE70F39202FAB2061C013_m5FB8FFA35F038F1FA477322978BA68A404D1D890 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tA87B98453C30200D82E386D21C4F1E013425C114 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t2EA23868735B2C8BC5BA038EA9D464A2AC00EA83 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t477E393168078F9089C3C0EB372584969A08D09F_mCE6D48DA617116CA434DE12ED78A6022659D22A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tF2A60CD3C72BDAE30C921188C5AFB7FF0C4C823E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t9997200FA02E0446655BD9B62EC13898D22F955B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Addressables_LoadAssetsAsync_TisTObject_t184AE07321097D25AC2CF47744972363BF6F7DAE_m555041BE7ABD018926F29265176BBAB018C9C5F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tA5A42F791A2360DCF0BFCCA8DA62CD84C2FE12A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t1F4C94FEA60084E0F457F488D0C73CA9F58243CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tE44BFCA5ED06EA11BE6CA922C606E7D2684CAB42_m27BE6DA496B33CA7F810E4F43FB1DFC62F7B6B3E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tB624CE5547C4C4807BCDE40BD2B17097B19C6FB0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t603C8A88650B578932B65463A38A091A8B29B595 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tAC4FF711D61C8BBFFA0E84331BA8D1933C176DA7_m3F039E287364B83B354AB0754D7378827FDDDE69 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t35C437489DD5915ECFB7DDAF56E0F380078FF8F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t00FDC168632015F31412F5A2E9F58D594B4646DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_Release_TisTObject_t00FDC168632015F31412F5A2E9F58D594B4646DB_mFE06CDAE8B6DDE001BA002D799022FC896ED979A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t31F89A5814AD3DD937BDD99AD4FB156E2E68FF4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Release_mF53EAB51AB4DEB8AE4F42552D2794CFDED25F6EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t31F89A5814AD3DD937BDD99AD4FB156E2E68FF4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tB85E5566418F03D7879AA7C57AB9AF9634C80789_m1946B432DD92759A9D22543B9F4CA7685B689E03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tF2F1D02C48102240E54E55BEF24B87E5F67AFE45_mBC4BCF1113B6B6167BD264A2D9BBDAACBA8CAABE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tF7A518D0F449B81B7D0CC7BACFC295D3C967A17A_m0B7B19C7B79D2AFC73AABD1D15AD867C79302408 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_t7EA7AEDEB7A20E1ECF5F7281A4D0440903EF7C93_m80017C248809A4E19457E70276B42F577BB2A992 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_CreateCatalogLocationWithHashDependencies_TisT_tDB845FE2584EB07D6E83684FD20ECED78BEF8875_m2B853AF7F19E41D34F6EDA8CB4EDC9775CA861AE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tC2910AB26617D6C33D1AE8E697E002AA4D2A6F2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t601AFD21F40BE3262224D983DF7B9FBFD4AA25AC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_add_CompletedTypeless_m8FC274E029D8A6A09B3025BB711843F753A6CAD9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t601AFD21F40BE3262224D983DF7B9FBFD4AA25AC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetWithChain_TisTObject_t25812EB46FC6CA78E96373AE343B8ECF6BE99066_mC10CE34CA6D6B6754AF29648536DBAE2533C4443 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t21230FEED6CEEAA2582D44D69360D1088882A48A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_TrackHandle_TisTObject_t25812EB46FC6CA78E96373AE343B8ECF6BE99066_m3B6059B77B65F1070D610880DB7A23439DC7B741 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_ProvideResource_TisTObject_t25812EB46FC6CA78E96373AE343B8ECF6BE99066_m28418ED3F9186A20656B7D7B290C7CD6237CEF02 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass78_0_1_tB6BB7E9B29F0DA98831D713000684C371426B729 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass78_0_1__ctor_m1511DD62AB4F9D50E1F6D04AF197B057F6DACA93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass78_0_1_U3CLoadAssetWithChainU3Eb__0_m9E2BCAFD6A1B868A86F25F26B9738ACC1D6D8189 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t4552A6F0F1C35C8AE642D0AE111035B632C18FB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m9BA5CE9378F8E3F0FC9730395A369C7719BB7F80 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateChainOperation_TisTObject_tB23896CE6A3BD20CA1337B2D4C3EB83FCC9120E8_mB367B9B7787E6C2B48CA5ED2CF38C9A115A09DE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tF54925803D45878BF81F330005037E4047F293D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass79_0_1_tE6FC2D5B530EE439B64BC758FA690FBADB82F487 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass79_0_1__ctor_mE73C10472B67DB08A9356B23FB00AE3E0BC25945 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass79_0_1_U3CLoadAssetWithChainU3Eb__0_m8B518AAA45B2CA95C4F9D2DE18AB6FBD4045CED7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t6961B74AD1DB9DF61B78642A1E7E51A636B95232 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_mAD52CAD2F6931D6350F99FCB545C4E9B3B7F935F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateChainOperation_TisTObject_tE63E9FA7B6EE6805E71EEC79B904518AD71C8B53_mF5DAFC874B3C112D113503A84761D1A174E16FE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t720B551BDBD0006E30B5C4C5B47E4C7B08B77A80 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetWithChain_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_m02D2D60AB3AF13C57F5EF71431D8D70567FEE1C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t5FF1A15052824BD770C59C6D6F7332A4BF2BA968 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_TrackHandle_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_m9BE1B86C2836147053D7DAFDEA56809E424F4D29 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_ProvideResource_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_mD4C857293BA8070F74B580B61D151C2FDF1734EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperationWithException_TisTObject_t01F8B1908B20A8D36EEA7B3B1F927685B3E7F363_m30CC3E73B06ED9E4898A6EDE0E8F787BEA752704 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t9972FB6408E3964D31917587638AD0D83877E3EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsWithChain_TisTObject_t229A8C5CFB730ABC04A02FB31C45C03D38D679E7_mFDACB4E482BB4440B34F1A2123F45A441853061B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t462DBF99749452B1B768C3529D46F2BB2D9AD573 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_TrackHandle_TisIList_1_t92EFB6C2ABB70D599B9ECD7862A011DF66FD9D49_m3192C6BA1120B085BF74C0B4863DCBB0A29D28B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_ProvideResources_TisTObject_t229A8C5CFB730ABC04A02FB31C45C03D38D679E7_mC994FE197A8FA888B6D99734D87E03C2EA196615 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass88_0_1_t6CD1D32BD8FFCE9993A9C3CCE5638BFE2CDFE5E9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass88_0_1__ctor_m70D3E5E8BA8E8B6EA18338B4DCBBF06669C0A9E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tBE92AD202CB3822F11A17177D5FB0BA4104D3DDF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass88_0_1_U3CLoadAssetsWithChainU3Eb__0_m04720D4F12E06C638EB74E6569A5CAC9CEF00E0F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t56EADA2C4CC536BD5E1811B384B0E905E5EAF73A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m2D563884E113DD38DDB7CA631CDFC89D5C3050B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t59023A908D5E0AD73BDBFBC75B521B5A85F63A5A_mFE7DE981C84B2D3EEB1C4207EDAB06089A7C9802 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tD41773C7CA0D3D6FA072AB0C314F687BFB3B8089 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass89_0_1_t51956D20D7DCC209FED4CF46FE33BC5C9E3E0D09 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass89_0_1__ctor_mF14C077ABEB1167AFA5F836D53717E55E278E5BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tEFA7B38998A8A37D61BC1D3B7CE4B9DEF029DE5D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass89_0_1_U3CLoadAssetsWithChainU3Eb__0_m55C92DBB0000B39563EFEA6220F49DDB841FDAF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t8843F03F0B085F7BE2B1189840CE4D2F22D702AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m4EB9BBD17D77E28C4057C981F2534A3A531A55A3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t46E9712EF2E30F08D2A6F6B144782074A611A374_m060CD3D1C7CAC067A6577509BAC0E5430D63F5EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t8400E96BB273A54C51D2C91CFCB37AD751568C06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t5BF39666DB0D780734534AFC316033E08F68FBCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsWithChain_TisTObject_t1070783765A76781FB11BEC87FBB5E87746365A6_mD15A94BE85642AE977A7965F38476136F1ED914B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t7F90B447768A501AB6B527ED04910688A4E6983D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_TrackHandle_TisIList_1_t7BE8D5B63B873CB9F8B31EFD1F7E6A5842CF9F43_m4FA768DB5B58B4A5C497BC5376D2C68D49939B4D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_t1070783765A76781FB11BEC87FBB5E87746365A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperationWithException_TisIList_1_t7BE8D5B63B873CB9F8B31EFD1F7E6A5842CF9F43_m54CBADA41372519143EB3D7ECCB3CF9CAB56F61A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t7BE8D5B63B873CB9F8B31EFD1F7E6A5842CF9F43 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t1070783765A76781FB11BEC87FBB5E87746365A6_m0EC911ACBBB9B921A80CFC673CE5829C45624B2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass91_0_1_t3873F4D81CC935D7C5929AAAD491BB08F466444E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass91_0_1__ctor_m454BC2D93D0EC75A2C08F5435F6E8E72FA653AB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t9C4124AED933BF36DEAC6109D3252B9578B4C280 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass91_0_1_U3CLoadAssetsWithChainU3Eb__0_m71E2A0F3D90821279C24396635EA3CABBBBFF34F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t3E6597C5D96435782D9620478696B659D85FB5A4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m3B37A5A40C0DDB8CC2B3FA1E2BAC19D9A62ABD39 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t406609552E680D14BE0B532C39367988A6BDAF28_mF2053E6F8BC5897086DD382BB733B6054BCA9CCA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tA61CBA6748897026E2D3EAFB8418D40DDAFF65D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t198C1E98FDB6A8FBC8A2E49D34C2A936EF8D34D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsWithChain_TisTObject_t8DDFF8BAB2663CE90EC5B8D8FA022DFAC43488FD_mAB0D8699F3A213AC931F1C5384E1845210725EAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t5D85335786ED31D6478FDDAC788AEBEC885C660F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_TrackHandle_TisIList_1_tB3E004E00A129D6C3E20168829D6E81A81CE6A19_m2F2FA12EE611B90656954DFCCA3AEC1E8D776430 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_t8DDFF8BAB2663CE90EC5B8D8FA022DFAC43488FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperationWithException_TisIList_1_tB3E004E00A129D6C3E20168829D6E81A81CE6A19_m0BBA5C2B6D0F14C7522B4350B0C7E29BC3ED0724 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_tB3E004E00A129D6C3E20168829D6E81A81CE6A19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t8DDFF8BAB2663CE90EC5B8D8FA022DFAC43488FD_m49E656ADB8C5B7E17D1F9AEAC9CB39DE1B0B2392 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t75E8B112770CB79459859F4ADF3B25094E02B874 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t5A5F5CF35ED3515FA19142CFE8F795248C01A040 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_mF71F652E8022A65B907B28A06015E09F15718A6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t5A5F5CF35ED3515FA19142CFE8F795248C01A040 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t815714C45597852648518C57F10991307473C82B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__115_1_t244548CC8F8DCD2C2EAA66B72B1386F4BD00903D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__115_1_t244548CC8F8DCD2C2EAA66B72B1386F4BD00903D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__115_1_U3CAutoReleaseHandleOnTypelessCompletionU3Eb__115_0_mC377C679092AEBC1B633751BB2688A02E513815F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_add_CompletedTypeless_mD0349B08C063353EDF0412D9869E7C416EFA6E46 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t815714C45597852648518C57F10991307473C82B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__115_1_tAB11FA575BFB174EC317DA600D5C04D391D99C4C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__115_1__ctor_m576B595FCF6CFEFE240CD89C9FEC37B5A1D077EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__115_1_tAB11FA575BFB174EC317DA600D5C04D391D99C4C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass78_0_1_t912A50E91B1D8BDA2A963C0758DFEDB5372B1886 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_tBE0362ACE5A257F63CE57B39F2B9EDE1D8946928_m553693CB16EB669E2F44A501A30DF45938D985D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t6D0D23069641D8368768DB5D17E627C9B97E5CB0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass79_0_1_tA1C55CF411C4614D3590BFDFB09E7751AB30B836 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetAsync_TisTObject_t916BC20E79E6632ECB30FA61062A600ED07B962E_m11A577F3AA03B3F9C46606FA86649181A3FF1BF6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t067D1A55CDC5681EED53C29F1328BA92652AB2A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass88_0_1_tB8D6EE613557F1B8E96E9F86227B2D767AA356C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tBE148041DE06B1734725C16AA5EAFCCD7649BEA9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_tD424FEC722DF4F2325F8944800702D604C22CD99_m6FE7DD6F0BDC673D2A07F431A3B8894A60AF1397 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t4FC9F84A403F2D56AF25C8FE3F71933976E20114 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass89_0_1_tB963553CCF16525D7CDF97A663D8AF9CFEADF509 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t88147053C6F0578F79F179F3DA6A52893DC54934 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t3956A45282C819BB2EF04DEB61F2A48D71612C8E_mA148078A900EA0036AEB03960A6CE9A75D3ECA92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tEC02A91F8BD9C4D043B6C3EB04B187DD848EF816 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass91_0_1_tF9743D98FFBCF327AB6D96434E51D640BC51A631 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t289DB0097FFA0115585FAD81FFADB7BE0C06604C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AddressablesImpl_LoadAssetsAsync_TisTObject_t366D809791FA1513F433822A7B2E8EC03156AC4A_m4AA344F16BDC48FB77274814D3425695B9311B05 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t34EEB84C3663F1AFA3E7910AAC7E10DC97D445DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AssetReferenceT_1_t68138FEB5AE3FA2B84967F0CBC4D7A6508326643 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AssetReferenceT_1_LoadAssetAsync_m482374F3BD1898016A65A00499B1826BBFC1C21E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tD6A6A991F5E65961307D4487931E4693C9495E91 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AssetReference_LoadAssetAsync_TisTObject_t4FA98A908960430AEA27200AAD43192E7F8D3211_m7697163C09CAC6F7D5D9DFA9EB4BB52BE6C41A0F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_t4FA98A908960430AEA27200AAD43192E7F8D3211 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t561DC3B36142EE989A1F5D2A107EA3128493272A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperation_TisT_t561DC3B36142EE989A1F5D2A107EA3128493272A_mB9B48ECC729342203A18BB9601028C6C2D82A7AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tEFAFC46D1BF00984F3625945C35BE54C606EE28C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AssetReference_LoadAssetAsync_TisTObject_tB183741CC1DE5D3EF4874329425CCE0FAE8043DE_m8558E525EE1E78C7779E5F6E2D15D5B34A6D2D82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tC892198BADAFECD672A6A61C375B5F30EEE3C6D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t2ECE65F58A05C2E7D4B4EFC31F3EB68C4581B88E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Addressables_LoadAssetAsync_TisTObject_tD2E24E2A28CC2396831C2A3612FFFE0B84801EB5_mBC53A8F23829DF623FCAD5C7DD44F2C00D5AA36E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m880860E2091721AE6005FD9E7D0A81274C4A7146 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t2ECE65F58A05C2E7D4B4EFC31F3EB68C4581B88E },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Addressables_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Addressables_CodeGenModule = 
{
	"Unity.Addressables.dll",
	654,
	s_methodPointers,
	1,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	47,
	s_rgctxIndices,
	170,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
