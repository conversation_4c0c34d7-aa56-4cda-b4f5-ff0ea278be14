﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ConsentDebugSettings__ctor_m55A234F30D3B678E063DFBB6B9D838DC5768744D (void);
extern void ConsentRequestParameters__ctor_mB689D636424571F47A023696E0916A769EE1EC5C (void);
extern void FormError__ctor_m74D2F9BD01E242B45657155A11219192DF02A8A7 (void);
extern void FormError_set_ErrorCode_mCC38EFF4FB071A3DA5B2E0D0F77A527E1D5B5426 (void);
extern void FormError_set_Message_mA6F54ADCA0D8A37C7CBD0A68B213AAE392BA8F1F (void);
static Il2CppMethodPointer s_methodPointers[5] = 
{
	ConsentDebugSettings__ctor_m55A234F30D3B678E063DFBB6B9D838DC5768744D,
	ConsentRequestParameters__ctor_mB689D636424571F47A023696E0916A769EE1EC5C,
	FormError__ctor_m74D2F9BD01E242B45657155A11219192DF02A8A7,
	FormError_set_ErrorCode_mCC38EFF4FB071A3DA5B2E0D0F77A527E1D5B5426,
	FormError_set_Message_mA6F54ADCA0D8A37C7CBD0A68B213AAE392BA8F1F,
};
static const int32_t s_InvokerIndices[5] = 
{
	7120,
	7120,
	3140,
	5774,
	5806,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Ump_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Ump_CodeGenModule = 
{
	"GoogleMobileAds.Ump.dll",
	5,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
