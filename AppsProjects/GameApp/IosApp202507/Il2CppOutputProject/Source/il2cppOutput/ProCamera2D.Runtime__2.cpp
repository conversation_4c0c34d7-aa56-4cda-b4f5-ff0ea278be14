﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A;
struct Action_1_t17E52B12DC24FA6C9DD52F87043C85BEA889BB81;
struct Func_2_tA45088AF622CA589F4CD8430D3EEDFBAE25A5C20;
struct Func_2_tA0C0D332A7F1664641151BC0812CE939DF34EC70;
struct Func_2_t31457D5C50F287F0C63022AE444AE11AEA6EDC2A;
struct Func_2_tF493F9AA6A1730EF679F44523CE514D11732CB74;
struct Func_2_t7F9E68047EDFD5F73F937CD36779BC974D576235;
struct Func_2_t04EF9EE3E7B9B89A6B31ECF5CE00D5C5A3A0F9F5;
struct Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC;
struct Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812;
struct Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D;
struct IList_1_t0DF1E5F56EE58E1A7F1FE26A676FC9FBF4D52A07;
struct List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57;
struct List_1_tA3C7FC5F5E0B4B4569A099562D024EC820B7A736;
struct List_1_tD57C9D53046AC3BB8C09D4663F4EBFC90DE5DD3D;
struct List_1_tAE8C17631329643D4DD91E2AFC733129518F1658;
struct List_1_t22C8706215DB8F66A4ECB4526AE9EACCDDD09F13;
struct List_1_t846754A72A8D0F5018C71915DBB1733C81EA768A;
struct List_1_t8A4448F486334EEE52C4B80E9DCFEACC9A34E2A5;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B;
struct CameraTargetU5BU5D_tFA937EF2A5B95B91D79EFC517BAEB019EA2B6BAA;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
struct IPositionDeltaChanger_t93E4993292F0F2ADE7D703936B556C88E6127E6C;
struct IPositionOverrider_tC9347D4AB08FFEA490CBFCEC5D56E2F50D5C9D48;
struct IPostMover_t6A910DC888FF7D754AC25BE3196A996D4C2901E9;
struct IPreMover_tD3CA31343D6CC59AA2A2B96C561699059954B25D;
struct ISizeDeltaChanger_tA9186D1A6CE365CC4424F10913923FC7FD0AD93B;
struct ISizeOverrider_tA40C62D96A91BEE41F0FC5F0E36E4311D4D0A826;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
struct ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct Version_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct WaitForFixedUpdate_t86F5BC0B6A668AEF2903DE9ADB52062E457BD1B7;
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;
struct U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3;
struct U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B;
struct U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E;
struct U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913;
struct U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B;
struct U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3;

IL2CPP_EXTERN_C RuntimeClass* IList_1_t0DF1E5F56EE58E1A7F1FE26A676FC9FBF4D52A07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IPositionDeltaChanger_t93E4993292F0F2ADE7D703936B556C88E6127E6C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IPositionOverrider_tC9347D4AB08FFEA490CBFCEC5D56E2F50D5C9D48_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IPostMover_t6A910DC888FF7D754AC25BE3196A996D4C2901E9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IPreMover_tD3CA31343D6CC59AA2A2B96C561699059954B25D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ISizeDeltaChanger_tA9186D1A6CE365CC4424F10913923FC7FD0AD93B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ISizeOverrider_tA40C62D96A91BEE41F0FC5F0E36E4311D4D0A826_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Remove_mF2501D49FBA2C626A44219B01C945D53CCA6536F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_Reset_mCC25D8F322985E1FF8D8B0211383D93A0B9951DE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_Reset_m4C8D8FB13B284CA12B2268B5E6DFEF45FFBA299E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_Reset_mB6EB8C8AF011198498FF41032C55A4FC25ECB51E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_Reset_m8F61EFF21AD9780E02E38B110807DA2770E921D7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_Reset_mA0B540206D18E1C21A1314421A16B808A4B8B0D0_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57  : public RuntimeObject
{
	CameraTargetU5BU5D_tFA937EF2A5B95B91D79EFC517BAEB019EA2B6BAA* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_tDD5FCF744FE947A9AF272045E849EA3AF8C97784  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D  : public RuntimeObject
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
};
struct U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3  : public RuntimeObject
{
};
struct U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* ___cameraTarget;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ___U3CU3E4__this;
	float ___duration;
	float ___influenceH;
	float ___influenceV;
	bool ___removeIfZeroInfluence;
	float ___U3CstartInfluenceHU3E5__2;
	float ___U3CstartInfluenceVU3E5__3;
	float ___U3CtU3E5__4;
};
struct U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___durations;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ___U3CU3E4__this;
	RuntimeObject* ___influences;
	int32_t ___U3CcountU3E5__2;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D3196_t4858B6F0A198A060747ADF784391FB3E3EFA29D7 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D3196_t4858B6F0A198A060747ADF784391FB3E3EFA29D7__padding[3196];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D3321_tFB7EC4AD37C5A8DAA45F9C3370D66703CD1B7BA2 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D3321_tFB7EC4AD37C5A8DAA45F9C3370D66703CD1B7BA2__padding[3321];
	};
};
#pragma pack(pop, tp)
struct Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE 
{
	bool ___hasValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___value;
};
struct CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4  : public RuntimeObject
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___TargetTransform;
	float ___TargetInfluenceH;
	float ___TargetInfluenceV;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___TargetOffset;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____targetPosition;
};
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
	intptr_t ___m_Ptr;
};
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B_marshaled_pinvoke : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
};
struct Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B_marshaled_com : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
	intptr_t ___m_Ptr;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct EaseType_t1FC67ECE18F0BCF3C9D072EF542517CD5E7DFB70 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct MovementAxis_t946C7D8B5D37F946995D2E084A44DEB1C5B1311B 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct UpdateType_tC78D6C16E0565CC7C393A050CF169983047B10D7 
{
	int32_t ___value__;
};
struct U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	float ___duration;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ___U3CU3E4__this;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___influence;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ___U3CU3E4__this;
	float ___duration;
	float ___finalFOV;
	int32_t ___easeType;
	float ___U3CstartFOVU3E5__2;
	float ___U3CnewFOVU3E5__3;
	float ___U3CtU3E5__4;
};
struct U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	RuntimeObject* ___U3CU3E2__current;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ___U3CU3E4__this;
	float ___duration;
	float ___finalSize;
	int32_t ___easeType;
	float ___U3CstartSizeU3E5__2;
	float ___U3CnewSizeU3E5__3;
	float ___U3CtU3E5__4;
};
struct Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A  : public MulticastDelegate_t
{
};
struct Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC  : public MulticastDelegate_t
{
};
struct Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D  : public MulticastDelegate_t
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57* ___CameraTargets;
	bool ___CenterTargetOnStart;
	int32_t ___Axis;
	int32_t ___UpdateType;
	bool ___FollowHorizontal;
	float ___HorizontalFollowSmoothness;
	bool ___FollowVertical;
	float ___VerticalFollowSmoothness;
	float ___OffsetX;
	float ___OffsetY;
	bool ___IsRelativeOffset;
	bool ___ZoomWithFOV;
	bool ___IgnoreTimeScale;
	float ____cameraTargetHorizontalPositionSmoothed;
	float ____cameraTargetVerticalPositionSmoothed;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CStartScreenSizeInWorldCoordinatesU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CScreenSizeInWorldCoordinatesU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CPreviousTargetsMidPointU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CTargetsMidPointU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CCameraTargetPositionU3Ek__BackingField;
	float ___U3CDeltaTimeU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___U3CParentPositionU3Ek__BackingField;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____influencesSum;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___PreMoveUpdate;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___PostMoveUpdate;
	Action_1_t17E52B12DC24FA6C9DD52F87043C85BEA889BB81* ___OnCameraResize;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___OnUpdateScreenSizeFinished;
	Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* ___OnDollyZoomFinished;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnReset;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___ExclusiveTargetPosition;
	int32_t ___CurrentZoomTriggerID;
	bool ___IsCameraPositionLeftBounded;
	bool ___IsCameraPositionRightBounded;
	bool ___IsCameraPositionTopBounded;
	bool ___IsCameraPositionBottomBounded;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___GameCamera;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3H;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3V;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___Vector3D;
	Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812* ___VectorHV;
	Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* ___VectorHVD;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____updateScreenSizeCoroutine;
	Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* ____dollyZoomRoutine;
	List_1_t77B94703E05C519A9010DD0614F757F974E1CD8B* ____influences;
	float ____originalCameraDepthSign;
	float ____previousCameraTargetHorizontalPositionSmoothed;
	float ____previousCameraTargetVerticalPositionSmoothed;
	int32_t ____previousScreenWidth;
	int32_t ____previousScreenHeight;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____previousCameraPosition;
	WaitForFixedUpdate_t86F5BC0B6A668AEF2903DE9ADB52062E457BD1B7* ____waitForFixedUpdate;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transform;
	List_1_t22C8706215DB8F66A4ECB4526AE9EACCDDD09F13* ____preMovers;
	List_1_tA3C7FC5F5E0B4B4569A099562D024EC820B7A736* ____positionDeltaChangers;
	List_1_tD57C9D53046AC3BB8C09D4663F4EBFC90DE5DD3D* ____positionOverriders;
	List_1_t846754A72A8D0F5018C71915DBB1733C81EA768A* ____sizeDeltaChangers;
	List_1_t8A4448F486334EEE52C4B80E9DCFEACC9A34E2A5* ____sizeOverriders;
	List_1_tAE8C17631329643D4DD91E2AFC733129518F1658* ____postMovers;
};
struct List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57_StaticFields
{
	CameraTargetU5BU5D_tFA937EF2A5B95B91D79EFC517BAEB019EA2B6BAA* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_tDD5FCF744FE947A9AF272045E849EA3AF8C97784_StaticFields
{
	__StaticArrayInitTypeSizeU3D3321_tFB7EC4AD37C5A8DAA45F9C3370D66703CD1B7BA2 ___39485295EDBEB4E0D4FA6502B5DDB7B801C89F833901D0923D8D16C00F9C1C2F;
	__StaticArrayInitTypeSizeU3D3196_t4858B6F0A198A060747ADF784391FB3E3EFA29D7 ___79A4B6FCCAC766016AFD580EDFF30A5713B3C27B922BB171D31176EC50820421;
};
struct U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_StaticFields
{
	U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* ___U3CU3E9;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_0;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_1;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_2;
	Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812* ___U3CU3E9__131_3;
	Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* ___U3CU3E9__131_4;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_5;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_6;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_7;
	Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812* ___U3CU3E9__131_8;
	Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* ___U3CU3E9__131_9;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_10;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_11;
	Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* ___U3CU3E9__131_12;
	Func_3_t7CCD158087A7018AD07681FC1E6CEB8699731812* ___U3CU3E9__131_13;
	Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* ___U3CU3E9__131_14;
	Func_2_tF493F9AA6A1730EF679F44523CE514D11732CB74* ___U3CU3E9__142_0;
	Func_2_tA45088AF622CA589F4CD8430D3EEDFBAE25A5C20* ___U3CU3E9__145_0;
	Func_2_tA0C0D332A7F1664641151BC0812CE939DF34EC70* ___U3CU3E9__148_0;
	Func_2_t7F9E68047EDFD5F73F937CD36779BC974D576235* ___U3CU3E9__151_0;
	Func_2_t04EF9EE3E7B9B89A6B31ECF5CE00D5C5A3A0F9F5* ___U3CU3E9__154_0;
	Func_2_t31457D5C50F287F0C63022AE444AE11AEA6EDC2A* ___U3CU3E9__157_0;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
struct ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A_StaticFields
{
	Version_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7* ___Version;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* ____instance;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C  : public RuntimeArray
{
	ALIGN_FIELD (8) float m_Items[1];

	inline float GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline float* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, float value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline float GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline float* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, float value)
	{
		m_Items[index] = value;
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool List_1_Remove_m4DFA48F4CEB9169601E75FC28517C5C06EFA5AD7_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Func_2_Invoke_m1512A344733CFB3C2D59C468C852A374239D3B52_gshared_inline (Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_arg, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Func_4_Invoke_m6A779A1925A6EF41D5DF3ADF5E390CF39798250B_gshared_inline (Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* __this, float ___0_arg1, float ___1_arg2, float ___2_arg3, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_gshared_inline (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* __this, float ___0_obj, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_mD315A5D40510C4F060D0906D20F1573E4F17D902 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1_inline (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31 (float ___0_start, float ___1_end, float ___2_value, int32_t ___3_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D* ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, const RuntimeMethod* method) ;
inline bool List_1_Remove_mF2501D49FBA2C626A44219B01C945D53CCA6536F (List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57* __this, CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* ___0_item, const RuntimeMethod* method)
{
	return ((  bool (*) (List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57*, CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4*, const RuntimeMethod*))List_1_Remove_m4DFA48F4CEB9169601E75FC28517C5C06EFA5AD7_gshared)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2D_ApplyInfluence_m43513A9BCD9D112787F90B9B4A73FC54B9646139 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_influence, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ProCamera2D_ApplyInfluenceTimedRoutine_m339B6445B9B97C5FDB26978174E1EA7FD86E7BF5 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_influence, float ___1_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812 (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, RuntimeObject* ___0_routine, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Camera_get_fieldOfView_m9A93F17BBF89F496AE231C21817AFD1C1E833FBB (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_fieldOfView_m5AA9EED4D1603A1DEDBF883D9C42814B2BDEB777 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
inline float Func_2_Invoke_m1512A344733CFB3C2D59C468C852A374239D3B52_inline (Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_arg, const RuntimeMethod* method)
{
	return ((  float (*) (Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2, const RuntimeMethod*))Func_2_Invoke_m1512A344733CFB3C2D59C468C852A374239D3B52_gshared_inline)(__this, ___0_arg, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4_inline (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ProCamera2D_GetCameraDistanceForFOV_m2FCA916D4D6C57A1A44274D78A2277AC569C4703 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, float ___0_fov, float ___1_cameraHeight, const RuntimeMethod* method) ;
inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Func_4_Invoke_m6A779A1925A6EF41D5DF3ADF5E390CF39798250B_inline (Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* __this, float ___0_arg1, float ___1_arg2, float ___2_arg3, const RuntimeMethod* method)
{
	return ((  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 (*) (Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D*, float, float, float, const RuntimeMethod*))Func_4_Invoke_m6A779A1925A6EF41D5DF3ADF5E390CF39798250B_gshared_inline)(__this, ___0_arg1, ___1_arg2, ___2_arg3, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
inline void Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_inline (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* __this, float ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A*, float, const RuntimeMethod*))Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_gshared_inline)(__this, ___0_obj, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProCamera2D_SetScreenSize_m88156B34D588B0975BDF321FDE8387FF9E6E7072 (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, float ___0_newSize, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m2CACA806FBBF16DF2FB8F6A8828F7263B626D1BA (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* L_0 = (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3*)il2cpp_codegen_object_new(U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_mD315A5D40510C4F060D0906D20F1573E4F17D902(L_0, NULL);
		((U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_mD315A5D40510C4F060D0906D20F1573E4F17D902 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_0_m98C4A6EA221600DEA0EB0BFC56FC39E30FC645E2 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_1_m505F97B3E36295C2F8D91FF7B2A489957F716D95 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___y;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_2_m94DED5B08884FD9DAA54F16106366464B8AE9C37 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___z;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_3_m34B944A7A62CAA4608F1280C21047F380C264A2C (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, float ___0_h, float ___1_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_h;
		float L_1 = ___1_v;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		memset((&L_2), 0, sizeof(L_2));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_2), L_0, L_1, (0.0f), NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_4_mAFBB04EA8528969D31E755801C9AB8A9C111B315 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, float ___0_h, float ___1_v, float ___2_d, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_h;
		float L_1 = ___1_v;
		float L_2 = ___2_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_5_mBC6CC32C88B641B37D48B1859E1C266DF02460C4 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_6_m61821683F61E356BF8C6C269651CCA497DE6B87C (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___z;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_7_m0052A98D089C5B3875D03CC5ECE82BD37AB87D20 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___y;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_8_m98512A0D74D77EC23469A7FC6966E3B7E90BAAE9 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, float ___0_h, float ___1_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_h;
		float L_1 = ___1_v;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		memset((&L_2), 0, sizeof(L_2));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_2), L_0, (0.0f), L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_9_m709F5BEF2F0A9AFA5AB3C247D96932B28258D2AB (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, float ___0_h, float ___1_v, float ___2_d, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_h;
		float L_1 = ___2_d;
		float L_2 = ___1_v;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_10_m96598B4C7DC04B826B3A38D8C19950D4B69249E0 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___z;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_11_m8CA2966F66F4A767B38164CE452742B4A4229D4D (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___y;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_12_mDDD87D483B6B6AFFDC53E0835347D9158751CF5A (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_13_mDEF3D7670DF97A8BA3604D9165B65C616E19B4F4 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, float ___0_h, float ___1_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___1_v;
		float L_1 = ___0_h;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		memset((&L_2), 0, sizeof(L_2));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_2), (0.0f), L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_14_mDEF0C08680FAEB5187CDE8257960EED9CF57163C (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, float ___0_h, float ___1_v, float ___2_d, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_d;
		float L_1 = ___1_v;
		float L_2 = ___0_h;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CSortPreMoversU3Eb__142_0_mD9EEE20A043C09922C819C512AEFC688D2BAC5EA (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, RuntimeObject* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPreMover_tD3CA31343D6CC59AA2A2B96C561699059954B25D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_a;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(1, IPreMover_tD3CA31343D6CC59AA2A2B96C561699059954B25D_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CSortPositionDeltaChangersU3Eb__145_0_mC93C34687515ABA35A0D290D89D7A2500426C09D (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, RuntimeObject* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPositionDeltaChanger_t93E4993292F0F2ADE7D703936B556C88E6127E6C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_a;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(1, IPositionDeltaChanger_t93E4993292F0F2ADE7D703936B556C88E6127E6C_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CSortPositionOverridersU3Eb__148_0_mBCBF1157F9E8EF24C994B246C0D04F8F814A0FD1 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, RuntimeObject* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPositionOverrider_tC9347D4AB08FFEA490CBFCEC5D56E2F50D5C9D48_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_a;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(1, IPositionOverrider_tC9347D4AB08FFEA490CBFCEC5D56E2F50D5C9D48_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CSortSizeDeltaChangersU3Eb__151_0_m1D3264ABC981E11D4D3A09DEF14DED70888AFA71 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, RuntimeObject* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ISizeDeltaChanger_tA9186D1A6CE365CC4424F10913923FC7FD0AD93B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_a;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(1, ISizeDeltaChanger_tA9186D1A6CE365CC4424F10913923FC7FD0AD93B_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CSortSizeOverridersU3Eb__154_0_m6D5A8C79EA1D342DA47F6571AC7D4912321136E4 (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, RuntimeObject* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ISizeOverrider_tA40C62D96A91BEE41F0FC5F0E36E4311D4D0A826_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_a;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(1, ISizeOverrider_tA40C62D96A91BEE41F0FC5F0E36E4311D4D0A826_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CSortPostMoversU3Eb__157_0_m9A1567248619630583D4F099F51B78340EC4507E (U3CU3Ec_tFFC98425EEAEA584C4359EE9EA13B838597A94F3* __this, RuntimeObject* ___0_a, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPostMover_t6A910DC888FF7D754AC25BE3196A996D4C2901E9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_a;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = InterfaceFuncInvoker0< int32_t >::Invoke(1, IPostMover_t6A910DC888FF7D754AC25BE3196A996D4C2901E9_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CAdjustTargetInfluenceRoutineU3Ed__135__ctor_m409A5B5ADD805D2BDE208CF8E4EDA32EEFA74CD5 (U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CAdjustTargetInfluenceRoutineU3Ed__135_System_IDisposable_Dispose_m104C961491C839A3C9776417DE0F123408AA1B44 (U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CAdjustTargetInfluenceRoutineU3Ed__135_MoveNext_m69DD891BF32D680F323C30BB1AB662ED99782801 (U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Remove_mF2501D49FBA2C626A44219B01C945D53CCA6536F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_00c5;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_4 = __this->___cameraTarget;
		NullCheck(L_4);
		float L_5 = L_4->___TargetInfluenceH;
		__this->___U3CstartInfluenceHU3E5__2 = L_5;
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_6 = __this->___cameraTarget;
		NullCheck(L_6);
		float L_7 = L_6->___TargetInfluenceV;
		__this->___U3CstartInfluenceVU3E5__3 = L_7;
		__this->___U3CtU3E5__4 = (0.0f);
		goto IL_00cc;
	}

IL_0050:
	{
		float L_8 = __this->___U3CtU3E5__4;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_9 = V_1;
		NullCheck(L_9);
		float L_10;
		L_10 = ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1_inline(L_9, NULL);
		float L_11 = __this->___duration;
		__this->___U3CtU3E5__4 = ((float)il2cpp_codegen_add(L_8, ((float)(L_10/L_11))));
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_12 = __this->___cameraTarget;
		float L_13 = __this->___U3CstartInfluenceHU3E5__2;
		float L_14 = __this->___influenceH;
		float L_15 = __this->___U3CtU3E5__4;
		float L_16;
		L_16 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_13, L_14, L_15, 3, NULL);
		NullCheck(L_12);
		L_12->___TargetInfluenceH = L_16;
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_17 = __this->___cameraTarget;
		float L_18 = __this->___U3CstartInfluenceVU3E5__3;
		float L_19 = __this->___influenceV;
		float L_20 = __this->___U3CtU3E5__4;
		float L_21;
		L_21 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_18, L_19, L_20, 3, NULL);
		NullCheck(L_17);
		L_17->___TargetInfluenceV = L_21;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_22 = V_1;
		NullCheck(L_22);
		YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D* L_23;
		L_23 = ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F(L_22, NULL);
		__this->___U3CU3E2__current = L_23;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_23);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_00c5:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_00cc:
	{
		float L_24 = __this->___U3CtU3E5__4;
		if ((((float)L_24) <= ((float)(1.0f))))
		{
			goto IL_0050;
		}
	}
	{
		bool L_25 = __this->___removeIfZeroInfluence;
		if (!L_25)
		{
			goto IL_011a;
		}
	}
	{
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_26 = __this->___cameraTarget;
		NullCheck(L_26);
		float L_27 = L_26->___TargetInfluenceH;
		if ((!(((float)L_27) <= ((float)(0.0f)))))
		{
			goto IL_011a;
		}
	}
	{
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_28 = __this->___cameraTarget;
		NullCheck(L_28);
		float L_29 = L_28->___TargetInfluenceV;
		if ((!(((float)L_29) <= ((float)(0.0f)))))
		{
			goto IL_011a;
		}
	}
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_30 = V_1;
		NullCheck(L_30);
		List_1_t1C4E70537D7670502BADD167F04A9772D2C7AA57* L_31 = L_30->___CameraTargets;
		CameraTarget_tACB1DCA57256D4A4EE800E75B871A2E5852763D4* L_32 = __this->___cameraTarget;
		NullCheck(L_31);
		bool L_33;
		L_33 = List_1_Remove_mF2501D49FBA2C626A44219B01C945D53CCA6536F(L_31, L_32, List_1_Remove_mF2501D49FBA2C626A44219B01C945D53CCA6536F_RuntimeMethod_var);
	}

IL_011a:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3E54B43550C7989511D73C6289AF00E4539F8CB9 (U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_Reset_mCC25D8F322985E1FF8D8B0211383D93A0B9951DE (U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_Reset_mCC25D8F322985E1FF8D8B0211383D93A0B9951DE_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_get_Current_mC8453BEE2628B21F321FF2017146FAFF03FDE2DB (U3CAdjustTargetInfluenceRoutineU3Ed__135_tE62D385E520B0542A7642089133599541ACE847B* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CApplyInfluenceTimedRoutineU3Ed__134__ctor_m51120204F59658F8C42D1A7DA60F5FA0983504C8 (U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CApplyInfluenceTimedRoutineU3Ed__134_System_IDisposable_Dispose_m3BD00051FBAE7E40849BA912A5B22A1EFE8B6212 (U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CApplyInfluenceTimedRoutineU3Ed__134_MoveNext_mE5593442539FBC5813B0B161DFF09FCEBB3463BA (U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0054;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state = (-1);
		goto IL_005b;
	}

IL_0020:
	{
		float L_4 = __this->___duration;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_5 = V_1;
		NullCheck(L_5);
		float L_6;
		L_6 = ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1_inline(L_5, NULL);
		__this->___duration = ((float)il2cpp_codegen_subtract(L_4, L_6));
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_7 = V_1;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_8 = __this->___influence;
		NullCheck(L_7);
		ProCamera2D_ApplyInfluence_m43513A9BCD9D112787F90B9B4A73FC54B9646139(L_7, L_8, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_9 = V_1;
		NullCheck(L_9);
		YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D* L_10;
		L_10 = ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F(L_9, NULL);
		__this->___U3CU3E2__current = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_10);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0054:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_005b:
	{
		float L_11 = __this->___duration;
		if ((((float)L_11) > ((float)(0.0f))))
		{
			goto IL_0020;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD448A87768163EA43D5653178F0DBDC60006F315 (U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_Reset_m4C8D8FB13B284CA12B2268B5E6DFEF45FFBA299E (U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_Reset_m4C8D8FB13B284CA12B2268B5E6DFEF45FFBA299E_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_get_Current_mB679A88BC7061F08573F801056767C37EE6A14CF (U3CApplyInfluenceTimedRoutineU3Ed__134_t6F310C2F3C78E21CB47AA239C7C5A91A8F5AE04E* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CApplyInfluencesTimedRoutineU3Ed__133__ctor_mA8B4ECFBF07C3573B979B4B9B37BA42105F26EA3 (U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CApplyInfluencesTimedRoutineU3Ed__133_System_IDisposable_Dispose_m686AF8919E826C8AA67FD4733F65F40C49F4A442 (U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CApplyInfluencesTimedRoutineU3Ed__133_MoveNext_m91FA6C67DB6E6C3EC63E0E3A64AD626B7DAAC84B (U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IList_1_t0DF1E5F56EE58E1A7F1FE26A676FC9FBF4D52A07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* V_1 = NULL;
	float V_2 = 0.0f;
	int32_t V_3 = 0;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0072;
		}
	}
	{
		return (bool)0;
	}

IL_0017:
	{
		__this->___U3CU3E1__state = (-1);
		__this->___U3CcountU3E5__2 = (-1);
		goto IL_0079;
	}

IL_0027:
	{
		int32_t L_4 = __this->___U3CcountU3E5__2;
		V_3 = L_4;
		int32_t L_5 = V_3;
		__this->___U3CcountU3E5__2 = ((int32_t)il2cpp_codegen_add(L_5, 1));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_6 = __this->___durations;
		int32_t L_7 = __this->___U3CcountU3E5__2;
		NullCheck(L_6);
		int32_t L_8 = L_7;
		float L_9 = (L_6)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		V_2 = L_9;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_10 = V_1;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_11 = V_1;
		RuntimeObject* L_12 = __this->___influences;
		int32_t L_13 = __this->___U3CcountU3E5__2;
		NullCheck(L_12);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_14;
		L_14 = InterfaceFuncInvoker1< Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7, int32_t >::Invoke(0, IList_1_t0DF1E5F56EE58E1A7F1FE26A676FC9FBF4D52A07_il2cpp_TypeInfo_var, L_12, L_13);
		float L_15 = V_2;
		NullCheck(L_11);
		RuntimeObject* L_16;
		L_16 = ProCamera2D_ApplyInfluenceTimedRoutine_m339B6445B9B97C5FDB26978174E1EA7FD86E7BF5(L_11, L_14, L_15, NULL);
		NullCheck(L_10);
		Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B* L_17;
		L_17 = MonoBehaviour_StartCoroutine_m4CAFF732AA28CD3BDC5363B44A863575530EC812(L_10, L_16, NULL);
		__this->___U3CU3E2__current = L_17;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_17);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0072:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_0079:
	{
		int32_t L_18 = __this->___U3CcountU3E5__2;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_19 = __this->___durations;
		NullCheck(L_19);
		if ((((int32_t)L_18) < ((int32_t)((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_19)->max_length)), 1)))))
		{
			goto IL_0027;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0194873426BA7960743E862A7F4C57767100281 (U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_Reset_mB6EB8C8AF011198498FF41032C55A4FC25ECB51E (U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_Reset_mB6EB8C8AF011198498FF41032C55A4FC25ECB51E_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_get_Current_mBDE02E8A3BD380C22F8B33A3210D60E7ECFCE5E1 (U3CApplyInfluencesTimedRoutineU3Ed__133_tEE137E7DA5DBB36F6047143F9D35B56FD1E4A913* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CDollyZoomRoutineU3Ed__137__ctor_mF86B1AC195636774504B0E7AFE50740934B9E3F1 (U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CDollyZoomRoutineU3Ed__137_System_IDisposable_Dispose_m6C6308A5A215173B7666278C8383CB7B701BBB64 (U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CDollyZoomRoutineU3Ed__137_MoveNext_m3BA1B2172EA265671591C670D6A2D6925BEB90D4 (U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_0111;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_4 = V_1;
		NullCheck(L_4);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_5 = L_4->___GameCamera;
		NullCheck(L_5);
		float L_6;
		L_6 = Camera_get_fieldOfView_m9A93F17BBF89F496AE231C21817AFD1C1E833FBB(L_5, NULL);
		__this->___U3CstartFOVU3E5__2 = L_6;
		float L_7 = __this->___U3CstartFOVU3E5__2;
		__this->___U3CnewFOVU3E5__3 = L_7;
		__this->___U3CtU3E5__4 = (0.0f);
		goto IL_0118;
	}

IL_004e:
	{
		float L_8 = __this->___U3CtU3E5__4;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_9 = V_1;
		NullCheck(L_9);
		float L_10;
		L_10 = ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1_inline(L_9, NULL);
		float L_11 = __this->___duration;
		__this->___U3CtU3E5__4 = ((float)il2cpp_codegen_add(L_8, ((float)(L_10/L_11))));
		float L_12 = __this->___U3CstartFOVU3E5__2;
		float L_13 = __this->___finalFOV;
		float L_14 = __this->___U3CtU3E5__4;
		int32_t L_15 = __this->___easeType;
		float L_16;
		L_16 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_12, L_13, L_14, L_15, NULL);
		__this->___U3CnewFOVU3E5__3 = L_16;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_17 = V_1;
		NullCheck(L_17);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_18 = L_17->___GameCamera;
		float L_19 = __this->___U3CnewFOVU3E5__3;
		NullCheck(L_18);
		Camera_set_fieldOfView_m5AA9EED4D1603A1DEDBF883D9C42814B2BDEB777(L_18, L_19, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_20 = V_1;
		NullCheck(L_20);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_21 = L_20->____transform;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_22 = V_1;
		NullCheck(L_22);
		Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* L_23 = L_22->___VectorHVD;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_24 = V_1;
		NullCheck(L_24);
		Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* L_25 = L_24->___Vector3H;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_26 = V_1;
		NullCheck(L_26);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_27 = L_26->____transform;
		NullCheck(L_27);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_28;
		L_28 = Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95(L_27, NULL);
		NullCheck(L_25);
		float L_29;
		L_29 = Func_2_Invoke_m1512A344733CFB3C2D59C468C852A374239D3B52_inline(L_25, L_28, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_30 = V_1;
		NullCheck(L_30);
		Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* L_31 = L_30->___Vector3V;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_32 = V_1;
		NullCheck(L_32);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_33 = L_32->____transform;
		NullCheck(L_33);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_34;
		L_34 = Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95(L_33, NULL);
		NullCheck(L_31);
		float L_35;
		L_35 = Func_2_Invoke_m1512A344733CFB3C2D59C468C852A374239D3B52_inline(L_31, L_34, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_36 = V_1;
		float L_37 = __this->___U3CnewFOVU3E5__3;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_38 = V_1;
		NullCheck(L_38);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_39;
		L_39 = ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4_inline(L_38, NULL);
		float L_40 = L_39.___y;
		NullCheck(L_36);
		float L_41;
		L_41 = ProCamera2D_GetCameraDistanceForFOV_m2FCA916D4D6C57A1A44274D78A2277AC569C4703(L_36, L_37, L_40, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_42 = V_1;
		NullCheck(L_42);
		float L_43 = L_42->____originalCameraDepthSign;
		NullCheck(L_23);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_44;
		L_44 = Func_4_Invoke_m6A779A1925A6EF41D5DF3ADF5E390CF39798250B_inline(L_23, L_29, L_35, ((float)il2cpp_codegen_multiply(L_41, L_43)), NULL);
		NullCheck(L_21);
		Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134(L_21, L_44, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_45 = V_1;
		NullCheck(L_45);
		YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D* L_46;
		L_46 = ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F(L_45, NULL);
		__this->___U3CU3E2__current = L_46;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_46);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_0111:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_0118:
	{
		float L_47 = __this->___U3CtU3E5__4;
		if ((((float)L_47) <= ((float)(1.0f))))
		{
			goto IL_004e;
		}
	}
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_48 = V_1;
		NullCheck(L_48);
		L_48->____dollyZoomRoutine = (Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&L_48->____dollyZoomRoutine), (void*)(Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B*)NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_49 = V_1;
		NullCheck(L_49);
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_50 = L_49->___OnDollyZoomFinished;
		if (!L_50)
		{
			goto IL_0148;
		}
	}
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_51 = V_1;
		NullCheck(L_51);
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_52 = L_51->___OnDollyZoomFinished;
		float L_53 = __this->___U3CnewFOVU3E5__3;
		NullCheck(L_52);
		Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_inline(L_52, L_53, NULL);
	}

IL_0148:
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_54 = V_1;
		NullCheck(L_54);
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_55 = L_54->___OnUpdateScreenSizeFinished;
		if (!L_55)
		{
			goto IL_016c;
		}
	}
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_56 = V_1;
		NullCheck(L_56);
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_57 = L_56->___OnUpdateScreenSizeFinished;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_58 = V_1;
		NullCheck(L_58);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_59;
		L_59 = ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4_inline(L_58, NULL);
		float L_60 = L_59.___y;
		NullCheck(L_57);
		Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_inline(L_57, ((float)il2cpp_codegen_multiply(L_60, (0.5f))), NULL);
	}

IL_016c:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CDollyZoomRoutineU3Ed__137_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB7668F67DDEBD720BF1FD5ECB5741AE75D0C5B2E (U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_Reset_m8F61EFF21AD9780E02E38B110807DA2770E921D7 (U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_Reset_m8F61EFF21AD9780E02E38B110807DA2770E921D7_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_get_Current_mA5B7EE658EFDFE06CBF0CE6A6ADEA7E0C001ED8F (U3CDollyZoomRoutineU3Ed__137_t11C7148C8527DDB7D6DE55A4C9B387B959E1D97B* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateScreenSizeRoutineU3Ed__136__ctor_m0C35F21342157299F0E20519FE7B8601DB070C6B (U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateScreenSizeRoutineU3Ed__136_System_IDisposable_Dispose_m3313BBBBC7B2FDA68B024FB518C4C523C50D3402 (U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CUpdateScreenSizeRoutineU3Ed__136_MoveNext_m8FF50A132F993A1E88CEBC9751FDA587ACCC19AF (U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* V_1 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_1 = __this->___U3CU3E4__this;
		V_1 = L_1;
		int32_t L_2 = V_0;
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_00af;
		}
	}
	{
		return (bool)0;
	}

IL_001a:
	{
		__this->___U3CU3E1__state = (-1);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_4 = V_1;
		NullCheck(L_4);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5;
		L_5 = ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4_inline(L_4, NULL);
		float L_6 = L_5.___y;
		__this->___U3CstartSizeU3E5__2 = ((float)il2cpp_codegen_multiply(L_6, (0.5f)));
		float L_7 = __this->___U3CstartSizeU3E5__2;
		__this->___U3CnewSizeU3E5__3 = L_7;
		__this->___U3CtU3E5__4 = (0.0f);
		goto IL_00b6;
	}

IL_0051:
	{
		float L_8 = __this->___U3CtU3E5__4;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_9 = V_1;
		NullCheck(L_9);
		float L_10;
		L_10 = ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1_inline(L_9, NULL);
		float L_11 = __this->___duration;
		__this->___U3CtU3E5__4 = ((float)il2cpp_codegen_add(L_8, ((float)(L_10/L_11))));
		float L_12 = __this->___U3CstartSizeU3E5__2;
		float L_13 = __this->___finalSize;
		float L_14 = __this->___U3CtU3E5__4;
		int32_t L_15 = __this->___easeType;
		float L_16;
		L_16 = Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31(L_12, L_13, L_14, L_15, NULL);
		__this->___U3CnewSizeU3E5__3 = L_16;
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_17 = V_1;
		float L_18 = __this->___U3CnewSizeU3E5__3;
		NullCheck(L_17);
		ProCamera2D_SetScreenSize_m88156B34D588B0975BDF321FDE8387FF9E6E7072(L_17, L_18, NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_19 = V_1;
		NullCheck(L_19);
		YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D* L_20;
		L_20 = ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F(L_19, NULL);
		__this->___U3CU3E2__current = L_20;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_20);
		__this->___U3CU3E1__state = 1;
		return (bool)1;
	}

IL_00af:
	{
		__this->___U3CU3E1__state = (-1);
	}

IL_00b6:
	{
		float L_21 = __this->___U3CtU3E5__4;
		if ((((float)L_21) <= ((float)(1.0f))))
		{
			goto IL_0051;
		}
	}
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_22 = V_1;
		NullCheck(L_22);
		L_22->____updateScreenSizeCoroutine = (Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&L_22->____updateScreenSizeCoroutine), (void*)(Coroutine_t85EA685566A254C23F3FD77AB5BDFFFF8799596B*)NULL);
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_23 = V_1;
		NullCheck(L_23);
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_24 = L_23->___OnUpdateScreenSizeFinished;
		if (!L_24)
		{
			goto IL_00e3;
		}
	}
	{
		ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* L_25 = V_1;
		NullCheck(L_25);
		Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* L_26 = L_25->___OnUpdateScreenSizeFinished;
		float L_27 = __this->___U3CnewSizeU3E5__3;
		NullCheck(L_26);
		Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_inline(L_26, L_27, NULL);
	}

IL_00e3:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m63DFE9D92CC3D757C5DC39EC31DB292CE5EE5EF8 (U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_Reset_mA0B540206D18E1C21A1314421A16B808A4B8B0D0 (U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_Reset_mA0B540206D18E1C21A1314421A16B808A4B8B0D0_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_get_Current_mD34C3430382F0730625E618C5B542235566C7D06 (U3CUpdateScreenSizeRoutineU3Ed__136_t8D12714BDCA682FAB8F5FBC422F3701792F5B7C3* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1_inline (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CDeltaTimeU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4_inline (ProCamera2D_t22E2231C40E0CD03706863B78656D01BD2DBBA8A* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3CScreenSizeInWorldCoordinatesU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Func_2_Invoke_m1512A344733CFB3C2D59C468C852A374239D3B52_gshared_inline (Func_2_tDC72553AEF8707070A5FFB9D46F144F9BE06A9EC* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_arg, const RuntimeMethod* method) 
{
	typedef float (*FunctionPointerType) (RuntimeObject*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_arg, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Func_4_Invoke_m6A779A1925A6EF41D5DF3ADF5E390CF39798250B_gshared_inline (Func_4_t86BB8318802579CA09795E586FEDCB0F3364879D* __this, float ___0_arg1, float ___1_arg2, float ___2_arg3, const RuntimeMethod* method) 
{
	typedef Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 (*FunctionPointerType) (RuntimeObject*, float, float, float, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_arg1, ___1_arg2, ___2_arg3, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mA8F89FB04FEA0F48A4F22EC84B5F9ADB2914341F_gshared_inline (Action_1_t310F18CB4338A2740CA701F160C62E2C3198E66A* __this, float ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, float, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
