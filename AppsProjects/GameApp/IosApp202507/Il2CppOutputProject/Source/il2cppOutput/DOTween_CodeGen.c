﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E (void);
extern void Color2_op_Addition_m4BD6D878284D56DB00BF838BFF135155E70D6C1A (void);
extern void Color2_op_Subtraction_m78CDC06AF474D662931568BFA73CD8C477BE2D99 (void);
extern void Color2_op_Multiply_m1AE5DF5597AA4375991E023FA04AABAAB64870C0 (void);
extern void TweenCallback__ctor_m68CC9304423CBDE43001F9B1413B5DAAF70DB621 (void);
extern void TweenCallback_Invoke_mE4105043678D7086C7740B0D7B7589B734C14E1F (void);
extern void EaseFunction__ctor_mD630BE102357BB21BD878DF5E98F90BFE785A0F8 (void);
extern void EaseFunction_Invoke_mC30ABF785F84A8769541950EDC3C2CB0B8F6FB8D (void);
extern void DOTween_get_logBehaviour_m50FAF61152D634B61FFA7D3B04F7C2A10E6E9B97 (void);
extern void DOTween_set_logBehaviour_mD910C3B966CBE72E0ED43B7EA96BA06331090D6A (void);
extern void DOTween_get_debugStoreTargetId_m96F1367AD4955D84B2284A01EDD146DCE64B3A75 (void);
extern void DOTween_set_debugStoreTargetId_m9A024F9090E856AC801B9DD5AECE4B8CAFBD96A5 (void);
extern void DOTween_AutoInit_mF7B0D31019E4A0D0212902AC43E359A1BD763C29 (void);
extern void DOTween_Init_m31648CA12FD2195F125B2B4773B7BF8DAFA11080 (void);
extern void DOTween_Clear_m6CFE7E673765E730176BF919B55B1D7C1A923075 (void);
extern void DOTween_To_m9C9EBC0FB6CF94364DD4FF85C476D8EE0A7FF4B1 (void);
extern void DOTween_To_mA4E61D06204BD01537C08EEB9ED148C18ABC75ED (void);
extern void DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680 (void);
extern void DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8 (void);
extern void DOTween_To_m7A731ADF3CCD5C4439F8710B2CD16BC6CEB051D0 (void);
extern void DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339 (void);
extern void DOTween_To_mA77855459ADB369B17DB84A390E88A1CC27868F4 (void);
extern void DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1 (void);
extern void DOTween_Punch_mD470A46B3BEC0E312D0438C14C0980E853CA4D32 (void);
extern void DOTween_Shake_mD96D04E4D767144E80C613CA7B49F55BF36E10B3 (void);
extern void DOTween_Shake_mBF64A5C44F1258CE3B097F89352473FC6B1B298C (void);
extern void DOTween_Shake_mF69B5F924A2D3633342CDE459375D677A930820B (void);
extern void DOTween_ToArray_mEC8B5DBDFCBC6DE60994B6E6DD0CCDDA43F001AB (void);
extern void DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89 (void);
extern void DOTween_Complete_m12E2987F7D42218DCCE051E2FDB803E75FD8BA91 (void);
extern void DOTween_CompleteAndReturnKilledTot_m77F801701C03DB55B442098F543A09F2C5600452 (void);
extern void DOTween_Flip_mCE1C7CBA31A981D035B52150957C3A01B7F2A978 (void);
extern void DOTween_Goto_m19ABF14E2B1ACAFFA17E8AB48AEE2207B6FFA6C9 (void);
extern void DOTween_Kill_mAB4C96CE1F1BCF25E5347AE0FC295D064EA53FB2 (void);
extern void DOTween_Pause_m498BECFBBC8FBD76425B8AE1F38E2ECC9AE296D4 (void);
extern void DOTween_Play_m466F46F9DF6585E17C595438BA15147319540DC4 (void);
extern void DOTween_Play_mEFD3A1E3CC218D3916032325F2E119C2004D9473 (void);
extern void DOTween_PlayBackwards_m3709E26071CCE5CB368584477DF2FC9A8B9D052B (void);
extern void DOTween_PlayBackwards_mC4FB110A49C220C2B5FE768989D133F0F8721FA7 (void);
extern void DOTween_PlayForward_m4F27092024989DEB74466D9D9C370D5FA6621DCF (void);
extern void DOTween_PlayForward_mC1FF85BCF9DC773A3E4FA3DC12377D1F9E1BADD4 (void);
extern void DOTween_Restart_mEE2F85FC8741BBCE7C1E76C143B3DCE5B9C78DEF (void);
extern void DOTween_Restart_m89E03C91814831E13EA36751C4A8471CF41FA249 (void);
extern void DOTween_Rewind_m5C4020E9007FAAF719C1BE01CE440EBB39193619 (void);
extern void DOTween_SmoothRewind_m36B53DA282A0BC7948453A6725939D60A33D5C22 (void);
extern void DOTween_TogglePause_m8CE7CA00FE30F3C926F34362E2DE67A161536598 (void);
extern void DOTween_InitCheck_mA3F71F5F48DEF60104F960552849E293CDDEBA7E (void);
extern void DOTween__cctor_m278575843F5C4300324E26C3AC8D91B665F2F155 (void);
extern void DOVirtual_EasedValue_m40F83FCD3705E8DD33558C84B3F7067D91F3B9DF (void);
extern void TweenExtensions_Complete_m27D13C838F7DCBFE14632132B60338F42B3ACB73 (void);
extern void TweenExtensions_Complete_m5B347FC17642C404A25E17D231836B2BE39E0632 (void);
extern void TweenExtensions_Goto_m0256CB1AA2FAB6786021BF3322D737AE4D37B2FC (void);
extern void TweenExtensions_Kill_mFFDE4DE45D8A91C357ABFF2F8FA95D412924F466 (void);
extern void TweenExtensions_PlayBackwards_mF585684B8D5082A2A895C628F372FDABF690E0FB (void);
extern void TweenExtensions_PlayForward_mE9D0A1BCADDDFD7429F97CF8AE9BE9D1E81F6439 (void);
extern void TweenExtensions_Restart_m4080AE8184C33AFCB12BFBB850424D13836E6214 (void);
extern void TweenExtensions_Rewind_m0ECE9F671C1A1BE35270FD24F9AC81DC5645DAF1 (void);
extern void TweenExtensions_TogglePause_m6AA5BB6139190D6F7A5B1516802D650DBC813322 (void);
extern void TweenExtensions_CompletedLoops_m5A7B5AEE691F491182E5FD7009C21E3BBC90CA8B (void);
extern void TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222 (void);
extern void TweenExtensions_ElapsedPercentage_m2B88D6261A10FE69DA344E2EDF1D72F0DC7C4FA8 (void);
extern void TweenExtensions_ElapsedDirectionalPercentage_m86020EAE1A0CA49FDE72191B7F45FEE990B9CFD5 (void);
extern void TweenExtensions_IsActive_m7CB8E490D86B9E14B6B4B4004F1D199790397743 (void);
extern void TweenExtensions_IsComplete_mBB619B64C19A85AB4EEEBB3D37D3E720A9E0AF4C (void);
extern void TweenExtensions_IsInitialized_m8C2B24B55147A6773849EB32D4D00E128CAB211B (void);
extern void TweenExtensions_IsPlaying_m32EF28DEB59B931FA4607BAC3BED0DE275A1D843 (void);
extern void Sequence__ctor_mFFB83C470D70B8512E2B393A0C07D90FEC2CBC84 (void);
extern void Sequence_DoInsert_mD948F47640F2159358EE6B18952EB77AEE6610F4 (void);
extern void Sequence_DoAppendInterval_m0C3E54F3B28A78293C67DD1AFFBDB27A665E59BE (void);
extern void Sequence_DoPrependInterval_m5349CCACB49068164F7EEDE05FB705AA7458D248 (void);
extern void Sequence_Reset_m006B0E92244A1C149A954C5183E9826DC5C828AE (void);
extern void Sequence_Startup_m76F5EC2C0703BDAFA28DA67BB0AD4B97BE0A9D4E (void);
extern void Sequence_ApplyTween_mC85811FDB77E639F3665931DD46CCC17626354A7 (void);
extern void Sequence_Setup_mDCF62E1E0C88A3090CBDD2D79A544EF03150202A (void);
extern void Sequence_DoStartup_mEC96B51F5254BE451DD5CBFED2EFC13FE463F7F0 (void);
extern void Sequence_DoApplyTween_m06E4746BDB1F214259ADC078EDD812BBBFCFC54D (void);
extern void Sequence_ApplyInternalCycle_m2B145923EEC8BE7893BB8F17B217F26318AC8B94 (void);
extern void Sequence_StableSortSequencedObjs_mC3780B8F109A9114A2D5CE3C2605903E282E1FAB (void);
extern void Sequence_IsAnyCallbackSet_m98D9A7B3915C4C54A385E0F39646C3DF9D62A600 (void);
extern void ShortcutExtensions_DOAspect_mE9ECE416D6C7FD3BDE1DB73DFBCAA738589062E2 (void);
extern void ShortcutExtensions_DOColor_mC438262691549AA19473BFC4777A39E4DF995E03 (void);
extern void ShortcutExtensions_DOFieldOfView_mDF8F791F7D4672A51988CE4F3434A589F0F7F62D (void);
extern void ShortcutExtensions_DOOrthoSize_mE96037CC51B44CB1B04CF436779EAF60BF6C0CD3 (void);
extern void ShortcutExtensions_DOPixelRect_m30CEF5D00DEFB324BB8514B95CF0328929274598 (void);
extern void ShortcutExtensions_DORect_m21D8649612CDF6196C7D4C591382743112FEAFD3 (void);
extern void ShortcutExtensions_DOColor_mAC2C2C38A8C15064D70E6B39F5735A09DDA0D581 (void);
extern void ShortcutExtensions_DOIntensity_m27397BA48763F123CFEE2F08D1B68C536BD1AE12 (void);
extern void ShortcutExtensions_DOColor_m7770E3969D58563343B129139B857669312EFAA9 (void);
extern void ShortcutExtensions_DOColor_mF41D0D0338A1C71122B56F08353CAFA9931B5183 (void);
extern void ShortcutExtensions_DOFade_m1C499FE6483845A6BBE9C4EB80D11062C9859FC8 (void);
extern void ShortcutExtensions_DOMove_m82274FDC0216A91A1FAF16844805D06BF9A287FF (void);
extern void ShortcutExtensions_DOLocalMove_m22F3EB581DADB5A3FC59B69F7F6F05A86F8E8348 (void);
extern void ShortcutExtensions_DORotate_mA2804C1A3E4780383111262752CC7056BBC7D470 (void);
extern void ShortcutExtensions_DOLocalRotate_m6EB8F37963023C6B157C60013B98D2B612816DA4 (void);
extern void ShortcutExtensions_DOScale_mF7AC6EA0FD71B399776D758AD57B94F18A47F580 (void);
extern void ShortcutExtensions_DOPunchPosition_mD022015ABB94942EE909F7F8E0F3660D52FA3D9E (void);
extern void ShortcutExtensions_DOPunchScale_mD7D825D1761F0264BC1D00027B79330844400B9A (void);
extern void ShortcutExtensions_DOPunchRotation_mDC55C1F23E2C17A4E9D4BF5BB787BB1DE98D7AC4 (void);
extern void ShortcutExtensions_DOShakePosition_mEF231F12CB359BF88DEA0B5BF1480A270DE9366D (void);
extern void ShortcutExtensions_DOShakeRotation_m36228095EBEDF630E0B87230083E6947A04DBB5E (void);
extern void ShortcutExtensions_DOShakeScale_m6DF910EF19D54F5136100A2F3175B3DD53D85BBA (void);
extern void ShortcutExtensions_DOPath_m3E70D921DDA265292CF467212AC676371F110691 (void);
extern void ShortcutExtensions_DOLocalPath_m4F4C77B2C481DDCB0FDBCE8B3C4442D897F1B2DA (void);
extern void U3CU3Ec__DisplayClass0_0__ctor_m14F8614CA85AED6EEB9F83CD5240ECB824FD30BC (void);
extern void U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__0_m3B8BE2D1BEEAEDD0102C3DC4A32B80C07DD8264F (void);
extern void U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__1_mDDD2F090D812F2AF5DC451DF365B91E91410B0B0 (void);
extern void U3CU3Ec__DisplayClass1_0__ctor_mF7AE84F589865AFA97F3F0613C42A63F7ADF9FD2 (void);
extern void U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__0_mB50622EF9AB0EA0CF6318319A488B134B1E441EA (void);
extern void U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__1_mC208F6B646E6706D73391F28237DE9FB3E01C022 (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_mD3DB006B7B8A1A194339F0ECC23CA1E04787EFD9 (void);
extern void U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__0_m15FBEF870514F3B7A0E107807AA4CC626C0A5B91 (void);
extern void U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__1_mE0B6B23B688947A0F131A1CC31CE19D153052990 (void);
extern void U3CU3Ec__DisplayClass5_0__ctor_m7783B6BCA09BDF4543ADF7F62889ABBA94DB3A3A (void);
extern void U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__0_m034E070A3B9354C6D1D5DE8A1B26FCF53208F73D (void);
extern void U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__1_m2AC152D033DE5ACFE3232261EF59650B917174AE (void);
extern void U3CU3Ec__DisplayClass6_0__ctor_m3EF7021A9BC9E02A6D31DAECC153759EF98F1745 (void);
extern void U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__0_m056478689B03F8E3C264EF28603426A02853E288 (void);
extern void U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__1_m5FD55DBC4B5BD7120C6D09D6155A91E15FAE6BE0 (void);
extern void U3CU3Ec__DisplayClass7_0__ctor_m99FED5DE1D5FCE5A033A41690EEE45C7B02436FA (void);
extern void U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__0_m323992D6C4F67E9BCDAC970E9132420617355BEC (void);
extern void U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__1_m326FD99ABCA6F25C8F9BC3438FFA62CC05A6813E (void);
extern void U3CU3Ec__DisplayClass12_0__ctor_m77B82950D8ED2AE3169C0193888A86E0D538A5CB (void);
extern void U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__0_m32DE3BA95C1F0C32BE0552EE5599FFBB0FE97DF1 (void);
extern void U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__1_m4D05D90FBB17A5455CC66D17A83CF1656A87755B (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m06708EDA2D1D9444D4BBD595985B8EFFD0595710 (void);
extern void U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__0_mCDD8C584608D56E4644AEEFEF0993C89615D2ADA (void);
extern void U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__1_m91CCE11F8FA50E2CE4CCAF30A2CBD50FF6F6AEF8 (void);
extern void U3CU3Ec__DisplayClass16_0__ctor_m2B3D033F265B069E406A5330FE153EDADECEBC28 (void);
extern void U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__0_m68313755D418CC0E079ECB927D1DA4C137927F73 (void);
extern void U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__1_mBCABAA139578080574B0C7AFBD7938C6E49961EF (void);
extern void U3CU3Ec__DisplayClass17_0__ctor_mE986B184C7B7F578C4EFDDCDC57D33970F613668 (void);
extern void U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__0_mF06E3103940FEA26AE34A10EB7737BEF9322200D (void);
extern void U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__1_mD8C4BBDABA25C6A116793F58B6EED5DF8EDF4145 (void);
extern void U3CU3Ec__DisplayClass19_0__ctor_m6A019E5099794BFD3EC9819B9C19AC696B218865 (void);
extern void U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__0_m50D60171D21D0F9B87A60CA102A9CE3DAE504842 (void);
extern void U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__1_m5203CDD55EDCD16C106CF2230BEB634973FA87C1 (void);
extern void U3CU3Ec__DisplayClass32_0__ctor_mF99E40BAC9D70A41F9D49D720B24F55F5B514161 (void);
extern void U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__0_m15E1AAFD0E46945E290D6BE466818E3ED7F7F6E3 (void);
extern void U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__1_m580B3AFDB23D638C3BA6A8EE6DE4B6420CE8FE0A (void);
extern void U3CU3Ec__DisplayClass36_0__ctor_mAE58EFC3DD1DD7C1F36380102ECD20D2AA00F1CE (void);
extern void U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__0_m6AF2104AC0D94DF07EC176F21E354FE77D112485 (void);
extern void U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__1_m92CAF0D1D04465B4298655176590064880FA0DAC (void);
extern void U3CU3Ec__DisplayClass40_0__ctor_m8E6EAA117C9611630A2AF2B34959429DA441AF03 (void);
extern void U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__0_m89100800A2C8745BE1323653608E893652183B7E (void);
extern void U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__1_mE57D43D5CBC331CCB313FE97DF304962B4B61272 (void);
extern void U3CU3Ec__DisplayClass42_0__ctor_m894ECDDCD8FA39381D104A78723ED0D3B0FEFA24 (void);
extern void U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__0_m60162FBB56ACF20CFD036154E08DB13B16C3CE47 (void);
extern void U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__1_m8DBE8031D1E7627BE19794A3F97744BA72D358D0 (void);
extern void U3CU3Ec__DisplayClass44_0__ctor_mC24979AC46FE287403167265ABFD5C08E7C62D33 (void);
extern void U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__0_m0A1B1E2996430F9455E9FF81C312223B3C46CE35 (void);
extern void U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__1_m95D31585339C2303A33F37C20E75697B13FC0FF4 (void);
extern void U3CU3Ec__DisplayClass50_0__ctor_m341F0483D9CE810847D6A7850A9172093B714A8F (void);
extern void U3CU3Ec__DisplayClass50_0_U3CDOPunchPositionU3Eb__0_m303073F867A68180C2EEB67F1F51D98D8F269CC0 (void);
extern void U3CU3Ec__DisplayClass50_0_U3CDOPunchPositionU3Eb__1_mFE149A4C533BF050157EE7790CFD07548A53437D (void);
extern void U3CU3Ec__DisplayClass51_0__ctor_m6993C5B08436C61812CD70E98322A42665AC82C9 (void);
extern void U3CU3Ec__DisplayClass51_0_U3CDOPunchScaleU3Eb__0_m734D371379B3B6A6F92D6BDDCDF5B2A0C1A31693 (void);
extern void U3CU3Ec__DisplayClass51_0_U3CDOPunchScaleU3Eb__1_m527EFAFDA64873F3E0E9C6F0A83675D4AD90E04D (void);
extern void U3CU3Ec__DisplayClass52_0__ctor_mF34A0E0114DC164E1A106453AA3A5B83EA8ACB6B (void);
extern void U3CU3Ec__DisplayClass52_0_U3CDOPunchRotationU3Eb__0_mA4391918ED1F4B585FE5EB19EE1CE16DFF027811 (void);
extern void U3CU3Ec__DisplayClass52_0_U3CDOPunchRotationU3Eb__1_m4F593B3E41B2E9859050C5A4BDF54067E63E8880 (void);
extern void U3CU3Ec__DisplayClass54_0__ctor_m8487623AB289AE5A29AAE68FBD16094EBED4619A (void);
extern void U3CU3Ec__DisplayClass54_0_U3CDOShakePositionU3Eb__0_m43FAC97692AABAE5A1C3025EA84D5F918B1238AE (void);
extern void U3CU3Ec__DisplayClass54_0_U3CDOShakePositionU3Eb__1_m14F132270B6638FF470D9DEC202349E651C36733 (void);
extern void U3CU3Ec__DisplayClass56_0__ctor_mE3F409583C85BFFAD329D6657AE57326CEA380B8 (void);
extern void U3CU3Ec__DisplayClass56_0_U3CDOShakeRotationU3Eb__0_mB2C6F5F6FAF6A27B193E1EFBC982F0662BBB59E6 (void);
extern void U3CU3Ec__DisplayClass56_0_U3CDOShakeRotationU3Eb__1_m09F951E47B7ED5C1EB69455C495CBEC0422D79DC (void);
extern void U3CU3Ec__DisplayClass58_0__ctor_m53D84AC3C9E88A1B5850B1D7B2CF4A782504B6CB (void);
extern void U3CU3Ec__DisplayClass58_0_U3CDOShakeScaleU3Eb__0_mCCB34095300E1247CBBAF5C7A45E25EB062033DA (void);
extern void U3CU3Ec__DisplayClass58_0_U3CDOShakeScaleU3Eb__1_m42F5DCA159CCB3B8B43361B3CF9F7C4176212480 (void);
extern void U3CU3Ec__DisplayClass63_0__ctor_mB96BCCBA4CD18893BF3FE33921D1A1349C579F9A (void);
extern void U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__0_m8D3440E5FE89819FD8DCDAFCFE7FC1AAFCEB92DE (void);
extern void U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__1_m87BE9FA5B9FA36647B1AB9EAC06C498131DA3077 (void);
extern void U3CU3Ec__DisplayClass64_0__ctor_m797499323817E936BD2292B270BFC32D447AD78B (void);
extern void U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__0_mDE31EA6776321007A3438FC8F6A329C3F1DDCC88 (void);
extern void U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__1_mD3A0960DF5B0665A23903CAA811D3FF0703B2D10 (void);
extern void TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F (void);
extern void TweenSettingsExtensions_Join_mBA1D659EE0310BBE1F42148057403E7C5EEDB777 (void);
extern void TweenSettingsExtensions_AppendInterval_m36B7A337E62568050B2A3220C9140D06CD50CD82 (void);
extern void TweenSettingsExtensions_PrependInterval_mCC5525CBFA2E20938D4D095DF5F78720C23228E0 (void);
extern void TweenSettingsExtensions_SetOptions_m94B0ECDB7445CABBF2814531E137F51582AE5425 (void);
extern void TweenSettingsExtensions_SetOptions_m470EC93A8B43B25894F7143B876B117AFF2B000E (void);
extern void TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C (void);
extern void TweenSettingsExtensions_SetOptions_m6FA72AD20A82D69FDB0189B31D81B5A78653FF50 (void);
extern void TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700 (void);
extern void TweenSettingsExtensions_SetOptions_mDBFD729D4FEE37F7A828AD98BFC532A802617F08 (void);
extern void TweenSettingsExtensions_SetOptions_m571339B54CA3A4BD935C07D719EE40E4FAE2C9D2 (void);
extern void TweenSettingsExtensions_SetOptions_m3AF4D0166C4D467CCEFCB9B3116EA69D200C73E2 (void);
extern void TweenSettingsExtensions_SetOptions_m5E6F9A1145201D9D34824DE50132581D3DDC8DCE (void);
extern void TweenSettingsExtensions_SetLookAt_m92E5AE38463525D9672DF0E98D6980096DBC2814 (void);
extern void TweenSettingsExtensions_SetLookAt_m2D965125CC14890614DFBFF2A5E762852F9F1AC2 (void);
extern void TweenSettingsExtensions_SetLookAt_mECF406EE42F980E66C745916D2BBDF63F7E4C0D6 (void);
extern void TweenSettingsExtensions_SetPathForwardDirection_mF2A4AB7983A3BBDA380EA993C205C6D8AFE0B64C (void);
extern void Tween_get_isRelative_mC31C34D21C3953F9AA7F25C0429BEBE45D2DBAE2 (void);
extern void Tween_set_isRelative_m881085052780C20122B970FA26766E551DA3B8EB (void);
extern void Tween_get_active_mD4253DD1A64623E342282E139081B787935A3E5E (void);
extern void Tween_set_active_m7E2D493098F2406830BAE9201422B8A1E7ADE2C7 (void);
extern void Tween_get_playedOnce_mDA42B6964058549DB8BBC9217DBBB2F0EB67A335 (void);
extern void Tween_set_playedOnce_mC95D34B48FDF13A9C3B8451B2794A9FDA537019F (void);
extern void Tween_get_position_mF8A2FF9C0DA291DEC595AC8C00E2E096A009B5A8 (void);
extern void Tween_set_position_mFA8507C0C9F2E513D037EB506CDC444AC993F4B2 (void);
extern void Tween_Reset_m7E3A4C092BDB502A8B12E5DBB461602400A31C8D (void);
extern void Tween_UpdateDelay_m3BCCB4073A5EEDBD7DCE43C250D8BB79D255AE6B (void);
extern void Tween_DoGoto_m1A61731CB2E2D27D1E08ADD844E575A5DAD8939C (void);
extern void Tween_OnTweenCallback_mAF944138F2F0D8BFF50EC9B5EC1C24ADD7623FB8 (void);
extern void Tween__ctor_m92AEA714BCF3EAFB7FAE3E6714A03B8F2CB45D17 (void);
extern void Tweener__ctor_m04B7FAE8742229AF46C846C73F08E0F12A943F26 (void);
extern void Color2Plugin_Reset_m4D1585F6639130F1C1D63606F8627B5AF78A54A0 (void);
extern void Color2Plugin_SetFrom_mC3F20E4EDA65C6449C25BACD6DDB6C1199952155 (void);
extern void Color2Plugin_ConvertToStartValue_m9F85F95CB7AA6AFCAE9F5F20AB785B89048FBDC2 (void);
extern void Color2Plugin_SetRelativeEndValue_m18D1D370D75DDEF9636CBE1AA964BD11D98A9490 (void);
extern void Color2Plugin_SetChangeValue_m72A9F3272BC54989B589E62DC802685AB4BCD641 (void);
extern void Color2Plugin_GetSpeedBasedDuration_mBACAE2EDEDED990D2E2B4078D9F6255DC52C8EA2 (void);
extern void Color2Plugin_EvaluateAndApply_mB30B49EECB72DA2E1CC2B8C6874B4DD4A9FEB347 (void);
extern void Color2Plugin__ctor_mBCE84E5A0ECEAF61FFA54965D1341A4C24E3FA46 (void);
extern void DoublePlugin_Reset_mC48E0D04271DED61DE1422AC67CBFB42DD39D065 (void);
extern void DoublePlugin_SetFrom_m45DF6B4F897082911AB5E64942EB89FCBC61267A (void);
extern void DoublePlugin_ConvertToStartValue_m3BA8C6742F81078B49EDAE5BE1B277BD3BDD6768 (void);
extern void DoublePlugin_SetRelativeEndValue_m16BB892BED6D8E6DA25C367941CE14D1B5117F88 (void);
extern void DoublePlugin_SetChangeValue_m2F806A26CE2DB009ADF69F33C05A136E5F1455D6 (void);
extern void DoublePlugin_GetSpeedBasedDuration_m0661238953316795039FB9C7D05DBD4591710C33 (void);
extern void DoublePlugin_EvaluateAndApply_mFEA0376503352E121081A53927580DBAC2A29C19 (void);
extern void DoublePlugin__ctor_mBC919A2E3792A7F49C9B42888306B7D328FAA373 (void);
extern void LongPlugin_Reset_m37478A613E00FF0F05D3A58BEF11D649BFED5AC2 (void);
extern void LongPlugin_SetFrom_m51A61DCC7C9CC9B0B32921B7A0B4A776A9441455 (void);
extern void LongPlugin_ConvertToStartValue_mE222FBCEE94E48591988FEE4CEC005EFA70A1209 (void);
extern void LongPlugin_SetRelativeEndValue_mF1C87E177CCEA2C17A5090CA9B1201F093BE1C7B (void);
extern void LongPlugin_SetChangeValue_mB10261C79DC621100E22EEB1DACA566C9C7AC3F4 (void);
extern void LongPlugin_GetSpeedBasedDuration_mDC5560F8BC27B8917E870B1D3A2923E5E0C277C4 (void);
extern void LongPlugin_EvaluateAndApply_mA80DD4E127270F467A5164D165557A5D78F3E926 (void);
extern void LongPlugin__ctor_m2AD9AB566ED8B00E5944E0338ED86569A3E6A143 (void);
extern void UlongPlugin_Reset_mA7D583EA46AE862A4A279127535806B67AC93863 (void);
extern void UlongPlugin_SetFrom_mB55DCE5245B000AF510915EDF942F8C91DF2E8ED (void);
extern void UlongPlugin_ConvertToStartValue_m3F94E8568572F1E2BD3E03F043E625C2B356870D (void);
extern void UlongPlugin_SetRelativeEndValue_m6CE5E8F05C004378BB3CC4414FDA18B2F31C8C73 (void);
extern void UlongPlugin_SetChangeValue_mD9BAA6099E44D738055E34640E4C33A4E0986C89 (void);
extern void UlongPlugin_GetSpeedBasedDuration_mFA983E09C14C54961FE6DE1D3D3CCCE6DFC306B2 (void);
extern void UlongPlugin_EvaluateAndApply_m582D6FF13C0F011E768C47FC428E5A9478A1AB82 (void);
extern void UlongPlugin__ctor_m03E4C0827FFAC8322DEAC2D784708416A15DBD56 (void);
extern void Vector3ArrayPlugin_Reset_mA352ED531258DEAD2F621144C41DB9258D0B9571 (void);
extern void Vector3ArrayPlugin_SetFrom_m69DD6E6C7E4B79129DAEAF6B7D42272560894D62 (void);
extern void Vector3ArrayPlugin_ConvertToStartValue_mE150B601DA8164B66376565D966458967B80849D (void);
extern void Vector3ArrayPlugin_SetRelativeEndValue_m301BAFA7B81A646DD9E0ACFE44A4E03DD3DA8762 (void);
extern void Vector3ArrayPlugin_SetChangeValue_m6CCE20B567B1B0A291C3F99001D73DAAE4000173 (void);
extern void Vector3ArrayPlugin_GetSpeedBasedDuration_m3DE9F80A2BB6E51D8306D5FD0B7394D299B09B3E (void);
extern void Vector3ArrayPlugin_EvaluateAndApply_mCFA6779FACD78B605A6C2F5DF4A9473DE2CA63E4 (void);
extern void Vector3ArrayPlugin__ctor_m7E64B2C0B6FFD43FDEA1634185763064A0511EA3 (void);
extern void PathPlugin_Reset_mF8A7585640B692E1337D76DC9E193C9EED0F7AC2 (void);
extern void PathPlugin_SetFrom_mF6B10813AD9588EAA43C524A1A53886ECEBC45D4 (void);
extern void PathPlugin_Get_m997FB98C4FEB9E5E74B71034B36E2A40D0153BC6 (void);
extern void PathPlugin_ConvertToStartValue_m2DD9E300B9FF4CA1DCD746E8E16548AE29CA86A2 (void);
extern void PathPlugin_SetRelativeEndValue_m41C96CE4555F5DA30851CB54577E1997D954F579 (void);
extern void PathPlugin_SetChangeValue_m8C85C4B7E85276B4D0885D390DE430E3DD349CBE (void);
extern void PathPlugin_GetSpeedBasedDuration_mCEA473E67BBD3A8BE7356A87B2F69FB13FF59B3B (void);
extern void PathPlugin_EvaluateAndApply_m5ECDC4CA14C4570B1027D1E3049E1196048E2AB1 (void);
extern void PathPlugin_SetOrientation_m3F5BCB1FCBB546654FEB270D7B568F1667E9977B (void);
extern void PathPlugin__ctor_mD8811EA5B57B17FC56CD3E926FF69CB41C5FC183 (void);
extern void ColorPlugin_Reset_mD5C17D1107C847229AE3AF5513CB3FA5194961F4 (void);
extern void ColorPlugin_SetFrom_m479A8C5CD0FCA868D41ED7A29A7214B511640CBD (void);
extern void ColorPlugin_ConvertToStartValue_mCFE988E065959F8C5898AAA3B209ABAFCD8AF3A8 (void);
extern void ColorPlugin_SetRelativeEndValue_m66E263246FC9F63F50A4C5AFFC3C5E0D70B59E84 (void);
extern void ColorPlugin_SetChangeValue_m185BDA16E9FFD1AE83152A1C8B4C55F13E4B74E7 (void);
extern void ColorPlugin_GetSpeedBasedDuration_mABC5DCBD1B8D595243C93FA40FCDED9E25469AA0 (void);
extern void ColorPlugin_EvaluateAndApply_m24B4CFBDB842298B9839B532CE19E43E97F7DC4D (void);
extern void ColorPlugin__ctor_m633C70643C227166D0DE895DCD198751ADB832B8 (void);
extern void IntPlugin_Reset_mB5E54408080BA058CE3141E70024CF7BBD4124A1 (void);
extern void IntPlugin_SetFrom_mD5482FE1E980BDD2A15F9EF5A3DBA53C84CC2C30 (void);
extern void IntPlugin_ConvertToStartValue_m18C3236122A0CA21CCC67F1E04178CA298FDD21A (void);
extern void IntPlugin_SetRelativeEndValue_m2998B97675D44F79988F48E53F764D589E40A76C (void);
extern void IntPlugin_SetChangeValue_m0546A7F4D7D2E054EEBE0CD36AAD672E92F74C43 (void);
extern void IntPlugin_GetSpeedBasedDuration_mDD8E0299B88828750EA43F995838A8A14F052CB7 (void);
extern void IntPlugin_EvaluateAndApply_m5A8433003F21BBFC7A24C0E0D4D36C9EAC60C549 (void);
extern void IntPlugin__ctor_mDD5E0E9B685B85810B0E99FD3A724390EE99C913 (void);
extern void QuaternionPlugin_Reset_mF9544485D1461B1A60EC89CEAD333B5ABE4B3D7B (void);
extern void QuaternionPlugin_SetFrom_m06BACD08FF14D0BD3229DD0520C80C70C80E7F1C (void);
extern void QuaternionPlugin_ConvertToStartValue_m6D2E6DD4E47A7B6377A89DBBF830864048B739D5 (void);
extern void QuaternionPlugin_SetRelativeEndValue_m5363B1A3F417A3EFA102252017BBABDEC0EB11D4 (void);
extern void QuaternionPlugin_SetChangeValue_m40DCF9B299943301BBBAF466B57F8604C4475CD2 (void);
extern void QuaternionPlugin_GetSpeedBasedDuration_m8BE69247A323044EABCF6D824250F3B9BEA9D9FC (void);
extern void QuaternionPlugin_EvaluateAndApply_mE095DCB28F57411C6906613CAD458765FFD9D9AB (void);
extern void QuaternionPlugin__ctor_m300B7DE9FF8C992D2A5A7E069AFD7A2054D9960C (void);
extern void RectOffsetPlugin_Reset_m94F0DAB51730C6879FDF35EC1B1063950C16DD93 (void);
extern void RectOffsetPlugin_SetFrom_m450A669373A57D51711CA2F4BDDA5F47F6A8A3A9 (void);
extern void RectOffsetPlugin_ConvertToStartValue_m0FD425737084A6587013AF55BAE8E4A4DF478025 (void);
extern void RectOffsetPlugin_SetRelativeEndValue_mBD08440BECDFC30E1C2DF9019ED71925C23EAF35 (void);
extern void RectOffsetPlugin_SetChangeValue_m56A91F6ABD143A79390BA34C4218B4A7F79D9D61 (void);
extern void RectOffsetPlugin_GetSpeedBasedDuration_m0A00284A81996026211253E284C1FDDAE195E504 (void);
extern void RectOffsetPlugin_EvaluateAndApply_mA4A5EA385BB4C1FFCFA7E0D6D3924F355C193E3E (void);
extern void RectOffsetPlugin__ctor_m889C8F4862C868D55130CED884544F46AA4A4066 (void);
extern void RectOffsetPlugin__cctor_mDEE0140B33EF6EA1F0DC496427FBD4F505427071 (void);
extern void RectPlugin_Reset_m608F606EA7062DD500E16CF96D44D1A803AC1CC0 (void);
extern void RectPlugin_SetFrom_m364E3B14FB559ECA15C19CF930EBB07D3A619D71 (void);
extern void RectPlugin_ConvertToStartValue_m3584F27A3B1467B5184137FC28302E391EB1C828 (void);
extern void RectPlugin_SetRelativeEndValue_mA2683C712701D3C1978D2648A07C0567BCF11518 (void);
extern void RectPlugin_SetChangeValue_m51E32400665D3D389B296EABAF8A9F31BD654466 (void);
extern void RectPlugin_GetSpeedBasedDuration_m6898F84A4B2C11EA25628AF6C81261F6FD965D42 (void);
extern void RectPlugin_EvaluateAndApply_mDE5457292DF3C29DFA2162AFF48D80A21FF819AA (void);
extern void RectPlugin__ctor_mC8C57164273B43CA3968BD9892AF8FAFF86431ED (void);
extern void UintPlugin_Reset_m70C443EF07DEF1E40EE7775F3273819C457718A3 (void);
extern void UintPlugin_SetFrom_mD132C598273DD59BCE7D4F749BCB2CDBBDB8E4FC (void);
extern void UintPlugin_ConvertToStartValue_m3DEA6264D97583AC1E26E44D58496406ADDF5711 (void);
extern void UintPlugin_SetRelativeEndValue_m3239D0B60543DE9E7A58EEAFCAEC224B81C95C91 (void);
extern void UintPlugin_SetChangeValue_m5F1E259311413FE8EC8857E3FAAFB165AF7066E5 (void);
extern void UintPlugin_GetSpeedBasedDuration_mAF144DF48A8B58A24DE487C81E932181FD8C329F (void);
extern void UintPlugin_EvaluateAndApply_mDFC4BACBBCC44508B0631462A7C2B7FA4E2F8DE7 (void);
extern void UintPlugin__ctor_mB34F86253C685C80872915B2976E4546D1D8E897 (void);
extern void Vector2Plugin_Reset_mC73661350E837DB36291164C3C728BA866B087AC (void);
extern void Vector2Plugin_SetFrom_mABACF87EB7C31E680DEB311AD713B3EFF80C94C3 (void);
extern void Vector2Plugin_ConvertToStartValue_m77193485E58C5FA705208BAD82CEFD51A5CD0A55 (void);
extern void Vector2Plugin_SetRelativeEndValue_m4669281FAC1BC5858C42AE3F74F570F8FDC8B511 (void);
extern void Vector2Plugin_SetChangeValue_m8EF0B36B68AA1D37982E0A781448A02363BF0505 (void);
extern void Vector2Plugin_GetSpeedBasedDuration_m8F03E4891B90E95E147600C8652258F403DBD9D8 (void);
extern void Vector2Plugin_EvaluateAndApply_mC7663D348A00EB794D5807C104ADA372A7032339 (void);
extern void Vector2Plugin__ctor_m2023C1B763C66A6B5DADA4EA95AABC590996058C (void);
extern void Vector4Plugin_Reset_m77B2CBFC27C6F6F64FA0BB07FBBD72A7B7C9F360 (void);
extern void Vector4Plugin_SetFrom_m8B96E0FCBA5D14E5E5D6D8C37EE36704D270A264 (void);
extern void Vector4Plugin_ConvertToStartValue_m1D126F93BDF693BD05F2A4E3379CA87B979FD512 (void);
extern void Vector4Plugin_SetRelativeEndValue_m69588D0E049A70F6B0ACABD34DCBDF8E19CC5652 (void);
extern void Vector4Plugin_SetChangeValue_m69D1C0143C4D88BF84ABF66533A0E815D26F6FBE (void);
extern void Vector4Plugin_GetSpeedBasedDuration_mBA1AF59D5B2CB2A44BFED869AAAD474902E7122C (void);
extern void Vector4Plugin_EvaluateAndApply_m96933D49521F09B4CE403FD68EEE58B3EC5CCF33 (void);
extern void Vector4Plugin__ctor_mF9E2DC11518FE5B03FE1BE2B6C930318307C2002 (void);
extern void StringPlugin_SetFrom_mA6E09A5AB048B4210846D451F5D83821518CA7A3 (void);
extern void StringPlugin_Reset_mCE185934F14CBC66803FFE6326EF09B5D8B3C0D8 (void);
extern void StringPlugin_ConvertToStartValue_m34D7B1A6492C9FBCA91B13E86865E1C450E231A6 (void);
extern void StringPlugin_SetRelativeEndValue_m281AAEA3815035C5354255DDEA8AE9AEBAF7B81E (void);
extern void StringPlugin_SetChangeValue_m154A8DEFEC01D8C683858C11602A2CB62123841B (void);
extern void StringPlugin_GetSpeedBasedDuration_mA2F5245600035283DBF76137C655DC84268B0B4D (void);
extern void StringPlugin_EvaluateAndApply_m9D74DC7327571231B9476B2B284111AD39F0A927 (void);
extern void StringPlugin_Append_m393FFA9AFB5B28A407DEE6BEA599F096A2AD4BD1 (void);
extern void StringPlugin_ScrambledCharsToUse_mB94A339E51635D7F97F27274382BEF24D40A85FB (void);
extern void StringPlugin__ctor_m431ECBCEC5363E152AA20286544F3444FC0BD70A (void);
extern void StringPlugin__cctor_m43D24AE51E83690FDDDF7DA16CF9BCD78E38672C (void);
extern void StringPluginExtensions__cctor_m0EF08249B07EA4E0C6FADEC570B9B33E308FEDC7 (void);
extern void StringPluginExtensions_ScrambleChars_mEF2DC4717926EAEECA971FD887352673AC8683F2 (void);
extern void StringPluginExtensions_AppendScrambledChars_mB1EC8925920C497377D1B1E8958C0B13BF88AF3F (void);
extern void FloatPlugin_Reset_mDCE52CE11F99836C0653864F7F58BD29B65439C4 (void);
extern void FloatPlugin_SetFrom_m232CA49201386D4A445C495174456277698E3356 (void);
extern void FloatPlugin_ConvertToStartValue_m0158D70929F3E0AAD5ACB0085AC8E55729019B6E (void);
extern void FloatPlugin_SetRelativeEndValue_m62E86FC1881D233AF898B585417FCC59AB8FC9E4 (void);
extern void FloatPlugin_SetChangeValue_mBDCF4EFF8AC92B8BF2CBFBC4A9B88474C976DE50 (void);
extern void FloatPlugin_GetSpeedBasedDuration_m069F5CAC863F1D352534798BA23B9CD53ADA76D0 (void);
extern void FloatPlugin_EvaluateAndApply_m4605FA536FD5D066DCAD0416FEA5AA5D24DC3D66 (void);
extern void FloatPlugin__ctor_mDDE3C38DE5AF13E77CCAFC0F11823A547D4723C7 (void);
extern void Vector3Plugin_Reset_m3917A46D223807D40CA67E69C3F45FA2E00AA338 (void);
extern void Vector3Plugin_SetFrom_m675F63D9FD954A1B29876A6E2C5E445476D54046 (void);
extern void Vector3Plugin_ConvertToStartValue_m42E52ED0EAAE4671EA20E8261F495A1657BBFBC8 (void);
extern void Vector3Plugin_SetRelativeEndValue_m2C1B8ECD82B9174C83EB7E1E4FBE121F54209249 (void);
extern void Vector3Plugin_SetChangeValue_m08F27B7FB6DE60F46C78163F088FA5E41DCFA98C (void);
extern void Vector3Plugin_GetSpeedBasedDuration_m415AFD7A8572E5DC5A8BF1601705B12967BC5317 (void);
extern void Vector3Plugin_EvaluateAndApply_mA5AA5B5C049C1DD2CA0CC96BC2C0058C452E4FF3 (void);
extern void Vector3Plugin__ctor_m83B9306A6E1F7F6A4ED2F534D0614235705C8AEC (void);
extern void PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060 (void);
extern void QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E (void);
extern void UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC (void);
extern void Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589 (void);
extern void NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E (void);
extern void ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4 (void);
extern void FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE (void);
extern void RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300 (void);
extern void StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397 (void);
extern void VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE (void);
extern void SpecialPluginsUtils_SetLookAt_m032BE1B008809A24C0B4BD53A9689F7DD70F7FCF (void);
extern void SpecialPluginsUtils_SetPunch_m65C9920310ABC6FC6C483A6CEC088F3A360EE3CA (void);
extern void SpecialPluginsUtils_SetShake_m6F23CBF51E66BEAB3791161223E67374E976FF20 (void);
extern void SpecialPluginsUtils_SetCameraShakePosition_m49B0FC03FA80CF51647589CCCB44BA1487B037A3 (void);
extern void PluginsManager_PurgeAll_m9EDAE828FEBAFA93291F199449C5AE6648FB949D (void);
extern void CubicBezierDecoder_FinalizePath_mE98400AC5E2006EC8BF639774C71A37C3E47E03C (void);
extern void CubicBezierDecoder_GetPoint_m1FA6505950448A4973E190441F2762203F2C0C71 (void);
extern void CubicBezierDecoder_SetTimeToLengthTables_m8DE088CFF73E9F1E1A90A428D307B856FC92094C (void);
extern void CubicBezierDecoder_SetWaypointsLengths_mC384E54972E37D9157D8799E6E502BB0093CBC40 (void);
extern void CubicBezierDecoder__ctor_mEF0D332E62BCB16F6DA71192746F7A43A9B3B461 (void);
extern void CubicBezierDecoder__cctor_mF060F45215DCDEF59F446852B0B71F2E578B015D (void);
extern void ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668 (void);
extern void ControlPoint_op_Addition_m273B684A11735A299C1886F36F41C7C6CB27E49D (void);
extern void ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236 (void);
extern void ABSPathDecoder__ctor_m9000360B01DBC18CF8310F47FFEC61E0FC0E4C96 (void);
extern void CatmullRomDecoder_FinalizePath_m9BF4FC78055D6A3E593E05BD4B05A496215A4D4E (void);
extern void CatmullRomDecoder_GetPoint_mCDAE4F84C87110712EA863C289A715BC1E232835 (void);
extern void CatmullRomDecoder_SetTimeToLengthTables_m0E06C670A5FBDCA57207AB97B869ED8191D3ABED (void);
extern void CatmullRomDecoder_SetWaypointsLengths_mFB5DE54C8D41124A153BA13614F79F49BEC3ACC1 (void);
extern void CatmullRomDecoder__ctor_m40C3B62E2B6753C8C604B192AE38CB0E00751B63 (void);
extern void CatmullRomDecoder__cctor_m171A41BE4CB68DC3602E24837E2F5E067FB150D0 (void);
extern void LinearDecoder_FinalizePath_m8A38C47B480F0AFC98FDC223622982AED28DAF65 (void);
extern void LinearDecoder_GetPoint_m923A45C4AECF4B832BACDFE7D338611E3014172E (void);
extern void LinearDecoder_SetTimeToLengthTables_mB336D6CE0306247E6C80F9B472E86F744520550B (void);
extern void LinearDecoder__ctor_mD23879EA491AC8A5105D0FC69F6D16BDA0998CA5 (void);
extern void Path__ctor_mE17CD95D405E8FF0440A6631D97C8876074B4824 (void);
extern void Path__ctor_mE316C2800B03412006B86883F27ECC5CC08CA3AB (void);
extern void Path_FinalizePath_m2D9A9AE99327DBCDF8EDBAE55E149ED1F4BD3BB7 (void);
extern void Path_GetPoint_m6D04BA28C0F375D3030DD2714E2295A2A656AFE1 (void);
extern void Path_ConvertToConstantPathPerc_m946321C867B331E56BFBF358F57391804611B570 (void);
extern void Path_GetWaypointIndexFromPerc_m3C41176271872EA1D2B5AE1C18EFAAE729CB2C00 (void);
extern void Path_RefreshNonLinearDrawWps_m80E683DA112432AAE39D0E8C3F2C8B41CBB60285 (void);
extern void Path_Destroy_mB5139AE354F434F76149B4672ACE7B835FBE029C (void);
extern void Path_CloneIncremental_m02F0B70C4F51FBAEA81DB888E37FBA2E4962D428 (void);
extern void Path_AssignWaypoints_mF34F707A39321C2912B6C7E4D23DAEEEEF61D2F8 (void);
extern void Path_AssignDecoder_mD3A97C75986B01E822BF9726461B65DE77BC0B98 (void);
extern void Path_Draw_mAE9CA81D5ED87ECC9C1213EFCA01639CF0AE2B32 (void);
extern void Path_Draw_m1D276328F6B71518A310A8D1CCB4B29B5455E33C (void);
extern void ABSSequentiable__ctor_m70B4D2A525C71C87049ED3177A821B264D682A7B (void);
extern void Debugger_get_logPriority_m7AC0D46AE0BA9AC3ED1ED50AD20734033D16D0AC (void);
extern void Debugger_Log_mED54C2BED376B5F8631F5B23490BE1608675E456 (void);
extern void Debugger_LogWarning_mB528DCD3175EB2D670A62A6507A656F8DE76D06E (void);
extern void Debugger_LogError_mF9DFC776376FF7455755A73F555197E68A45124F (void);
extern void Debugger_LogReport_m2A1E8B46BC302E45B799171C5C51BFA4DD07693C (void);
extern void Debugger_LogSafeModeReport_m2B8F77FCC5EAA0AD7F936AF62EFBC4FDE5D19E71 (void);
extern void Debugger_LogInvalidTween_mEC44C60EC29E4EE401EFE473B01A209D79F3A83F (void);
extern void Debugger_LogNestedTween_m409C30CE26DFF284388E93FDD37738CF2116F128 (void);
extern void Debugger_LogNullTween_m9C6A433A68F2C3DFCDFF127434135F8BBC116740 (void);
extern void Debugger_LogMissingMaterialProperty_m49458D73DDB5BBBFB7AE84BA0A1C638A6A5B27C7 (void);
extern void Debugger_LogMissingMaterialProperty_m8847B17D16E01FB04A460D0984CAD3FEEF9AB854 (void);
extern void Debugger_LogRemoveActiveTweenError_m30A8AF0E0595F76045BF409584252064AE8460CD (void);
extern void Debugger_LogAddActiveTweenError_mAD5420AA037EB3CAAF612D228AF6055A0D80200E (void);
extern void Debugger_SetLogPriority_m14859DD9F9DD27E2D875BF50939F31D0F7043D6E (void);
extern void DOTweenComponent_Awake_m2B8731C5F756551F8CF374ABA9BBA5D97C36A956 (void);
extern void DOTweenComponent_Start_m14BD516448C0D8B487AA199C84AFA5984EE38E71 (void);
extern void DOTweenComponent_Update_m1EB628942CC4EA34F24E9AA2326532D8AD5B0541 (void);
extern void DOTweenComponent_LateUpdate_mB523F2163A4033594262DB217EDC62C6806DBAF8 (void);
extern void DOTweenComponent_FixedUpdate_m5D4B2412E608B1F490333A9A5680074A5C02A7C0 (void);
extern void DOTweenComponent_OnDrawGizmos_m28A4D3B2A76DC3FE70A1BBAE4D5E993624526FCE (void);
extern void DOTweenComponent_OnDestroy_m813D82282FC9C21C3DF589E5A954FA11FAFEA3BA (void);
extern void DOTweenComponent_OnApplicationPause_m93370BF7B8AA301B841B0BBCC61E9579AD3CDDF7 (void);
extern void DOTweenComponent_SetCapacity_m3B4936853427792607B2BC9A022300724C6275B8 (void);
extern void DOTweenComponent_WaitForCompletion_m1F26E152C4B17EF6797B49AD8070C40B872D615C (void);
extern void DOTweenComponent_WaitForRewind_m7719D9F5E014FC7403F70045B4E732D40428CB27 (void);
extern void DOTweenComponent_WaitForKill_m53130F016C0B24C5345C66972D287236F374AC37 (void);
extern void DOTweenComponent_WaitForElapsedLoops_mE9D67BF81141C1D92F85ECD20FFC51E2E8777774 (void);
extern void DOTweenComponent_WaitForPosition_m0F07976F5331F98F88289288ACE220D0F708FE84 (void);
extern void DOTweenComponent_WaitForStart_m91F59DF680F874160B5ED20691215591B75A4B65 (void);
extern void DOTweenComponent_Create_m2F098BE99694BEFEF39E22C3D8294ED04E9A11D1 (void);
extern void DOTweenComponent_DestroyInstance_mA0743B89EF2340212A90DA6C777A021BB346723E (void);
extern void DOTweenComponent__ctor_mA7CF37E0C0B8644ED78CCB9DD9A0386349236501 (void);
extern void U3CWaitForCompletionU3Ed__15__ctor_m278DB61099F91A9B8C609CC1E4466C55A0D08E66 (void);
extern void U3CWaitForCompletionU3Ed__15_System_IDisposable_Dispose_m6D566F2A4C22727AD6BB0A66A6477AF70790A3AB (void);
extern void U3CWaitForCompletionU3Ed__15_MoveNext_m851444820FFB5D7235E16C907DFDE051B85B2595 (void);
extern void U3CWaitForCompletionU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA089CFA8B6389FF6E74FB49F1472EED9B38D73CA (void);
extern void U3CWaitForCompletionU3Ed__15_System_Collections_IEnumerator_Reset_m8B440D87F90F8ADA7DA25F3330972D9392124D50 (void);
extern void U3CWaitForCompletionU3Ed__15_System_Collections_IEnumerator_get_Current_m87F9F17886D192533FED668E98D9290966811332 (void);
extern void U3CWaitForRewindU3Ed__16__ctor_m16EC7A1FA30FBBDE1C2ADA8F638D4DC1E9877D62 (void);
extern void U3CWaitForRewindU3Ed__16_System_IDisposable_Dispose_m1150BEB141FDD5CD21CB00BEC0D9475F8F0D64A7 (void);
extern void U3CWaitForRewindU3Ed__16_MoveNext_mD150954F5ADA8738216554F8875BA25203E1CBAE (void);
extern void U3CWaitForRewindU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE4451A369CF8E676AE2AA87E70C71A815ED9AE53 (void);
extern void U3CWaitForRewindU3Ed__16_System_Collections_IEnumerator_Reset_m9DA203D99A0B394909DE3DC47E409FD82175F4B6 (void);
extern void U3CWaitForRewindU3Ed__16_System_Collections_IEnumerator_get_Current_mAE8AE36BC00FAC53F580B5763D932CC693F7FA05 (void);
extern void U3CWaitForKillU3Ed__17__ctor_m046008403D3297CB3BEB9E55302B73D9E0FD079F (void);
extern void U3CWaitForKillU3Ed__17_System_IDisposable_Dispose_m339BA2E6D307212AFDA912CBF566F92F6F490F38 (void);
extern void U3CWaitForKillU3Ed__17_MoveNext_mE2C11A5D1C6BC8D7C5794C2378C56CCAE8627FDB (void);
extern void U3CWaitForKillU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m052E324FBD92CF1ADF0C3AAD3EC1A1A65EDB82DB (void);
extern void U3CWaitForKillU3Ed__17_System_Collections_IEnumerator_Reset_mF31474C7A6F1D540D5E0B8C82ABC03133B0408C6 (void);
extern void U3CWaitForKillU3Ed__17_System_Collections_IEnumerator_get_Current_m09FA39FEE3BF8E91C9015A27818F9CF15B1262B4 (void);
extern void U3CWaitForElapsedLoopsU3Ed__18__ctor_mB348D70420323372FE35F14C69D22BC361F99800 (void);
extern void U3CWaitForElapsedLoopsU3Ed__18_System_IDisposable_Dispose_m973DEB809C01D899B2D92C3BC4F42AE609CD2BA2 (void);
extern void U3CWaitForElapsedLoopsU3Ed__18_MoveNext_m79044A48FC9C1648D844BECF7EEB914341E97B08 (void);
extern void U3CWaitForElapsedLoopsU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6D815BF3D4EB44CD702B4964924346E0C33BC6E7 (void);
extern void U3CWaitForElapsedLoopsU3Ed__18_System_Collections_IEnumerator_Reset_m9DC4ED99A6402A5C717F5653E74A88D81518DB3C (void);
extern void U3CWaitForElapsedLoopsU3Ed__18_System_Collections_IEnumerator_get_Current_m9D9C76BEAF69EEC3A4852EE77F4706DAED8D9F19 (void);
extern void U3CWaitForPositionU3Ed__19__ctor_mEAEA31DDDF69E86D1C19E336AE2A0F319B3BB0A1 (void);
extern void U3CWaitForPositionU3Ed__19_System_IDisposable_Dispose_mEA96DC849CF8E202B2030E7BF893E6EF28842025 (void);
extern void U3CWaitForPositionU3Ed__19_MoveNext_m1D0D9960D10990576643F78ABA51C9B6E29FC4B8 (void);
extern void U3CWaitForPositionU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDECF758E238727FB9DB51F72930EC884F568EDCD (void);
extern void U3CWaitForPositionU3Ed__19_System_Collections_IEnumerator_Reset_mE6517A33DC8BEA448978248437AD244E5B8BED17 (void);
extern void U3CWaitForPositionU3Ed__19_System_Collections_IEnumerator_get_Current_m34DE755B710583B60DD01095BC56A6FDF0D0B722 (void);
extern void U3CWaitForStartU3Ed__20__ctor_mC8DFD3FE03D432361CE1FE97EBE2CE2762625243 (void);
extern void U3CWaitForStartU3Ed__20_System_IDisposable_Dispose_m44DAF5DCB78298ABE23D3749FACA4724D2464905 (void);
extern void U3CWaitForStartU3Ed__20_MoveNext_m6BF5F12952DF53D36D2A890B68FBA6C58177FD93 (void);
extern void U3CWaitForStartU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8FC5EA116F53A193BD651D6B1F649D7621DF3C72 (void);
extern void U3CWaitForStartU3Ed__20_System_Collections_IEnumerator_Reset_m8B2E51695757C5AD6B3BF1F6F1E09EEAF16132DF (void);
extern void U3CWaitForStartU3Ed__20_System_Collections_IEnumerator_get_Current_m7182BC5A22362E35A2DC275208B5B6B8EA87A660 (void);
extern void DOTweenSettings__ctor_m887E5989C5E07DD06A56C7BB9651D513DBE8E04A (void);
extern void SafeModeOptions__ctor_m30119D0CFA2C471B7AA0F2AFBE5AE8130D4AADA7 (void);
extern void ModulesSetup__ctor_mFC121E34A299D3C21E06DDCDA8392FF3EC4CDB28 (void);
extern void DOTweenExternalCommand_add_SetOrientationOnPath_mBD8B37B1978CBE4534F9B8868C734F5C0A2B2804 (void);
extern void DOTweenExternalCommand_remove_SetOrientationOnPath_m552309BE5B7623397429DDAC02D5C6FDB0C0FB44 (void);
extern void DOTweenExternalCommand_Dispatch_SetOrientationOnPath_m66381EB3F8DF2CB2F8D0FF0462C40E2AEA21EE3C (void);
extern void SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5 (void);
extern void SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9 (void);
extern void SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133 (void);
extern void SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98 (void);
extern void SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790 (void);
extern void SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082 (void);
extern void SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840 (void);
extern void SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C (void);
extern void SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20 (void);
extern void SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70 (void);
extern void TweenManager__cctor_m32BF1B0E0166CC1C1832F3980654478B2C445F07 (void);
extern void TweenManager_GetSequence_m64E8F2E01C9BDE2A4006C4265C84614F1DF92CB2 (void);
extern void TweenManager_SetUpdateType_m4C1E97DD675C70ABA66E53D283EDABB6337E44B4 (void);
extern void TweenManager_AddActiveTweenToSequence_m2D83B988481444A51AA9330D5AB9E63BF1FD3B18 (void);
extern void TweenManager_Despawn_mF56A8F1D2B480230F68F0149F6122D0BBAA129FC (void);
extern void TweenManager_PurgeAll_m78AD5E228A1158702999D39817AFA01A7D413F16 (void);
extern void TweenManager_PurgePools_m00794B3E7D39DF9078C9524DA48A8E9C500F9C05 (void);
extern void TweenManager_RemoveTweenLink_mA00A9ACA1D2E241FF5E4C63D88686194CB0D092E (void);
extern void TweenManager_ResetCapacities_m6B74BE623F2ADFCDD966F76EDBF2B2D5DD7898B8 (void);
extern void TweenManager_SetCapacities_m1A1D0CA467C77D297C7CD9AB105DB5609112175F (void);
extern void TweenManager_Update_mB27C9AE8D928418163CE32FD6EF1C82F9A9F63C7 (void);
extern void TweenManager_FilteredOperation_mEF0C87B677542616CB4324153651CA06F548BE39 (void);
extern void TweenManager_Complete_mCE26F442C189C358288018529BD88F0F1D99C390 (void);
extern void TweenManager_Flip_mAE0BD66D990CE593A474271E4D48B994451AF5B7 (void);
extern void TweenManager_ForceInit_m490AB9D6D7F427844FBC663194066EF146853737 (void);
extern void TweenManager_Goto_m04F0FF9EC0394154CCFE8552D797374147D31CDC (void);
extern void TweenManager_Pause_mF53E744566930DFD9A6642B63E872D12F23B4269 (void);
extern void TweenManager_Play_m16548C8C4203A88870C6E54E44F03CDA11763DA2 (void);
extern void TweenManager_PlayBackwards_mCC2A5578B578BEE7005204D8D7A546957BE605E9 (void);
extern void TweenManager_PlayForward_mD5568593F49E0036FA185E22CC1B24A22B35DEF2 (void);
extern void TweenManager_Restart_mB3E06722F56E0371559BC8DA43D5D09D8D391A7C (void);
extern void TweenManager_Rewind_mF610181130C4218E0F462966D54EE193E8171A7F (void);
extern void TweenManager_SmoothRewind_m3C5DE57B8A3DDE3EBBA223EC0AA2CDFD9E567E82 (void);
extern void TweenManager_TogglePause_m9352B05E74FE75A5CC99DF8E0AB7B03D020B9C49 (void);
extern void TweenManager_MarkForKilling_m13B34939F0A6AC4BAB7D3FE2CDD8D3AF895C31EB (void);
extern void TweenManager_EvaluateTweenLink_mE5358C53588B9201A77D284C6EA08C13090F5398 (void);
extern void TweenManager_AddActiveTween_mDE4BACC61DA83AB6BD50B1AA57CB16F40272CD05 (void);
extern void TweenManager_ReorganizeActiveTweens_mB40CEDBB24B90540B86274D77D7795EAE1330EA3 (void);
extern void TweenManager_DespawnActiveTweens_m61D3A0AA83721EB57E1E3DF0C9C75F9F71632AC3 (void);
extern void TweenManager_RemoveActiveTween_m666B3E84DC9E1CD8F8E8F79A44EBFE16CA03AE49 (void);
extern void TweenManager_ClearTweenArray_mA86AEF4A53B4DCEF3319D39B9769AECE4E0093E8 (void);
extern void TweenManager_IncreaseCapacities_m5316415F2F95212320B0745CAF123B139A8CDA62 (void);
extern void TweenManager_ManageOnRewindCallbackWhenAlreadyRewinded_m1054CA72AB6AC784DD9A50AE4F89EE2D4DC38480 (void);
extern void Utils_Vector3FromAngle_mB6466F235E3686C580BAC10255C6284AE704AC6B (void);
extern void Utils_Angle2D_mA49F298AC1F19FAF2D0791E9E5424015DB0BF1C4 (void);
extern void Utils_Vector3AreApproximatelyEqual_mF1A29021A7E98EB0E9847B4EB711D9446469B12B (void);
extern void Utils_GetLooseScriptType_m351AF7C36684EAC083A58197F93D49B7DD9A3EC1 (void);
extern void Utils__cctor_mE6DB1A2102B2BF7FEDBEE5A07EF1BAAD8848B548 (void);
extern void Bounce_EaseIn_m1253ADF94B39EF139C56EEE7AD5EC2F5E0C29769 (void);
extern void Bounce_EaseOut_mEF5499252352724ADC55887B541B5053DACFC27F (void);
extern void Bounce_EaseInOut_m6D386BE3A485A50DE77E480D40008D01ADDE79F0 (void);
extern void EaseManager_Evaluate_mD683DD74534996BFA45C075DCC2927E089C4E26E (void);
extern void EaseManager_Evaluate_m26A532BC322B246C5CE9D45ABC16384F58F8389A (void);
extern void EaseManager_IsFlashEase_mB7D47A96B8C663F7FA56AD8D10B9586C76204FF6 (void);
extern void EaseCurve__ctor_m29BD0E232922C8CE2E4AC877F79EF6096C34EDFB (void);
extern void EaseCurve_Evaluate_m036A88A768920A29FA6C7EA6E78646F679C9DFEE (void);
extern void Flash_Ease_mA28C135D4B118A9A4469FEFFDEC3329226E6A096 (void);
extern void Flash_EaseIn_m7A2DCE17466DCF086004A0147F534851240EADFA (void);
extern void Flash_EaseOut_m306F24AC2A2EF38682E1C0AB8834FAC036658955 (void);
extern void Flash_EaseInOut_m71C9C9CD9B50F446B635F0ABB97D1AFA7F52F4A9 (void);
extern void Flash_WeightedEase_mF9EC6A43BAEE75E4D2E93FCA21E099B0FA8CCA35 (void);
static Il2CppMethodPointer s_methodPointers[602] = 
{
	Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E,
	Color2_op_Addition_m4BD6D878284D56DB00BF838BFF135155E70D6C1A,
	Color2_op_Subtraction_m78CDC06AF474D662931568BFA73CD8C477BE2D99,
	Color2_op_Multiply_m1AE5DF5597AA4375991E023FA04AABAAB64870C0,
	TweenCallback__ctor_m68CC9304423CBDE43001F9B1413B5DAAF70DB621,
	TweenCallback_Invoke_mE4105043678D7086C7740B0D7B7589B734C14E1F,
	NULL,
	NULL,
	EaseFunction__ctor_mD630BE102357BB21BD878DF5E98F90BFE785A0F8,
	EaseFunction_Invoke_mC30ABF785F84A8769541950EDC3C2CB0B8F6FB8D,
	DOTween_get_logBehaviour_m50FAF61152D634B61FFA7D3B04F7C2A10E6E9B97,
	DOTween_set_logBehaviour_mD910C3B966CBE72E0ED43B7EA96BA06331090D6A,
	DOTween_get_debugStoreTargetId_m96F1367AD4955D84B2284A01EDD146DCE64B3A75,
	DOTween_set_debugStoreTargetId_m9A024F9090E856AC801B9DD5AECE4B8CAFBD96A5,
	DOTween_AutoInit_mF7B0D31019E4A0D0212902AC43E359A1BD763C29,
	DOTween_Init_m31648CA12FD2195F125B2B4773B7BF8DAFA11080,
	DOTween_Clear_m6CFE7E673765E730176BF919B55B1D7C1A923075,
	DOTween_To_m9C9EBC0FB6CF94364DD4FF85C476D8EE0A7FF4B1,
	DOTween_To_mA4E61D06204BD01537C08EEB9ED148C18ABC75ED,
	DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680,
	DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8,
	DOTween_To_m7A731ADF3CCD5C4439F8710B2CD16BC6CEB051D0,
	DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339,
	DOTween_To_mA77855459ADB369B17DB84A390E88A1CC27868F4,
	NULL,
	DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1,
	DOTween_Punch_mD470A46B3BEC0E312D0438C14C0980E853CA4D32,
	DOTween_Shake_mD96D04E4D767144E80C613CA7B49F55BF36E10B3,
	DOTween_Shake_mBF64A5C44F1258CE3B097F89352473FC6B1B298C,
	DOTween_Shake_mF69B5F924A2D3633342CDE459375D677A930820B,
	DOTween_ToArray_mEC8B5DBDFCBC6DE60994B6E6DD0CCDDA43F001AB,
	DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89,
	DOTween_Complete_m12E2987F7D42218DCCE051E2FDB803E75FD8BA91,
	DOTween_CompleteAndReturnKilledTot_m77F801701C03DB55B442098F543A09F2C5600452,
	DOTween_Flip_mCE1C7CBA31A981D035B52150957C3A01B7F2A978,
	DOTween_Goto_m19ABF14E2B1ACAFFA17E8AB48AEE2207B6FFA6C9,
	DOTween_Kill_mAB4C96CE1F1BCF25E5347AE0FC295D064EA53FB2,
	DOTween_Pause_m498BECFBBC8FBD76425B8AE1F38E2ECC9AE296D4,
	DOTween_Play_m466F46F9DF6585E17C595438BA15147319540DC4,
	DOTween_Play_mEFD3A1E3CC218D3916032325F2E119C2004D9473,
	DOTween_PlayBackwards_m3709E26071CCE5CB368584477DF2FC9A8B9D052B,
	DOTween_PlayBackwards_mC4FB110A49C220C2B5FE768989D133F0F8721FA7,
	DOTween_PlayForward_m4F27092024989DEB74466D9D9C370D5FA6621DCF,
	DOTween_PlayForward_mC1FF85BCF9DC773A3E4FA3DC12377D1F9E1BADD4,
	DOTween_Restart_mEE2F85FC8741BBCE7C1E76C143B3DCE5B9C78DEF,
	DOTween_Restart_m89E03C91814831E13EA36751C4A8471CF41FA249,
	DOTween_Rewind_m5C4020E9007FAAF719C1BE01CE440EBB39193619,
	DOTween_SmoothRewind_m36B53DA282A0BC7948453A6725939D60A33D5C22,
	DOTween_TogglePause_m8CE7CA00FE30F3C926F34362E2DE67A161536598,
	DOTween_InitCheck_mA3F71F5F48DEF60104F960552849E293CDDEBA7E,
	NULL,
	DOTween__cctor_m278575843F5C4300324E26C3AC8D91B665F2F155,
	DOVirtual_EasedValue_m40F83FCD3705E8DD33558C84B3F7067D91F3B9DF,
	TweenExtensions_Complete_m27D13C838F7DCBFE14632132B60338F42B3ACB73,
	TweenExtensions_Complete_m5B347FC17642C404A25E17D231836B2BE39E0632,
	TweenExtensions_Goto_m0256CB1AA2FAB6786021BF3322D737AE4D37B2FC,
	TweenExtensions_Kill_mFFDE4DE45D8A91C357ABFF2F8FA95D412924F466,
	NULL,
	NULL,
	TweenExtensions_PlayBackwards_mF585684B8D5082A2A895C628F372FDABF690E0FB,
	TweenExtensions_PlayForward_mE9D0A1BCADDDFD7429F97CF8AE9BE9D1E81F6439,
	TweenExtensions_Restart_m4080AE8184C33AFCB12BFBB850424D13836E6214,
	TweenExtensions_Rewind_m0ECE9F671C1A1BE35270FD24F9AC81DC5645DAF1,
	TweenExtensions_TogglePause_m6AA5BB6139190D6F7A5B1516802D650DBC813322,
	TweenExtensions_CompletedLoops_m5A7B5AEE691F491182E5FD7009C21E3BBC90CA8B,
	TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222,
	TweenExtensions_ElapsedPercentage_m2B88D6261A10FE69DA344E2EDF1D72F0DC7C4FA8,
	TweenExtensions_ElapsedDirectionalPercentage_m86020EAE1A0CA49FDE72191B7F45FEE990B9CFD5,
	TweenExtensions_IsActive_m7CB8E490D86B9E14B6B4B4004F1D199790397743,
	TweenExtensions_IsComplete_mBB619B64C19A85AB4EEEBB3D37D3E720A9E0AF4C,
	TweenExtensions_IsInitialized_m8C2B24B55147A6773849EB32D4D00E128CAB211B,
	TweenExtensions_IsPlaying_m32EF28DEB59B931FA4607BAC3BED0DE275A1D843,
	Sequence__ctor_mFFB83C470D70B8512E2B393A0C07D90FEC2CBC84,
	Sequence_DoInsert_mD948F47640F2159358EE6B18952EB77AEE6610F4,
	Sequence_DoAppendInterval_m0C3E54F3B28A78293C67DD1AFFBDB27A665E59BE,
	Sequence_DoPrependInterval_m5349CCACB49068164F7EEDE05FB705AA7458D248,
	Sequence_Reset_m006B0E92244A1C149A954C5183E9826DC5C828AE,
	Sequence_Startup_m76F5EC2C0703BDAFA28DA67BB0AD4B97BE0A9D4E,
	Sequence_ApplyTween_mC85811FDB77E639F3665931DD46CCC17626354A7,
	Sequence_Setup_mDCF62E1E0C88A3090CBDD2D79A544EF03150202A,
	Sequence_DoStartup_mEC96B51F5254BE451DD5CBFED2EFC13FE463F7F0,
	Sequence_DoApplyTween_m06E4746BDB1F214259ADC078EDD812BBBFCFC54D,
	Sequence_ApplyInternalCycle_m2B145923EEC8BE7893BB8F17B217F26318AC8B94,
	Sequence_StableSortSequencedObjs_mC3780B8F109A9114A2D5CE3C2605903E282E1FAB,
	Sequence_IsAnyCallbackSet_m98D9A7B3915C4C54A385E0F39646C3DF9D62A600,
	ShortcutExtensions_DOAspect_mE9ECE416D6C7FD3BDE1DB73DFBCAA738589062E2,
	ShortcutExtensions_DOColor_mC438262691549AA19473BFC4777A39E4DF995E03,
	ShortcutExtensions_DOFieldOfView_mDF8F791F7D4672A51988CE4F3434A589F0F7F62D,
	ShortcutExtensions_DOOrthoSize_mE96037CC51B44CB1B04CF436779EAF60BF6C0CD3,
	ShortcutExtensions_DOPixelRect_m30CEF5D00DEFB324BB8514B95CF0328929274598,
	ShortcutExtensions_DORect_m21D8649612CDF6196C7D4C591382743112FEAFD3,
	ShortcutExtensions_DOColor_mAC2C2C38A8C15064D70E6B39F5735A09DDA0D581,
	ShortcutExtensions_DOIntensity_m27397BA48763F123CFEE2F08D1B68C536BD1AE12,
	ShortcutExtensions_DOColor_m7770E3969D58563343B129139B857669312EFAA9,
	ShortcutExtensions_DOColor_mF41D0D0338A1C71122B56F08353CAFA9931B5183,
	ShortcutExtensions_DOFade_m1C499FE6483845A6BBE9C4EB80D11062C9859FC8,
	ShortcutExtensions_DOMove_m82274FDC0216A91A1FAF16844805D06BF9A287FF,
	ShortcutExtensions_DOLocalMove_m22F3EB581DADB5A3FC59B69F7F6F05A86F8E8348,
	ShortcutExtensions_DORotate_mA2804C1A3E4780383111262752CC7056BBC7D470,
	ShortcutExtensions_DOLocalRotate_m6EB8F37963023C6B157C60013B98D2B612816DA4,
	ShortcutExtensions_DOScale_mF7AC6EA0FD71B399776D758AD57B94F18A47F580,
	ShortcutExtensions_DOPunchPosition_mD022015ABB94942EE909F7F8E0F3660D52FA3D9E,
	ShortcutExtensions_DOPunchScale_mD7D825D1761F0264BC1D00027B79330844400B9A,
	ShortcutExtensions_DOPunchRotation_mDC55C1F23E2C17A4E9D4BF5BB787BB1DE98D7AC4,
	ShortcutExtensions_DOShakePosition_mEF231F12CB359BF88DEA0B5BF1480A270DE9366D,
	ShortcutExtensions_DOShakeRotation_m36228095EBEDF630E0B87230083E6947A04DBB5E,
	ShortcutExtensions_DOShakeScale_m6DF910EF19D54F5136100A2F3175B3DD53D85BBA,
	ShortcutExtensions_DOPath_m3E70D921DDA265292CF467212AC676371F110691,
	ShortcutExtensions_DOLocalPath_m4F4C77B2C481DDCB0FDBCE8B3C4442D897F1B2DA,
	U3CU3Ec__DisplayClass0_0__ctor_m14F8614CA85AED6EEB9F83CD5240ECB824FD30BC,
	U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__0_m3B8BE2D1BEEAEDD0102C3DC4A32B80C07DD8264F,
	U3CU3Ec__DisplayClass0_0_U3CDOAspectU3Eb__1_mDDD2F090D812F2AF5DC451DF365B91E91410B0B0,
	U3CU3Ec__DisplayClass1_0__ctor_mF7AE84F589865AFA97F3F0613C42A63F7ADF9FD2,
	U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__0_mB50622EF9AB0EA0CF6318319A488B134B1E441EA,
	U3CU3Ec__DisplayClass1_0_U3CDOColorU3Eb__1_mC208F6B646E6706D73391F28237DE9FB3E01C022,
	U3CU3Ec__DisplayClass3_0__ctor_mD3DB006B7B8A1A194339F0ECC23CA1E04787EFD9,
	U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__0_m15FBEF870514F3B7A0E107807AA4CC626C0A5B91,
	U3CU3Ec__DisplayClass3_0_U3CDOFieldOfViewU3Eb__1_mE0B6B23B688947A0F131A1CC31CE19D153052990,
	U3CU3Ec__DisplayClass5_0__ctor_m7783B6BCA09BDF4543ADF7F62889ABBA94DB3A3A,
	U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__0_m034E070A3B9354C6D1D5DE8A1B26FCF53208F73D,
	U3CU3Ec__DisplayClass5_0_U3CDOOrthoSizeU3Eb__1_m2AC152D033DE5ACFE3232261EF59650B917174AE,
	U3CU3Ec__DisplayClass6_0__ctor_m3EF7021A9BC9E02A6D31DAECC153759EF98F1745,
	U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__0_m056478689B03F8E3C264EF28603426A02853E288,
	U3CU3Ec__DisplayClass6_0_U3CDOPixelRectU3Eb__1_m5FD55DBC4B5BD7120C6D09D6155A91E15FAE6BE0,
	U3CU3Ec__DisplayClass7_0__ctor_m99FED5DE1D5FCE5A033A41690EEE45C7B02436FA,
	U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__0_m323992D6C4F67E9BCDAC970E9132420617355BEC,
	U3CU3Ec__DisplayClass7_0_U3CDORectU3Eb__1_m326FD99ABCA6F25C8F9BC3438FFA62CC05A6813E,
	U3CU3Ec__DisplayClass12_0__ctor_m77B82950D8ED2AE3169C0193888A86E0D538A5CB,
	U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__0_m32DE3BA95C1F0C32BE0552EE5599FFBB0FE97DF1,
	U3CU3Ec__DisplayClass12_0_U3CDOColorU3Eb__1_m4D05D90FBB17A5455CC66D17A83CF1656A87755B,
	U3CU3Ec__DisplayClass13_0__ctor_m06708EDA2D1D9444D4BBD595985B8EFFD0595710,
	U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__0_mCDD8C584608D56E4644AEEFEF0993C89615D2ADA,
	U3CU3Ec__DisplayClass13_0_U3CDOIntensityU3Eb__1_m91CCE11F8FA50E2CE4CCAF30A2CBD50FF6F6AEF8,
	U3CU3Ec__DisplayClass16_0__ctor_m2B3D033F265B069E406A5330FE153EDADECEBC28,
	U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__0_m68313755D418CC0E079ECB927D1DA4C137927F73,
	U3CU3Ec__DisplayClass16_0_U3CDOColorU3Eb__1_mBCABAA139578080574B0C7AFBD7938C6E49961EF,
	U3CU3Ec__DisplayClass17_0__ctor_mE986B184C7B7F578C4EFDDCDC57D33970F613668,
	U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__0_mF06E3103940FEA26AE34A10EB7737BEF9322200D,
	U3CU3Ec__DisplayClass17_0_U3CDOColorU3Eb__1_mD8C4BBDABA25C6A116793F58B6EED5DF8EDF4145,
	U3CU3Ec__DisplayClass19_0__ctor_m6A019E5099794BFD3EC9819B9C19AC696B218865,
	U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__0_m50D60171D21D0F9B87A60CA102A9CE3DAE504842,
	U3CU3Ec__DisplayClass19_0_U3CDOFadeU3Eb__1_m5203CDD55EDCD16C106CF2230BEB634973FA87C1,
	U3CU3Ec__DisplayClass32_0__ctor_mF99E40BAC9D70A41F9D49D720B24F55F5B514161,
	U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__0_m15E1AAFD0E46945E290D6BE466818E3ED7F7F6E3,
	U3CU3Ec__DisplayClass32_0_U3CDOMoveU3Eb__1_m580B3AFDB23D638C3BA6A8EE6DE4B6420CE8FE0A,
	U3CU3Ec__DisplayClass36_0__ctor_mAE58EFC3DD1DD7C1F36380102ECD20D2AA00F1CE,
	U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__0_m6AF2104AC0D94DF07EC176F21E354FE77D112485,
	U3CU3Ec__DisplayClass36_0_U3CDOLocalMoveU3Eb__1_m92CAF0D1D04465B4298655176590064880FA0DAC,
	U3CU3Ec__DisplayClass40_0__ctor_m8E6EAA117C9611630A2AF2B34959429DA441AF03,
	U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__0_m89100800A2C8745BE1323653608E893652183B7E,
	U3CU3Ec__DisplayClass40_0_U3CDORotateU3Eb__1_mE57D43D5CBC331CCB313FE97DF304962B4B61272,
	U3CU3Ec__DisplayClass42_0__ctor_m894ECDDCD8FA39381D104A78723ED0D3B0FEFA24,
	U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__0_m60162FBB56ACF20CFD036154E08DB13B16C3CE47,
	U3CU3Ec__DisplayClass42_0_U3CDOLocalRotateU3Eb__1_m8DBE8031D1E7627BE19794A3F97744BA72D358D0,
	U3CU3Ec__DisplayClass44_0__ctor_mC24979AC46FE287403167265ABFD5C08E7C62D33,
	U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__0_m0A1B1E2996430F9455E9FF81C312223B3C46CE35,
	U3CU3Ec__DisplayClass44_0_U3CDOScaleU3Eb__1_m95D31585339C2303A33F37C20E75697B13FC0FF4,
	U3CU3Ec__DisplayClass50_0__ctor_m341F0483D9CE810847D6A7850A9172093B714A8F,
	U3CU3Ec__DisplayClass50_0_U3CDOPunchPositionU3Eb__0_m303073F867A68180C2EEB67F1F51D98D8F269CC0,
	U3CU3Ec__DisplayClass50_0_U3CDOPunchPositionU3Eb__1_mFE149A4C533BF050157EE7790CFD07548A53437D,
	U3CU3Ec__DisplayClass51_0__ctor_m6993C5B08436C61812CD70E98322A42665AC82C9,
	U3CU3Ec__DisplayClass51_0_U3CDOPunchScaleU3Eb__0_m734D371379B3B6A6F92D6BDDCDF5B2A0C1A31693,
	U3CU3Ec__DisplayClass51_0_U3CDOPunchScaleU3Eb__1_m527EFAFDA64873F3E0E9C6F0A83675D4AD90E04D,
	U3CU3Ec__DisplayClass52_0__ctor_mF34A0E0114DC164E1A106453AA3A5B83EA8ACB6B,
	U3CU3Ec__DisplayClass52_0_U3CDOPunchRotationU3Eb__0_mA4391918ED1F4B585FE5EB19EE1CE16DFF027811,
	U3CU3Ec__DisplayClass52_0_U3CDOPunchRotationU3Eb__1_m4F593B3E41B2E9859050C5A4BDF54067E63E8880,
	U3CU3Ec__DisplayClass54_0__ctor_m8487623AB289AE5A29AAE68FBD16094EBED4619A,
	U3CU3Ec__DisplayClass54_0_U3CDOShakePositionU3Eb__0_m43FAC97692AABAE5A1C3025EA84D5F918B1238AE,
	U3CU3Ec__DisplayClass54_0_U3CDOShakePositionU3Eb__1_m14F132270B6638FF470D9DEC202349E651C36733,
	U3CU3Ec__DisplayClass56_0__ctor_mE3F409583C85BFFAD329D6657AE57326CEA380B8,
	U3CU3Ec__DisplayClass56_0_U3CDOShakeRotationU3Eb__0_mB2C6F5F6FAF6A27B193E1EFBC982F0662BBB59E6,
	U3CU3Ec__DisplayClass56_0_U3CDOShakeRotationU3Eb__1_m09F951E47B7ED5C1EB69455C495CBEC0422D79DC,
	U3CU3Ec__DisplayClass58_0__ctor_m53D84AC3C9E88A1B5850B1D7B2CF4A782504B6CB,
	U3CU3Ec__DisplayClass58_0_U3CDOShakeScaleU3Eb__0_mCCB34095300E1247CBBAF5C7A45E25EB062033DA,
	U3CU3Ec__DisplayClass58_0_U3CDOShakeScaleU3Eb__1_m42F5DCA159CCB3B8B43361B3CF9F7C4176212480,
	U3CU3Ec__DisplayClass63_0__ctor_mB96BCCBA4CD18893BF3FE33921D1A1349C579F9A,
	U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__0_m8D3440E5FE89819FD8DCDAFCFE7FC1AAFCEB92DE,
	U3CU3Ec__DisplayClass63_0_U3CDOPathU3Eb__1_m87BE9FA5B9FA36647B1AB9EAC06C498131DA3077,
	U3CU3Ec__DisplayClass64_0__ctor_m797499323817E936BD2292B270BFC32D447AD78B,
	U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__0_mDE31EA6776321007A3438FC8F6A329C3F1DDCC88,
	U3CU3Ec__DisplayClass64_0_U3CDOLocalPathU3Eb__1_mD3A0960DF5B0665A23903CAA811D3FF0703B2D10,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F,
	TweenSettingsExtensions_Join_mBA1D659EE0310BBE1F42148057403E7C5EEDB777,
	TweenSettingsExtensions_AppendInterval_m36B7A337E62568050B2A3220C9140D06CD50CD82,
	TweenSettingsExtensions_PrependInterval_mCC5525CBFA2E20938D4D095DF5F78720C23228E0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TweenSettingsExtensions_SetOptions_m94B0ECDB7445CABBF2814531E137F51582AE5425,
	TweenSettingsExtensions_SetOptions_m470EC93A8B43B25894F7143B876B117AFF2B000E,
	TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C,
	TweenSettingsExtensions_SetOptions_m6FA72AD20A82D69FDB0189B31D81B5A78653FF50,
	TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700,
	TweenSettingsExtensions_SetOptions_mDBFD729D4FEE37F7A828AD98BFC532A802617F08,
	TweenSettingsExtensions_SetOptions_m571339B54CA3A4BD935C07D719EE40E4FAE2C9D2,
	TweenSettingsExtensions_SetOptions_m3AF4D0166C4D467CCEFCB9B3116EA69D200C73E2,
	TweenSettingsExtensions_SetOptions_m5E6F9A1145201D9D34824DE50132581D3DDC8DCE,
	TweenSettingsExtensions_SetLookAt_m92E5AE38463525D9672DF0E98D6980096DBC2814,
	TweenSettingsExtensions_SetLookAt_m2D965125CC14890614DFBFF2A5E762852F9F1AC2,
	TweenSettingsExtensions_SetLookAt_mECF406EE42F980E66C745916D2BBDF63F7E4C0D6,
	TweenSettingsExtensions_SetPathForwardDirection_mF2A4AB7983A3BBDA380EA993C205C6D8AFE0B64C,
	Tween_get_isRelative_mC31C34D21C3953F9AA7F25C0429BEBE45D2DBAE2,
	Tween_set_isRelative_m881085052780C20122B970FA26766E551DA3B8EB,
	Tween_get_active_mD4253DD1A64623E342282E139081B787935A3E5E,
	Tween_set_active_m7E2D493098F2406830BAE9201422B8A1E7ADE2C7,
	Tween_get_playedOnce_mDA42B6964058549DB8BBC9217DBBB2F0EB67A335,
	Tween_set_playedOnce_mC95D34B48FDF13A9C3B8451B2794A9FDA537019F,
	Tween_get_position_mF8A2FF9C0DA291DEC595AC8C00E2E096A009B5A8,
	Tween_set_position_mFA8507C0C9F2E513D037EB506CDC444AC993F4B2,
	Tween_Reset_m7E3A4C092BDB502A8B12E5DBB461602400A31C8D,
	Tween_UpdateDelay_m3BCCB4073A5EEDBD7DCE43C250D8BB79D255AE6B,
	NULL,
	NULL,
	Tween_DoGoto_m1A61731CB2E2D27D1E08ADD844E575A5DAD8939C,
	Tween_OnTweenCallback_mAF944138F2F0D8BFF50EC9B5EC1C24ADD7623FB8,
	NULL,
	Tween__ctor_m92AEA714BCF3EAFB7FAE3E6714A03B8F2CB45D17,
	Tweener__ctor_m04B7FAE8742229AF46C846C73F08E0F12A943F26,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Color2Plugin_Reset_m4D1585F6639130F1C1D63606F8627B5AF78A54A0,
	Color2Plugin_SetFrom_mC3F20E4EDA65C6449C25BACD6DDB6C1199952155,
	Color2Plugin_ConvertToStartValue_m9F85F95CB7AA6AFCAE9F5F20AB785B89048FBDC2,
	Color2Plugin_SetRelativeEndValue_m18D1D370D75DDEF9636CBE1AA964BD11D98A9490,
	Color2Plugin_SetChangeValue_m72A9F3272BC54989B589E62DC802685AB4BCD641,
	Color2Plugin_GetSpeedBasedDuration_mBACAE2EDEDED990D2E2B4078D9F6255DC52C8EA2,
	Color2Plugin_EvaluateAndApply_mB30B49EECB72DA2E1CC2B8C6874B4DD4A9FEB347,
	Color2Plugin__ctor_mBCE84E5A0ECEAF61FFA54965D1341A4C24E3FA46,
	DoublePlugin_Reset_mC48E0D04271DED61DE1422AC67CBFB42DD39D065,
	DoublePlugin_SetFrom_m45DF6B4F897082911AB5E64942EB89FCBC61267A,
	DoublePlugin_ConvertToStartValue_m3BA8C6742F81078B49EDAE5BE1B277BD3BDD6768,
	DoublePlugin_SetRelativeEndValue_m16BB892BED6D8E6DA25C367941CE14D1B5117F88,
	DoublePlugin_SetChangeValue_m2F806A26CE2DB009ADF69F33C05A136E5F1455D6,
	DoublePlugin_GetSpeedBasedDuration_m0661238953316795039FB9C7D05DBD4591710C33,
	DoublePlugin_EvaluateAndApply_mFEA0376503352E121081A53927580DBAC2A29C19,
	DoublePlugin__ctor_mBC919A2E3792A7F49C9B42888306B7D328FAA373,
	LongPlugin_Reset_m37478A613E00FF0F05D3A58BEF11D649BFED5AC2,
	LongPlugin_SetFrom_m51A61DCC7C9CC9B0B32921B7A0B4A776A9441455,
	LongPlugin_ConvertToStartValue_mE222FBCEE94E48591988FEE4CEC005EFA70A1209,
	LongPlugin_SetRelativeEndValue_mF1C87E177CCEA2C17A5090CA9B1201F093BE1C7B,
	LongPlugin_SetChangeValue_mB10261C79DC621100E22EEB1DACA566C9C7AC3F4,
	LongPlugin_GetSpeedBasedDuration_mDC5560F8BC27B8917E870B1D3A2923E5E0C277C4,
	LongPlugin_EvaluateAndApply_mA80DD4E127270F467A5164D165557A5D78F3E926,
	LongPlugin__ctor_m2AD9AB566ED8B00E5944E0338ED86569A3E6A143,
	UlongPlugin_Reset_mA7D583EA46AE862A4A279127535806B67AC93863,
	UlongPlugin_SetFrom_mB55DCE5245B000AF510915EDF942F8C91DF2E8ED,
	UlongPlugin_ConvertToStartValue_m3F94E8568572F1E2BD3E03F043E625C2B356870D,
	UlongPlugin_SetRelativeEndValue_m6CE5E8F05C004378BB3CC4414FDA18B2F31C8C73,
	UlongPlugin_SetChangeValue_mD9BAA6099E44D738055E34640E4C33A4E0986C89,
	UlongPlugin_GetSpeedBasedDuration_mFA983E09C14C54961FE6DE1D3D3CCCE6DFC306B2,
	UlongPlugin_EvaluateAndApply_m582D6FF13C0F011E768C47FC428E5A9478A1AB82,
	UlongPlugin__ctor_m03E4C0827FFAC8322DEAC2D784708416A15DBD56,
	Vector3ArrayPlugin_Reset_mA352ED531258DEAD2F621144C41DB9258D0B9571,
	Vector3ArrayPlugin_SetFrom_m69DD6E6C7E4B79129DAEAF6B7D42272560894D62,
	Vector3ArrayPlugin_ConvertToStartValue_mE150B601DA8164B66376565D966458967B80849D,
	Vector3ArrayPlugin_SetRelativeEndValue_m301BAFA7B81A646DD9E0ACFE44A4E03DD3DA8762,
	Vector3ArrayPlugin_SetChangeValue_m6CCE20B567B1B0A291C3F99001D73DAAE4000173,
	Vector3ArrayPlugin_GetSpeedBasedDuration_m3DE9F80A2BB6E51D8306D5FD0B7394D299B09B3E,
	Vector3ArrayPlugin_EvaluateAndApply_mCFA6779FACD78B605A6C2F5DF4A9473DE2CA63E4,
	Vector3ArrayPlugin__ctor_m7E64B2C0B6FFD43FDEA1634185763064A0511EA3,
	PathPlugin_Reset_mF8A7585640B692E1337D76DC9E193C9EED0F7AC2,
	PathPlugin_SetFrom_mF6B10813AD9588EAA43C524A1A53886ECEBC45D4,
	PathPlugin_Get_m997FB98C4FEB9E5E74B71034B36E2A40D0153BC6,
	PathPlugin_ConvertToStartValue_m2DD9E300B9FF4CA1DCD746E8E16548AE29CA86A2,
	PathPlugin_SetRelativeEndValue_m41C96CE4555F5DA30851CB54577E1997D954F579,
	PathPlugin_SetChangeValue_m8C85C4B7E85276B4D0885D390DE430E3DD349CBE,
	PathPlugin_GetSpeedBasedDuration_mCEA473E67BBD3A8BE7356A87B2F69FB13FF59B3B,
	PathPlugin_EvaluateAndApply_m5ECDC4CA14C4570B1027D1E3049E1196048E2AB1,
	PathPlugin_SetOrientation_m3F5BCB1FCBB546654FEB270D7B568F1667E9977B,
	PathPlugin__ctor_mD8811EA5B57B17FC56CD3E926FF69CB41C5FC183,
	ColorPlugin_Reset_mD5C17D1107C847229AE3AF5513CB3FA5194961F4,
	ColorPlugin_SetFrom_m479A8C5CD0FCA868D41ED7A29A7214B511640CBD,
	ColorPlugin_ConvertToStartValue_mCFE988E065959F8C5898AAA3B209ABAFCD8AF3A8,
	ColorPlugin_SetRelativeEndValue_m66E263246FC9F63F50A4C5AFFC3C5E0D70B59E84,
	ColorPlugin_SetChangeValue_m185BDA16E9FFD1AE83152A1C8B4C55F13E4B74E7,
	ColorPlugin_GetSpeedBasedDuration_mABC5DCBD1B8D595243C93FA40FCDED9E25469AA0,
	ColorPlugin_EvaluateAndApply_m24B4CFBDB842298B9839B532CE19E43E97F7DC4D,
	ColorPlugin__ctor_m633C70643C227166D0DE895DCD198751ADB832B8,
	IntPlugin_Reset_mB5E54408080BA058CE3141E70024CF7BBD4124A1,
	IntPlugin_SetFrom_mD5482FE1E980BDD2A15F9EF5A3DBA53C84CC2C30,
	IntPlugin_ConvertToStartValue_m18C3236122A0CA21CCC67F1E04178CA298FDD21A,
	IntPlugin_SetRelativeEndValue_m2998B97675D44F79988F48E53F764D589E40A76C,
	IntPlugin_SetChangeValue_m0546A7F4D7D2E054EEBE0CD36AAD672E92F74C43,
	IntPlugin_GetSpeedBasedDuration_mDD8E0299B88828750EA43F995838A8A14F052CB7,
	IntPlugin_EvaluateAndApply_m5A8433003F21BBFC7A24C0E0D4D36C9EAC60C549,
	IntPlugin__ctor_mDD5E0E9B685B85810B0E99FD3A724390EE99C913,
	QuaternionPlugin_Reset_mF9544485D1461B1A60EC89CEAD333B5ABE4B3D7B,
	QuaternionPlugin_SetFrom_m06BACD08FF14D0BD3229DD0520C80C70C80E7F1C,
	QuaternionPlugin_ConvertToStartValue_m6D2E6DD4E47A7B6377A89DBBF830864048B739D5,
	QuaternionPlugin_SetRelativeEndValue_m5363B1A3F417A3EFA102252017BBABDEC0EB11D4,
	QuaternionPlugin_SetChangeValue_m40DCF9B299943301BBBAF466B57F8604C4475CD2,
	QuaternionPlugin_GetSpeedBasedDuration_m8BE69247A323044EABCF6D824250F3B9BEA9D9FC,
	QuaternionPlugin_EvaluateAndApply_mE095DCB28F57411C6906613CAD458765FFD9D9AB,
	QuaternionPlugin__ctor_m300B7DE9FF8C992D2A5A7E069AFD7A2054D9960C,
	RectOffsetPlugin_Reset_m94F0DAB51730C6879FDF35EC1B1063950C16DD93,
	RectOffsetPlugin_SetFrom_m450A669373A57D51711CA2F4BDDA5F47F6A8A3A9,
	RectOffsetPlugin_ConvertToStartValue_m0FD425737084A6587013AF55BAE8E4A4DF478025,
	RectOffsetPlugin_SetRelativeEndValue_mBD08440BECDFC30E1C2DF9019ED71925C23EAF35,
	RectOffsetPlugin_SetChangeValue_m56A91F6ABD143A79390BA34C4218B4A7F79D9D61,
	RectOffsetPlugin_GetSpeedBasedDuration_m0A00284A81996026211253E284C1FDDAE195E504,
	RectOffsetPlugin_EvaluateAndApply_mA4A5EA385BB4C1FFCFA7E0D6D3924F355C193E3E,
	RectOffsetPlugin__ctor_m889C8F4862C868D55130CED884544F46AA4A4066,
	RectOffsetPlugin__cctor_mDEE0140B33EF6EA1F0DC496427FBD4F505427071,
	RectPlugin_Reset_m608F606EA7062DD500E16CF96D44D1A803AC1CC0,
	RectPlugin_SetFrom_m364E3B14FB559ECA15C19CF930EBB07D3A619D71,
	RectPlugin_ConvertToStartValue_m3584F27A3B1467B5184137FC28302E391EB1C828,
	RectPlugin_SetRelativeEndValue_mA2683C712701D3C1978D2648A07C0567BCF11518,
	RectPlugin_SetChangeValue_m51E32400665D3D389B296EABAF8A9F31BD654466,
	RectPlugin_GetSpeedBasedDuration_m6898F84A4B2C11EA25628AF6C81261F6FD965D42,
	RectPlugin_EvaluateAndApply_mDE5457292DF3C29DFA2162AFF48D80A21FF819AA,
	RectPlugin__ctor_mC8C57164273B43CA3968BD9892AF8FAFF86431ED,
	UintPlugin_Reset_m70C443EF07DEF1E40EE7775F3273819C457718A3,
	UintPlugin_SetFrom_mD132C598273DD59BCE7D4F749BCB2CDBBDB8E4FC,
	UintPlugin_ConvertToStartValue_m3DEA6264D97583AC1E26E44D58496406ADDF5711,
	UintPlugin_SetRelativeEndValue_m3239D0B60543DE9E7A58EEAFCAEC224B81C95C91,
	UintPlugin_SetChangeValue_m5F1E259311413FE8EC8857E3FAAFB165AF7066E5,
	UintPlugin_GetSpeedBasedDuration_mAF144DF48A8B58A24DE487C81E932181FD8C329F,
	UintPlugin_EvaluateAndApply_mDFC4BACBBCC44508B0631462A7C2B7FA4E2F8DE7,
	UintPlugin__ctor_mB34F86253C685C80872915B2976E4546D1D8E897,
	Vector2Plugin_Reset_mC73661350E837DB36291164C3C728BA866B087AC,
	Vector2Plugin_SetFrom_mABACF87EB7C31E680DEB311AD713B3EFF80C94C3,
	Vector2Plugin_ConvertToStartValue_m77193485E58C5FA705208BAD82CEFD51A5CD0A55,
	Vector2Plugin_SetRelativeEndValue_m4669281FAC1BC5858C42AE3F74F570F8FDC8B511,
	Vector2Plugin_SetChangeValue_m8EF0B36B68AA1D37982E0A781448A02363BF0505,
	Vector2Plugin_GetSpeedBasedDuration_m8F03E4891B90E95E147600C8652258F403DBD9D8,
	Vector2Plugin_EvaluateAndApply_mC7663D348A00EB794D5807C104ADA372A7032339,
	Vector2Plugin__ctor_m2023C1B763C66A6B5DADA4EA95AABC590996058C,
	Vector4Plugin_Reset_m77B2CBFC27C6F6F64FA0BB07FBBD72A7B7C9F360,
	Vector4Plugin_SetFrom_m8B96E0FCBA5D14E5E5D6D8C37EE36704D270A264,
	Vector4Plugin_ConvertToStartValue_m1D126F93BDF693BD05F2A4E3379CA87B979FD512,
	Vector4Plugin_SetRelativeEndValue_m69588D0E049A70F6B0ACABD34DCBDF8E19CC5652,
	Vector4Plugin_SetChangeValue_m69D1C0143C4D88BF84ABF66533A0E815D26F6FBE,
	Vector4Plugin_GetSpeedBasedDuration_mBA1AF59D5B2CB2A44BFED869AAAD474902E7122C,
	Vector4Plugin_EvaluateAndApply_m96933D49521F09B4CE403FD68EEE58B3EC5CCF33,
	Vector4Plugin__ctor_mF9E2DC11518FE5B03FE1BE2B6C930318307C2002,
	StringPlugin_SetFrom_mA6E09A5AB048B4210846D451F5D83821518CA7A3,
	StringPlugin_Reset_mCE185934F14CBC66803FFE6326EF09B5D8B3C0D8,
	StringPlugin_ConvertToStartValue_m34D7B1A6492C9FBCA91B13E86865E1C450E231A6,
	StringPlugin_SetRelativeEndValue_m281AAEA3815035C5354255DDEA8AE9AEBAF7B81E,
	StringPlugin_SetChangeValue_m154A8DEFEC01D8C683858C11602A2CB62123841B,
	StringPlugin_GetSpeedBasedDuration_mA2F5245600035283DBF76137C655DC84268B0B4D,
	StringPlugin_EvaluateAndApply_m9D74DC7327571231B9476B2B284111AD39F0A927,
	StringPlugin_Append_m393FFA9AFB5B28A407DEE6BEA599F096A2AD4BD1,
	StringPlugin_ScrambledCharsToUse_mB94A339E51635D7F97F27274382BEF24D40A85FB,
	StringPlugin__ctor_m431ECBCEC5363E152AA20286544F3444FC0BD70A,
	StringPlugin__cctor_m43D24AE51E83690FDDDF7DA16CF9BCD78E38672C,
	StringPluginExtensions__cctor_m0EF08249B07EA4E0C6FADEC570B9B33E308FEDC7,
	StringPluginExtensions_ScrambleChars_mEF2DC4717926EAEECA971FD887352673AC8683F2,
	StringPluginExtensions_AppendScrambledChars_mB1EC8925920C497377D1B1E8958C0B13BF88AF3F,
	FloatPlugin_Reset_mDCE52CE11F99836C0653864F7F58BD29B65439C4,
	FloatPlugin_SetFrom_m232CA49201386D4A445C495174456277698E3356,
	FloatPlugin_ConvertToStartValue_m0158D70929F3E0AAD5ACB0085AC8E55729019B6E,
	FloatPlugin_SetRelativeEndValue_m62E86FC1881D233AF898B585417FCC59AB8FC9E4,
	FloatPlugin_SetChangeValue_mBDCF4EFF8AC92B8BF2CBFBC4A9B88474C976DE50,
	FloatPlugin_GetSpeedBasedDuration_m069F5CAC863F1D352534798BA23B9CD53ADA76D0,
	FloatPlugin_EvaluateAndApply_m4605FA536FD5D066DCAD0416FEA5AA5D24DC3D66,
	FloatPlugin__ctor_mDDE3C38DE5AF13E77CCAFC0F11823A547D4723C7,
	Vector3Plugin_Reset_m3917A46D223807D40CA67E69C3F45FA2E00AA338,
	Vector3Plugin_SetFrom_m675F63D9FD954A1B29876A6E2C5E445476D54046,
	Vector3Plugin_ConvertToStartValue_m42E52ED0EAAE4671EA20E8261F495A1657BBFBC8,
	Vector3Plugin_SetRelativeEndValue_m2C1B8ECD82B9174C83EB7E1E4FBE121F54209249,
	Vector3Plugin_SetChangeValue_m08F27B7FB6DE60F46C78163F088FA5E41DCFA98C,
	Vector3Plugin_GetSpeedBasedDuration_m415AFD7A8572E5DC5A8BF1601705B12967BC5317,
	Vector3Plugin_EvaluateAndApply_mA5AA5B5C049C1DD2CA0CC96BC2C0058C452E4FF3,
	Vector3Plugin__ctor_m83B9306A6E1F7F6A4ED2F534D0614235705C8AEC,
	NULL,
	PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060,
	QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E,
	UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC,
	Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589,
	NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E,
	ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4,
	FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE,
	RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300,
	StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397,
	VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE,
	SpecialPluginsUtils_SetLookAt_m032BE1B008809A24C0B4BD53A9689F7DD70F7FCF,
	SpecialPluginsUtils_SetPunch_m65C9920310ABC6FC6C483A6CEC088F3A360EE3CA,
	SpecialPluginsUtils_SetShake_m6F23CBF51E66BEAB3791161223E67374E976FF20,
	SpecialPluginsUtils_SetCameraShakePosition_m49B0FC03FA80CF51647589CCCB44BA1487B037A3,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PluginsManager_PurgeAll_m9EDAE828FEBAFA93291F199449C5AE6648FB949D,
	CubicBezierDecoder_FinalizePath_mE98400AC5E2006EC8BF639774C71A37C3E47E03C,
	CubicBezierDecoder_GetPoint_m1FA6505950448A4973E190441F2762203F2C0C71,
	CubicBezierDecoder_SetTimeToLengthTables_m8DE088CFF73E9F1E1A90A428D307B856FC92094C,
	CubicBezierDecoder_SetWaypointsLengths_mC384E54972E37D9157D8799E6E502BB0093CBC40,
	CubicBezierDecoder__ctor_mEF0D332E62BCB16F6DA71192746F7A43A9B3B461,
	CubicBezierDecoder__cctor_mF060F45215DCDEF59F446852B0B71F2E578B015D,
	ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668,
	ControlPoint_op_Addition_m273B684A11735A299C1886F36F41C7C6CB27E49D,
	ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236,
	NULL,
	NULL,
	ABSPathDecoder__ctor_m9000360B01DBC18CF8310F47FFEC61E0FC0E4C96,
	CatmullRomDecoder_FinalizePath_m9BF4FC78055D6A3E593E05BD4B05A496215A4D4E,
	CatmullRomDecoder_GetPoint_mCDAE4F84C87110712EA863C289A715BC1E232835,
	CatmullRomDecoder_SetTimeToLengthTables_m0E06C670A5FBDCA57207AB97B869ED8191D3ABED,
	CatmullRomDecoder_SetWaypointsLengths_mFB5DE54C8D41124A153BA13614F79F49BEC3ACC1,
	CatmullRomDecoder__ctor_m40C3B62E2B6753C8C604B192AE38CB0E00751B63,
	CatmullRomDecoder__cctor_m171A41BE4CB68DC3602E24837E2F5E067FB150D0,
	LinearDecoder_FinalizePath_m8A38C47B480F0AFC98FDC223622982AED28DAF65,
	LinearDecoder_GetPoint_m923A45C4AECF4B832BACDFE7D338611E3014172E,
	LinearDecoder_SetTimeToLengthTables_mB336D6CE0306247E6C80F9B472E86F744520550B,
	LinearDecoder__ctor_mD23879EA491AC8A5105D0FC69F6D16BDA0998CA5,
	Path__ctor_mE17CD95D405E8FF0440A6631D97C8876074B4824,
	Path__ctor_mE316C2800B03412006B86883F27ECC5CC08CA3AB,
	Path_FinalizePath_m2D9A9AE99327DBCDF8EDBAE55E149ED1F4BD3BB7,
	Path_GetPoint_m6D04BA28C0F375D3030DD2714E2295A2A656AFE1,
	Path_ConvertToConstantPathPerc_m946321C867B331E56BFBF358F57391804611B570,
	Path_GetWaypointIndexFromPerc_m3C41176271872EA1D2B5AE1C18EFAAE729CB2C00,
	Path_RefreshNonLinearDrawWps_m80E683DA112432AAE39D0E8C3F2C8B41CBB60285,
	Path_Destroy_mB5139AE354F434F76149B4672ACE7B835FBE029C,
	Path_CloneIncremental_m02F0B70C4F51FBAEA81DB888E37FBA2E4962D428,
	Path_AssignWaypoints_mF34F707A39321C2912B6C7E4D23DAEEEEF61D2F8,
	Path_AssignDecoder_mD3A97C75986B01E822BF9726461B65DE77BC0B98,
	Path_Draw_mAE9CA81D5ED87ECC9C1213EFCA01639CF0AE2B32,
	Path_Draw_m1D276328F6B71518A310A8D1CCB4B29B5455E33C,
	ABSSequentiable__ctor_m70B4D2A525C71C87049ED3177A821B264D682A7B,
	NULL,
	NULL,
	NULL,
	NULL,
	Debugger_get_logPriority_m7AC0D46AE0BA9AC3ED1ED50AD20734033D16D0AC,
	Debugger_Log_mED54C2BED376B5F8631F5B23490BE1608675E456,
	Debugger_LogWarning_mB528DCD3175EB2D670A62A6507A656F8DE76D06E,
	Debugger_LogError_mF9DFC776376FF7455755A73F555197E68A45124F,
	Debugger_LogReport_m2A1E8B46BC302E45B799171C5C51BFA4DD07693C,
	Debugger_LogSafeModeReport_m2B8F77FCC5EAA0AD7F936AF62EFBC4FDE5D19E71,
	Debugger_LogInvalidTween_mEC44C60EC29E4EE401EFE473B01A209D79F3A83F,
	Debugger_LogNestedTween_m409C30CE26DFF284388E93FDD37738CF2116F128,
	Debugger_LogNullTween_m9C6A433A68F2C3DFCDFF127434135F8BBC116740,
	Debugger_LogMissingMaterialProperty_m49458D73DDB5BBBFB7AE84BA0A1C638A6A5B27C7,
	Debugger_LogMissingMaterialProperty_m8847B17D16E01FB04A460D0984CAD3FEEF9AB854,
	Debugger_LogRemoveActiveTweenError_m30A8AF0E0595F76045BF409584252064AE8460CD,
	Debugger_LogAddActiveTweenError_mAD5420AA037EB3CAAF612D228AF6055A0D80200E,
	Debugger_SetLogPriority_m14859DD9F9DD27E2D875BF50939F31D0F7043D6E,
	DOTweenComponent_Awake_m2B8731C5F756551F8CF374ABA9BBA5D97C36A956,
	DOTweenComponent_Start_m14BD516448C0D8B487AA199C84AFA5984EE38E71,
	DOTweenComponent_Update_m1EB628942CC4EA34F24E9AA2326532D8AD5B0541,
	DOTweenComponent_LateUpdate_mB523F2163A4033594262DB217EDC62C6806DBAF8,
	DOTweenComponent_FixedUpdate_m5D4B2412E608B1F490333A9A5680074A5C02A7C0,
	DOTweenComponent_OnDrawGizmos_m28A4D3B2A76DC3FE70A1BBAE4D5E993624526FCE,
	DOTweenComponent_OnDestroy_m813D82282FC9C21C3DF589E5A954FA11FAFEA3BA,
	DOTweenComponent_OnApplicationPause_m93370BF7B8AA301B841B0BBCC61E9579AD3CDDF7,
	DOTweenComponent_SetCapacity_m3B4936853427792607B2BC9A022300724C6275B8,
	DOTweenComponent_WaitForCompletion_m1F26E152C4B17EF6797B49AD8070C40B872D615C,
	DOTweenComponent_WaitForRewind_m7719D9F5E014FC7403F70045B4E732D40428CB27,
	DOTweenComponent_WaitForKill_m53130F016C0B24C5345C66972D287236F374AC37,
	DOTweenComponent_WaitForElapsedLoops_mE9D67BF81141C1D92F85ECD20FFC51E2E8777774,
	DOTweenComponent_WaitForPosition_m0F07976F5331F98F88289288ACE220D0F708FE84,
	DOTweenComponent_WaitForStart_m91F59DF680F874160B5ED20691215591B75A4B65,
	DOTweenComponent_Create_m2F098BE99694BEFEF39E22C3D8294ED04E9A11D1,
	DOTweenComponent_DestroyInstance_mA0743B89EF2340212A90DA6C777A021BB346723E,
	DOTweenComponent__ctor_mA7CF37E0C0B8644ED78CCB9DD9A0386349236501,
	U3CWaitForCompletionU3Ed__15__ctor_m278DB61099F91A9B8C609CC1E4466C55A0D08E66,
	U3CWaitForCompletionU3Ed__15_System_IDisposable_Dispose_m6D566F2A4C22727AD6BB0A66A6477AF70790A3AB,
	U3CWaitForCompletionU3Ed__15_MoveNext_m851444820FFB5D7235E16C907DFDE051B85B2595,
	U3CWaitForCompletionU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA089CFA8B6389FF6E74FB49F1472EED9B38D73CA,
	U3CWaitForCompletionU3Ed__15_System_Collections_IEnumerator_Reset_m8B440D87F90F8ADA7DA25F3330972D9392124D50,
	U3CWaitForCompletionU3Ed__15_System_Collections_IEnumerator_get_Current_m87F9F17886D192533FED668E98D9290966811332,
	U3CWaitForRewindU3Ed__16__ctor_m16EC7A1FA30FBBDE1C2ADA8F638D4DC1E9877D62,
	U3CWaitForRewindU3Ed__16_System_IDisposable_Dispose_m1150BEB141FDD5CD21CB00BEC0D9475F8F0D64A7,
	U3CWaitForRewindU3Ed__16_MoveNext_mD150954F5ADA8738216554F8875BA25203E1CBAE,
	U3CWaitForRewindU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE4451A369CF8E676AE2AA87E70C71A815ED9AE53,
	U3CWaitForRewindU3Ed__16_System_Collections_IEnumerator_Reset_m9DA203D99A0B394909DE3DC47E409FD82175F4B6,
	U3CWaitForRewindU3Ed__16_System_Collections_IEnumerator_get_Current_mAE8AE36BC00FAC53F580B5763D932CC693F7FA05,
	U3CWaitForKillU3Ed__17__ctor_m046008403D3297CB3BEB9E55302B73D9E0FD079F,
	U3CWaitForKillU3Ed__17_System_IDisposable_Dispose_m339BA2E6D307212AFDA912CBF566F92F6F490F38,
	U3CWaitForKillU3Ed__17_MoveNext_mE2C11A5D1C6BC8D7C5794C2378C56CCAE8627FDB,
	U3CWaitForKillU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m052E324FBD92CF1ADF0C3AAD3EC1A1A65EDB82DB,
	U3CWaitForKillU3Ed__17_System_Collections_IEnumerator_Reset_mF31474C7A6F1D540D5E0B8C82ABC03133B0408C6,
	U3CWaitForKillU3Ed__17_System_Collections_IEnumerator_get_Current_m09FA39FEE3BF8E91C9015A27818F9CF15B1262B4,
	U3CWaitForElapsedLoopsU3Ed__18__ctor_mB348D70420323372FE35F14C69D22BC361F99800,
	U3CWaitForElapsedLoopsU3Ed__18_System_IDisposable_Dispose_m973DEB809C01D899B2D92C3BC4F42AE609CD2BA2,
	U3CWaitForElapsedLoopsU3Ed__18_MoveNext_m79044A48FC9C1648D844BECF7EEB914341E97B08,
	U3CWaitForElapsedLoopsU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6D815BF3D4EB44CD702B4964924346E0C33BC6E7,
	U3CWaitForElapsedLoopsU3Ed__18_System_Collections_IEnumerator_Reset_m9DC4ED99A6402A5C717F5653E74A88D81518DB3C,
	U3CWaitForElapsedLoopsU3Ed__18_System_Collections_IEnumerator_get_Current_m9D9C76BEAF69EEC3A4852EE77F4706DAED8D9F19,
	U3CWaitForPositionU3Ed__19__ctor_mEAEA31DDDF69E86D1C19E336AE2A0F319B3BB0A1,
	U3CWaitForPositionU3Ed__19_System_IDisposable_Dispose_mEA96DC849CF8E202B2030E7BF893E6EF28842025,
	U3CWaitForPositionU3Ed__19_MoveNext_m1D0D9960D10990576643F78ABA51C9B6E29FC4B8,
	U3CWaitForPositionU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDECF758E238727FB9DB51F72930EC884F568EDCD,
	U3CWaitForPositionU3Ed__19_System_Collections_IEnumerator_Reset_mE6517A33DC8BEA448978248437AD244E5B8BED17,
	U3CWaitForPositionU3Ed__19_System_Collections_IEnumerator_get_Current_m34DE755B710583B60DD01095BC56A6FDF0D0B722,
	U3CWaitForStartU3Ed__20__ctor_mC8DFD3FE03D432361CE1FE97EBE2CE2762625243,
	U3CWaitForStartU3Ed__20_System_IDisposable_Dispose_m44DAF5DCB78298ABE23D3749FACA4724D2464905,
	U3CWaitForStartU3Ed__20_MoveNext_m6BF5F12952DF53D36D2A890B68FBA6C58177FD93,
	U3CWaitForStartU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8FC5EA116F53A193BD651D6B1F649D7621DF3C72,
	U3CWaitForStartU3Ed__20_System_Collections_IEnumerator_Reset_m8B2E51695757C5AD6B3BF1F6F1E09EEAF16132DF,
	U3CWaitForStartU3Ed__20_System_Collections_IEnumerator_get_Current_m7182BC5A22362E35A2DC275208B5B6B8EA87A660,
	DOTweenSettings__ctor_m887E5989C5E07DD06A56C7BB9651D513DBE8E04A,
	SafeModeOptions__ctor_m30119D0CFA2C471B7AA0F2AFBE5AE8130D4AADA7,
	ModulesSetup__ctor_mFC121E34A299D3C21E06DDCDA8392FF3EC4CDB28,
	NULL,
	NULL,
	NULL,
	DOTweenExternalCommand_add_SetOrientationOnPath_mBD8B37B1978CBE4534F9B8868C734F5C0A2B2804,
	DOTweenExternalCommand_remove_SetOrientationOnPath_m552309BE5B7623397429DDAC02D5C6FDB0C0FB44,
	DOTweenExternalCommand_Dispatch_SetOrientationOnPath_m66381EB3F8DF2CB2F8D0FF0462C40E2AEA21EE3C,
	SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5,
	SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9,
	SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133,
	SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98,
	SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790,
	SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082,
	SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840,
	SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C,
	SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20,
	SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70,
	TweenManager__cctor_m32BF1B0E0166CC1C1832F3980654478B2C445F07,
	NULL,
	TweenManager_GetSequence_m64E8F2E01C9BDE2A4006C4265C84614F1DF92CB2,
	TweenManager_SetUpdateType_m4C1E97DD675C70ABA66E53D283EDABB6337E44B4,
	TweenManager_AddActiveTweenToSequence_m2D83B988481444A51AA9330D5AB9E63BF1FD3B18,
	TweenManager_Despawn_mF56A8F1D2B480230F68F0149F6122D0BBAA129FC,
	TweenManager_PurgeAll_m78AD5E228A1158702999D39817AFA01A7D413F16,
	TweenManager_PurgePools_m00794B3E7D39DF9078C9524DA48A8E9C500F9C05,
	TweenManager_RemoveTweenLink_mA00A9ACA1D2E241FF5E4C63D88686194CB0D092E,
	TweenManager_ResetCapacities_m6B74BE623F2ADFCDD966F76EDBF2B2D5DD7898B8,
	TweenManager_SetCapacities_m1A1D0CA467C77D297C7CD9AB105DB5609112175F,
	TweenManager_Update_mB27C9AE8D928418163CE32FD6EF1C82F9A9F63C7,
	TweenManager_FilteredOperation_mEF0C87B677542616CB4324153651CA06F548BE39,
	TweenManager_Complete_mCE26F442C189C358288018529BD88F0F1D99C390,
	TweenManager_Flip_mAE0BD66D990CE593A474271E4D48B994451AF5B7,
	TweenManager_ForceInit_m490AB9D6D7F427844FBC663194066EF146853737,
	TweenManager_Goto_m04F0FF9EC0394154CCFE8552D797374147D31CDC,
	TweenManager_Pause_mF53E744566930DFD9A6642B63E872D12F23B4269,
	TweenManager_Play_m16548C8C4203A88870C6E54E44F03CDA11763DA2,
	TweenManager_PlayBackwards_mCC2A5578B578BEE7005204D8D7A546957BE605E9,
	TweenManager_PlayForward_mD5568593F49E0036FA185E22CC1B24A22B35DEF2,
	TweenManager_Restart_mB3E06722F56E0371559BC8DA43D5D09D8D391A7C,
	TweenManager_Rewind_mF610181130C4218E0F462966D54EE193E8171A7F,
	TweenManager_SmoothRewind_m3C5DE57B8A3DDE3EBBA223EC0AA2CDFD9E567E82,
	TweenManager_TogglePause_m9352B05E74FE75A5CC99DF8E0AB7B03D020B9C49,
	TweenManager_MarkForKilling_m13B34939F0A6AC4BAB7D3FE2CDD8D3AF895C31EB,
	TweenManager_EvaluateTweenLink_mE5358C53588B9201A77D284C6EA08C13090F5398,
	TweenManager_AddActiveTween_mDE4BACC61DA83AB6BD50B1AA57CB16F40272CD05,
	TweenManager_ReorganizeActiveTweens_mB40CEDBB24B90540B86274D77D7795EAE1330EA3,
	TweenManager_DespawnActiveTweens_m61D3A0AA83721EB57E1E3DF0C9C75F9F71632AC3,
	TweenManager_RemoveActiveTween_m666B3E84DC9E1CD8F8E8F79A44EBFE16CA03AE49,
	TweenManager_ClearTweenArray_mA86AEF4A53B4DCEF3319D39B9769AECE4E0093E8,
	TweenManager_IncreaseCapacities_m5316415F2F95212320B0745CAF123B139A8CDA62,
	TweenManager_ManageOnRewindCallbackWhenAlreadyRewinded_m1054CA72AB6AC784DD9A50AE4F89EE2D4DC38480,
	Utils_Vector3FromAngle_mB6466F235E3686C580BAC10255C6284AE704AC6B,
	Utils_Angle2D_mA49F298AC1F19FAF2D0791E9E5424015DB0BF1C4,
	Utils_Vector3AreApproximatelyEqual_mF1A29021A7E98EB0E9847B4EB711D9446469B12B,
	Utils_GetLooseScriptType_m351AF7C36684EAC083A58197F93D49B7DD9A3EC1,
	Utils__cctor_mE6DB1A2102B2BF7FEDBEE5A07EF1BAAD8848B548,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Bounce_EaseIn_m1253ADF94B39EF139C56EEE7AD5EC2F5E0C29769,
	Bounce_EaseOut_mEF5499252352724ADC55887B541B5053DACFC27F,
	Bounce_EaseInOut_m6D386BE3A485A50DE77E480D40008D01ADDE79F0,
	EaseManager_Evaluate_mD683DD74534996BFA45C075DCC2927E089C4E26E,
	EaseManager_Evaluate_m26A532BC322B246C5CE9D45ABC16384F58F8389A,
	EaseManager_IsFlashEase_mB7D47A96B8C663F7FA56AD8D10B9586C76204FF6,
	EaseCurve__ctor_m29BD0E232922C8CE2E4AC877F79EF6096C34EDFB,
	EaseCurve_Evaluate_m036A88A768920A29FA6C7EA6E78646F679C9DFEE,
	Flash_Ease_mA28C135D4B118A9A4469FEFFDEC3329226E6A096,
	Flash_EaseIn_m7A2DCE17466DCF086004A0147F534851240EADFA,
	Flash_EaseOut_m306F24AC2A2EF38682E1C0AB8834FAC036658955,
	Flash_EaseInOut_m71C9C9CD9B50F446B635F0ABB97D1AFA7F52F4A9,
	Flash_WeightedEase_mF9EC6A43BAEE75E4D2E93FCA21E099B0FA8CCA35,
};
extern void Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E_AdjustorThunk (void);
extern void PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060_AdjustorThunk (void);
extern void QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E_AdjustorThunk (void);
extern void UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC_AdjustorThunk (void);
extern void Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589_AdjustorThunk (void);
extern void NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E_AdjustorThunk (void);
extern void ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4_AdjustorThunk (void);
extern void FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE_AdjustorThunk (void);
extern void RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300_AdjustorThunk (void);
extern void StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397_AdjustorThunk (void);
extern void VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE_AdjustorThunk (void);
extern void ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668_AdjustorThunk (void);
extern void ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236_AdjustorThunk (void);
extern void SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5_AdjustorThunk (void);
extern void SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9_AdjustorThunk (void);
extern void SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133_AdjustorThunk (void);
extern void SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98_AdjustorThunk (void);
extern void SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790_AdjustorThunk (void);
extern void SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082_AdjustorThunk (void);
extern void SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840_AdjustorThunk (void);
extern void SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C_AdjustorThunk (void);
extern void SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20_AdjustorThunk (void);
extern void SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[23] = 
{
	{ 0x06000001, Color2__ctor_m0D4E285146629A09FA473F8FDE3D4A4F3889D58E_AdjustorThunk },
	{ 0x06000187, PathOptions_Reset_mCF088F48CAA5F7D294ACE99D8D12CD07F4BEA060_AdjustorThunk },
	{ 0x06000188, QuaternionOptions_Reset_mFC1BCF4AC4D766AFE3BF6E93A16C5CB2146C2B9E_AdjustorThunk },
	{ 0x06000189, UintOptions_Reset_m3A17AAA2C83433C4B0246CFFDB26210823E19EFC_AdjustorThunk },
	{ 0x0600018A, Vector3ArrayOptions_Reset_m5FBD5DB30FA9B5B3ECFF1AB327B53C95179E9589_AdjustorThunk },
	{ 0x0600018B, NoOptions_Reset_m4C770E8106D2D141A2C9E1347A9EDF05CD4F0B9E_AdjustorThunk },
	{ 0x0600018C, ColorOptions_Reset_m61D38CFBD63AA3DB49D4ABAC7460191E9EA425A4_AdjustorThunk },
	{ 0x0600018D, FloatOptions_Reset_m7796B4F4CD7ACD23C0D11F4191A6DEC735E515DE_AdjustorThunk },
	{ 0x0600018E, RectOptions_Reset_m06D669A680AF4A4BE9C113D5748F2727C9AF0300_AdjustorThunk },
	{ 0x0600018F, StringOptions_Reset_mC44E99B51F61681EE22B9D7FB9D0F4698A568397_AdjustorThunk },
	{ 0x06000190, VectorOptions_Reset_mD2EB7C2BEE4AD4B30E6552CD5CF925B06B1EDEFE_AdjustorThunk },
	{ 0x060001A6, ControlPoint__ctor_m61705A2A5C9615DC8E052515CB9B1F62A4600668_AdjustorThunk },
	{ 0x060001A8, ControlPoint_ToString_m160B7B0BCC744DE7EF312A520CE814561A24C236_AdjustorThunk },
	{ 0x06000215, SafeModeReport_get_totMissingTargetOrFieldErrors_mFC456B568815DFA27E1C10970DC3926B210BFFE5_AdjustorThunk },
	{ 0x06000216, SafeModeReport_set_totMissingTargetOrFieldErrors_m6D3CEE836FE7F2CFFA794FED0E551DDD0ADF7AA9_AdjustorThunk },
	{ 0x06000217, SafeModeReport_get_totCallbackErrors_m31FC475B782E0CCD82744DB4A5079F28D3973133_AdjustorThunk },
	{ 0x06000218, SafeModeReport_set_totCallbackErrors_m4215E716C771AE5BBDEE51AA0520815AF1690D98_AdjustorThunk },
	{ 0x06000219, SafeModeReport_get_totStartupErrors_m5ED6052FB179745BA7C225BC301811A253B15790_AdjustorThunk },
	{ 0x0600021A, SafeModeReport_set_totStartupErrors_m293BB3C69C6A944F04276A9DFAB2EDCAE91C9082_AdjustorThunk },
	{ 0x0600021B, SafeModeReport_get_totUnsetErrors_m136EA51EF49FF199E3939A17AD7575C0E9785840_AdjustorThunk },
	{ 0x0600021C, SafeModeReport_set_totUnsetErrors_m9232EA974F7A04A1D816F7B49B268D9F2F3E837C_AdjustorThunk },
	{ 0x0600021D, SafeModeReport_Add_mC993434DFE20DDF1E44389DACD34085F31E6ED20_AdjustorThunk },
	{ 0x0600021E, SafeModeReport_GetTotErrors_mE0AC77EDDFA876A6332177C51DA53E4A94F5ED70_AdjustorThunk },
};
static const int32_t s_InvokerIndices[602] = 
{
	2815,
	9208,
	9208,
	9209,
	3361,
	7120,
	0,
	0,
	3361,
	1160,
	10412,
	10288,
	10397,
	10284,
	10455,
	8218,
	10284,
	8262,
	8257,
	8263,
	8265,
	8265,
	8249,
	8258,
	0,
	8262,
	7477,
	7310,
	7371,
	7278,
	8256,
	10420,
	9280,
	9942,
	9942,
	8633,
	9280,
	9942,
	9942,
	9282,
	9942,
	9282,
	9942,
	9282,
	8619,
	8087,
	9280,
	9942,
	9942,
	10455,
	0,
	10455,
	8313,
	10293,
	9618,
	8971,
	9618,
	0,
	0,
	10293,
	10293,
	8944,
	9618,
	10293,
	9942,
	9450,
	9450,
	10110,
	9834,
	9834,
	9834,
	9834,
	7120,
	8739,
	9384,
	9384,
	7120,
	6887,
	281,
	10293,
	9834,
	7426,
	7347,
	10293,
	9834,
	8747,
	8725,
	8747,
	8747,
	8742,
	8742,
	8725,
	8747,
	8725,
	8227,
	8747,
	8288,
	8288,
	8289,
	8289,
	8758,
	7482,
	7793,
	7793,
	7379,
	7480,
	7480,
	8261,
	8261,
	7120,
	7043,
	5851,
	7120,
	6890,
	5706,
	7120,
	7043,
	5851,
	7120,
	7043,
	5851,
	7120,
	7017,
	5831,
	7120,
	7017,
	5831,
	7120,
	6890,
	5706,
	7120,
	7043,
	5851,
	7120,
	6890,
	5706,
	7120,
	6890,
	5706,
	7120,
	6890,
	5706,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7008,
	5823,
	7120,
	7008,
	5823,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	7120,
	7111,
	5915,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9381,
	9381,
	9384,
	9384,
	0,
	0,
	0,
	0,
	0,
	9369,
	9369,
	8729,
	9369,
	8729,
	9369,
	8225,
	9369,
	8224,
	8285,
	8245,
	8270,
	8935,
	6887,
	5703,
	6887,
	5703,
	6887,
	5703,
	7043,
	5851,
	7120,
	5265,
	0,
	0,
	7991,
	9127,
	0,
	7120,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	5806,
	3349,
	2357,
	5806,
	5806,
	1667,
	24,
	7120,
	5806,
	3349,
	2365,
	5806,
	5806,
	1670,
	29,
	7120,
	5806,
	3349,
	2639,
	5806,
	5806,
	1672,
	31,
	7120,
	5806,
	3349,
	2749,
	5806,
	5806,
	1674,
	33,
	7120,
	5806,
	3349,
	2685,
	5806,
	5806,
	1690,
	42,
	7120,
	5806,
	3349,
	10420,
	2685,
	5806,
	5806,
	1680,
	35,
	393,
	7120,
	5806,
	3349,
	2355,
	5806,
	5806,
	1666,
	23,
	7120,
	5806,
	3349,
	2473,
	5806,
	5806,
	1671,
	30,
	7120,
	5806,
	3349,
	2760,
	5806,
	5806,
	1681,
	36,
	7120,
	5806,
	3349,
	2682,
	5806,
	5806,
	1673,
	32,
	7120,
	10455,
	5806,
	3349,
	2712,
	5806,
	5806,
	1682,
	38,
	7120,
	5806,
	3349,
	2740,
	5806,
	5806,
	1688,
	41,
	7120,
	5806,
	3349,
	2753,
	5806,
	5806,
	1691,
	43,
	7120,
	5806,
	3349,
	2768,
	5806,
	5806,
	1693,
	45,
	7120,
	3349,
	5806,
	2682,
	5806,
	5806,
	1687,
	40,
	1117,
	5186,
	7120,
	10455,
	10455,
	10293,
	8732,
	5806,
	3349,
	2720,
	5806,
	5806,
	1668,
	25,
	7120,
	5806,
	3349,
	2761,
	5806,
	5806,
	1692,
	44,
	7120,
	0,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	9834,
	9834,
	9834,
	9834,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10455,
	1905,
	1170,
	3358,
	3358,
	7120,
	10455,
	3450,
	9216,
	6992,
	0,
	0,
	7120,
	1905,
	1170,
	3358,
	3358,
	7120,
	10455,
	1905,
	1170,
	3358,
	7120,
	1233,
	7120,
	1774,
	2763,
	5265,
	2501,
	10293,
	7120,
	5178,
	3349,
	5774,
	7120,
	10293,
	7120,
	0,
	0,
	0,
	0,
	10412,
	10293,
	9630,
	10293,
	10293,
	10293,
	10293,
	10293,
	10293,
	10293,
	10288,
	9630,
	9630,
	10288,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	5703,
	2670,
	5181,
	5181,
	5181,
	2681,
	2683,
	5181,
	10455,
	10455,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	0,
	0,
	0,
	10293,
	10293,
	8425,
	6957,
	5774,
	6957,
	5774,
	6957,
	5774,
	6957,
	5774,
	5774,
	6957,
	10455,
	0,
	10420,
	8952,
	10293,
	9618,
	10455,
	10455,
	10293,
	10455,
	9575,
	8913,
	7349,
	8528,
	9834,
	9618,
	7990,
	9834,
	9834,
	9834,
	9834,
	8529,
	9123,
	9834,
	9834,
	10293,
	10293,
	10293,
	10455,
	10293,
	10293,
	10293,
	10288,
	9618,
	9541,
	9460,
	9192,
	10054,
	10455,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8314,
	8314,
	8314,
	7801,
	7486,
	9831,
	5806,
	1160,
	8314,
	8314,
	8314,
	8314,
	7489,
};
static const Il2CppTokenRangePair s_rgctxIndices[38] = 
{
	{ 0x02000072, { 87, 16 } },
	{ 0x06000019, { 0, 6 } },
	{ 0x06000033, { 6, 7 } },
	{ 0x0600003A, { 13, 1 } },
	{ 0x0600003B, { 14, 1 } },
	{ 0x060000B6, { 15, 1 } },
	{ 0x060000B7, { 16, 1 } },
	{ 0x060000B8, { 17, 1 } },
	{ 0x060000B9, { 18, 1 } },
	{ 0x060000BA, { 19, 1 } },
	{ 0x060000BB, { 20, 1 } },
	{ 0x060000BC, { 21, 1 } },
	{ 0x060000BD, { 22, 1 } },
	{ 0x060000BE, { 23, 1 } },
	{ 0x060000BF, { 24, 1 } },
	{ 0x060000C0, { 25, 1 } },
	{ 0x060000C1, { 26, 1 } },
	{ 0x060000C2, { 27, 1 } },
	{ 0x060000C3, { 28, 1 } },
	{ 0x060000C4, { 29, 1 } },
	{ 0x060000C9, { 30, 1 } },
	{ 0x060000CA, { 31, 1 } },
	{ 0x060000CB, { 32, 1 } },
	{ 0x060000CC, { 33, 1 } },
	{ 0x060000CD, { 34, 1 } },
	{ 0x060000E9, { 35, 3 } },
	{ 0x060000EF, { 38, 6 } },
	{ 0x060000F0, { 44, 1 } },
	{ 0x060000F1, { 45, 11 } },
	{ 0x060000F2, { 56, 10 } },
	{ 0x060000F3, { 66, 1 } },
	{ 0x060000F4, { 67, 5 } },
	{ 0x0600019D, { 72, 3 } },
	{ 0x0600019E, { 75, 4 } },
	{ 0x0600020F, { 79, 1 } },
	{ 0x06000210, { 80, 1 } },
	{ 0x06000211, { 81, 1 } },
	{ 0x06000220, { 82, 5 } },
};
extern const uint32_t g_rgctx_DOGetter_1_tE232F132BDAAFCC9B63F1B020A92C01983D8FF51;
extern const uint32_t g_rgctx_DOSetter_1_t64ADD5C85F1EC651EC7D7642CAC07E1CAC7DEDDA;
extern const uint32_t g_rgctx_T2_tE16D758978DC41937EFE53526735423128DEB6FA;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t2C4A2E6DA4E89C0251EDC9219E9E6F1D472EB313;
extern const uint32_t g_rgctx_DOTween_ApplyTo_TisT1_tB29B2CFDD69968D6D628B779C9710C73006C2417_TisT2_tE16D758978DC41937EFE53526735423128DEB6FA_TisTPlugOptions_tF4E5BC1FB1B6881D042D606EC83D30BFF0805B0D_mC8A118B50D9A693DCA46E4A53C9B87E446A20C3F;
extern const uint32_t g_rgctx_TweenerCore_3_t7E5C2901CF5D601E76BCB18293C5348DFF5F428B;
extern const uint32_t g_rgctx_TweenManager_GetTweener_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mC72DDD522C47490B4E486D817A446D67C717EDE4;
extern const uint32_t g_rgctx_TweenerCore_3_t9D29722C142B334398C0E2575F4D5D106E134621;
extern const uint32_t g_rgctx_DOGetter_1_tDF558BBD7CE237B87BC1A88EC225FFA63F0DE138;
extern const uint32_t g_rgctx_DOSetter_1_t8F5DE7F863FF343E1E5299D70FCCE30428BBC506;
extern const uint32_t g_rgctx_T2_tF01CCBCD3E34E6D72A045C92DD4892353990C150;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tF4F9215A9EB645424C5F4FF28DE00DBF24AE911A;
extern const uint32_t g_rgctx_Tweener_Setup_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mA26311B5B8F1E244E67F28AF0A11DBF8C76E17F4;
extern const uint32_t g_rgctx_T_t8E0A9C9428635DAAAC14C08267E9609154E4177C;
extern const uint32_t g_rgctx_T_t996D6B7E8FA1BABB3D1A1B9690735FD622C102DA;
extern const uint32_t g_rgctx_T_tA5DD03EABE06D18D770FBEDC46B4EE855F5F6A98;
extern const uint32_t g_rgctx_T_tC95AA700112143744CE040FCE8CA1F37DDA97E1C;
extern const uint32_t g_rgctx_T_t11F01B69A544DF4BB1C6AAE5C052D9224BB1E5F5;
extern const uint32_t g_rgctx_T_t8A99B97BA032ACB6F023EEDB0853639277FB0BA7;
extern const uint32_t g_rgctx_T_t1C191339A523137758AF2831B20DCE2459EFAA84;
extern const uint32_t g_rgctx_T_t1DF72BEDBB34F7C74155B5A45061FE8F7BE3BD55;
extern const uint32_t g_rgctx_T_t17AF76E85A75C68E983331BFDFAD0241E5D02DE7;
extern const uint32_t g_rgctx_T_t0A357877DEBBD01658525941B74E1B086E183ED2;
extern const uint32_t g_rgctx_T_t3A687DEB8CCB201E6BB6ED4E79D2A551E735877D;
extern const uint32_t g_rgctx_T_t306587FEA3FE65775A7721321233D245CE355F75;
extern const uint32_t g_rgctx_T_tDD42A76EC9AED48E0D0DD3BD09330B288F2F7CF3;
extern const uint32_t g_rgctx_T_t7C35FCE102F563100BDAAA642AC03FBD188B66A0;
extern const uint32_t g_rgctx_T_t4CF9E03112E6C01B8A4E1C87E694D5A0827C748D;
extern const uint32_t g_rgctx_T_t8F96B3349E77C59590A65A4F70A037A1472920EA;
extern const uint32_t g_rgctx_T_tA3BB1FFFA0765247C57E4C7BC37CD0F8BEBF2C4E;
extern const uint32_t g_rgctx_T_tDC1DF5AEAC70F0309289880BEADEF11125E6F2E0;
extern const uint32_t g_rgctx_T_t15C5A937AE468E0CEF3F8E0B1BDF36E3D5B53C81;
extern const uint32_t g_rgctx_T_tC3E28A59EB6F378A0D8DAA40A693372D731DA1D2;
extern const uint32_t g_rgctx_T_tB2B8731EB1F33484765DC971CE3E4B99B2072D39;
extern const uint32_t g_rgctx_T_t3A36DEF11383848C17EF894E4143FF466AA49F44;
extern const uint32_t g_rgctx_TweenCallback_1_t877925C635725FEEA8C8EB4D45DC7793D93D28B4;
extern const uint32_t g_rgctx_T_t24C02A1324216D668707B836DEE441F5AF258081;
extern const uint32_t g_rgctx_TweenCallback_1_Invoke_mB35201EA284C80915255DDC706CBB58D64CFC0FE;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tD1B3F38DB61901D8835507448768B08701C3746D;
extern const uint32_t g_rgctx_TweenerCore_3_t31B04D935D4E890DE0A30DFA01B8B931FF5DBFFD;
extern const uint32_t g_rgctx_PluginsManager_GetDefaultPlugin_TisT1_t0E372CEAC223E24829181714BD7081D1ED0E8855_TisT2_t1A317392D2BC29857CB52C1A930D6A4DD8BD2535_TisTPlugOptions_tD1D47CBD660D9137AD25629864DBC7ECAD9B4B21_m98665500FF3C2637C791DF60ABE06E95ACF02A1D;
extern const uint32_t g_rgctx_DOGetter_1_t275E429810626F7339469573855B5F9021789CFA;
extern const uint32_t g_rgctx_DOSetter_1_t6E05A324E8073D715EA95CBC391FE70B14ECC3E3;
extern const uint32_t g_rgctx_T2_t1A317392D2BC29857CB52C1A930D6A4DD8BD2535;
extern const uint32_t g_rgctx_TweenerCore_3_t0C5DEDF5728E3004B3DE1297FCFFF1DF4C9825D2;
extern const uint32_t g_rgctx_TweenerCore_3_t54ABD46A155450A839042A970B8A3E6548087B28;
extern const uint32_t g_rgctx_Tweener_DOStartupSpecials_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_m6FC23B801A9C5649CF6479553FE514E796906A89;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tE15BC2F103281E62B718D20B7BB9DDD3CA3AFC28;
extern const uint32_t g_rgctx_DOGetter_1_t88C4DFDF86DFFB6407F08DF6037B3695F7F5BAE1;
extern const uint32_t g_rgctx_DOGetter_1_Invoke_mA2E144F27E67179DAE3B6109B55EE262F92A7BFB;
extern const uint32_t g_rgctx_T1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mAD5DB662E7C52EFEE5CEFA21FD0202566A2011D1;
extern const uint32_t g_rgctx_T2_tC4A76B819474D2BA458497F91AF356C820C52665;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetRelativeEndValue_m822FDD211A289A90039247BB1837E19D55BB8BAD;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetChangeValue_m7E722039C4FA3663DC761B3131878BBE455CACCF;
extern const uint32_t g_rgctx_Tweener_DOStartupDurationBased_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_mE7A85ABBA6CBC87082DBF866E455D7625738A121;
extern const uint32_t g_rgctx_TweenerCore_3_tC5697CE5E11F7E0523504C0A7C74A751716C8F71;
extern const uint32_t g_rgctx_T2_tE48501F6F32076F79C09C65FC49FD7C48012689A;
extern const uint32_t g_rgctx_Tweener_DOStartupSpecials_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_mD6E69265EA8A0D3688C27732AF45932E724EACB2;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tBA2E2F88BF83E9A87073C91C5FD73DFF23A23BA0;
extern const uint32_t g_rgctx_DOGetter_1_t23BC1B197BDFE6BE418FA0E21E33A23B49D3807F;
extern const uint32_t g_rgctx_DOGetter_1_Invoke_m41E85F763632E85D83AAC979D5DE5C44D11A1A6A;
extern const uint32_t g_rgctx_T1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mFF5A754363FEA2C83960B675F143A94BE54F387A;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetChangeValue_m1B2E33F9FB1A52B4B475D87506FCFB87BD811186;
extern const uint32_t g_rgctx_Tweener_DOStartupDurationBased_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_m1F1B3EE9D5471D06FB46CE9AD30833F49F7392AF;
extern const uint32_t g_rgctx_TweenerCore_3_t5756CB69570B9C889182A88B6F32082721CE12E0;
extern const uint32_t g_rgctx_TweenerCore_3_tBDB7EC4EC3549E999B77349B5A20028E2BBDE1C9;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_tCA2FCF7AA9959A985B00252151C895B69EA3A15F;
extern const uint32_t g_rgctx_TPlugOptions_t81F31C9EB1EF9CDF582023A3E98CD64BDEF38AE2;
extern const uint32_t g_rgctx_T2_tE3473B09F549037482683D400BE8E56768D983CF;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_GetSpeedBasedDuration_m61C28E14F638BE4A1E7A27125FF7174E76615B3E;
extern const uint32_t g_rgctx_T1_t56A1D382DD6C07F3648B8262CDB7AD3604F1DD50;
extern const uint32_t g_rgctx_T2_tC288634F7135CC961B6CBCC0354BA7262633F50A;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t85A5DEEAB068C58207EBA88688D8D5B186828792;
extern const uint32_t g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t456028CE0EA15DC4A666100047C0558E4C6E82CD;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0_m96BD2338C04130B436D330F23E8960CBA097AB06;
extern const uint32_t g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0;
extern const uint32_t g_rgctx_T_t744CCA93F42B6AB407D56BA97402304508E8D126;
extern const uint32_t g_rgctx_TweenerCore_3_t4E74A76CB105CF046EF3331B0412FF23ED036CA5;
extern const uint32_t g_rgctx_TweenerCore_3_t53DC76D5984ADF08F803D2EF4B64EE98F4A57892;
extern const uint32_t g_rgctx_T1_tB93B55C6E17E38076065933CF07D49689DB02FE9;
extern const uint32_t g_rgctx_T2_tD013770946BAD079A42F798BC5F230E0FF5F69EE;
extern const uint32_t g_rgctx_TPlugOptions_t9A66C58DDECE4DACC0C69B129D543BEE21B3F31B;
extern const uint32_t g_rgctx_TweenerCore_3_t1A91562B894654D9A32CD90D2B7B3DC2EBEF280F;
extern const uint32_t g_rgctx_TweenerCore_3__ctor_m191BF86F9477FAADDABF38521D973DF5C3F4D3E3;
extern const uint32_t g_rgctx_T1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA;
extern const uint32_t g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00;
extern const uint32_t g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F;
extern const uint32_t g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00;
extern const uint32_t g_rgctx_Tweener_DoChangeEndValue_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mE766FEF8C2F23A334FD3F30CBC396FBB176329EC;
extern const uint32_t g_rgctx_TweenerCore_3_t841E07FBE92B7B1D2448883910420C746AB0616A;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_t5AB0AED1E5225912CAC7F48FA4FF66BAE1933071;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_SetFrom_m48C5D8F8A180AB9DC080E05C59645B234BEC379D;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_Reset_mA12C9B5353FD460FA20AA3CD721D5B17FC95B0D2;
extern const uint32_t g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F;
extern const Il2CppRGCTXConstrainedData g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_IPlugOptions_Reset_m55A4D8608C4E079C3A7E9CD41AC6688929CC7606;
extern const uint32_t g_rgctx_DOGetter_1_t7802D68DE7558EF7801E6CA418C5D553AD76CA6F;
extern const uint32_t g_rgctx_DOSetter_1_tEC198FBF803582D0CD192AE4EEFD9A4C88981A1B;
extern const uint32_t g_rgctx_Tweener_DoUpdateDelay_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_m35E1A653C5CD8389FB8E96C215A42ACFEF43742E;
extern const uint32_t g_rgctx_Tweener_DoStartup_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mA9E5230B08C67D3E34A768D86717D3B469806DD5;
extern const uint32_t g_rgctx_ABSTweenPlugin_3_EvaluateAndApply_mBDC24C4678B22CCCA94F560A49E8752ED3192BD4;
static const Il2CppRGCTXDefinition s_rgctxValues[103] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_tE232F132BDAAFCC9B63F1B020A92C01983D8FF51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOSetter_1_t64ADD5C85F1EC651EC7D7642CAC07E1CAC7DEDDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tE16D758978DC41937EFE53526735423128DEB6FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t2C4A2E6DA4E89C0251EDC9219E9E6F1D472EB313 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOTween_ApplyTo_TisT1_tB29B2CFDD69968D6D628B779C9710C73006C2417_TisT2_tE16D758978DC41937EFE53526735423128DEB6FA_TisTPlugOptions_tF4E5BC1FB1B6881D042D606EC83D30BFF0805B0D_mC8A118B50D9A693DCA46E4A53C9B87E446A20C3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t7E5C2901CF5D601E76BCB18293C5348DFF5F428B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenManager_GetTweener_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mC72DDD522C47490B4E486D817A446D67C717EDE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t9D29722C142B334398C0E2575F4D5D106E134621 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_tDF558BBD7CE237B87BC1A88EC225FFA63F0DE138 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOSetter_1_t8F5DE7F863FF343E1E5299D70FCCE30428BBC506 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tF01CCBCD3E34E6D72A045C92DD4892353990C150 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tF4F9215A9EB645424C5F4FF28DE00DBF24AE911A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_Setup_TisT1_tF11D66D53F34C9586467E282349637F99CC4AC67_TisT2_tF01CCBCD3E34E6D72A045C92DD4892353990C150_TisTPlugOptions_tC6FC5A71472FF3CE6598FCCFD787FDCDDB3B5729_mA26311B5B8F1E244E67F28AF0A11DBF8C76E17F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8E0A9C9428635DAAAC14C08267E9609154E4177C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t996D6B7E8FA1BABB3D1A1B9690735FD622C102DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA5DD03EABE06D18D770FBEDC46B4EE855F5F6A98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC95AA700112143744CE040FCE8CA1F37DDA97E1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t11F01B69A544DF4BB1C6AAE5C052D9224BB1E5F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8A99B97BA032ACB6F023EEDB0853639277FB0BA7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1C191339A523137758AF2831B20DCE2459EFAA84 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1DF72BEDBB34F7C74155B5A45061FE8F7BE3BD55 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t17AF76E85A75C68E983331BFDFAD0241E5D02DE7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0A357877DEBBD01658525941B74E1B086E183ED2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3A687DEB8CCB201E6BB6ED4E79D2A551E735877D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t306587FEA3FE65775A7721321233D245CE355F75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDD42A76EC9AED48E0D0DD3BD09330B288F2F7CF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7C35FCE102F563100BDAAA642AC03FBD188B66A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4CF9E03112E6C01B8A4E1C87E694D5A0827C748D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8F96B3349E77C59590A65A4F70A037A1472920EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA3BB1FFFA0765247C57E4C7BC37CD0F8BEBF2C4E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDC1DF5AEAC70F0309289880BEADEF11125E6F2E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t15C5A937AE468E0CEF3F8E0B1BDF36E3D5B53C81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC3E28A59EB6F378A0D8DAA40A693372D731DA1D2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB2B8731EB1F33484765DC971CE3E4B99B2072D39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3A36DEF11383848C17EF894E4143FF466AA49F44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenCallback_1_t877925C635725FEEA8C8EB4D45DC7793D93D28B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t24C02A1324216D668707B836DEE441F5AF258081 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenCallback_1_Invoke_mB35201EA284C80915255DDC706CBB58D64CFC0FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tD1B3F38DB61901D8835507448768B08701C3746D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t31B04D935D4E890DE0A30DFA01B8B931FF5DBFFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PluginsManager_GetDefaultPlugin_TisT1_t0E372CEAC223E24829181714BD7081D1ED0E8855_TisT2_t1A317392D2BC29857CB52C1A930D6A4DD8BD2535_TisTPlugOptions_tD1D47CBD660D9137AD25629864DBC7ECAD9B4B21_m98665500FF3C2637C791DF60ABE06E95ACF02A1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t275E429810626F7339469573855B5F9021789CFA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOSetter_1_t6E05A324E8073D715EA95CBC391FE70B14ECC3E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t1A317392D2BC29857CB52C1A930D6A4DD8BD2535 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t0C5DEDF5728E3004B3DE1297FCFFF1DF4C9825D2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t54ABD46A155450A839042A970B8A3E6548087B28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupSpecials_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_m6FC23B801A9C5649CF6479553FE514E796906A89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tE15BC2F103281E62B718D20B7BB9DDD3CA3AFC28 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t88C4DFDF86DFFB6407F08DF6037B3695F7F5BAE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOGetter_1_Invoke_mA2E144F27E67179DAE3B6109B55EE262F92A7BFB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mAD5DB662E7C52EFEE5CEFA21FD0202566A2011D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tC4A76B819474D2BA458497F91AF356C820C52665 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetRelativeEndValue_m822FDD211A289A90039247BB1837E19D55BB8BAD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetChangeValue_m7E722039C4FA3663DC761B3131878BBE455CACCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupDurationBased_TisT1_t5767A194576CC1FC1703FC747AE22F6F38E4DC35_TisT2_tC4A76B819474D2BA458497F91AF356C820C52665_TisTPlugOptions_t5A83E4DE28BE3F411DB15E4B0AB1CC567A47C48C_mE7A85ABBA6CBC87082DBF866E455D7625738A121 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_tC5697CE5E11F7E0523504C0A7C74A751716C8F71 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tE48501F6F32076F79C09C65FC49FD7C48012689A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupSpecials_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_mD6E69265EA8A0D3688C27732AF45932E724EACB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tBA2E2F88BF83E9A87073C91C5FD73DFF23A23BA0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t23BC1B197BDFE6BE418FA0E21E33A23B49D3807F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DOGetter_1_Invoke_m41E85F763632E85D83AAC979D5DE5C44D11A1A6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_ConvertToStartValue_mFF5A754363FEA2C83960B675F143A94BE54F387A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetChangeValue_m1B2E33F9FB1A52B4B475D87506FCFB87BD811186 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DOStartupDurationBased_TisT1_t9BE45F2AC84095915053DF1355DA7AC1326CACC4_TisT2_tE48501F6F32076F79C09C65FC49FD7C48012689A_TisTPlugOptions_tB643754D9EDC8BE0118A9AC83507E6467E22C9E5_m1F1B3EE9D5471D06FB46CE9AD30833F49F7392AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t5756CB69570B9C889182A88B6F32082721CE12E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_tBDB7EC4EC3549E999B77349B5A20028E2BBDE1C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_tCA2FCF7AA9959A985B00252151C895B69EA3A15F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TPlugOptions_t81F31C9EB1EF9CDF582023A3E98CD64BDEF38AE2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tE3473B09F549037482683D400BE8E56768D983CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_GetSpeedBasedDuration_m61C28E14F638BE4A1E7A27125FF7174E76615B3E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_t56A1D382DD6C07F3648B8262CDB7AD3604F1DD50 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_tC288634F7135CC961B6CBCC0354BA7262633F50A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t85A5DEEAB068C58207EBA88688D8D5B186828792 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t456028CE0EA15DC4A666100047C0558E4C6E82CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0_m96BD2338C04130B436D330F23E8960CBA097AB06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TPlugin_t65B0BEBEFF39DADF3B36F0A9D1FD12B05F2AD5B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t744CCA93F42B6AB407D56BA97402304508E8D126 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t4E74A76CB105CF046EF3331B0412FF23ED036CA5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t53DC76D5984ADF08F803D2EF4B64EE98F4A57892 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_tB93B55C6E17E38076065933CF07D49689DB02FE9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_tD013770946BAD079A42F798BC5F230E0FF5F69EE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TPlugOptions_t9A66C58DDECE4DACC0C69B129D543BEE21B3F31B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t1A91562B894654D9A32CD90D2B7B3DC2EBEF280F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenerCore_3__ctor_m191BF86F9477FAADDABF38521D973DF5C3F4D3E3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoChangeEndValue_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mE766FEF8C2F23A334FD3F30CBC396FBB176329EC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenerCore_3_t841E07FBE92B7B1D2448883910420C746AB0616A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ABSTweenPlugin_3_t5AB0AED1E5225912CAC7F48FA4FF66BAE1933071 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_SetFrom_m48C5D8F8A180AB9DC080E05C59645B234BEC379D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_Reset_mA12C9B5353FD460FA20AA3CD721D5B17FC95B0D2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_IPlugOptions_Reset_m55A4D8608C4E079C3A7E9CD41AC6688929CC7606 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOGetter_1_t7802D68DE7558EF7801E6CA418C5D553AD76CA6F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DOSetter_1_tEC198FBF803582D0CD192AE4EEFD9A4C88981A1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoUpdateDelay_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_m35E1A653C5CD8389FB8E96C215A42ACFEF43742E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tweener_DoStartup_TisT1_tB1F68C2D1908AB30427BB0754E97C17BBB0447DA_TisT2_t5AEA8765F0C6AEEBD1F7D858573B72440FFDEE00_TisTPlugOptions_tB19EF07E5CA83CF7A0B08277A724AA6098ACA83F_mA9E5230B08C67D3E34A768D86717D3B469806DD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ABSTweenPlugin_3_EvaluateAndApply_mBDC24C4678B22CCCA94F560A49E8752ED3192BD4 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_DOTween_CodeGenModule;
const Il2CppCodeGenModule g_DOTween_CodeGenModule = 
{
	"DOTween.dll",
	602,
	s_methodPointers,
	23,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	38,
	s_rgctxIndices,
	103,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
