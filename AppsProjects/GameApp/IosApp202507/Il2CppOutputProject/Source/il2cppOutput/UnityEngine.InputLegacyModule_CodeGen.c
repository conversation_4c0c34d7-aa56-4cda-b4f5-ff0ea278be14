﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD (void);
extern void Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A (void);
extern void Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B (void);
extern void Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D (void);
extern void Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B (void);
extern void Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299 (void);
extern void Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A (void);
extern void Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC (void);
extern void Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6 (void);
extern void Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0 (void);
extern void Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C (void);
extern void Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE (void);
extern void Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745 (void);
extern void Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391 (void);
extern void Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490 (void);
extern void Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960 (void);
extern void Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21 (void);
extern void CameraRaycastHelper_RaycastTry_m79A654495BD2C09623E9067BCC70D23A0DA3BF58 (void);
extern void CameraRaycastHelper_RaycastTry2D_m132832B9171CD030AD231A63BF70D1226ED1F373 (void);
extern void CameraRaycastHelper_RaycastTry_Injected_m4A9EA285FB7B24B7B3D894E7EE997B41ED302DEF (void);
extern void CameraRaycastHelper_RaycastTry2D_Injected_m2620821FE8CB793C314AAE43E3B4C7BEAE5D4C9E (void);
extern void Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62 (void);
extern void Input_GetAxisRaw_m47C0CF8E090561A2F407A4E11D5F2A45044EB8E4 (void);
extern void Input_GetButtonDown_mEF5F80C9E8F04104E807D9CBD6F70CDB98751579 (void);
extern void Input_GetKeyInt_m2FFCC49AF36B74247CC1B412E9787A15D0984E95 (void);
extern void Input_GetKeyUpInt_mB26B433DD3A21ACAF04D23252B09068EFFEDA0F9 (void);
extern void Input_GetKeyDownInt_m0B655F969FCBC011BC2616E3E5A657CF7D76568A (void);
extern void Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA (void);
extern void Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3 (void);
extern void Input_GetMouseButtonUp_mBE89CC9C69BBEA9A863819E77EA54411B0476ED6 (void);
extern void Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1 (void);
extern void Input_GetLastPenContactEvent_mAB37B70407FDE3DE8230B55D3D4828DC50EAF0BF (void);
extern void Input_ClearLastPenContactEvent_m3240C9BA6CE089E85624BD0FA031A48CDE2664A3 (void);
extern void Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434 (void);
extern void Input_GetKeyUp_m9A962E395811A9901E7E05F267E198A533DBEF2F (void);
extern void Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2 (void);
extern void Input_get_anyKey_m6FF00BB4F01FA996E2FB3F3727EBC414C8EB2CEB (void);
extern void Input_get_inputString_mB022DED50E8838B8319C591830BEB21E4904561B (void);
extern void Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C (void);
extern void Input_get_mouseScrollDelta_mD112408E9182AA0F529179FF31E21D8DCD5CFA74 (void);
extern void Input_get_imeCompositionMode_mAD9C0224B3845A9132D4265AF468FF203AA43BAC (void);
extern void Input_set_imeCompositionMode_m0399964447DDFE54E04F516A01696862F7174C9A (void);
extern void Input_get_compositionString_mC9E603E4FB61090827F77A3D509BF3AA0A48C9A9 (void);
extern void Input_get_compositionCursorPos_mE1E48997CA0C30D206D08FAF06455123D8D24D15 (void);
extern void Input_set_compositionCursorPos_m16A856BFBF1DAE42B0089696906F530334861E98 (void);
extern void Input_get_mousePresent_mAD77FFD987CD5B998AFAD4DAECADBC76034026BF (void);
extern void Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39 (void);
extern void Input_get_touchSupported_m2A4FA398A793861AE1BC5971A1363552AB33BEEF (void);
extern void Input_get_multiTouchEnabled_m2A24C57718A0D5395A31FEC6B1E9A9CB98359108 (void);
extern void Input_get_touches_m7CFDF6848F3EC3A8FE458436B2B8BD14B5C65CEF (void);
extern void Input_CheckDisabled_m359B281F7F5DDAB74780E1898311AECD9B0ECCE1 (void);
extern void Input_GetTouch_Injected_m04E25DD035583531339AB310FBDD4F5A30817F87 (void);
extern void Input_GetLastPenContactEvent_Injected_mA7F2DE408F86C38FC15357E468645CD82D38A84A (void);
extern void Input_get_mousePosition_Injected_m7EF43ADB535051F9182A366CA84951F946984E1A (void);
extern void Input_get_mouseScrollDelta_Injected_m31BF633C98E1BBA4583E7FCE0573BDECB1BA4A29 (void);
extern void Input_get_compositionCursorPos_Injected_m67C1CB8A21F4708CA76FAB39E3BC436DE33C214E (void);
extern void Input_set_compositionCursorPos_Injected_m46E4934CD2A9F2E97B8A86D52169C848EF6D91E8 (void);
extern void SendMouseEvents_UpdateMouse_m7EC9A21B75612D3AA9ECEE2BB142A27481147FF1 (void);
extern void SendMouseEvents_SetMouseMoved_mDA82278267CC62E9942C9D6154610AD7F3308B51 (void);
extern void SendMouseEvents_DoSendMouseEvents_m17FCC3A684C7BC4A7A6AA7EBB62E3F56AAB416A7 (void);
extern void SendMouseEvents_SendEvents_m3DA609154485AAA0F9501BAA602F63A9E357D35C (void);
extern void SendMouseEvents__cctor_m6B1E043BF3142442AC8312E9B28A54C487A5A755 (void);
extern void HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541 (void);
extern void HitInfo_op_Implicit_m4162F5E6640E1D2CB82AB0AE00090AB46CE997AC (void);
extern void HitInfo_Compare_m374F9DF7CFE9C31264CD38D42FFFCA4DB0E6CD05 (void);
extern void InputUnsafeUtility_GetKeyString__Unmanaged_mD736022C3E6C98ED4B4AEA824AC2DA98A3A1B223 (void);
extern void InputUnsafeUtility_GetKeyUpString__Unmanaged_mFD0D4ED74B64E565CB3F543CBBE38F6AE84AC27A (void);
extern void InputUnsafeUtility_GetKeyDownString__Unmanaged_m53E2CCD225AC1CE7CB40002403EDC5AEF266EB8D (void);
extern void InputUnsafeUtility_GetAxis_m034889C00C914572B94F091E8EC646E86408A29F (void);
extern void InputUnsafeUtility_GetAxis__Unmanaged_m288AEE066E04CCFDD229629882CA42DBD1D85CF2 (void);
extern void InputUnsafeUtility_GetAxisRaw_m0EBF75200CDA664FCCD51BDB4C8C8798CDD6097B (void);
extern void InputUnsafeUtility_GetAxisRaw__Unmanaged_m31953D955E75028D274A75661CAB2E13E13320AE (void);
extern void InputUnsafeUtility_GetButton__Unmanaged_m270892EB7D8C88443200DF972F76076AC28952B2 (void);
extern void InputUnsafeUtility_GetButtonDown_mB35B46B92DA28196820B996E2420E5E320088EE4 (void);
extern void InputUnsafeUtility_GetButtonDown__Unmanaged_m411E8743768382A33B3778E734AF072F8DBDECC4 (void);
extern void InputUnsafeUtility_GetButtonUp__Unmanaged_mCCE34158CE05432541102B1C2AEC6545E25D0732 (void);
static Il2CppMethodPointer s_methodPointers[76] = 
{
	Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD,
	Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A,
	Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B,
	Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D,
	Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B,
	Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299,
	Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A,
	Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC,
	Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6,
	Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0,
	Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C,
	Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE,
	Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745,
	Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391,
	Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490,
	Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960,
	Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21,
	CameraRaycastHelper_RaycastTry_m79A654495BD2C09623E9067BCC70D23A0DA3BF58,
	CameraRaycastHelper_RaycastTry2D_m132832B9171CD030AD231A63BF70D1226ED1F373,
	CameraRaycastHelper_RaycastTry_Injected_m4A9EA285FB7B24B7B3D894E7EE997B41ED302DEF,
	CameraRaycastHelper_RaycastTry2D_Injected_m2620821FE8CB793C314AAE43E3B4C7BEAE5D4C9E,
	Input_GetAxis_m10372E6C5FF591668D2DC5F58C58D213CC598A62,
	Input_GetAxisRaw_m47C0CF8E090561A2F407A4E11D5F2A45044EB8E4,
	Input_GetButtonDown_mEF5F80C9E8F04104E807D9CBD6F70CDB98751579,
	Input_GetKeyInt_m2FFCC49AF36B74247CC1B412E9787A15D0984E95,
	Input_GetKeyUpInt_mB26B433DD3A21ACAF04D23252B09068EFFEDA0F9,
	Input_GetKeyDownInt_m0B655F969FCBC011BC2616E3E5A657CF7D76568A,
	Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA,
	Input_GetMouseButtonDown_m8DFC792D15FFF15D311614D5CC6C5D055E5A1DE3,
	Input_GetMouseButtonUp_mBE89CC9C69BBEA9A863819E77EA54411B0476ED6,
	Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1,
	Input_GetLastPenContactEvent_mAB37B70407FDE3DE8230B55D3D4828DC50EAF0BF,
	Input_ClearLastPenContactEvent_m3240C9BA6CE089E85624BD0FA031A48CDE2664A3,
	Input_GetKey_mE5681EF775F3CEBA7EAD7C63984F7B34C8E8D434,
	Input_GetKeyUp_m9A962E395811A9901E7E05F267E198A533DBEF2F,
	Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2,
	Input_get_anyKey_m6FF00BB4F01FA996E2FB3F3727EBC414C8EB2CEB,
	Input_get_inputString_mB022DED50E8838B8319C591830BEB21E4904561B,
	Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C,
	Input_get_mouseScrollDelta_mD112408E9182AA0F529179FF31E21D8DCD5CFA74,
	Input_get_imeCompositionMode_mAD9C0224B3845A9132D4265AF468FF203AA43BAC,
	Input_set_imeCompositionMode_m0399964447DDFE54E04F516A01696862F7174C9A,
	Input_get_compositionString_mC9E603E4FB61090827F77A3D509BF3AA0A48C9A9,
	Input_get_compositionCursorPos_mE1E48997CA0C30D206D08FAF06455123D8D24D15,
	Input_set_compositionCursorPos_m16A856BFBF1DAE42B0089696906F530334861E98,
	Input_get_mousePresent_mAD77FFD987CD5B998AFAD4DAECADBC76034026BF,
	Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39,
	Input_get_touchSupported_m2A4FA398A793861AE1BC5971A1363552AB33BEEF,
	Input_get_multiTouchEnabled_m2A24C57718A0D5395A31FEC6B1E9A9CB98359108,
	Input_get_touches_m7CFDF6848F3EC3A8FE458436B2B8BD14B5C65CEF,
	Input_CheckDisabled_m359B281F7F5DDAB74780E1898311AECD9B0ECCE1,
	Input_GetTouch_Injected_m04E25DD035583531339AB310FBDD4F5A30817F87,
	Input_GetLastPenContactEvent_Injected_mA7F2DE408F86C38FC15357E468645CD82D38A84A,
	Input_get_mousePosition_Injected_m7EF43ADB535051F9182A366CA84951F946984E1A,
	Input_get_mouseScrollDelta_Injected_m31BF633C98E1BBA4583E7FCE0573BDECB1BA4A29,
	Input_get_compositionCursorPos_Injected_m67C1CB8A21F4708CA76FAB39E3BC436DE33C214E,
	Input_set_compositionCursorPos_Injected_m46E4934CD2A9F2E97B8A86D52169C848EF6D91E8,
	SendMouseEvents_UpdateMouse_m7EC9A21B75612D3AA9ECEE2BB142A27481147FF1,
	SendMouseEvents_SetMouseMoved_mDA82278267CC62E9942C9D6154610AD7F3308B51,
	SendMouseEvents_DoSendMouseEvents_m17FCC3A684C7BC4A7A6AA7EBB62E3F56AAB416A7,
	SendMouseEvents_SendEvents_m3DA609154485AAA0F9501BAA602F63A9E357D35C,
	SendMouseEvents__cctor_m6B1E043BF3142442AC8312E9B28A54C487A5A755,
	HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541,
	HitInfo_op_Implicit_m4162F5E6640E1D2CB82AB0AE00090AB46CE997AC,
	HitInfo_Compare_m374F9DF7CFE9C31264CD38D42FFFCA4DB0E6CD05,
	InputUnsafeUtility_GetKeyString__Unmanaged_mD736022C3E6C98ED4B4AEA824AC2DA98A3A1B223,
	InputUnsafeUtility_GetKeyUpString__Unmanaged_mFD0D4ED74B64E565CB3F543CBBE38F6AE84AC27A,
	InputUnsafeUtility_GetKeyDownString__Unmanaged_m53E2CCD225AC1CE7CB40002403EDC5AEF266EB8D,
	InputUnsafeUtility_GetAxis_m034889C00C914572B94F091E8EC646E86408A29F,
	InputUnsafeUtility_GetAxis__Unmanaged_m288AEE066E04CCFDD229629882CA42DBD1D85CF2,
	InputUnsafeUtility_GetAxisRaw_m0EBF75200CDA664FCCD51BDB4C8C8798CDD6097B,
	InputUnsafeUtility_GetAxisRaw__Unmanaged_m31953D955E75028D274A75661CAB2E13E13320AE,
	InputUnsafeUtility_GetButton__Unmanaged_m270892EB7D8C88443200DF972F76076AC28952B2,
	InputUnsafeUtility_GetButtonDown_mB35B46B92DA28196820B996E2420E5E320088EE4,
	InputUnsafeUtility_GetButtonDown__Unmanaged_m411E8743768382A33B3778E734AF072F8DBDECC4,
	InputUnsafeUtility_GetButtonUp__Unmanaged_mCCE34158CE05432541102B1C2AEC6545E25D0732,
};
extern void Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD_AdjustorThunk (void);
extern void Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A_AdjustorThunk (void);
extern void Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B_AdjustorThunk (void);
extern void Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D_AdjustorThunk (void);
extern void Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B_AdjustorThunk (void);
extern void Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299_AdjustorThunk (void);
extern void Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A_AdjustorThunk (void);
extern void Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC_AdjustorThunk (void);
extern void Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6_AdjustorThunk (void);
extern void Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0_AdjustorThunk (void);
extern void Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C_AdjustorThunk (void);
extern void Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE_AdjustorThunk (void);
extern void Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745_AdjustorThunk (void);
extern void Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391_AdjustorThunk (void);
extern void Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490_AdjustorThunk (void);
extern void Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960_AdjustorThunk (void);
extern void Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21_AdjustorThunk (void);
extern void HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[18] = 
{
	{ 0x06000001, Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD_AdjustorThunk },
	{ 0x06000002, Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A_AdjustorThunk },
	{ 0x06000003, Touch_set_position_mF024C46352D1CB82991022138D48BC84D9248E6B_AdjustorThunk },
	{ 0x06000004, Touch_get_rawPosition_m15F230BC7B4B672380BF221E9BA1DC275180863D_AdjustorThunk },
	{ 0x06000005, Touch_set_rawPosition_m734916CD0826F5CD242A0C6647AC55E53272590B_AdjustorThunk },
	{ 0x06000006, Touch_get_deltaPosition_m2D51F960B74C94821ED0F6A09E44C80FD796D299_AdjustorThunk },
	{ 0x06000007, Touch_set_deltaPosition_mD2323B6E679DA9CE9FE1E3F4D3E2D12A33328E7A_AdjustorThunk },
	{ 0x06000008, Touch_get_deltaTime_mD07672B54CBA02C226097B54E286C1DFE96EC3BC_AdjustorThunk },
	{ 0x06000009, Touch_get_tapCount_mE75D2783AC38FCF536C99F36AB9F76AFA3EB7EB6_AdjustorThunk },
	{ 0x0600000A, Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0_AdjustorThunk },
	{ 0x0600000B, Touch_get_pressure_mB8214D0E920156CA4679BAC03E86106E8E4BDA5C_AdjustorThunk },
	{ 0x0600000C, Touch_get_maximumPossiblePressure_m2D147A58465EB39B397722D8597CF9E06AC85FAE_AdjustorThunk },
	{ 0x0600000D, Touch_get_type_mB505EF2DCF13160DFA0C6AAF406DCB4CBED20745_AdjustorThunk },
	{ 0x0600000E, Touch_get_altitudeAngle_m26DEF010E2CDC23F4FADE8E49A986D557C07D391_AdjustorThunk },
	{ 0x0600000F, Touch_get_azimuthAngle_m2F11532183492E608922A2F9D9EC9AC31D34F490_AdjustorThunk },
	{ 0x06000010, Touch_get_radius_m5BC9C50DABBB17B07742BAFC6CC36A6736AE7960_AdjustorThunk },
	{ 0x06000011, Touch_get_radiusVariance_m6F54BE964B91C3B2F8FA2A483E1FDB644B282B21_AdjustorThunk },
	{ 0x0600003F, HitInfo_SendMessage_m7834418ACE250BBCBA38ADCF0892E475BD1AD541_AdjustorThunk },
};
static const int32_t s_InvokerIndices[76] = 
{
	6957,
	7109,
	5913,
	7109,
	5913,
	7109,
	5913,
	7043,
	6957,
	6957,
	7043,
	7043,
	6957,
	7043,
	7043,
	7043,
	7043,
	8266,
	8266,
	8222,
	8222,
	10110,
	10110,
	9834,
	9831,
	9831,
	9831,
	9831,
	9831,
	9831,
	10213,
	10422,
	10455,
	9831,
	9831,
	9831,
	10397,
	10420,
	10452,
	10450,
	10412,
	10288,
	10420,
	10450,
	10303,
	10397,
	10412,
	10397,
	10397,
	10420,
	10397,
	9572,
	10282,
	10282,
	10282,
	10282,
	10282,
	10455,
	10455,
	10288,
	9581,
	10455,
	5806,
	9847,
	9199,
	9066,
	9066,
	9066,
	10110,
	9446,
	10110,
	9446,
	9066,
	9834,
	9066,
	9066,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule = 
{
	"UnityEngine.InputLegacyModule.dll",
	76,
	s_methodPointers,
	18,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
