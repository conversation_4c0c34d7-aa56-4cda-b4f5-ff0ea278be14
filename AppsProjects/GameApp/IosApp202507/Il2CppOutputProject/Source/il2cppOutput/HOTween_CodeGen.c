﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ABSTweenPlugin_get_initialized_mBDDF3D1051BAFBF04CAAF5600D799AE51D452397 (void);
extern void ABSTweenPlugin_get_duration_m89FD525C11A2B0C1D4E5E3EF97463D03E0A2294F (void);
extern void ABSTweenPlugin_get_easeReversed_mAAEC274D4BDA281C6D951D19219CBC7DF87EC7F5 (void);
extern void ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB (void);
extern void ABSTweenPlugin_get_pluginId_mFCE87898552AD55F94F187D04F122744A1F84732 (void);
extern void ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482 (void);
extern void ABSTweenPlugin_Init_mA17A13339EA1B9D8A939B5E8144C57FE9342CC29 (void);
extern void ABSTweenPlugin_Startup_m65BD374E8FE6506B6A18E96781C5EDE421C67085 (void);
extern void ABSTweenPlugin_Startup_mFD189898D282DDE09A868E5EE01F75FCE9AD17EE (void);
extern void ABSTweenPlugin_ForceSetSpeedBasedDuration_m6E588939B5461E0C32C5B2481E99352E5D2884BE (void);
extern void ABSTweenPlugin_ValidateTarget_m66406EE875742E1BCB7C9DCAE2EE1161B2C9E1AF (void);
extern void ABSTweenPlugin_Update_m54620FC8D82BF95907988D37F4A121FE62796599 (void);
extern void ABSTweenPlugin_Rewind_m738D072B63A84CA9B808B389B7DF22F35A3E4FBC (void);
extern void ABSTweenPlugin_Complete_m25B5AD6CE114E6224C2BC80D575E5148753855DB (void);
extern void ABSTweenPlugin_ReverseEase_m92B9B2CBB7BB54EB77D3B10096BA95DFFACE1416 (void);
extern void ABSTweenPlugin_SetEase_m4AAE182A2A27955FBF83F423E4767EBF6A6C6088 (void);
extern void ABSTweenPlugin_CloneBasic_mCA9249440372C5ECD0B8A07D357C7D005CBDF22E (void);
extern void ABSTweenPlugin_ForceSetIncremental_mA9ECF8FE53328732FF65B7D89CF8141C2BFA8CAE (void);
extern void ABSTweenPlugin_SetValue_mC7C27E3FC25603C4746F4ABC04B40608D04FD945 (void);
extern void ABSTweenPlugin_GetValue_m810D5F423DFD9C32A186F424A5D3A663A2321793 (void);
extern void ABSTweenPlugin_U3CInitU3Eb__0_m3AC9DD44A7F99ACCECE7A1032DD05F37F80E5515 (void);
extern void ABSTweenPlugin_U3CInitU3Eb__1_m6353601D6C7B6BF15D5E220A706E95B92255F5EE (void);
extern void ABSTweenPlugin_U3CInitU3Eb__2_mCD61D8F86333FDB6F318431897E932B9A7378AD3 (void);
extern void ABSTweenPlugin_U3CInitU3Eb__3_m9838D2BCC7A3AC0CD94BDD8A692EF2A4989D7E88 (void);
extern void ABSTweenPlugin_U3CInitU3Eb__4_m17DE55E31DBA6AF46B5712CBAD7F51D6E318CD0A (void);
extern void ABSTweenPlugin_U3CInitU3Eb__5_m7138A8ADEA042C5A8A6058AAF399C19AFF0AC6A7 (void);
extern void ABSTweenPlugin_U3CInitU3Eb__6_m589A0B22872A4412794687FDF0842AACB79967B5 (void);
extern void ABSTweenPlugin_U3CInitU3Eb__7_m41F013450C5ECA30A766DAAFC75469F9A9BF1DFA (void);
extern void ABSTweenPlugin_U3CInitU3Eb__8_m3B5286D95153A1F203EBA402A15050A3B5B8513B (void);
extern void ABSTweenPlugin_U3CInitU3Eb__9_m08E6957101740BB9B0AD1BE0C4B33C74E7AB96C9 (void);
extern void PlugVector4_get_startVal_m323C2CCC02E56CA7734774684DC5D586E7092D0F (void);
extern void PlugVector4_set_startVal_mE3D096554EF7BB66851D208D734407E3910FAD40 (void);
extern void PlugVector4_set_endVal_mEBA96CDA1789F6830535C48CB0FDEAF46A00465A (void);
extern void PlugVector4__ctor_m348E95DFFA753B9E5A4DF1A5AB25DEA5DBD84E81 (void);
extern void PlugVector4_GetSpeedBasedDuration_m5FB4CCB881C268A469C2F70E51FC6616B3468DA5 (void);
extern void PlugVector4_SetChangeVal_m38D897F2F327F342273B2D156AAE907114F09196 (void);
extern void PlugVector4_SetIncremental_mDC1CF72D8F738B9882CABFA3ED5E21A6E67A7ACC (void);
extern void PlugVector4_DoUpdate_m18904AE76350DF08B13A2C571094A826693B40C7 (void);
extern void PlugVector4__cctor_mF9292E195392CFB203296E5BA1499A26243DBA75 (void);
extern void Strong_EaseIn_m0483C21A71E5F16EFC4D10F1609B98F8513AEF9B (void);
extern void Strong_EaseOut_m0A19DB1451FF95150B4299BD17C3CBE0236FA7C8 (void);
extern void Strong_EaseInOut_mAC63986A2D328C9C559620FBFC2CF56A55220306 (void);
extern void Sine_EaseIn_m8F2DF0C35FBBC081FAF538ECD423FC8560B5BF1C (void);
extern void Sine_EaseOut_m339B51615647BF15DA6D29468516B4511A8D461E (void);
extern void Sine_EaseInOut_m3074821CC490E1CBDC21DF0388146C8E1CA0BD1D (void);
extern void PlugVector3_get_startVal_m46FD420263EDA94FCDF8DC93621763BFA0E1D80C (void);
extern void PlugVector3_set_startVal_mF099CCA24944603F06CED437FEBDFEA1BB39C06D (void);
extern void PlugVector3_set_endVal_m709148639019BF2E7B7EF7DB5CF0C58CC2992060 (void);
extern void PlugVector3__ctor_mFAEE32D17D68FA03776ED57F2C2A351D19A2621B (void);
extern void PlugVector3_GetSpeedBasedDuration_m1DDEFBF69FFD1B465B6FD356BF3F90BE33FD79DC (void);
extern void PlugVector3_SetChangeVal_m675B7A4DAF1755A41D55E1C34A9176FE9DE652D6 (void);
extern void PlugVector3_SetIncremental_m0A677E5D8DD0E2A7CBD84E7A4158CF8681934D35 (void);
extern void PlugVector3_DoUpdate_m8634A68A8D2D930C910432BDE09A89A4D577A5D2 (void);
extern void PlugVector3__cctor_m6CECC632BFFBDF370FAC7E574097E1612F418929 (void);
extern void Expo_EaseIn_mE079107F93E684246C15E6C9F68D1ABDD1893CEA (void);
extern void Expo_EaseOut_mFE84EA64AC24075B4E4F7A0B59E3F7FE53410463 (void);
extern void Expo_EaseInOut_mABDB0911FC0AAA62F27E40461DEC1C834F160DB2 (void);
extern void ABSTweenComponent_get_steadyIgnoreCallbacks_m9E9A5C415C84D543DFD48CE5F3D02DBD6F30210D (void);
extern void ABSTweenComponent_set_steadyIgnoreCallbacks_m1D4B5669107E0C1FCF0C82EA392A7B0C07396D2A (void);
extern void ABSTweenComponent_get_id_m5A9E8E7E68CA48D8A21DECF2210EC783F8C6476A (void);
extern void ABSTweenComponent_get_intId_m298D39375AF5ABB6E7EC0BAC00C822C6FA19DE1E (void);
extern void ABSTweenComponent_get_autoKillOnComplete_m751B1195EBAA58794EFE10B0213DCBC98156F22C (void);
extern void ABSTweenComponent_set_autoKillOnComplete_m6AC9EF9FACB5242B63298B37924E26ADA623327B (void);
extern void ABSTweenComponent_get_timeScale_mF396ECC7DF96D2A7F668A573A29955101E2EE10A (void);
extern void ABSTweenComponent_set_loops_m978B41F5E22987B72272D45160E0AACBE6D9CCCB (void);
extern void ABSTweenComponent_get_loopType_m776B4D637E82D1974ED75CE147976142B5A4494D (void);
extern void ABSTweenComponent_get_duration_m33D5D9D4A443B22FF0600B088495472828CF4D57 (void);
extern void ABSTweenComponent_get_updateType_mCE83133275081B1853C81C29F76D513B021EA3CA (void);
extern void ABSTweenComponent_get_completedLoops_mCB99D27D16B35BA7C3945C581A0A5F6267F776B9 (void);
extern void ABSTweenComponent_get_destroyed_m4FE7ACE9A38BE5BED05C117B3F147838083CFC01 (void);
extern void ABSTweenComponent_get_isEmpty_m297A2DC5196DD23873EC5363FB67EA08CB653105 (void);
extern void ABSTweenComponent_get_isPaused_mC4658335E31DD94220CD838FA773B1A38B6420C9 (void);
extern void ABSTweenComponent_get_isComplete_m709E527B954A24C4FC9BFA6AAEAF82332441991F (void);
extern void ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F (void);
extern void ABSTweenComponent_Kill_m2E726969956C5A1DD8ECEC32E7A91F70358D686E (void);
extern void ABSTweenComponent_Kill_m3DFA228E5371D61661F164FAB94E5A115CA02744 (void);
extern void ABSTweenComponent_Play_mA4E25844A54A78E6103097AE8B3E805B515F32D7 (void);
extern void ABSTweenComponent_PlayIfPaused_m0C8C4728B484F6E707B808FB4FC74C43E0B3EDF0 (void);
extern void ABSTweenComponent_PlayForward_m42BA5CCCC60C3F56EB0062699ECC45904C6C54CA (void);
extern void ABSTweenComponent_PlayBackwards_m23D206CC2390B71E78C9C862C6498ED8A56E583E (void);
extern void ABSTweenComponent_Pause_m9546E13997477B5C042B2C1D01FD0D7C738F589D (void);
extern void ABSTweenComponent_Reverse_mD83B291E248DA79C2C891D50BDFE2FE917E35ED5 (void);
extern void ABSTweenComponent_Complete_m2578EED27A83C77A4D0CA98ECDBD87B00B46D68A (void);
extern void ABSTweenComponent_GoTo_mE0BD939714FD53919BBBDC0FC582C7904A42C1FE (void);
extern void ABSTweenComponent_GoTo_m2D44CADB0991294DEA56BBA7CF1F7F363A09E9C2 (void);
extern void ABSTweenComponent_Update_mF03B81B1EB29C4DB2A22EF5EDCF3D43AFA445391 (void);
extern void ABSTweenComponent_Update_m8AF70CD3E8BFA5884D27601693DA3247E3DD8D53 (void);
extern void ABSTweenComponent_Update_m99A24CE62CF9C1A448F852586139A974265FBBB2 (void);
extern void ABSTweenComponent_Startup_m4CC98C5D235884440C27E64D16C4075D3C125FCA (void);
extern void ABSTweenComponent_OnStart_m178F7E4D946A12175D04BAF7C8C3874478E0284D (void);
extern void ABSTweenComponent_OnUpdate_m1FD80B3FCA41421822A7EAE81F82F3B3430C30C0 (void);
extern void ABSTweenComponent_OnPluginUpdated_m0DC19FAC831C175A9A8EE5F99AE2779F9E00A2D9 (void);
extern void ABSTweenComponent_OnPause_mD0B7490543C83B590BD8769B02A60E2BC81291B3 (void);
extern void ABSTweenComponent_OnPlay_m1810BC48C50A7BE4168D8BC3C07045F4C19A1FC0 (void);
extern void ABSTweenComponent_OnRewinded_m5298F6161E77258A8830CB70A9DB480759D3881C (void);
extern void ABSTweenComponent_OnStepComplete_m87E71F36D6F8A96C568A2180DE25C54E20AB5C44 (void);
extern void ABSTweenComponent_OnComplete_m1B8AB14B8416FA66F696ACD7C2B5A8DD173D8849 (void);
extern void ABSTweenComponent_OnCompleteDispatch_mCA9855AB885055006C5704367128A343506BA27D (void);
extern void ABSTweenComponent_SetFullDuration_mBA55B39D9FB2454121316DAFE3177E4FF6EB2540 (void);
extern void ABSTweenComponent_SetElapsed_m169521518328B85FAEB2CF09E7B55E9BAFE04FF9 (void);
extern void ABSTweenComponent_SetLoops_m1E36E59B9B01524A512A988DF270F0BDFE9BD61E (void);
extern void ABSTweenComponent_ManageObjects_mC9F35A9F01F21F8286171C5BF0CAC28BE348B02A (void);
extern void ABSTweenComponent__ctor_m465808B3338E38EB1DAEC7F3DE4396F0E1C93030 (void);
extern void PlugVector2_get_startVal_m5CF12299805FE8E0F8025F4EB163ACFFE5959FC9 (void);
extern void PlugVector2_set_startVal_mD5F1A09E26CCB04568CC52B689BAD5525AA3B7D2 (void);
extern void PlugVector2_set_endVal_m06ED4E7B4B603F1C60FBED5ACE8B28E8743DB656 (void);
extern void PlugVector2__ctor_mD38E3F80476EF22E23B0D6902C1EBFBE597E50DD (void);
extern void PlugVector2_GetSpeedBasedDuration_m964C066C06FBCD8491AD2981F44CF0616A0277C7 (void);
extern void PlugVector2_SetChangeVal_mD65187D3C7890307A4B50D094E3B78C611C5A8A0 (void);
extern void PlugVector2_SetIncremental_mB7BAC7344EF60FD50B0094632A692ED23F6FCD07 (void);
extern void PlugVector2_DoUpdate_m3D096C0AAD00D846EA88768415FD59DEC8BC6A85 (void);
extern void PlugVector2__cctor_m2921F7533DBB4C6DF95964D7321C6C4A294C68BE (void);
extern void Sequence_get_steadyIgnoreCallbacks_mB25992C362BB1974788076165BBBA95156D0036B (void);
extern void Sequence_set_steadyIgnoreCallbacks_m5002FB7444EDB08B1178E4DD00C7361C998A07C5 (void);
extern void Sequence__ctor_m4CF40D7E6CC4079B0B4142E79FFAAEF3A9D8C7B4 (void);
extern void Sequence__ctor_m7DE2851F8E5515E78804DB5F0A7D62E8A2C3FE66 (void);
extern void Sequence_Append_m22CE048A4EBA99DFEE293457DED03EB5B6F54EEA (void);
extern void Sequence_Append_mD2C07D17BCC7ABE2CA226C607B80D18B4B394774 (void);
extern void Sequence_Insert_m510E64285AF5B8E8065C0BCCFC7F4D647599309C (void);
extern void Sequence_Insert_m72C0CB6A93DC49A1E3272D534899E1E77DF4CA99 (void);
extern void Sequence_Kill_m386796B2BB421A98363AA686056519969FA5CC8E (void);
extern void Sequence_Rewind_m615BB348B5E222DE91219DC90CAEF8610B654A82 (void);
extern void Sequence_Restart_m4F5DAD41C6FBE0B375F8386163ADB39C5CA436D4 (void);
extern void Sequence_IsTweening_mDBB3E207924CC428F52574B63D9423E288CDE56E (void);
extern void Sequence_IsTweening_mA89838BCF977E80FC565E45FD5E7FA8CDAD2FA87 (void);
extern void Sequence_IsTweening_mAB0EBDFB6487155C088FA207FB0C9C907EC9C486 (void);
extern void Sequence_IsLinkedTo_mF612EB8E4A639CB00D1AC6FE7DE2346AC85003BD (void);
extern void Sequence_GetTweenTargets_m75046F7C7279951C48804C84EF8B840FBE0181E9 (void);
extern void Sequence_GetTweenersByTarget_m6A0E6AD2196387303A8C5CA860ABA84F2242177F (void);
extern void Sequence_GetTweensById_mA061DF3F0661645ED393E4C657628066567C4020 (void);
extern void Sequence_GetTweensByIntId_m8F9789343A6FF8DB3C570EE0D33F1E526DB2C38D (void);
extern void Sequence_Remove_mC0A8D195AF01D4D8514D7515286352256C677E31 (void);
extern void Sequence_Complete_m85B4798DE45C436030821A377E5086944708AFD9 (void);
extern void Sequence_Update_mFBE3519EC1E0E338786DDC2F4D5B9336A565024C (void);
extern void Sequence_SetIncremental_m0F90853B8868690B51B772996970E20A46212764 (void);
extern void Sequence_GoTo_m1D906AB8B2015367612171C2A30E0264A1C8000C (void);
extern void Sequence_Rewind_m5B26ED45F7E39420AA2339B9002DB806D09A17F2 (void);
extern void Sequence_TweenStartupIteration_mB0DBC249010A4139A17F60D7089A0FECA9C8014C (void);
extern void Sequence_CheckSpeedBasedTween_mE5AA1DC3678A02B64608A42F1832AF9766372053 (void);
extern void Sequence_Startup_mE310D4C19F20A59C8383630EC7644415576A9D4C (void);
extern void Sequence_FillPluginsList_m601C0AAB6D2CE2D537BF5BA32AF4A4E5C6A7ED76 (void);
extern void HOTSeqItem_get_duration_mBDDCCEE33423F7AE6CC984A2D656AD0BF475E550 (void);
extern void HOTSeqItem__ctor_m2D5D94147964710E915377E55DD94C892A329D49 (void);
extern void HOTSeqItem__ctor_m90C3D7AE021A85A40DE53956A35FB621DE3E241B (void);
extern void PlugFloat_get_startVal_mAFA28FC0AE84BA35FB40EFC28A28E092F090EFEF (void);
extern void PlugFloat_set_startVal_mB9C2F4C152C4E3D3E6562038CC5620A2E2E4D533 (void);
extern void PlugFloat_set_endVal_m419C832FBB5F33F6C1F6A1A0E5182970A177B8D9 (void);
extern void PlugFloat__ctor_m7F3FBD710426F3E263968ABEA94E1083679AB401 (void);
extern void PlugFloat_GetSpeedBasedDuration_mB98828F340C4BACF333264CE7E0B7B01EFF78892 (void);
extern void PlugFloat_SetChangeVal_mA6149A2EB83CA4013CD65F39522A9AD7E17DD24B (void);
extern void PlugFloat_SetIncremental_mEF440A47F879F1F59B1F756165B77305BCC9624E (void);
extern void PlugFloat_DoUpdate_m51CFF48B1CD24F1C70654315A46E4C5956FB2ADD (void);
extern void PlugFloat__cctor_m6695317CFF74497C3994AC080566ECD8183F2312 (void);
extern void EaseInfo__ctor_mAC1FA8B506CA44FBF0FF0D33CDFC23EBFADB4B81 (void);
extern void EaseInfo_GetEaseInfo_m10B4224CB3CF864CE6542B884D237591EA5600D9 (void);
extern void EaseInfo__cctor_m3C7ECF412052C42FFFF15C3F734E2930954716A1 (void);
extern void PlugVector3X_get_pluginId_m7A84FA60242AFDB5E811083B56FAE5BA891772A1 (void);
extern void PlugVector3X_get_startVal_mAF8F88BBEBDDA3569072D9F453C6CE99D73DCB2E (void);
extern void PlugVector3X_set_startVal_m44B3DE0AB61DE71F44B429262F0D913541786347 (void);
extern void PlugVector3X_set_endVal_m045ABC1819FF22929A8696971ED0FFA53C0F7005 (void);
extern void PlugVector3X__ctor_m644C8B4AC23A419FC5F30EDFEA83F7EBD5F5E16E (void);
extern void PlugVector3X_GetSpeedBasedDuration_m31B8811FFDD60A611FE72FF0869F5BECAA3936FF (void);
extern void PlugVector3X_Rewind_m706D239191A7AB6060E863B082FCCDB3671AE472 (void);
extern void PlugVector3X_Complete_mEA7A09DCF2264EC6C32A920064FE81CAF0F25216 (void);
extern void PlugVector3X_SetChangeVal_m82D1173A51856439A9B00890D44F100BC9B956B8 (void);
extern void PlugVector3X_SetIncremental_m88D87FFA47890D198E31B18F2F7750BA7B3D18B7 (void);
extern void PlugVector3X_DoUpdate_m0149A41389A97C8EC68C61D8A6975E3AB25266F3 (void);
extern void PlugVector3X__cctor_mA2207A63AA5A93EB8D5C40F114ABADD00035703B (void);
extern void Utils_SimpleClassName_m04D18EADDE8255C2C1DDB00067B4F55C8EB8F5FA (void);
extern void TweenInfo__ctor_mDDCD207D5005E97C81EEE05059E224D728D6F634 (void);
extern void MemberAccessorException__ctor_mBCB11A3A1EBE888618E5287DCE0DF3456A185C34 (void);
extern void MemberAccessor__ctor_m078BFB6F5CBCD6787EC382C9556A9DCC38246937 (void);
extern void MemberAccessor_Make_m335B46F216D80D7407F3E3B0C2D6697BCE52238F (void);
extern void MemberAccessor__cctor_m692857559A8D2F924B30F98B509791D9B6373054 (void);
extern void MemberAccessor_Get_m86E1F739791AC8BA478811A0B3691A2444DC236C (void);
extern void MemberAccessor_Set_m65771271AEAC7D7B6DA2EFB0706FAF6C3B02A2EC (void);
extern void MemberAccessor_EnsureInit_m36D58457E073ED0916D2B98A17DE37069D17F29C (void);
extern void MemberAccessor_EmitAssembly_mAB3D7FE853EE8705A0EAD4F922B8ACA0486B4BB7 (void);
extern void EaseCurve__ctor_mA7CCE59E7AF1173FE998BD193C38541E3737996F (void);
extern void EaseCurve_Evaluate_m147EB11018D649E704C57B17AFF002CB52082F96 (void);
extern void PlugVector3Y_get_pluginId_m31C85298E6443FD1A84F8C19F2917D8FB312948E (void);
extern void PlugVector3Y_get_startVal_mD7B2EA0CA3DA609A0C334AFF8AA7F34BC40E8E99 (void);
extern void PlugVector3Y_set_startVal_mFA8D4801329EEB2800BABC559A9A2ADB80F0619D (void);
extern void PlugVector3Y_set_endVal_mAEB32584F6CD999654482B7E5A17B76F086E2923 (void);
extern void PlugVector3Y__ctor_m40F8C95F6E65A18125D169E35F64EFA5FF9A6CEB (void);
extern void PlugVector3Y_Rewind_mE1985E442456D45F543D5E45D036BC13C2AECE5E (void);
extern void PlugVector3Y_Complete_m54DA943C22DC711EFD31D9A5874E795B155E65CD (void);
extern void PlugVector3Y_DoUpdate_mB62EF707B95E01B4F954D0727EED84E4BDD93C89 (void);
extern void Quart_EaseIn_mA75F2285E9606F752546479F052900C39B64391E (void);
extern void Quart_EaseOut_m8A2E53A1A9FBB7112210EF3B1D41A2B7FDFE91B9 (void);
extern void Quart_EaseInOut_m838F40008D0C12C0FD24085ACEEDC9526A4A0A62 (void);
extern void Bounce_EaseIn_m608B24D4E668356B0B3C1359D7416F7070A16431 (void);
extern void Bounce_EaseOut_mDA691F17504DAD856566F150117053316BA0FEF2 (void);
extern void Bounce_EaseInOut_m992D9E8068FD6BA636C474B396E6B6F9F590E202 (void);
extern void PlugColor_get_startVal_mF3BFAD49D057AC4DE22A5E4FFBCEBD2F5B5C1DA8 (void);
extern void PlugColor_set_startVal_m60426CA6075DD867FB45A0DB5E6F5BCE0279253D (void);
extern void PlugColor_set_endVal_m9FCF95741BBE985346108C1988D172D6652EB154 (void);
extern void PlugColor__ctor_m9587F07E6E13DF59F6DBB8795BC7408688ABF745 (void);
extern void PlugColor_GetSpeedBasedDuration_mAE713EDAFC8DDEE3D32C5488437280890C8F51F7 (void);
extern void PlugColor_SetChangeVal_mAE13564F434264B39D81026E3B968E475D424847 (void);
extern void PlugColor_SetIncremental_m745C86AEA2F415DBE6839C5E08052220BC546601 (void);
extern void PlugColor_DoUpdate_mB2437E92EC4ADAAA2BD6FBEB866BF32CF21AA1AC (void);
extern void PlugColor__cctor_m215B7C1B8700A5F91CED1F74B2DE93446B407EA5 (void);
extern void Elastic_EaseIn_mE0A62F93A794AB086662988DB43D14AB9B537585 (void);
extern void Elastic_EaseOut_m0D11F3641F175171662B07A6E4BB4830666BFB77 (void);
extern void Elastic_EaseInOut_m0BE3D30A293D0966D2100377C8536244A4707A64 (void);
extern void TweenEvent_get_parms_m8C20A9085A932E57113912DD9C86FAA4F28F928D (void);
extern void TweenEvent__ctor_m20EB08AE4E804741D72FBED05DE8925CC9C132EF (void);
extern void TweenEvent__ctor_mB18727BF083858DFD49D0CC8B608DDD8E12FF581 (void);
extern void PlugString_get_startVal_m757CF3E811D5C7475E6A3A4A5D1EADEEF865C118 (void);
extern void PlugString_set_startVal_mA635110F2B3871A7907EB3CF0E8B99838CDEC87A (void);
extern void PlugString_set_endVal_m195FB73B4089AE62AB3568DE42805494CA4B06E7 (void);
extern void PlugString__ctor_mBC5CF13283AEDED7546061AFDDCD1BC3049D9D12 (void);
extern void PlugString_GetSpeedBasedDuration_mE283B4B082A68676E5AC69B531632ABA5FCE232F (void);
extern void PlugString_SetChangeVal_mE2FECDE431F2C460DF03D83789F49D66106DDD10 (void);
extern void PlugString_SetIncremental_m788A0DB8289D31B245BC40F1A7395C7B82E9C4DF (void);
extern void PlugString_DoUpdate_mA7F54BB02DB019141AA55FB1BB8C8DBCABDE7D10 (void);
extern void PlugString__cctor_m32C5FA5636E25BC6682C935D16E5CD1D7A168B29 (void);
extern void PropertyAccessor__ctor_mAC50BF853972A4BEB92F2FB0B7D2A0ED7D7101E3 (void);
extern void PropertyAccessor_get_CanRead_m66D5F8B1AFB928B4BC6B669A3526FE3DF04C6ABB (void);
extern void PropertyAccessor_get_CanWrite_mC0B86BBB3800EDC15DE50B8E21ADF73CB08C4D71 (void);
extern void PropertyAccessor__EmitSetter_m97663FCFD5A4B196293F7BC4FB91B3840EA17736 (void);
extern void PropertyAccessor__EmitGetter_m3AC0F73816480A18BAB48C5D095DD4A67E42AFF8 (void);
extern void Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39 (void);
extern void Tweener_set_isFrom_m3E5ABBC9B076D66C6006F2E422A6B15C0899CD24 (void);
extern void Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612 (void);
extern void Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA (void);
extern void Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E (void);
extern void Tweener_get_pixelPerfect_mFE6C00B8CA66560011BB93EC9DD91F9C75565CB5 (void);
extern void Tweener_get_speedBased_m696E4B1D829288D5294B1F036ABD7B3664569ADA (void);
extern void Tweener__ctor_mBFCBFA145B7C00694B7D240C5D5BD2CC22FD3BCB (void);
extern void Tweener_Kill_mDA58BAFECA0A60B2BEFA61AEBF61585520CD8C0E (void);
extern void Tweener_Play_mF00B9C69E4391700F195756339B2EF55F0C5C77C (void);
extern void Tweener_PlayForward_mC868ED59C0604AADBA54BDD068C3AAC3D598A89E (void);
extern void Tweener_Rewind_mB06F27F64873E5270A05DF612DA543D87927CA1A (void);
extern void Tweener_Rewind_m5583DE03D36135ADC8D1933323A976420E64F8BE (void);
extern void Tweener_Restart_mBEBE51196CBE7FB0C57E0FBBA6AD57F186CC1475 (void);
extern void Tweener_Restart_m55A6C4EC8E2BCAA7B783C432259EA5084563B66F (void);
extern void Tweener_Complete_mFB87F383300ECDF0AF040504E25CAEB94270766D (void);
extern void Tweener_IsTweening_m8350AF1094F0E5CE0688EDA0A4D1C04FFBB3F1F2 (void);
extern void Tweener_IsTweening_mCEFA7D8C3532AC54FCBC9E2EB78B4E973EA4C830 (void);
extern void Tweener_IsTweening_m7858F896B234BFBED87B5F7D3575B896F2A4B8D0 (void);
extern void Tweener_IsLinkedTo_m820FD8453215EAD28758B4FE45D81F0D055AA060 (void);
extern void Tweener_GetTweenTargets_m7BF7E00E68AA6B1283DF6C835FAA61E5375C5B5B (void);
extern void Tweener_GetTweensById_mE53739DD0397C7B8235582CA493D6CC97EF11478 (void);
extern void Tweener_GetTweensByIntId_m7F7391D0479C83D537B500C23FD75F9964D2605F (void);
extern void Tweener_Update_mF903632092FCFF830BDB19BA3DDE9DB7A869C8A5 (void);
extern void Tweener_Update_m54E5B2B861DD80E328B93CF8A683DB587041CF56 (void);
extern void Tweener_SetIncremental_mFC28846E3CF17F337C6EF38CB8D8C003940BD1D6 (void);
extern void Tweener_ForceSetSpeedBasedDuration_mFED6284796D464777B09FEC58F5CD4E873883A29 (void);
extern void Tweener_GoTo_mB3C4272B8D3A2F1B7A792BB200C2BA2B9531FE34 (void);
extern void Tweener_Rewind_mB36B7B2F32E757A3A1CAF944437CF57DC16AC796 (void);
extern void Tweener_SkipDelay_m353FBA4781C15F780D9105893158A3DEB53B1C2A (void);
extern void Tweener_Startup_m4F0AB7F88BA7E6386180C9BEDCE18A9A22CA7093 (void);
extern void Tweener_Startup_m163CF924A92960258D81EFBC440981926A8B2007 (void);
extern void Tweener_OnStart_mE5CCF6D2DAD86B09215A2C58E42E52E50E556EC0 (void);
extern void Tweener_OnPlay_m456EA0CCCA16F7E3FBB0E228F43C9CE6105CCA9A (void);
extern void Tweener_FillPluginsList_m3ED9C044BD5F755A0E057E74460B02FF06792D26 (void);
extern void PlugRect_get_startVal_m5B29DFC493FBFC91AF89863EEAF844D6A7CC0360 (void);
extern void PlugRect_set_startVal_m312992D761647EDC5E4352717531F6F5C58A2559 (void);
extern void PlugRect_set_endVal_m730C2A32D4576C0E29D56E1D8E513BBC40D49A9B (void);
extern void PlugRect__ctor_m1CCAC707C847323D566B4B359BD492E0368C1750 (void);
extern void PlugRect_GetSpeedBasedDuration_m63BBF18C7E298DAE0D828D7BEC304BB780CD8918 (void);
extern void PlugRect_SetChangeVal_m9D64E909A37CA7EC8E2B9844746CF66A23D40424 (void);
extern void PlugRect_SetIncremental_mD40687AE3C223F1A30DADF1F0ABACF65A77838C9 (void);
extern void PlugRect_DoUpdate_m338FA2B05212F0414CB3A2777D8842F5EED8E39D (void);
extern void PlugRect__cctor_mEF9A9FD09CB70CE6039922AF832CF4EE8F7BA39C (void);
extern void HOTween_get_isUpdateLoop_m4E4B8E1ABC396200F116DE8534974F4ECE5AA8F7 (void);
extern void HOTween_set_isUpdateLoop_m471A911475EF097FA21C9926E5FF228F01AF41CC (void);
extern void HOTween_get_totTweens_mA0CC42C6BFFAA9D44C63D93E239CA842C8EA1E8C (void);
extern void HOTween_Init_mBB8E8744A2E181D7AF508FB4C7F750CD9503F69D (void);
extern void HOTween_Init_m60025DE48C10CAAC7794B9B747F5A929120F56B4 (void);
extern void HOTween_Init_m4DC575B4903BABA72C0D32E425F350A1B48638A4 (void);
extern void HOTween_OnApplicationQuit_mC9E5E82C54FDF0D11127556581210E8F92433311 (void);
extern void HOTween_OnDrawGizmos_m5F42335229D4F73CADF4052F1E21D580107CC315 (void);
extern void HOTween_OnDestroy_m5F6768C0BE928261400533DD15044AF548F0B90D (void);
extern void HOTween_AddSequence_m41EB7C1A2575808595536E16DDB885E993330CE4 (void);
extern void HOTween_To_mEDE968C7E0AC34665AFC0C89838CA3DEC061828B (void);
extern void HOTween_To_mC3A013E5E30627489CC3F4FC9F8FEF6926FAD8EA (void);
extern void HOTween_To_m226F3DD76A9FA41694FA940763949859850C3272 (void);
extern void HOTween_To_m005266815F277CD43C97C6CA0DCC5CE29732B919 (void);
extern void HOTween_From_m78904A99B67A85085B08138053A36B72688EB223 (void);
extern void HOTween_From_m2B419086CB8D6015A14AB2B3F0885C84D726B1A2 (void);
extern void HOTween_From_m7B31A9447E4518E6DE143E9F87D046CD879CE164 (void);
extern void HOTween_From_mBFFB94DAA8281B2569B4FB8258395F9AD00CF3F8 (void);
extern void HOTween_Punch_m1EF6566B7E4D4C00634F856A7DD33DBA65018A24 (void);
extern void HOTween_Punch_mF377FE1704FCB001335CF825DFE187C735C04718 (void);
extern void HOTween_Punch_m7044D893616C60F04A75190534AE2EAA35D76E85 (void);
extern void HOTween_Shake_mAB76C4CBCC2B53F59F96BC18B45761FC1A86C21B (void);
extern void HOTween_Shake_mF501BEADE365CE91F6FF58F7E89C0EB2810B8D95 (void);
extern void HOTween_Shake_m74A8BB5F4FA716F312DD06EF705B46871AEA43E5 (void);
extern void HOTween_Update_m326B4EFAFCFEF46257C88BCCE0A320691A167C11 (void);
extern void HOTween_LateUpdate_m6FAA822325D5A35B1B67A56C5D4E1DE9BE56A7EB (void);
extern void HOTween_FixedUpdate_mF7D6CCF6B54FDC3E67655DDCDF059CB2076E0FFD (void);
extern void HOTween_TimeScaleIndependentUpdate_m7E58A37202376936025EC8EC6B66946A60ED4AF0 (void);
extern void HOTween_EnableOverwriteManager_m17350AF9D21B2495ACA002E83E71AD6E822FC919 (void);
extern void HOTween_DisableOverwriteManager_mCCC8B33B97F5B42A77AEBE5766502459D352F564 (void);
extern void HOTween_Pause_m36B20DDEE4E67EFF2E7559D65C1CFFB8CB4D9CAC (void);
extern void HOTween_Pause_m725824969B779D166239CFB85AA32B492F245579 (void);
extern void HOTween_Pause_mB0355B927283444D09CF0266C6A1A2AECE745DAA (void);
extern void HOTween_Pause_m29459E3CF37F589345C450B63D6B3B8FC8BE4202 (void);
extern void HOTween_Pause_m1AD3028CB9AA28D3DABBA5DA9F7F3C252B685440 (void);
extern void HOTween_Pause_mF29900B29EB4CABA21F56C584AA010E09672812F (void);
extern void HOTween_Play_m06588A995E7A78FE781D2FC6E951E9F8779D7D87 (void);
extern void HOTween_Play_m4026BBD807F2C8A104964059FDB0CCDE6F75296C (void);
extern void HOTween_Play_m2462D006C5C7B3CACD4C835C3642B2280076DDC5 (void);
extern void HOTween_Play_m39B35C13CD325D465CF95DCF26F8703EB6846048 (void);
extern void HOTween_Play_m513D5490E652A3C48FECD598186C1504FDF5F1E6 (void);
extern void HOTween_Play_m1B13EF6E77F0DC7D08EB6BE00740BB0888538DA2 (void);
extern void HOTween_Play_m77EC29251A7B9EBB4E23968A86F867939BEEF785 (void);
extern void HOTween_Play_m9597A9B825249484DFEE5A6FEA5333C9BE41BA4C (void);
extern void HOTween_Play_m3EAE9459F6F8B5123FCC0A59B79BFD7C6CF137CA (void);
extern void HOTween_Play_mED7D274F62C6B2FC7CB44A0D58651E74684962AE (void);
extern void HOTween_Play_mA958B4967451687E074FE8A08B6CCC844467B3EE (void);
extern void HOTween_PlayForward_m4924AA53B6348D8921ADFAEEDD0E5F2A39C3663F (void);
extern void HOTween_PlayForward_mBDBC9C0F0DC409A9AC9208BF0DDE6BE189570D3C (void);
extern void HOTween_PlayForward_mDAC9D2C47CCB0EC60DF597AABA5D7A78B2EA6961 (void);
extern void HOTween_PlayForward_m7693A31FFDCFE68F5F9C4E806492A11FC04C8649 (void);
extern void HOTween_PlayForward_mD05C3298D17515CB4F9015E910F75585E5128DAE (void);
extern void HOTween_PlayForward_m31B84925AC435E1E798D6A7AFEB48B6E1F967EBF (void);
extern void HOTween_PlayForward_m35ADFEAB59A00D1E2EDFCF1FE93543DC174CC4CC (void);
extern void HOTween_PlayForward_m7E01DDC24B3A329071E8F8D34FA73923487587AB (void);
extern void HOTween_PlayForward_mE8B3365418750AD6C0EF319CEBF1B70A5FB7C900 (void);
extern void HOTween_PlayForward_mD40FBBB81907B8A6DF2D3A7C0DF1C9C091D49374 (void);
extern void HOTween_PlayForward_m004C3162B0AC84A7F867A6B1873AAB2FCFB13374 (void);
extern void HOTween_PlayBackwards_m0800D56CBE16AD829B24FC7CFEA81B19E1BFD07F (void);
extern void HOTween_PlayBackwards_mC161D180361510B8033C2460E5F66042045FB63F (void);
extern void HOTween_PlayBackwards_m7D7A4726F618E79BD04B5D77C8C4C2E21DE38A76 (void);
extern void HOTween_PlayBackwards_m1BD235429F733F80E5CEBB5527EE03FF3FFCFBCF (void);
extern void HOTween_PlayBackwards_mC9B7CE3FDED1E665051D00876A45B31A172648DE (void);
extern void HOTween_PlayBackwards_mF556CC7CEBCAC16D5EC966081DA4A89E2237862C (void);
extern void HOTween_Rewind_mEA72EA86B693925D40C08696CA60D429F3322209 (void);
extern void HOTween_Rewind_m6CEF74A727325E63EAC8FBCE2BE2DDD012D2A673 (void);
extern void HOTween_Rewind_m4F4B5DDD509FB5B6C8E5B8B605D4B7B61972761B (void);
extern void HOTween_Rewind_m6F22978B08486426AD61828232356A6862FE3BA4 (void);
extern void HOTween_Rewind_m7C2934940ED05E6584001D62689924959BB864E1 (void);
extern void HOTween_Rewind_m84A90FFA16142BF593FB17C9920B84756DE510E0 (void);
extern void HOTween_Rewind_mC030D3E3DB98B54C33954A0470B9A039ABB1FD45 (void);
extern void HOTween_Rewind_mA10C86688E5965E628FEC06E583F3B25AABCB8B3 (void);
extern void HOTween_Rewind_m76A3F347F69420864B05D3038C6FFEA224C72EB5 (void);
extern void HOTween_Rewind_mB3C944C3749E04135F9A298B07A6C839822C36EC (void);
extern void HOTween_Rewind_m497F7B8CB58930A3ECDE0E6A44A8149B0AEDB47F (void);
extern void HOTween_Restart_m9D2D2E09D011A55D92EBE920D3EE50D8F0BC7DC2 (void);
extern void HOTween_Restart_m50838EB4F8CF53F3B0B5C7ADC525733B1DFC8E55 (void);
extern void HOTween_Restart_m09C607EF58F3430A29695103AA4FB6191D299343 (void);
extern void HOTween_Restart_mF61D02756212211AF922D119902B9B861B5E91CD (void);
extern void HOTween_Restart_m36338F07689E584EB8C37D4398634C3E8EA43D87 (void);
extern void HOTween_Restart_m71D8EA70A30A80AFF038DEBB82BD349AF714C463 (void);
extern void HOTween_Restart_m2AEA9978D2E36E0D2FE1FF0A0F43363B253A273D (void);
extern void HOTween_Restart_m8FA07E3F1C3114CEBF4C6EADA06E893A7E538E7A (void);
extern void HOTween_Restart_m397FB57EC223A2216C57254BE03A46A8F2228A25 (void);
extern void HOTween_Restart_mCA34CCF5B1AB25166F347C3C9E2C1D2033390005 (void);
extern void HOTween_Restart_m62FA6867560DF13E964C66385A403C80CD7A97E4 (void);
extern void HOTween_Reverse_mF2D4FA1AD09C321EDA6CE31B9BD15153D74F9663 (void);
extern void HOTween_Reverse_m089175060475AE5A087EF8C13041A643738BCCBE (void);
extern void HOTween_Reverse_mCBE55833929586E049CFA9B967F2FC9046AF87D6 (void);
extern void HOTween_Reverse_m868E3147AC24103FDB11DFC68883C990B2D76446 (void);
extern void HOTween_Reverse_mB2CE2D250B7A5525FFCFF23BBDCCF6321DFA33E7 (void);
extern void HOTween_Reverse_m1117289D45139FB8B11D18C00B494935638B6372 (void);
extern void HOTween_Complete_m7692C74B7D77EE060122E2899DC2C9F833836159 (void);
extern void HOTween_Complete_m7967016F92602D76F5B62BE3BB20543292CB69F2 (void);
extern void HOTween_Complete_m7C62723855B11A93368357F71587B3F4C17D0BC4 (void);
extern void HOTween_Complete_m264D7CAC799A02629F7885BF5F5D99E1E5154878 (void);
extern void HOTween_Complete_mE669EDC255F126CEB3145E08079042387FF78B52 (void);
extern void HOTween_Complete_m7A932984C40E0E64317A6C81EB91C3ABE1C574AE (void);
extern void HOTween_Kill_m542542A4D6E4C8D52C4E8B507B99E9CF357BB2C9 (void);
extern void HOTween_Kill_mB7D9658E79952D232C7D1D8E13436A4D9B32A63C (void);
extern void HOTween_Kill_m463128617EC67F15716F8088482E06FEED184E57 (void);
extern void HOTween_Kill_m479085E6D5B9C8A705AD9B0AFEB5BC5FA5D4BC35 (void);
extern void HOTween_Kill_m9F95EF3B73391D91D1C97E2C5762F01C1EB05CAB (void);
extern void HOTween_Kill_m605F46A651F8E92248EA1A7C4F0620AF0A767090 (void);
extern void HOTween_RemoveFromTweens_m3AAB66A7F25FD74281232E5DF44530F3A6A37A47 (void);
extern void HOTween_GetAllTweens_mE126EF81B97750F731F8397CBE9233B71A165FDF (void);
extern void HOTween_GetAllPlayingTweens_m76B55DBF92813D433293B451770770FED5935A78 (void);
extern void HOTween_GetAllPausedTweens_m5776DC5D7303846ED8F67A52BBEDA1A24BC871BE (void);
extern void HOTween_GetTweensById_m789AC5CC42B470248B6245BEA197AB82B3B8952D (void);
extern void HOTween_GetTweensByIntId_mA78091927D96162AF1403A55534CC31122F45480 (void);
extern void HOTween_GetTweenersByTarget_m6449D4AF921B6FFA83FA78202976116AACD18E29 (void);
extern void HOTween_IsTweening_m18DF4038D7CF357C9573667CB0E0BDDFCA6BA2B1 (void);
extern void HOTween_IsTweening_mFA40D585ADFCE8C34D3EF88450465BB0DEF07333 (void);
extern void HOTween_IsTweening_m069B84357C47D489CCB4BDDB33AC368805729CC6 (void);
extern void HOTween_IsLinkedTo_m98345AAD61153C87F268200CB21DD1639D306916 (void);
extern void HOTween_GetTweenInfos_mD0BDF65A625518D04354736F26D58C16EDE43829 (void);
extern void HOTween_DoUpdate_mE9209BD9CD358C3BEDCB5E9AE2D19C9FCD5BCE93 (void);
extern void HOTween_DoFilteredKill_mA3513323DE658C49A62A4ACDCD46CCC8ADAF3A40 (void);
extern void HOTween_DoFilteredPause_mB9D7A0A081B676D73D05BC0C69E05CC17DA388B9 (void);
extern void HOTween_DoFilteredPlay_m7FA13654F82E5E74743BC116A12561EA26E7D34F (void);
extern void HOTween_DoFilteredPlayForward_m992B7C98A7B222DB74BBB12AFB2803E5AF0775DB (void);
extern void HOTween_DoFilteredPlayBackwards_m6D27F391EAB2332553C22C27824F84A544661368 (void);
extern void HOTween_DoFilteredRewind_mC4B083061F3606034213278E6600B6317354839B (void);
extern void HOTween_DoFilteredRestart_m25C33E58CD019B72FABBBA1F924FA0650DAB749F (void);
extern void HOTween_DoFilteredReverse_mA99D351C5D1FB204A478866962D16B34B5EC3628 (void);
extern void HOTween_DoFilteredComplete_mBB72839D58F8AB1E57AAE8D065304B6660A0DD41 (void);
extern void HOTween_DoSendMessage_m88B006E16146E8559219FE3BF4553AF0A5B91BB8 (void);
extern void HOTween_AddTween_m70343A4A80FF6EBCFE8A3A5CF1CCF50519BC21CE (void);
extern void HOTween_NewTweenInstance_m64A9EBBA3B2352DAD7D8277CFBE2417B34983B05 (void);
extern void HOTween_StartCoroutines_mA41F4A1BB9CAC629090E1C6C16D3AF726BB966BC (void);
extern void HOTween_StartCoroutines_StartTimeScaleIndependentUpdate_m39073F449B395224689522B71A81E368060883C4 (void);
extern void HOTween_SetGOName_mCB87EE598B6150694B8E5F1C2432FE84244F439E (void);
extern void HOTween_CheckClear_m1096EC36B2E75C5C1069B3E636615C4BD1F69898 (void);
extern void HOTween_Clear_m4DBED773FBC8AF10FBF43685CCB36A9F56097357 (void);
extern void HOTween_DoFilteredIteration_m9A816AA5FD62E854BA4F29B51A350CEAF33A022A (void);
extern void HOTween_DoFilteredIteration_m2C0285CE25227C02F4D46F63E53A71C6D4A6F977 (void);
extern void HOTween_GetPlugins_m014CC142D2906F93B6DC186F3FBF4FCFC81BDADE (void);
extern void HOTween__ctor_m83C69B29768EFD44BACDDAD0C96F0D69545B5CD1 (void);
extern void HOTween__cctor_mDEA1B0C271C718BEDE47A294E42472BD6E41D758 (void);
extern void U3CTimeScaleIndependentUpdateU3Ed__0_MoveNext_m642A675FD815288A5F820C1C356417FA6FD070F8 (void);
extern void U3CTimeScaleIndependentUpdateU3Ed__0_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1AAC5219BED738C305C2716F9DB8FEF02AC9F3EF (void);
extern void U3CTimeScaleIndependentUpdateU3Ed__0_System_Collections_IEnumerator_Reset_m589A2198D792866E38A77693BBE03563BC1AF49A (void);
extern void U3CTimeScaleIndependentUpdateU3Ed__0_System_IDisposable_Dispose_m48394E1E7045AF4D24C37B768D0459BA251CF0EC (void);
extern void U3CTimeScaleIndependentUpdateU3Ed__0_System_Collections_IEnumerator_get_Current_mE0D0116FC360C92B577A621BCE57B555FB71F842 (void);
extern void U3CTimeScaleIndependentUpdateU3Ed__0__ctor_m79AC21166CD82771DF6D78B30DB6CC41C7DED140 (void);
extern void U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_MoveNext_m9B759D99EEDDDF2721862797B94C5234740E5390 (void);
extern void U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF22FC076C7FF17D4D58DF98408746C797A1D05A5 (void);
extern void U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_Collections_IEnumerator_Reset_m67BD98FBCC5EFE58C75164816655B22EA5007433 (void);
extern void U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_IDisposable_Dispose_m7CECDA3CBCDB3DDA7160FFE159B031B7FAC486B6 (void);
extern void U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_Collections_IEnumerator_get_Current_mE62126A6F33F60C9E4751B9524B6300205A1AB7C (void);
extern void U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3__ctor_m7F59C9019DAD54A7DE99633F2C57747C4F8830AC (void);
extern void Quad_EaseIn_m1F12867C09D0F87BC57FDFC7504AA13CF7ED9993 (void);
extern void Quad_EaseOut_m613527EA22BFD03098F51DC43E1FECB82487B426 (void);
extern void Quad_EaseInOut_mFDB4FB26541415C9EC683B2CFB3A88CFCD050570 (void);
extern void Linear_EaseNone_mF4515939B52D57647BEA8C86BC2B1B73B770CB7A (void);
extern void ABSTweenComponentParms_InitializeOwner_mF88937400BEA35A760F2DC698CA459C44FE82327 (void);
extern void ABSTweenComponentParms__ctor_m689C96ED2202D6F626DB88BBF1F031D265508270 (void);
extern void MemberAccessorCacher_Make_mACFE79F8BAC232F6DD2F042799641B3E960367DE (void);
extern void FieldAccessor__ctor_mC2C90F03375989FF08526586262E328B5E4814D9 (void);
extern void FieldAccessor_get_CanRead_m90FE4B9D735BE2C17A04D1C35F56F1C699D42EAA (void);
extern void FieldAccessor_get_CanWrite_m7F4CAEEA4640328EA708651F438115148E2DF549 (void);
extern void FieldAccessor__EmitSetter_mEFBF8A9D12ECC870C8D24772A3811D2EC8B44E5C (void);
extern void FieldAccessor__EmitGetter_m2EF1BF63AC90273DC9134DAE6E82CB54E86E3DEC (void);
extern void PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186 (void);
extern void PlugVector3Path_set_pathType_mB72EF8EB3956A20D2D45AA4DCEBB3727603CFABE (void);
extern void PlugVector3Path_get_startVal_m4B0C3A1FD61836CB35759B84E5C97CE5CEDA4DED (void);
extern void PlugVector3Path_set_startVal_m8D9E885781CE7F85A304FB17058D4B60A5A48B60 (void);
extern void PlugVector3Path_set_endVal_m105459D2CDA35CD6650A04F2CFB690BE1188200E (void);
extern void PlugVector3Path__ctor_m3263C04904CBAF1C7F7B41FCA8433F2DAF755798 (void);
extern void PlugVector3Path_Init_m85E520A75C4F71095E071D4B01BAE0008A06A651 (void);
extern void PlugVector3Path_ClosePath_m0832EA5BB568B5780C96EFA67DD46D636CAEED70 (void);
extern void PlugVector3Path_ClosePath_mFF30CD58A7ADBE3860716938AE1D0B590EECE6D2 (void);
extern void PlugVector3Path_OrientToPath_mD014C263C462386E51EB32BFE838C361BF2F6358 (void);
extern void PlugVector3Path_OrientToPath_mDD2FD17CAE023690D586637E863EAB2F75BEDCAA (void);
extern void PlugVector3Path_OrientToPath_mD952BB4DB29845EC1FBA4A84CFAC615054204F0A (void);
extern void PlugVector3Path_GetSpeedBasedDuration_mF385E24D04D41BCA0487141CC86AAC2DF468594E (void);
extern void PlugVector3Path_SetChangeVal_mED72B4F145B086DCAA6008DDA4F396A507EC444D (void);
extern void PlugVector3Path_SetIncremental_m4BDBAA71BDBA0EE9727AB9D0AADB8CCD2E85A828 (void);
extern void PlugVector3Path_DoUpdate_m48865BA96D2EEC21A396216C47D336A71EBD7AC4 (void);
extern void PlugVector3Path_Rewind_m1F677A68251CCFA24A43639E764E1BBC763B003C (void);
extern void PlugVector3Path_Complete_m23E64294CE7F24812B449861F669C82231288B4C (void);
extern void PlugVector3Path_GetConstPointOnPath_m00566EF01E12762EB32F6317A830E61461A670E4 (void);
extern void PlugVector3Path__cctor_m40152CCEDE327C8DD3FD458F8A06426653DA0C35 (void);
extern void OverwriteManager__ctor_mEFBC7A321BD74D1CD40B2F02991D1C41DF99FE2C (void);
extern void OverwriteManager_AddTween_mB998E62A5D1E0C495C718F07B805E4E5B51D01E3 (void);
extern void OverwriteManager_RemoveTween_m45DEDD84C3EDC7D2EF90E5DF5772201EB4C22F0F (void);
extern void Quint_EaseIn_m6C3211561F2D84CDC0E99DB5F74C961EE3EFCF4E (void);
extern void Quint_EaseOut_m89CBF1324AC66BB28842633E0AA7751E98DE3DEB (void);
extern void Quint_EaseInOut_m279B4513D0474EC373DA9C86641607FFD6950507 (void);
extern void PlugColor32_get_startVal_mD9C0E957F190B58BF9AF2F3874F7464E0E7261AB (void);
extern void PlugColor32_set_startVal_m3D8B40246FD173C803497DC4A94C6C5490FD5EEE (void);
extern void PlugColor32_set_endVal_mEFFB93CBFEC846BF6102A9A4C377146950333311 (void);
extern void PlugColor32__ctor_mA746143BEC963C76BB01E625BE07D6E7B6D83E4E (void);
extern void PlugColor32_GetSpeedBasedDuration_m1845E1E00E9BBD94FA4AF9A02E4774C3FDD8FCC1 (void);
extern void PlugColor32_SetChangeVal_m0F2E1D66423E4F3CF74DA63D0D8002E5CD7831C0 (void);
extern void PlugColor32_SetIncremental_m0D105D6FD4A79E01E0E94F9A2E3A060189AE3371 (void);
extern void PlugColor32_DoUpdate_mA43CD82817D34A333A0E49381E01BCC37AFB0C63 (void);
extern void PlugColor32__cctor_mA54543A3A75DEBA7711695153EE0560717E5FB15 (void);
extern void TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B (void);
extern void TweenWarning_Log_mDD27E543707A5EFEDCBE8A709413D3156D9A938F (void);
extern void Path__ctor_mB08F108F59563B544D546B8A9EB2105FD46D4588 (void);
extern void Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74 (void);
extern void Path_GetPoint_m931F8C934DA00412C36CCE7D011F45F2F4F80555 (void);
extern void Path_GizmoDraw_mC7CF7E5C1B7567B315E5F7BAAF2EEEC671E9A7B7 (void);
extern void Path_GetConstPoint_m1DADD874A6EC9E06D13C398963002B81F9017653 (void);
extern void Path_GetConstPoint_mC350B3F2078D6AB8F49B9C5B063BDD6C79B0654C (void);
extern void Path_StoreTimeToLenTables_mF3AFBB4D067AB81A9B6EE85D91D340361D198EC9 (void);
extern void Path_GetConstPathPercFromTimePerc_m05DF6CE5DEE89C0D965748560E2D3E701C9D01B5 (void);
extern void TweenParms_get_hasProps_mD3C0CFB119ABC3898AC73225309DF329FDBF0952 (void);
extern void TweenParms_InitializeObject_m0B9ABD1E886C5141D7B5742E4D5A9240E15ABAA8 (void);
extern void TweenParms_Ease_m2239F2056CC81A905ED58A77D51E87293B229BAC (void);
extern void TweenParms_Ease_mB302FD168B34BF99116AA23AC925761871053D9B (void);
extern void TweenParms_Ease_m72F10CB93D8FC98D43D9FA9672DACE62F687F81D (void);
extern void TweenParms_Delay_mBAFB272EC1B21EAF7FBA48BC96E636977A6D6643 (void);
extern void TweenParms_Pause_mA62F1F2E657D0A048F6EB4A437ECC48EE58FA18C (void);
extern void TweenParms_Prop_m1E6374BA7365EFA50DBA6654C21A3E2D89FD8C81 (void);
extern void TweenParms_Prop_mADE8C79AEFF46387BD66FD1281A5F4871A977D89 (void);
extern void TweenParms_Prop_m52667C136BA3A423786787A2E8B27D3BB1E25BA0 (void);
extern void TweenParms_Id_m3F778CAACB05B79BF6D06E875CB4186154FEDEB4 (void);
extern void TweenParms_AutoKill_m336EF4095BD04F73271FC6BC18172531EF63AB58 (void);
extern void TweenParms_UpdateType_m94959C570D78097687980C2EB04908F0145076D4 (void);
extern void TweenParms_TimeScale_m3B26C008E3C0F957F044A4FC0AB97ED8ABBA9FB8 (void);
extern void TweenParms_Loops_mE2C2F106984F8D49D02CFA2B2EC9A09AF3BBB632 (void);
extern void TweenParms_Loops_mFD3B261B9B6C37DD20528F6E622E0145F0B23974 (void);
extern void TweenParms_OnStart_m9044C94DE290F1657821BF07ACF465D6A704BCEA (void);
extern void TweenParms_OnStepComplete_m1561B57D3EFDA3B2EF0D9A8D39716DBD838909CE (void);
extern void TweenParms_OnComplete_mDFB601AC949292EA7FE01D75074307CC233B3559 (void);
extern void TweenParms_OnComplete_m96A7E52367591089EB46EF61EC3C19ED84EFDBA4 (void);
extern void TweenParms_IsFrom_m03B51C6DE24F9912B052954DDDACD644E13BAC39 (void);
extern void TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B (void);
extern void TweenParms__ctor_mBBE01A0AC0D7F5D39B15749CAD6F18A84A9A013E (void);
extern void TweenParms__cctor_m18FB70B742F40C9BFBF1AA63DEE2863FE67F3686 (void);
extern void HOTPropData__ctor_mEB72EC44DC80528C9615FBB1580D2208C1C27DEA (void);
extern void Cubic_EaseIn_mF68A8ADE66D4E5173A9738D5590AB972781CE169 (void);
extern void Cubic_EaseOut_m76FCBA54077D20DB2AADF0670AFCC518C33922C9 (void);
extern void Cubic_EaseInOut_m9D8AEF7EDC6B0F59FFB2E03FB46440E491D41112 (void);
extern void Circ_EaseIn_mCA6A087D56618ADEC9B3CEFB23DFBE2050B3D5C2 (void);
extern void Circ_EaseOut_mFF24BECA2B1B493A21CD525869E6A9C854DB51EB (void);
extern void Circ_EaseInOut_m93524D3A4D7315DAD226CF7CBA838B0FEEBB88D4 (void);
extern void Back_EaseIn_m6BFA78FC66458D32BFAB1D2FD39602A6D28D001A (void);
extern void Back_EaseOut_m7D6F1FE29C491DBF471231363C3D4781548509DE (void);
extern void Back_EaseInOut_m6714F2F1366BA14CF68B317C646B8AC04015835D (void);
extern void PlugQuaternion_get_startVal_mC0E8AA76C47F7E0766D894BEAD22B7C3BFE910F0 (void);
extern void PlugQuaternion_set_startVal_m50A46CF17B0B042F15212C2A6DEEFEE04A161C00 (void);
extern void PlugQuaternion_set_endVal_m1BD6F0B44D962C4932367EC99B2E822AA904A0BB (void);
extern void PlugQuaternion__ctor_m46BD79B83263F7486AA657F2BDB40E50A2198049 (void);
extern void PlugQuaternion__ctor_mE3BC50FE78C20B2554A668FCC648878459D4A608 (void);
extern void PlugQuaternion__ctor_m653333B63186F7A0F1430587FAF26EE4A67302D8 (void);
extern void PlugQuaternion_Beyond360_m6130714D6E69D9FB0D5F7B24158EC0D84F42EF22 (void);
extern void PlugQuaternion_GetSpeedBasedDuration_m118AC301EE4847507E6A5623AFEB9BC793750601 (void);
extern void PlugQuaternion_SetChangeVal_m365CBC37FAECB8923EFF1499567CB1C8A320660F (void);
extern void PlugQuaternion_SetIncremental_m8E6A96935BE3ABB1E13BEB7A3709C58125A9E0E2 (void);
extern void PlugQuaternion_DoUpdate_m32C0D2E921B842C4D4D4DB98754D8845D7736B8E (void);
extern void PlugQuaternion__cctor_m890A83C1A49BDDFA299C50F6D10781E623E1ED31 (void);
extern void TweenCallbackWParms__ctor_mB37CAD56CA9F34BDAC55ED611104A2DBBE80B520 (void);
extern void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB (void);
extern void TweenCallback__ctor_mBD3FF0903457762300B12CB3AEA092B04F2BFD94 (void);
extern void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2 (void);
extern void EaseFunc__ctor_m258028586FD5AF6078A75793226DE7D379A13EA3 (void);
extern void EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A (void);
extern void FilterFunc__ctor_m09E23A301B8AAC21FA70732CB97E2E6EB0086B04 (void);
extern void FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC (void);
extern void SequenceParms_InitializeSequence_m8210CDA3A53CC6BD8A0035A2831BE4848C26B287 (void);
extern void SequenceParms_Id_mD12A9F7430E0AA3A442D933E730A099FB8C87497 (void);
extern void SequenceParms_Loops_mB8A56A26FF1C3FE24291B3E3FC89829191978C0F (void);
extern void SequenceParms_OnComplete_mD046B01C9C44C8A7DD4DB3F8E41B962148831AF8 (void);
extern void SequenceParms__ctor_mEA6154BD45B02D2D0E5FF50B0EFA93ACC4BB6CDD (void);
extern void PlugInt_get_startVal_mEC395E74FAAAFE265631ABEEBDEFA84C82F1006D (void);
extern void PlugInt_set_startVal_m7CFC9E6A628F0BD6AB817471F2F73510359C91E8 (void);
extern void PlugInt_set_endVal_mE7D91C5706E4D4FF356FFBEF59AE62BE2852AA41 (void);
extern void PlugInt__ctor_m36BBA904D1AA75C2195D945C7D808BB4404D404D (void);
extern void PlugInt_GetSpeedBasedDuration_m6C3D8EAE856E47198C2C95EE5ADBD2C439BA4FC5 (void);
extern void PlugInt_SetChangeVal_m1F3BA173BD58844E40013DBEF1CED2F0059AAEBC (void);
extern void PlugInt_SetIncremental_m8E4454E97CD02F65FC741ECEB005B9F83722C434 (void);
extern void PlugInt_DoUpdate_m6B8283DFE8E57B477FAF53BC743D4D05D69717DA (void);
extern void PlugInt__cctor_m4F1377CB8036E178D51448051BDE98A26504B115 (void);
static Il2CppMethodPointer s_methodPointers[574] = 
{
	NULL,
	NULL,
	NULL,
	ABSTweenPlugin_get_initialized_mBDDF3D1051BAFBF04CAAF5600D799AE51D452397,
	ABSTweenPlugin_get_duration_m89FD525C11A2B0C1D4E5E3EF97463D03E0A2294F,
	ABSTweenPlugin_get_easeReversed_mAAEC274D4BDA281C6D951D19219CBC7DF87EC7F5,
	ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB,
	ABSTweenPlugin_get_pluginId_mFCE87898552AD55F94F187D04F122744A1F84732,
	ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482,
	ABSTweenPlugin_Init_mA17A13339EA1B9D8A939B5E8144C57FE9342CC29,
	ABSTweenPlugin_Startup_m65BD374E8FE6506B6A18E96781C5EDE421C67085,
	ABSTweenPlugin_Startup_mFD189898D282DDE09A868E5EE01F75FCE9AD17EE,
	ABSTweenPlugin_ForceSetSpeedBasedDuration_m6E588939B5461E0C32C5B2481E99352E5D2884BE,
	ABSTweenPlugin_ValidateTarget_m66406EE875742E1BCB7C9DCAE2EE1161B2C9E1AF,
	ABSTweenPlugin_Update_m54620FC8D82BF95907988D37F4A121FE62796599,
	NULL,
	ABSTweenPlugin_Rewind_m738D072B63A84CA9B808B389B7DF22F35A3E4FBC,
	ABSTweenPlugin_Complete_m25B5AD6CE114E6224C2BC80D575E5148753855DB,
	ABSTweenPlugin_ReverseEase_m92B9B2CBB7BB54EB77D3B10096BA95DFFACE1416,
	ABSTweenPlugin_SetEase_m4AAE182A2A27955FBF83F423E4767EBF6A6C6088,
	NULL,
	ABSTweenPlugin_CloneBasic_mCA9249440372C5ECD0B8A07D357C7D005CBDF22E,
	NULL,
	ABSTweenPlugin_ForceSetIncremental_mA9ECF8FE53328732FF65B7D89CF8141C2BFA8CAE,
	NULL,
	ABSTweenPlugin_SetValue_mC7C27E3FC25603C4746F4ABC04B40608D04FD945,
	ABSTweenPlugin_GetValue_m810D5F423DFD9C32A186F424A5D3A663A2321793,
	ABSTweenPlugin_U3CInitU3Eb__0_m3AC9DD44A7F99ACCECE7A1032DD05F37F80E5515,
	ABSTweenPlugin_U3CInitU3Eb__1_m6353601D6C7B6BF15D5E220A706E95B92255F5EE,
	ABSTweenPlugin_U3CInitU3Eb__2_mCD61D8F86333FDB6F318431897E932B9A7378AD3,
	ABSTweenPlugin_U3CInitU3Eb__3_m9838D2BCC7A3AC0CD94BDD8A692EF2A4989D7E88,
	ABSTweenPlugin_U3CInitU3Eb__4_m17DE55E31DBA6AF46B5712CBAD7F51D6E318CD0A,
	ABSTweenPlugin_U3CInitU3Eb__5_m7138A8ADEA042C5A8A6058AAF399C19AFF0AC6A7,
	ABSTweenPlugin_U3CInitU3Eb__6_m589A0B22872A4412794687FDF0842AACB79967B5,
	ABSTweenPlugin_U3CInitU3Eb__7_m41F013450C5ECA30A766DAAFC75469F9A9BF1DFA,
	ABSTweenPlugin_U3CInitU3Eb__8_m3B5286D95153A1F203EBA402A15050A3B5B8513B,
	ABSTweenPlugin_U3CInitU3Eb__9_m08E6957101740BB9B0AD1BE0C4B33C74E7AB96C9,
	PlugVector4_get_startVal_m323C2CCC02E56CA7734774684DC5D586E7092D0F,
	PlugVector4_set_startVal_mE3D096554EF7BB66851D208D734407E3910FAD40,
	PlugVector4_set_endVal_mEBA96CDA1789F6830535C48CB0FDEAF46A00465A,
	PlugVector4__ctor_m348E95DFFA753B9E5A4DF1A5AB25DEA5DBD84E81,
	PlugVector4_GetSpeedBasedDuration_m5FB4CCB881C268A469C2F70E51FC6616B3468DA5,
	PlugVector4_SetChangeVal_m38D897F2F327F342273B2D156AAE907114F09196,
	PlugVector4_SetIncremental_mDC1CF72D8F738B9882CABFA3ED5E21A6E67A7ACC,
	PlugVector4_DoUpdate_m18904AE76350DF08B13A2C571094A826693B40C7,
	PlugVector4__cctor_mF9292E195392CFB203296E5BA1499A26243DBA75,
	Strong_EaseIn_m0483C21A71E5F16EFC4D10F1609B98F8513AEF9B,
	Strong_EaseOut_m0A19DB1451FF95150B4299BD17C3CBE0236FA7C8,
	Strong_EaseInOut_mAC63986A2D328C9C559620FBFC2CF56A55220306,
	Sine_EaseIn_m8F2DF0C35FBBC081FAF538ECD423FC8560B5BF1C,
	Sine_EaseOut_m339B51615647BF15DA6D29468516B4511A8D461E,
	Sine_EaseInOut_m3074821CC490E1CBDC21DF0388146C8E1CA0BD1D,
	PlugVector3_get_startVal_m46FD420263EDA94FCDF8DC93621763BFA0E1D80C,
	PlugVector3_set_startVal_mF099CCA24944603F06CED437FEBDFEA1BB39C06D,
	PlugVector3_set_endVal_m709148639019BF2E7B7EF7DB5CF0C58CC2992060,
	PlugVector3__ctor_mFAEE32D17D68FA03776ED57F2C2A351D19A2621B,
	PlugVector3_GetSpeedBasedDuration_m1DDEFBF69FFD1B465B6FD356BF3F90BE33FD79DC,
	PlugVector3_SetChangeVal_m675B7A4DAF1755A41D55E1C34A9176FE9DE652D6,
	PlugVector3_SetIncremental_m0A677E5D8DD0E2A7CBD84E7A4158CF8681934D35,
	PlugVector3_DoUpdate_m8634A68A8D2D930C910432BDE09A89A4D577A5D2,
	PlugVector3__cctor_m6CECC632BFFBDF370FAC7E574097E1612F418929,
	Expo_EaseIn_mE079107F93E684246C15E6C9F68D1ABDD1893CEA,
	Expo_EaseOut_mFE84EA64AC24075B4E4F7A0B59E3F7FE53410463,
	Expo_EaseInOut_mABDB0911FC0AAA62F27E40461DEC1C834F160DB2,
	ABSTweenComponent_get_steadyIgnoreCallbacks_m9E9A5C415C84D543DFD48CE5F3D02DBD6F30210D,
	ABSTweenComponent_set_steadyIgnoreCallbacks_m1D4B5669107E0C1FCF0C82EA392A7B0C07396D2A,
	ABSTweenComponent_get_id_m5A9E8E7E68CA48D8A21DECF2210EC783F8C6476A,
	ABSTweenComponent_get_intId_m298D39375AF5ABB6E7EC0BAC00C822C6FA19DE1E,
	ABSTweenComponent_get_autoKillOnComplete_m751B1195EBAA58794EFE10B0213DCBC98156F22C,
	ABSTweenComponent_set_autoKillOnComplete_m6AC9EF9FACB5242B63298B37924E26ADA623327B,
	ABSTweenComponent_get_timeScale_mF396ECC7DF96D2A7F668A573A29955101E2EE10A,
	ABSTweenComponent_set_loops_m978B41F5E22987B72272D45160E0AACBE6D9CCCB,
	ABSTweenComponent_get_loopType_m776B4D637E82D1974ED75CE147976142B5A4494D,
	ABSTweenComponent_get_duration_m33D5D9D4A443B22FF0600B088495472828CF4D57,
	ABSTweenComponent_get_updateType_mCE83133275081B1853C81C29F76D513B021EA3CA,
	ABSTweenComponent_get_completedLoops_mCB99D27D16B35BA7C3945C581A0A5F6267F776B9,
	ABSTweenComponent_get_destroyed_m4FE7ACE9A38BE5BED05C117B3F147838083CFC01,
	ABSTweenComponent_get_isEmpty_m297A2DC5196DD23873EC5363FB67EA08CB653105,
	ABSTweenComponent_get_isPaused_mC4658335E31DD94220CD838FA773B1A38B6420C9,
	ABSTweenComponent_get_isComplete_m709E527B954A24C4FC9BFA6AAEAF82332441991F,
	ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F,
	ABSTweenComponent_Kill_m2E726969956C5A1DD8ECEC32E7A91F70358D686E,
	ABSTweenComponent_Kill_m3DFA228E5371D61661F164FAB94E5A115CA02744,
	ABSTweenComponent_Play_mA4E25844A54A78E6103097AE8B3E805B515F32D7,
	ABSTweenComponent_PlayIfPaused_m0C8C4728B484F6E707B808FB4FC74C43E0B3EDF0,
	ABSTweenComponent_PlayForward_m42BA5CCCC60C3F56EB0062699ECC45904C6C54CA,
	ABSTweenComponent_PlayBackwards_m23D206CC2390B71E78C9C862C6498ED8A56E583E,
	ABSTweenComponent_Pause_m9546E13997477B5C042B2C1D01FD0D7C738F589D,
	NULL,
	NULL,
	ABSTweenComponent_Reverse_mD83B291E248DA79C2C891D50BDFE2FE917E35ED5,
	ABSTweenComponent_Complete_m2578EED27A83C77A4D0CA98ECDBD87B00B46D68A,
	ABSTweenComponent_GoTo_mE0BD939714FD53919BBBDC0FC582C7904A42C1FE,
	ABSTweenComponent_GoTo_m2D44CADB0991294DEA56BBA7CF1F7F363A09E9C2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ABSTweenComponent_Update_mF03B81B1EB29C4DB2A22EF5EDCF3D43AFA445391,
	ABSTweenComponent_Update_m8AF70CD3E8BFA5884D27601693DA3247E3DD8D53,
	ABSTweenComponent_Update_m99A24CE62CF9C1A448F852586139A974265FBBB2,
	NULL,
	NULL,
	NULL,
	ABSTweenComponent_Startup_m4CC98C5D235884440C27E64D16C4075D3C125FCA,
	ABSTweenComponent_OnStart_m178F7E4D946A12175D04BAF7C8C3874478E0284D,
	ABSTweenComponent_OnUpdate_m1FD80B3FCA41421822A7EAE81F82F3B3430C30C0,
	ABSTweenComponent_OnPluginUpdated_m0DC19FAC831C175A9A8EE5F99AE2779F9E00A2D9,
	ABSTweenComponent_OnPause_mD0B7490543C83B590BD8769B02A60E2BC81291B3,
	ABSTweenComponent_OnPlay_m1810BC48C50A7BE4168D8BC3C07045F4C19A1FC0,
	ABSTweenComponent_OnRewinded_m5298F6161E77258A8830CB70A9DB480759D3881C,
	ABSTweenComponent_OnStepComplete_m87E71F36D6F8A96C568A2180DE25C54E20AB5C44,
	ABSTweenComponent_OnComplete_m1B8AB14B8416FA66F696ACD7C2B5A8DD173D8849,
	ABSTweenComponent_OnCompleteDispatch_mCA9855AB885055006C5704367128A343506BA27D,
	ABSTweenComponent_SetFullDuration_mBA55B39D9FB2454121316DAFE3177E4FF6EB2540,
	ABSTweenComponent_SetElapsed_m169521518328B85FAEB2CF09E7B55E9BAFE04FF9,
	ABSTweenComponent_SetLoops_m1E36E59B9B01524A512A988DF270F0BDFE9BD61E,
	ABSTweenComponent_ManageObjects_mC9F35A9F01F21F8286171C5BF0CAC28BE348B02A,
	NULL,
	ABSTweenComponent__ctor_m465808B3338E38EB1DAEC7F3DE4396F0E1C93030,
	PlugVector2_get_startVal_m5CF12299805FE8E0F8025F4EB163ACFFE5959FC9,
	PlugVector2_set_startVal_mD5F1A09E26CCB04568CC52B689BAD5525AA3B7D2,
	PlugVector2_set_endVal_m06ED4E7B4B603F1C60FBED5ACE8B28E8743DB656,
	PlugVector2__ctor_mD38E3F80476EF22E23B0D6902C1EBFBE597E50DD,
	PlugVector2_GetSpeedBasedDuration_m964C066C06FBCD8491AD2981F44CF0616A0277C7,
	PlugVector2_SetChangeVal_mD65187D3C7890307A4B50D094E3B78C611C5A8A0,
	PlugVector2_SetIncremental_mB7BAC7344EF60FD50B0094632A692ED23F6FCD07,
	PlugVector2_DoUpdate_m3D096C0AAD00D846EA88768415FD59DEC8BC6A85,
	PlugVector2__cctor_m2921F7533DBB4C6DF95964D7321C6C4A294C68BE,
	Sequence_get_steadyIgnoreCallbacks_mB25992C362BB1974788076165BBBA95156D0036B,
	Sequence_set_steadyIgnoreCallbacks_m5002FB7444EDB08B1178E4DD00C7361C998A07C5,
	Sequence__ctor_m4CF40D7E6CC4079B0B4142E79FFAAEF3A9D8C7B4,
	Sequence__ctor_m7DE2851F8E5515E78804DB5F0A7D62E8A2C3FE66,
	Sequence_Append_m22CE048A4EBA99DFEE293457DED03EB5B6F54EEA,
	Sequence_Append_mD2C07D17BCC7ABE2CA226C607B80D18B4B394774,
	Sequence_Insert_m510E64285AF5B8E8065C0BCCFC7F4D647599309C,
	Sequence_Insert_m72C0CB6A93DC49A1E3272D534899E1E77DF4CA99,
	Sequence_Kill_m386796B2BB421A98363AA686056519969FA5CC8E,
	Sequence_Rewind_m615BB348B5E222DE91219DC90CAEF8610B654A82,
	Sequence_Restart_m4F5DAD41C6FBE0B375F8386163ADB39C5CA436D4,
	Sequence_IsTweening_mDBB3E207924CC428F52574B63D9423E288CDE56E,
	Sequence_IsTweening_mA89838BCF977E80FC565E45FD5E7FA8CDAD2FA87,
	Sequence_IsTweening_mAB0EBDFB6487155C088FA207FB0C9C907EC9C486,
	Sequence_IsLinkedTo_mF612EB8E4A639CB00D1AC6FE7DE2346AC85003BD,
	Sequence_GetTweenTargets_m75046F7C7279951C48804C84EF8B840FBE0181E9,
	Sequence_GetTweenersByTarget_m6A0E6AD2196387303A8C5CA860ABA84F2242177F,
	Sequence_GetTweensById_mA061DF3F0661645ED393E4C657628066567C4020,
	Sequence_GetTweensByIntId_m8F9789343A6FF8DB3C570EE0D33F1E526DB2C38D,
	Sequence_Remove_mC0A8D195AF01D4D8514D7515286352256C677E31,
	Sequence_Complete_m85B4798DE45C436030821A377E5086944708AFD9,
	Sequence_Update_mFBE3519EC1E0E338786DDC2F4D5B9336A565024C,
	Sequence_SetIncremental_m0F90853B8868690B51B772996970E20A46212764,
	Sequence_GoTo_m1D906AB8B2015367612171C2A30E0264A1C8000C,
	Sequence_Rewind_m5B26ED45F7E39420AA2339B9002DB806D09A17F2,
	Sequence_TweenStartupIteration_mB0DBC249010A4139A17F60D7089A0FECA9C8014C,
	Sequence_CheckSpeedBasedTween_mE5AA1DC3678A02B64608A42F1832AF9766372053,
	Sequence_Startup_mE310D4C19F20A59C8383630EC7644415576A9D4C,
	Sequence_FillPluginsList_m601C0AAB6D2CE2D537BF5BA32AF4A4E5C6A7ED76,
	HOTSeqItem_get_duration_mBDDCCEE33423F7AE6CC984A2D656AD0BF475E550,
	HOTSeqItem__ctor_m2D5D94147964710E915377E55DD94C892A329D49,
	HOTSeqItem__ctor_m90C3D7AE021A85A40DE53956A35FB621DE3E241B,
	PlugFloat_get_startVal_mAFA28FC0AE84BA35FB40EFC28A28E092F090EFEF,
	PlugFloat_set_startVal_mB9C2F4C152C4E3D3E6562038CC5620A2E2E4D533,
	PlugFloat_set_endVal_m419C832FBB5F33F6C1F6A1A0E5182970A177B8D9,
	PlugFloat__ctor_m7F3FBD710426F3E263968ABEA94E1083679AB401,
	PlugFloat_GetSpeedBasedDuration_mB98828F340C4BACF333264CE7E0B7B01EFF78892,
	PlugFloat_SetChangeVal_mA6149A2EB83CA4013CD65F39522A9AD7E17DD24B,
	PlugFloat_SetIncremental_mEF440A47F879F1F59B1F756165B77305BCC9624E,
	PlugFloat_DoUpdate_m51CFF48B1CD24F1C70654315A46E4C5956FB2ADD,
	PlugFloat__cctor_m6695317CFF74497C3994AC080566ECD8183F2312,
	EaseInfo__ctor_mAC1FA8B506CA44FBF0FF0D33CDFC23EBFADB4B81,
	EaseInfo_GetEaseInfo_m10B4224CB3CF864CE6542B884D237591EA5600D9,
	EaseInfo__cctor_m3C7ECF412052C42FFFF15C3F734E2930954716A1,
	PlugVector3X_get_pluginId_m7A84FA60242AFDB5E811083B56FAE5BA891772A1,
	PlugVector3X_get_startVal_mAF8F88BBEBDDA3569072D9F453C6CE99D73DCB2E,
	PlugVector3X_set_startVal_m44B3DE0AB61DE71F44B429262F0D913541786347,
	PlugVector3X_set_endVal_m045ABC1819FF22929A8696971ED0FFA53C0F7005,
	PlugVector3X__ctor_m644C8B4AC23A419FC5F30EDFEA83F7EBD5F5E16E,
	PlugVector3X_GetSpeedBasedDuration_m31B8811FFDD60A611FE72FF0869F5BECAA3936FF,
	PlugVector3X_Rewind_m706D239191A7AB6060E863B082FCCDB3671AE472,
	PlugVector3X_Complete_mEA7A09DCF2264EC6C32A920064FE81CAF0F25216,
	PlugVector3X_SetChangeVal_m82D1173A51856439A9B00890D44F100BC9B956B8,
	PlugVector3X_SetIncremental_m88D87FFA47890D198E31B18F2F7750BA7B3D18B7,
	PlugVector3X_DoUpdate_m0149A41389A97C8EC68C61D8A6975E3AB25266F3,
	PlugVector3X__cctor_mA2207A63AA5A93EB8D5C40F114ABADD00035703B,
	Utils_SimpleClassName_m04D18EADDE8255C2C1DDB00067B4F55C8EB8F5FA,
	TweenInfo__ctor_mDDCD207D5005E97C81EEE05059E224D728D6F634,
	MemberAccessorException__ctor_mBCB11A3A1EBE888618E5287DCE0DF3456A185C34,
	NULL,
	NULL,
	MemberAccessor__ctor_m078BFB6F5CBCD6787EC382C9556A9DCC38246937,
	MemberAccessor_Make_m335B46F216D80D7407F3E3B0C2D6697BCE52238F,
	MemberAccessor__cctor_m692857559A8D2F924B30F98B509791D9B6373054,
	MemberAccessor_Get_m86E1F739791AC8BA478811A0B3691A2444DC236C,
	MemberAccessor_Set_m65771271AEAC7D7B6DA2EFB0706FAF6C3B02A2EC,
	NULL,
	NULL,
	MemberAccessor_EnsureInit_m36D58457E073ED0916D2B98A17DE37069D17F29C,
	MemberAccessor_EmitAssembly_mAB3D7FE853EE8705A0EAD4F922B8ACA0486B4BB7,
	NULL,
	NULL,
	EaseCurve__ctor_mA7CCE59E7AF1173FE998BD193C38541E3737996F,
	EaseCurve_Evaluate_m147EB11018D649E704C57B17AFF002CB52082F96,
	PlugVector3Y_get_pluginId_m31C85298E6443FD1A84F8C19F2917D8FB312948E,
	PlugVector3Y_get_startVal_mD7B2EA0CA3DA609A0C334AFF8AA7F34BC40E8E99,
	PlugVector3Y_set_startVal_mFA8D4801329EEB2800BABC559A9A2ADB80F0619D,
	PlugVector3Y_set_endVal_mAEB32584F6CD999654482B7E5A17B76F086E2923,
	PlugVector3Y__ctor_m40F8C95F6E65A18125D169E35F64EFA5FF9A6CEB,
	PlugVector3Y_Rewind_mE1985E442456D45F543D5E45D036BC13C2AECE5E,
	PlugVector3Y_Complete_m54DA943C22DC711EFD31D9A5874E795B155E65CD,
	PlugVector3Y_DoUpdate_mB62EF707B95E01B4F954D0727EED84E4BDD93C89,
	Quart_EaseIn_mA75F2285E9606F752546479F052900C39B64391E,
	Quart_EaseOut_m8A2E53A1A9FBB7112210EF3B1D41A2B7FDFE91B9,
	Quart_EaseInOut_m838F40008D0C12C0FD24085ACEEDC9526A4A0A62,
	Bounce_EaseIn_m608B24D4E668356B0B3C1359D7416F7070A16431,
	Bounce_EaseOut_mDA691F17504DAD856566F150117053316BA0FEF2,
	Bounce_EaseInOut_m992D9E8068FD6BA636C474B396E6B6F9F590E202,
	PlugColor_get_startVal_mF3BFAD49D057AC4DE22A5E4FFBCEBD2F5B5C1DA8,
	PlugColor_set_startVal_m60426CA6075DD867FB45A0DB5E6F5BCE0279253D,
	PlugColor_set_endVal_m9FCF95741BBE985346108C1988D172D6652EB154,
	PlugColor__ctor_m9587F07E6E13DF59F6DBB8795BC7408688ABF745,
	PlugColor_GetSpeedBasedDuration_mAE713EDAFC8DDEE3D32C5488437280890C8F51F7,
	PlugColor_SetChangeVal_mAE13564F434264B39D81026E3B968E475D424847,
	PlugColor_SetIncremental_m745C86AEA2F415DBE6839C5E08052220BC546601,
	PlugColor_DoUpdate_mB2437E92EC4ADAAA2BD6FBEB866BF32CF21AA1AC,
	PlugColor__cctor_m215B7C1B8700A5F91CED1F74B2DE93446B407EA5,
	Elastic_EaseIn_mE0A62F93A794AB086662988DB43D14AB9B537585,
	Elastic_EaseOut_m0D11F3641F175171662B07A6E4BB4830666BFB77,
	Elastic_EaseInOut_m0BE3D30A293D0966D2100377C8536244A4707A64,
	TweenEvent_get_parms_m8C20A9085A932E57113912DD9C86FAA4F28F928D,
	TweenEvent__ctor_m20EB08AE4E804741D72FBED05DE8925CC9C132EF,
	TweenEvent__ctor_mB18727BF083858DFD49D0CC8B608DDD8E12FF581,
	PlugString_get_startVal_m757CF3E811D5C7475E6A3A4A5D1EADEEF865C118,
	PlugString_set_startVal_mA635110F2B3871A7907EB3CF0E8B99838CDEC87A,
	PlugString_set_endVal_m195FB73B4089AE62AB3568DE42805494CA4B06E7,
	PlugString__ctor_mBC5CF13283AEDED7546061AFDDCD1BC3049D9D12,
	PlugString_GetSpeedBasedDuration_mE283B4B082A68676E5AC69B531632ABA5FCE232F,
	PlugString_SetChangeVal_mE2FECDE431F2C460DF03D83789F49D66106DDD10,
	PlugString_SetIncremental_m788A0DB8289D31B245BC40F1A7395C7B82E9C4DF,
	PlugString_DoUpdate_mA7F54BB02DB019141AA55FB1BB8C8DBCABDE7D10,
	PlugString__cctor_m32C5FA5636E25BC6682C935D16E5CD1D7A168B29,
	PropertyAccessor__ctor_mAC50BF853972A4BEB92F2FB0B7D2A0ED7D7101E3,
	PropertyAccessor_get_CanRead_m66D5F8B1AFB928B4BC6B669A3526FE3DF04C6ABB,
	PropertyAccessor_get_CanWrite_mC0B86BBB3800EDC15DE50B8E21ADF73CB08C4D71,
	PropertyAccessor__EmitSetter_m97663FCFD5A4B196293F7BC4FB91B3840EA17736,
	PropertyAccessor__EmitGetter_m3AC0F73816480A18BAB48C5D095DD4A67E42AFF8,
	Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39,
	Tweener_set_isFrom_m3E5ABBC9B076D66C6006F2E422A6B15C0899CD24,
	Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612,
	Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA,
	Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E,
	Tweener_get_pixelPerfect_mFE6C00B8CA66560011BB93EC9DD91F9C75565CB5,
	Tweener_get_speedBased_m696E4B1D829288D5294B1F036ABD7B3664569ADA,
	Tweener__ctor_mBFCBFA145B7C00694B7D240C5D5BD2CC22FD3BCB,
	Tweener_Kill_mDA58BAFECA0A60B2BEFA61AEBF61585520CD8C0E,
	Tweener_Play_mF00B9C69E4391700F195756339B2EF55F0C5C77C,
	Tweener_PlayForward_mC868ED59C0604AADBA54BDD068C3AAC3D598A89E,
	Tweener_Rewind_mB06F27F64873E5270A05DF612DA543D87927CA1A,
	Tweener_Rewind_m5583DE03D36135ADC8D1933323A976420E64F8BE,
	Tweener_Restart_mBEBE51196CBE7FB0C57E0FBBA6AD57F186CC1475,
	Tweener_Restart_m55A6C4EC8E2BCAA7B783C432259EA5084563B66F,
	Tweener_Complete_mFB87F383300ECDF0AF040504E25CAEB94270766D,
	Tweener_IsTweening_m8350AF1094F0E5CE0688EDA0A4D1C04FFBB3F1F2,
	Tweener_IsTweening_mCEFA7D8C3532AC54FCBC9E2EB78B4E973EA4C830,
	Tweener_IsTweening_m7858F896B234BFBED87B5F7D3575B896F2A4B8D0,
	Tweener_IsLinkedTo_m820FD8453215EAD28758B4FE45D81F0D055AA060,
	Tweener_GetTweenTargets_m7BF7E00E68AA6B1283DF6C835FAA61E5375C5B5B,
	Tweener_GetTweensById_mE53739DD0397C7B8235582CA493D6CC97EF11478,
	Tweener_GetTweensByIntId_m7F7391D0479C83D537B500C23FD75F9964D2605F,
	Tweener_Update_mF903632092FCFF830BDB19BA3DDE9DB7A869C8A5,
	Tweener_Update_m54E5B2B861DD80E328B93CF8A683DB587041CF56,
	Tweener_SetIncremental_mFC28846E3CF17F337C6EF38CB8D8C003940BD1D6,
	Tweener_ForceSetSpeedBasedDuration_mFED6284796D464777B09FEC58F5CD4E873883A29,
	Tweener_GoTo_mB3C4272B8D3A2F1B7A792BB200C2BA2B9531FE34,
	Tweener_Rewind_mB36B7B2F32E757A3A1CAF944437CF57DC16AC796,
	Tweener_SkipDelay_m353FBA4781C15F780D9105893158A3DEB53B1C2A,
	Tweener_Startup_m4F0AB7F88BA7E6386180C9BEDCE18A9A22CA7093,
	Tweener_Startup_m163CF924A92960258D81EFBC440981926A8B2007,
	Tweener_OnStart_mE5CCF6D2DAD86B09215A2C58E42E52E50E556EC0,
	Tweener_OnPlay_m456EA0CCCA16F7E3FBB0E228F43C9CE6105CCA9A,
	Tweener_FillPluginsList_m3ED9C044BD5F755A0E057E74460B02FF06792D26,
	PlugRect_get_startVal_m5B29DFC493FBFC91AF89863EEAF844D6A7CC0360,
	PlugRect_set_startVal_m312992D761647EDC5E4352717531F6F5C58A2559,
	PlugRect_set_endVal_m730C2A32D4576C0E29D56E1D8E513BBC40D49A9B,
	PlugRect__ctor_m1CCAC707C847323D566B4B359BD492E0368C1750,
	PlugRect_GetSpeedBasedDuration_m63BBF18C7E298DAE0D828D7BEC304BB780CD8918,
	PlugRect_SetChangeVal_m9D64E909A37CA7EC8E2B9844746CF66A23D40424,
	PlugRect_SetIncremental_mD40687AE3C223F1A30DADF1F0ABACF65A77838C9,
	PlugRect_DoUpdate_m338FA2B05212F0414CB3A2777D8842F5EED8E39D,
	PlugRect__cctor_mEF9A9FD09CB70CE6039922AF832CF4EE8F7BA39C,
	HOTween_get_isUpdateLoop_m4E4B8E1ABC396200F116DE8534974F4ECE5AA8F7,
	HOTween_set_isUpdateLoop_m471A911475EF097FA21C9926E5FF228F01AF41CC,
	HOTween_get_totTweens_mA0CC42C6BFFAA9D44C63D93E239CA842C8EA1E8C,
	HOTween_Init_mBB8E8744A2E181D7AF508FB4C7F750CD9503F69D,
	HOTween_Init_m60025DE48C10CAAC7794B9B747F5A929120F56B4,
	HOTween_Init_m4DC575B4903BABA72C0D32E425F350A1B48638A4,
	HOTween_OnApplicationQuit_mC9E5E82C54FDF0D11127556581210E8F92433311,
	HOTween_OnDrawGizmos_m5F42335229D4F73CADF4052F1E21D580107CC315,
	HOTween_OnDestroy_m5F6768C0BE928261400533DD15044AF548F0B90D,
	HOTween_AddSequence_m41EB7C1A2575808595536E16DDB885E993330CE4,
	HOTween_To_mEDE968C7E0AC34665AFC0C89838CA3DEC061828B,
	HOTween_To_mC3A013E5E30627489CC3F4FC9F8FEF6926FAD8EA,
	HOTween_To_m226F3DD76A9FA41694FA940763949859850C3272,
	HOTween_To_m005266815F277CD43C97C6CA0DCC5CE29732B919,
	HOTween_From_m78904A99B67A85085B08138053A36B72688EB223,
	HOTween_From_m2B419086CB8D6015A14AB2B3F0885C84D726B1A2,
	HOTween_From_m7B31A9447E4518E6DE143E9F87D046CD879CE164,
	HOTween_From_mBFFB94DAA8281B2569B4FB8258395F9AD00CF3F8,
	HOTween_Punch_m1EF6566B7E4D4C00634F856A7DD33DBA65018A24,
	HOTween_Punch_mF377FE1704FCB001335CF825DFE187C735C04718,
	HOTween_Punch_m7044D893616C60F04A75190534AE2EAA35D76E85,
	HOTween_Shake_mAB76C4CBCC2B53F59F96BC18B45761FC1A86C21B,
	HOTween_Shake_mF501BEADE365CE91F6FF58F7E89C0EB2810B8D95,
	HOTween_Shake_m74A8BB5F4FA716F312DD06EF705B46871AEA43E5,
	HOTween_Update_m326B4EFAFCFEF46257C88BCCE0A320691A167C11,
	HOTween_LateUpdate_m6FAA822325D5A35B1B67A56C5D4E1DE9BE56A7EB,
	HOTween_FixedUpdate_mF7D6CCF6B54FDC3E67655DDCDF059CB2076E0FFD,
	HOTween_TimeScaleIndependentUpdate_m7E58A37202376936025EC8EC6B66946A60ED4AF0,
	HOTween_EnableOverwriteManager_m17350AF9D21B2495ACA002E83E71AD6E822FC919,
	HOTween_DisableOverwriteManager_mCCC8B33B97F5B42A77AEBE5766502459D352F564,
	HOTween_Pause_m36B20DDEE4E67EFF2E7559D65C1CFFB8CB4D9CAC,
	HOTween_Pause_m725824969B779D166239CFB85AA32B492F245579,
	HOTween_Pause_mB0355B927283444D09CF0266C6A1A2AECE745DAA,
	HOTween_Pause_m29459E3CF37F589345C450B63D6B3B8FC8BE4202,
	HOTween_Pause_m1AD3028CB9AA28D3DABBA5DA9F7F3C252B685440,
	HOTween_Pause_mF29900B29EB4CABA21F56C584AA010E09672812F,
	HOTween_Play_m06588A995E7A78FE781D2FC6E951E9F8779D7D87,
	HOTween_Play_m4026BBD807F2C8A104964059FDB0CCDE6F75296C,
	HOTween_Play_m2462D006C5C7B3CACD4C835C3642B2280076DDC5,
	HOTween_Play_m39B35C13CD325D465CF95DCF26F8703EB6846048,
	HOTween_Play_m513D5490E652A3C48FECD598186C1504FDF5F1E6,
	HOTween_Play_m1B13EF6E77F0DC7D08EB6BE00740BB0888538DA2,
	HOTween_Play_m77EC29251A7B9EBB4E23968A86F867939BEEF785,
	HOTween_Play_m9597A9B825249484DFEE5A6FEA5333C9BE41BA4C,
	HOTween_Play_m3EAE9459F6F8B5123FCC0A59B79BFD7C6CF137CA,
	HOTween_Play_mED7D274F62C6B2FC7CB44A0D58651E74684962AE,
	HOTween_Play_mA958B4967451687E074FE8A08B6CCC844467B3EE,
	HOTween_PlayForward_m4924AA53B6348D8921ADFAEEDD0E5F2A39C3663F,
	HOTween_PlayForward_mBDBC9C0F0DC409A9AC9208BF0DDE6BE189570D3C,
	HOTween_PlayForward_mDAC9D2C47CCB0EC60DF597AABA5D7A78B2EA6961,
	HOTween_PlayForward_m7693A31FFDCFE68F5F9C4E806492A11FC04C8649,
	HOTween_PlayForward_mD05C3298D17515CB4F9015E910F75585E5128DAE,
	HOTween_PlayForward_m31B84925AC435E1E798D6A7AFEB48B6E1F967EBF,
	HOTween_PlayForward_m35ADFEAB59A00D1E2EDFCF1FE93543DC174CC4CC,
	HOTween_PlayForward_m7E01DDC24B3A329071E8F8D34FA73923487587AB,
	HOTween_PlayForward_mE8B3365418750AD6C0EF319CEBF1B70A5FB7C900,
	HOTween_PlayForward_mD40FBBB81907B8A6DF2D3A7C0DF1C9C091D49374,
	HOTween_PlayForward_m004C3162B0AC84A7F867A6B1873AAB2FCFB13374,
	HOTween_PlayBackwards_m0800D56CBE16AD829B24FC7CFEA81B19E1BFD07F,
	HOTween_PlayBackwards_mC161D180361510B8033C2460E5F66042045FB63F,
	HOTween_PlayBackwards_m7D7A4726F618E79BD04B5D77C8C4C2E21DE38A76,
	HOTween_PlayBackwards_m1BD235429F733F80E5CEBB5527EE03FF3FFCFBCF,
	HOTween_PlayBackwards_mC9B7CE3FDED1E665051D00876A45B31A172648DE,
	HOTween_PlayBackwards_mF556CC7CEBCAC16D5EC966081DA4A89E2237862C,
	HOTween_Rewind_mEA72EA86B693925D40C08696CA60D429F3322209,
	HOTween_Rewind_m6CEF74A727325E63EAC8FBCE2BE2DDD012D2A673,
	HOTween_Rewind_m4F4B5DDD509FB5B6C8E5B8B605D4B7B61972761B,
	HOTween_Rewind_m6F22978B08486426AD61828232356A6862FE3BA4,
	HOTween_Rewind_m7C2934940ED05E6584001D62689924959BB864E1,
	HOTween_Rewind_m84A90FFA16142BF593FB17C9920B84756DE510E0,
	HOTween_Rewind_mC030D3E3DB98B54C33954A0470B9A039ABB1FD45,
	HOTween_Rewind_mA10C86688E5965E628FEC06E583F3B25AABCB8B3,
	HOTween_Rewind_m76A3F347F69420864B05D3038C6FFEA224C72EB5,
	HOTween_Rewind_mB3C944C3749E04135F9A298B07A6C839822C36EC,
	HOTween_Rewind_m497F7B8CB58930A3ECDE0E6A44A8149B0AEDB47F,
	HOTween_Restart_m9D2D2E09D011A55D92EBE920D3EE50D8F0BC7DC2,
	HOTween_Restart_m50838EB4F8CF53F3B0B5C7ADC525733B1DFC8E55,
	HOTween_Restart_m09C607EF58F3430A29695103AA4FB6191D299343,
	HOTween_Restart_mF61D02756212211AF922D119902B9B861B5E91CD,
	HOTween_Restart_m36338F07689E584EB8C37D4398634C3E8EA43D87,
	HOTween_Restart_m71D8EA70A30A80AFF038DEBB82BD349AF714C463,
	HOTween_Restart_m2AEA9978D2E36E0D2FE1FF0A0F43363B253A273D,
	HOTween_Restart_m8FA07E3F1C3114CEBF4C6EADA06E893A7E538E7A,
	HOTween_Restart_m397FB57EC223A2216C57254BE03A46A8F2228A25,
	HOTween_Restart_mCA34CCF5B1AB25166F347C3C9E2C1D2033390005,
	HOTween_Restart_m62FA6867560DF13E964C66385A403C80CD7A97E4,
	HOTween_Reverse_mF2D4FA1AD09C321EDA6CE31B9BD15153D74F9663,
	HOTween_Reverse_m089175060475AE5A087EF8C13041A643738BCCBE,
	HOTween_Reverse_mCBE55833929586E049CFA9B967F2FC9046AF87D6,
	HOTween_Reverse_m868E3147AC24103FDB11DFC68883C990B2D76446,
	HOTween_Reverse_mB2CE2D250B7A5525FFCFF23BBDCCF6321DFA33E7,
	HOTween_Reverse_m1117289D45139FB8B11D18C00B494935638B6372,
	HOTween_Complete_m7692C74B7D77EE060122E2899DC2C9F833836159,
	HOTween_Complete_m7967016F92602D76F5B62BE3BB20543292CB69F2,
	HOTween_Complete_m7C62723855B11A93368357F71587B3F4C17D0BC4,
	HOTween_Complete_m264D7CAC799A02629F7885BF5F5D99E1E5154878,
	HOTween_Complete_mE669EDC255F126CEB3145E08079042387FF78B52,
	HOTween_Complete_m7A932984C40E0E64317A6C81EB91C3ABE1C574AE,
	HOTween_Kill_m542542A4D6E4C8D52C4E8B507B99E9CF357BB2C9,
	HOTween_Kill_mB7D9658E79952D232C7D1D8E13436A4D9B32A63C,
	HOTween_Kill_m463128617EC67F15716F8088482E06FEED184E57,
	HOTween_Kill_m479085E6D5B9C8A705AD9B0AFEB5BC5FA5D4BC35,
	HOTween_Kill_m9F95EF3B73391D91D1C97E2C5762F01C1EB05CAB,
	HOTween_Kill_m605F46A651F8E92248EA1A7C4F0620AF0A767090,
	HOTween_RemoveFromTweens_m3AAB66A7F25FD74281232E5DF44530F3A6A37A47,
	HOTween_GetAllTweens_mE126EF81B97750F731F8397CBE9233B71A165FDF,
	HOTween_GetAllPlayingTweens_m76B55DBF92813D433293B451770770FED5935A78,
	HOTween_GetAllPausedTweens_m5776DC5D7303846ED8F67A52BBEDA1A24BC871BE,
	HOTween_GetTweensById_m789AC5CC42B470248B6245BEA197AB82B3B8952D,
	HOTween_GetTweensByIntId_mA78091927D96162AF1403A55534CC31122F45480,
	HOTween_GetTweenersByTarget_m6449D4AF921B6FFA83FA78202976116AACD18E29,
	HOTween_IsTweening_m18DF4038D7CF357C9573667CB0E0BDDFCA6BA2B1,
	HOTween_IsTweening_mFA40D585ADFCE8C34D3EF88450465BB0DEF07333,
	HOTween_IsTweening_m069B84357C47D489CCB4BDDB33AC368805729CC6,
	HOTween_IsLinkedTo_m98345AAD61153C87F268200CB21DD1639D306916,
	HOTween_GetTweenInfos_mD0BDF65A625518D04354736F26D58C16EDE43829,
	HOTween_DoUpdate_mE9209BD9CD358C3BEDCB5E9AE2D19C9FCD5BCE93,
	HOTween_DoFilteredKill_mA3513323DE658C49A62A4ACDCD46CCC8ADAF3A40,
	HOTween_DoFilteredPause_mB9D7A0A081B676D73D05BC0C69E05CC17DA388B9,
	HOTween_DoFilteredPlay_m7FA13654F82E5E74743BC116A12561EA26E7D34F,
	HOTween_DoFilteredPlayForward_m992B7C98A7B222DB74BBB12AFB2803E5AF0775DB,
	HOTween_DoFilteredPlayBackwards_m6D27F391EAB2332553C22C27824F84A544661368,
	HOTween_DoFilteredRewind_mC4B083061F3606034213278E6600B6317354839B,
	HOTween_DoFilteredRestart_m25C33E58CD019B72FABBBA1F924FA0650DAB749F,
	HOTween_DoFilteredReverse_mA99D351C5D1FB204A478866962D16B34B5EC3628,
	HOTween_DoFilteredComplete_mBB72839D58F8AB1E57AAE8D065304B6660A0DD41,
	HOTween_DoSendMessage_m88B006E16146E8559219FE3BF4553AF0A5B91BB8,
	HOTween_AddTween_m70343A4A80FF6EBCFE8A3A5CF1CCF50519BC21CE,
	HOTween_NewTweenInstance_m64A9EBBA3B2352DAD7D8277CFBE2417B34983B05,
	HOTween_StartCoroutines_mA41F4A1BB9CAC629090E1C6C16D3AF726BB966BC,
	HOTween_StartCoroutines_StartTimeScaleIndependentUpdate_m39073F449B395224689522B71A81E368060883C4,
	HOTween_SetGOName_mCB87EE598B6150694B8E5F1C2432FE84244F439E,
	HOTween_CheckClear_m1096EC36B2E75C5C1069B3E636615C4BD1F69898,
	HOTween_Clear_m4DBED773FBC8AF10FBF43685CCB36A9F56097357,
	HOTween_DoFilteredIteration_m9A816AA5FD62E854BA4F29B51A350CEAF33A022A,
	HOTween_DoFilteredIteration_m2C0285CE25227C02F4D46F63E53A71C6D4A6F977,
	HOTween_GetPlugins_m014CC142D2906F93B6DC186F3FBF4FCFC81BDADE,
	HOTween__ctor_m83C69B29768EFD44BACDDAD0C96F0D69545B5CD1,
	HOTween__cctor_mDEA1B0C271C718BEDE47A294E42472BD6E41D758,
	U3CTimeScaleIndependentUpdateU3Ed__0_MoveNext_m642A675FD815288A5F820C1C356417FA6FD070F8,
	U3CTimeScaleIndependentUpdateU3Ed__0_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1AAC5219BED738C305C2716F9DB8FEF02AC9F3EF,
	U3CTimeScaleIndependentUpdateU3Ed__0_System_Collections_IEnumerator_Reset_m589A2198D792866E38A77693BBE03563BC1AF49A,
	U3CTimeScaleIndependentUpdateU3Ed__0_System_IDisposable_Dispose_m48394E1E7045AF4D24C37B768D0459BA251CF0EC,
	U3CTimeScaleIndependentUpdateU3Ed__0_System_Collections_IEnumerator_get_Current_mE0D0116FC360C92B577A621BCE57B555FB71F842,
	U3CTimeScaleIndependentUpdateU3Ed__0__ctor_m79AC21166CD82771DF6D78B30DB6CC41C7DED140,
	U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_MoveNext_m9B759D99EEDDDF2721862797B94C5234740E5390,
	U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF22FC076C7FF17D4D58DF98408746C797A1D05A5,
	U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_Collections_IEnumerator_Reset_m67BD98FBCC5EFE58C75164816655B22EA5007433,
	U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_IDisposable_Dispose_m7CECDA3CBCDB3DDA7160FFE159B031B7FAC486B6,
	U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3_System_Collections_IEnumerator_get_Current_mE62126A6F33F60C9E4751B9524B6300205A1AB7C,
	U3CStartCoroutines_StartTimeScaleIndependentUpdateU3Ed__3__ctor_m7F59C9019DAD54A7DE99633F2C57747C4F8830AC,
	Quad_EaseIn_m1F12867C09D0F87BC57FDFC7504AA13CF7ED9993,
	Quad_EaseOut_m613527EA22BFD03098F51DC43E1FECB82487B426,
	Quad_EaseInOut_mFDB4FB26541415C9EC683B2CFB3A88CFCD050570,
	Linear_EaseNone_mF4515939B52D57647BEA8C86BC2B1B73B770CB7A,
	ABSTweenComponentParms_InitializeOwner_mF88937400BEA35A760F2DC698CA459C44FE82327,
	ABSTweenComponentParms__ctor_m689C96ED2202D6F626DB88BBF1F031D265508270,
	MemberAccessorCacher_Make_mACFE79F8BAC232F6DD2F042799641B3E960367DE,
	FieldAccessor__ctor_mC2C90F03375989FF08526586262E328B5E4814D9,
	FieldAccessor_get_CanRead_m90FE4B9D735BE2C17A04D1C35F56F1C699D42EAA,
	FieldAccessor_get_CanWrite_m7F4CAEEA4640328EA708651F438115148E2DF549,
	FieldAccessor__EmitSetter_mEFBF8A9D12ECC870C8D24772A3811D2EC8B44E5C,
	FieldAccessor__EmitGetter_m2EF1BF63AC90273DC9134DAE6E82CB54E86E3DEC,
	PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186,
	PlugVector3Path_set_pathType_mB72EF8EB3956A20D2D45AA4DCEBB3727603CFABE,
	PlugVector3Path_get_startVal_m4B0C3A1FD61836CB35759B84E5C97CE5CEDA4DED,
	PlugVector3Path_set_startVal_m8D9E885781CE7F85A304FB17058D4B60A5A48B60,
	PlugVector3Path_set_endVal_m105459D2CDA35CD6650A04F2CFB690BE1188200E,
	PlugVector3Path__ctor_m3263C04904CBAF1C7F7B41FCA8433F2DAF755798,
	PlugVector3Path_Init_m85E520A75C4F71095E071D4B01BAE0008A06A651,
	PlugVector3Path_ClosePath_m0832EA5BB568B5780C96EFA67DD46D636CAEED70,
	PlugVector3Path_ClosePath_mFF30CD58A7ADBE3860716938AE1D0B590EECE6D2,
	PlugVector3Path_OrientToPath_mD014C263C462386E51EB32BFE838C361BF2F6358,
	PlugVector3Path_OrientToPath_mDD2FD17CAE023690D586637E863EAB2F75BEDCAA,
	PlugVector3Path_OrientToPath_mD952BB4DB29845EC1FBA4A84CFAC615054204F0A,
	PlugVector3Path_GetSpeedBasedDuration_mF385E24D04D41BCA0487141CC86AAC2DF468594E,
	PlugVector3Path_SetChangeVal_mED72B4F145B086DCAA6008DDA4F396A507EC444D,
	PlugVector3Path_SetIncremental_m4BDBAA71BDBA0EE9727AB9D0AADB8CCD2E85A828,
	PlugVector3Path_DoUpdate_m48865BA96D2EEC21A396216C47D336A71EBD7AC4,
	PlugVector3Path_Rewind_m1F677A68251CCFA24A43639E764E1BBC763B003C,
	PlugVector3Path_Complete_m23E64294CE7F24812B449861F669C82231288B4C,
	PlugVector3Path_GetConstPointOnPath_m00566EF01E12762EB32F6317A830E61461A670E4,
	PlugVector3Path__cctor_m40152CCEDE327C8DD3FD458F8A06426653DA0C35,
	OverwriteManager__ctor_mEFBC7A321BD74D1CD40B2F02991D1C41DF99FE2C,
	OverwriteManager_AddTween_mB998E62A5D1E0C495C718F07B805E4E5B51D01E3,
	OverwriteManager_RemoveTween_m45DEDD84C3EDC7D2EF90E5DF5772201EB4C22F0F,
	Quint_EaseIn_m6C3211561F2D84CDC0E99DB5F74C961EE3EFCF4E,
	Quint_EaseOut_m89CBF1324AC66BB28842633E0AA7751E98DE3DEB,
	Quint_EaseInOut_m279B4513D0474EC373DA9C86641607FFD6950507,
	PlugColor32_get_startVal_mD9C0E957F190B58BF9AF2F3874F7464E0E7261AB,
	PlugColor32_set_startVal_m3D8B40246FD173C803497DC4A94C6C5490FD5EEE,
	PlugColor32_set_endVal_mEFFB93CBFEC846BF6102A9A4C377146950333311,
	PlugColor32__ctor_mA746143BEC963C76BB01E625BE07D6E7B6D83E4E,
	PlugColor32_GetSpeedBasedDuration_m1845E1E00E9BBD94FA4AF9A02E4774C3FDD8FCC1,
	PlugColor32_SetChangeVal_m0F2E1D66423E4F3CF74DA63D0D8002E5CD7831C0,
	PlugColor32_SetIncremental_m0D105D6FD4A79E01E0E94F9A2E3A060189AE3371,
	PlugColor32_DoUpdate_mA43CD82817D34A333A0E49381E01BCC37AFB0C63,
	PlugColor32__cctor_mA54543A3A75DEBA7711695153EE0560717E5FB15,
	TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B,
	TweenWarning_Log_mDD27E543707A5EFEDCBE8A709413D3156D9A938F,
	Path__ctor_mB08F108F59563B544D546B8A9EB2105FD46D4588,
	Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74,
	Path_GetPoint_m931F8C934DA00412C36CCE7D011F45F2F4F80555,
	Path_GizmoDraw_mC7CF7E5C1B7567B315E5F7BAAF2EEEC671E9A7B7,
	Path_GetConstPoint_m1DADD874A6EC9E06D13C398963002B81F9017653,
	Path_GetConstPoint_mC350B3F2078D6AB8F49B9C5B063BDD6C79B0654C,
	Path_StoreTimeToLenTables_mF3AFBB4D067AB81A9B6EE85D91D340361D198EC9,
	Path_GetConstPathPercFromTimePerc_m05DF6CE5DEE89C0D965748560E2D3E701C9D01B5,
	TweenParms_get_hasProps_mD3C0CFB119ABC3898AC73225309DF329FDBF0952,
	TweenParms_InitializeObject_m0B9ABD1E886C5141D7B5742E4D5A9240E15ABAA8,
	TweenParms_Ease_m2239F2056CC81A905ED58A77D51E87293B229BAC,
	TweenParms_Ease_mB302FD168B34BF99116AA23AC925761871053D9B,
	TweenParms_Ease_m72F10CB93D8FC98D43D9FA9672DACE62F687F81D,
	TweenParms_Delay_mBAFB272EC1B21EAF7FBA48BC96E636977A6D6643,
	TweenParms_Pause_mA62F1F2E657D0A048F6EB4A437ECC48EE58FA18C,
	TweenParms_Prop_m1E6374BA7365EFA50DBA6654C21A3E2D89FD8C81,
	TweenParms_Prop_mADE8C79AEFF46387BD66FD1281A5F4871A977D89,
	TweenParms_Prop_m52667C136BA3A423786787A2E8B27D3BB1E25BA0,
	TweenParms_Id_m3F778CAACB05B79BF6D06E875CB4186154FEDEB4,
	TweenParms_AutoKill_m336EF4095BD04F73271FC6BC18172531EF63AB58,
	TweenParms_UpdateType_m94959C570D78097687980C2EB04908F0145076D4,
	TweenParms_TimeScale_m3B26C008E3C0F957F044A4FC0AB97ED8ABBA9FB8,
	TweenParms_Loops_mE2C2F106984F8D49D02CFA2B2EC9A09AF3BBB632,
	TweenParms_Loops_mFD3B261B9B6C37DD20528F6E622E0145F0B23974,
	TweenParms_OnStart_m9044C94DE290F1657821BF07ACF465D6A704BCEA,
	TweenParms_OnStepComplete_m1561B57D3EFDA3B2EF0D9A8D39716DBD838909CE,
	TweenParms_OnComplete_mDFB601AC949292EA7FE01D75074307CC233B3559,
	TweenParms_OnComplete_m96A7E52367591089EB46EF61EC3C19ED84EFDBA4,
	TweenParms_IsFrom_m03B51C6DE24F9912B052954DDDACD644E13BAC39,
	TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B,
	TweenParms__ctor_mBBE01A0AC0D7F5D39B15749CAD6F18A84A9A013E,
	TweenParms__cctor_m18FB70B742F40C9BFBF1AA63DEE2863FE67F3686,
	HOTPropData__ctor_mEB72EC44DC80528C9615FBB1580D2208C1C27DEA,
	Cubic_EaseIn_mF68A8ADE66D4E5173A9738D5590AB972781CE169,
	Cubic_EaseOut_m76FCBA54077D20DB2AADF0670AFCC518C33922C9,
	Cubic_EaseInOut_m9D8AEF7EDC6B0F59FFB2E03FB46440E491D41112,
	Circ_EaseIn_mCA6A087D56618ADEC9B3CEFB23DFBE2050B3D5C2,
	Circ_EaseOut_mFF24BECA2B1B493A21CD525869E6A9C854DB51EB,
	Circ_EaseInOut_m93524D3A4D7315DAD226CF7CBA838B0FEEBB88D4,
	Back_EaseIn_m6BFA78FC66458D32BFAB1D2FD39602A6D28D001A,
	Back_EaseOut_m7D6F1FE29C491DBF471231363C3D4781548509DE,
	Back_EaseInOut_m6714F2F1366BA14CF68B317C646B8AC04015835D,
	PlugQuaternion_get_startVal_mC0E8AA76C47F7E0766D894BEAD22B7C3BFE910F0,
	PlugQuaternion_set_startVal_m50A46CF17B0B042F15212C2A6DEEFEE04A161C00,
	PlugQuaternion_set_endVal_m1BD6F0B44D962C4932367EC99B2E822AA904A0BB,
	PlugQuaternion__ctor_m46BD79B83263F7486AA657F2BDB40E50A2198049,
	PlugQuaternion__ctor_mE3BC50FE78C20B2554A668FCC648878459D4A608,
	PlugQuaternion__ctor_m653333B63186F7A0F1430587FAF26EE4A67302D8,
	PlugQuaternion_Beyond360_m6130714D6E69D9FB0D5F7B24158EC0D84F42EF22,
	PlugQuaternion_GetSpeedBasedDuration_m118AC301EE4847507E6A5623AFEB9BC793750601,
	PlugQuaternion_SetChangeVal_m365CBC37FAECB8923EFF1499567CB1C8A320660F,
	PlugQuaternion_SetIncremental_m8E6A96935BE3ABB1E13BEB7A3709C58125A9E0E2,
	PlugQuaternion_DoUpdate_m32C0D2E921B842C4D4D4DB98754D8845D7736B8E,
	PlugQuaternion__cctor_m890A83C1A49BDDFA299C50F6D10781E623E1ED31,
	TweenCallbackWParms__ctor_mB37CAD56CA9F34BDAC55ED611104A2DBBE80B520,
	TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB,
	TweenCallback__ctor_mBD3FF0903457762300B12CB3AEA092B04F2BFD94,
	TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2,
	EaseFunc__ctor_m258028586FD5AF6078A75793226DE7D379A13EA3,
	EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A,
	FilterFunc__ctor_m09E23A301B8AAC21FA70732CB97E2E6EB0086B04,
	FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC,
	NULL,
	NULL,
	NULL,
	NULL,
	SequenceParms_InitializeSequence_m8210CDA3A53CC6BD8A0035A2831BE4848C26B287,
	SequenceParms_Id_mD12A9F7430E0AA3A442D933E730A099FB8C87497,
	SequenceParms_Loops_mB8A56A26FF1C3FE24291B3E3FC89829191978C0F,
	SequenceParms_OnComplete_mD046B01C9C44C8A7DD4DB3F8E41B962148831AF8,
	SequenceParms__ctor_mEA6154BD45B02D2D0E5FF50B0EFA93ACC4BB6CDD,
	PlugInt_get_startVal_mEC395E74FAAAFE265631ABEEBDEFA84C82F1006D,
	PlugInt_set_startVal_m7CFC9E6A628F0BD6AB817471F2F73510359C91E8,
	PlugInt_set_endVal_mE7D91C5706E4D4FF356FFBEF59AE62BE2852AA41,
	PlugInt__ctor_m36BBA904D1AA75C2195D945C7D808BB4404D404D,
	PlugInt_GetSpeedBasedDuration_m6C3D8EAE856E47198C2C95EE5ADBD2C439BA4FC5,
	PlugInt_SetChangeVal_m1F3BA173BD58844E40013DBEF1CED2F0059AAEBC,
	PlugInt_SetIncremental_m8E4454E97CD02F65FC741ECEB005B9F83722C434,
	PlugInt_DoUpdate_m6B8283DFE8E57B477FAF53BC743D4D05D69717DA,
	PlugInt__cctor_m4F1377CB8036E178D51448051BDE98A26504B115,
};
static const int32_t s_InvokerIndices[574] = 
{
	0,
	0,
	0,
	6887,
	7043,
	6887,
	6992,
	6957,
	3349,
	365,
	7120,
	5703,
	7120,
	4261,
	5851,
	0,
	7120,
	7120,
	7120,
	5774,
	0,
	6992,
	0,
	5774,
	0,
	5806,
	6992,
	5915,
	7111,
	5915,
	7111,
	5915,
	7111,
	5823,
	7008,
	5823,
	7008,
	6992,
	5806,
	5806,
	3452,
	5265,
	7120,
	5774,
	5851,
	10455,
	7490,
	7490,
	7490,
	7490,
	7490,
	7490,
	6992,
	5806,
	5806,
	3446,
	5265,
	7120,
	5774,
	5851,
	10455,
	7490,
	7490,
	7490,
	6887,
	5703,
	6992,
	6957,
	6887,
	5703,
	7043,
	5774,
	6957,
	7043,
	6957,
	6957,
	6887,
	6887,
	6887,
	6887,
	6887,
	7120,
	5703,
	7120,
	7120,
	7120,
	7120,
	7120,
	0,
	0,
	5703,
	7120,
	2238,
	1501,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4309,
	2238,
	1501,
	0,
	0,
	0,
	7120,
	7120,
	7120,
	5806,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	5703,
	0,
	7120,
	6992,
	5806,
	5806,
	3439,
	5265,
	7120,
	5774,
	5851,
	10455,
	6887,
	5703,
	7120,
	5806,
	5263,
	2720,
	2725,
	1684,
	5703,
	7120,
	7120,
	4261,
	4261,
	4231,
	4261,
	6992,
	5181,
	5181,
	5178,
	5806,
	5703,
	781,
	5774,
	781,
	5703,
	7120,
	10293,
	7120,
	5806,
	7043,
	3409,
	3410,
	6992,
	5806,
	5806,
	3406,
	5265,
	7120,
	5774,
	5851,
	10455,
	3363,
	10051,
	10455,
	6957,
	6992,
	5806,
	5806,
	3406,
	5265,
	7120,
	7120,
	7120,
	5774,
	5851,
	10455,
	10054,
	5806,
	5806,
	0,
	0,
	5806,
	9381,
	10455,
	5181,
	3363,
	0,
	0,
	7120,
	6992,
	0,
	0,
	5806,
	319,
	6957,
	6992,
	5806,
	5806,
	5851,
	7120,
	7120,
	5851,
	7490,
	7490,
	7490,
	7490,
	7490,
	7490,
	6992,
	5806,
	5806,
	2814,
	5265,
	7120,
	5774,
	5851,
	10455,
	7490,
	7490,
	7490,
	6992,
	3363,
	1909,
	6992,
	5806,
	5806,
	3349,
	5265,
	7120,
	5774,
	5851,
	10455,
	5806,
	6887,
	6887,
	5806,
	5806,
	6887,
	5703,
	7043,
	7043,
	6992,
	6887,
	6887,
	1921,
	5703,
	5703,
	5703,
	7120,
	5703,
	7120,
	5703,
	5703,
	4261,
	4261,
	4231,
	4261,
	6992,
	5181,
	5178,
	781,
	438,
	5774,
	7120,
	781,
	2809,
	7120,
	7120,
	5703,
	7120,
	7120,
	5806,
	6992,
	5806,
	5806,
	3396,
	5265,
	7120,
	5774,
	5851,
	10455,
	10397,
	10284,
	10412,
	10455,
	10284,
	8898,
	7120,
	7120,
	7120,
	10293,
	8271,
	7789,
	7375,
	8746,
	8271,
	7789,
	7375,
	8746,
	7479,
	7376,
	7790,
	7479,
	7376,
	7790,
	7120,
	7120,
	7120,
	10420,
	10284,
	10455,
	9942,
	9942,
	9938,
	9942,
	9942,
	10412,
	9942,
	9280,
	9942,
	9280,
	9938,
	9269,
	9942,
	9280,
	9942,
	10412,
	9932,
	9942,
	9280,
	9942,
	9280,
	9938,
	9269,
	9942,
	9280,
	9942,
	10412,
	9932,
	9942,
	9942,
	9938,
	9942,
	9942,
	10412,
	9942,
	9280,
	9942,
	9280,
	9938,
	9269,
	9942,
	9280,
	9942,
	10412,
	9932,
	9942,
	9280,
	9942,
	9280,
	9938,
	9269,
	9942,
	9280,
	9942,
	10412,
	9932,
	9280,
	9280,
	9269,
	9280,
	9280,
	9932,
	9942,
	9942,
	9938,
	9942,
	9942,
	10412,
	9942,
	9942,
	9938,
	9942,
	9942,
	10412,
	10293,
	10420,
	10420,
	10420,
	9369,
	9356,
	9369,
	9834,
	9834,
	9831,
	9834,
	10420,
	9580,
	9573,
	9573,
	9573,
	9573,
	9573,
	9573,
	9573,
	9573,
	9573,
	10293,
	10293,
	10455,
	7120,
	6992,
	10455,
	10397,
	10455,
	8629,
	8085,
	10420,
	7120,
	10455,
	6887,
	6992,
	7120,
	7120,
	6992,
	5774,
	6887,
	6992,
	7120,
	7120,
	6992,
	5774,
	7490,
	7490,
	7490,
	7490,
	5806,
	7120,
	8256,
	5806,
	6887,
	6887,
	5806,
	5806,
	6957,
	5774,
	6992,
	5806,
	5806,
	1872,
	365,
	6992,
	5169,
	6992,
	5169,
	1591,
	5265,
	7120,
	5774,
	5851,
	7120,
	7120,
	1169,
	10455,
	7120,
	5806,
	5806,
	7490,
	7490,
	7490,
	6992,
	5806,
	5806,
	2818,
	5265,
	7120,
	5774,
	5851,
	10455,
	10293,
	9618,
	3140,
	5378,
	2762,
	3406,
	5378,
	1714,
	5774,
	5265,
	6887,
	3363,
	5178,
	1604,
	5181,
	5184,
	5169,
	2682,
	2682,
	1627,
	5181,
	5169,
	5178,
	5184,
	5178,
	2670,
	5181,
	5181,
	5181,
	1135,
	6992,
	9127,
	7120,
	10455,
	1905,
	7490,
	7490,
	7490,
	7490,
	7490,
	7490,
	7490,
	7490,
	7490,
	6992,
	5806,
	5806,
	3395,
	5915,
	3446,
	5169,
	5265,
	7120,
	5774,
	5851,
	10455,
	3361,
	5806,
	3361,
	7120,
	3361,
	319,
	3361,
	3064,
	0,
	0,
	0,
	0,
	5806,
	5181,
	2670,
	5181,
	7120,
	6992,
	5806,
	5806,
	3406,
	5265,
	7120,
	5774,
	5851,
	10455,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_HOTween_CodeGenModule;
const Il2CppCodeGenModule g_HOTween_CodeGenModule = 
{
	"HOTween.dll",
	574,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
