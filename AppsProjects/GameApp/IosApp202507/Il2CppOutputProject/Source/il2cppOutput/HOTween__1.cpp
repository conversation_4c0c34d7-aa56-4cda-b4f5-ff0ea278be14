﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct VirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3, typename T4, typename T5, typename T6>
struct VirtualActionInvoker6
{
	typedef void (*Action)(void*, T1, T2, T3, T4, T5, T6, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5, T6 p6)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, p4, p5, p6, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct VirtualFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
struct GenericVirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (const RuntimeMethod* method, RuntimeObject* obj)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_virtual_invoke_data(method, obj, &invokeData);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
struct GenericInterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (const RuntimeMethod* method, RuntimeObject* obj)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_interface_invoke_data(method, obj, &invokeData);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1;
struct Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA;
struct Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588;
struct Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE;
struct HOAction_1_tFB2FBEE2362B9EA5FB3B490AF7DCB29F69FB49DB;
struct HOAction_1_t3C819BD915A5AB9ABC2CFC13278E30C26EC42BF2;
struct HOFunc_1_t40C6A6BEC4C5E783BF6FCDC4F38C337734402C44;
struct HOFunc_1_t41E04A0D95A65CF2022480C7F7AE604DD8E5A32C;
struct IEqualityComparer_1_tAE94C8F24AD5B94D4EE85CA9FC59E3409D41CAF7;
struct IEqualityComparer_1_t0C79004BFE79D9DBCE6C2250109D31D468A9A68E;
struct KeyCollection_tCC15D033281A6593E2488FAF5B205812A152AC03;
struct KeyCollection_t555B8656568D51D28955442D71A19D8860BFF88C;
struct List_1_t49F91546A5E6849CD21CAF9281555E44FBD71FFC;
struct List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A;
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F;
struct List_1_t698497CF3874D0FD4985709B1BF189D35D6EA4B0;
struct List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD;
struct ValueCollection_tCE6BD704B9571C131E2D8C8CED569DDEC4AE042B;
struct ValueCollection_t6E6C24D8CE99E9A850AB95B69939CBBA2CB9E7D9;
struct EntryU5BU5D_tEA0133B78B9FF7045128C508FA50247E525A94D6;
struct EntryU5BU5D_t7C07FADA3D121BF791083230AC898F54129541C8;
struct ABSTweenPluginU5BU5D_t475F412450955BB856940F6D8BD8088B8CF930C4;
struct BehaviourU5BU5D_t18066727E4902C04B4FFBCEEAB25AAC13418F9AA;
struct BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct TweenerU5BU5D_t1772BFD4FB12F62941EC6F73B9B8E495B8B22EC5;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct HOTPropDataU5BU5D_t06113F571F1402CE0E0D0D2DD1D465216AFE51BC;
struct ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737;
struct ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E;
struct ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A;
struct AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EaseCurve_tF0DFACE7D4AAA5781F27DE34E72E81660958CE61;
struct EaseInfo_tCF78178CA81F33CDDB727DB6FBFDF917CD3BA51F;
struct FieldInfo_t;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IHOTweenComponent_tB2C342F8B62140FB7E789358740C24FD75CCC90E;
struct IMemberAccessor_t8E5B4E92DDE3BD239E980ED7431E33E509032AAA;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MethodInfo_t;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct OverwriteManager_t25D8819D33516851D7144DBEE90D7FF232BAE825;
struct Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF;
struct PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF;
struct PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19;
struct PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B;
struct PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5;
struct PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1;
struct PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888;
struct PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580;
struct PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8;
struct PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E;
struct PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8;
struct PlugVector4_t182247639032B73333E7055ED1105099DEED99DF;
struct PropertyInfo_t;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279;
struct SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA;
struct String_t;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18;
struct TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D;
struct Tweener_t99074CD44759EE1C18B018744C9E38243A40871A;
struct Type_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75;
struct FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707;
struct TweenCallback_t636681A33D249FB51EB356E0746B53250D607704;
struct TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF;
struct HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79;

IL2CPP_EXTERN_C RuntimeClass* ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SendMessageOptions_t8C6881C01B06BF874EE578D27D8CF237EC2BFD54_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0082802CB33D711591EB7173923DE71C91BF6CBE;
IL2CPP_EXTERN_C String_t* _stringLiteral14C4F2807068D9640EE91247145D17939966A293;
IL2CPP_EXTERN_C String_t* _stringLiteral16B1A560D0508AB021624167CB1F87B6D48B02D6;
IL2CPP_EXTERN_C String_t* _stringLiteral19B7D722FFCBB1EBCC95DE76FB16F022050F3CC8;
IL2CPP_EXTERN_C String_t* _stringLiteral22019CCE5271D6EB84252727A240AB258D6BE609;
IL2CPP_EXTERN_C String_t* _stringLiteral27D9B7EF612AEB12509925B54604A1C6C9199F88;
IL2CPP_EXTERN_C String_t* _stringLiteral2F49C847A1A5CEB5577FEA54212488B3D7D0B825;
IL2CPP_EXTERN_C String_t* _stringLiteral3B53C838334DF89B87164B8A5EE26C8FD470850B;
IL2CPP_EXTERN_C String_t* _stringLiteral4B64ECB86CB3E3562CA21F15EDF2E19D670A51ED;
IL2CPP_EXTERN_C String_t* _stringLiteral5F43C61FF910780A25E22CD0232290820C30BA1D;
IL2CPP_EXTERN_C String_t* _stringLiteral7BC2733BAEC60A24A610EE1518219446E759790F;
IL2CPP_EXTERN_C String_t* _stringLiteral82B1FFF171100778CEDD884A0E4A65666906E7EE;
IL2CPP_EXTERN_C String_t* _stringLiteral8A9E9F41FB83E43385B4BF4AA395DC6C61CEF5AD;
IL2CPP_EXTERN_C String_t* _stringLiteralA98C7A22AA6A1C57588D0F7FF2DA7969390ED248;
IL2CPP_EXTERN_C String_t* _stringLiteralB375D52F58ABA319072C6F9F1880BCB36A59233C;
IL2CPP_EXTERN_C String_t* _stringLiteralBCA7DDD073AD5DB21CC612ADB1833BF1A5D32261;
IL2CPP_EXTERN_C String_t* _stringLiteralBED41A93D53C57A40BB6B79662E6D00E6BF4EFB1;
IL2CPP_EXTERN_C String_t* _stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677;
IL2CPP_EXTERN_C String_t* _stringLiteralCFA73882EBCB16AE44454CACF911EC21EF0A579C;
IL2CPP_EXTERN_C String_t* _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
IL2CPP_EXTERN_C String_t* _stringLiteralDB47297909F3BD6EDB8AD67A8511975233214355;
IL2CPP_EXTERN_C String_t* _stringLiteralEB60F7CAA481E19A64B444094946BAD0787BCE63;
IL2CPP_EXTERN_C String_t* _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D;
IL2CPP_EXTERN_C String_t* _stringLiteralF422850993212057809CBD984B2F3DAEC17A02ED;
IL2CPP_EXTERN_C const RuntimeMethod* Array_IndexOf_TisType_t_m2923AB55EE8374E8CABFAD02C349A1C742E82B8A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_TryGetValue_m835BB1E6EA8A8BF1242B51E28FD65B43FEF68E2A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_mBB2DBA9ECB2AD6046CB4CFB717FDD7E474A439AB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_mD41ECDF321C38DCCF6A9FFC5CC98C0D1D8E2764C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* HOTween_DoSendMessage_m88B006E16146E8559219FE3BF4553AF0A5B91BB8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m0C336245737552A850BF98B9B62610882672A341_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m18CB12DF523FE98B674A0D93FA002E47704F555E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m1CBA8A3D48739CC5AF6BCBBD86D0086BB762DE1A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_RemoveAt_mB0AE72F0CAE49940457AFDC332ED7869B9EADA8E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m39186FF5CA6EEBF0401FCC8D454A147188082B45_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m5D2B3DB01D3330882450D6B77EB81FBDA75042CA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m805576DBB9A4E83729241F9A56D3E75202DF9014_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mE437070E1C414F54A661124CFD73BAE04C1D0CC8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m68F0E22360E0088E4149CBCBDAE6A1E67C16CD6C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* String_t_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_tEA0133B78B9FF7045128C508FA50247E525A94D6* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_tCC15D033281A6593E2488FAF5B205812A152AC03* ____keys;
	ValueCollection_tCE6BD704B9571C131E2D8C8CED569DDEC4AE042B* ____values;
	RuntimeObject* ____syncRoot;
};
struct Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_t7C07FADA3D121BF791083230AC898F54129541C8* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_t555B8656568D51D28955442D71A19D8860BFF88C* ____keys;
	ValueCollection_t6E6C24D8CE99E9A850AB95B69939CBBA2CB9E7D9* ____values;
	RuntimeObject* ____syncRoot;
};
struct List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A  : public RuntimeObject
{
	ABSTweenPluginU5BU5D_t475F412450955BB856940F6D8BD8088B8CF930C4* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F  : public RuntimeObject
{
	TweenerU5BU5D_t1772BFD4FB12F62941EC6F73B9B8E495B8B22EC5* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD  : public RuntimeObject
{
	HOTPropDataU5BU5D_t06113F571F1402CE0E0D0D2DD1D465216AFE51BC* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF  : public RuntimeObject
{
};
struct Back_tBA8EAC4AE5202EC66D4FC40A61AB29FD17383958  : public RuntimeObject
{
};
struct Circ_t694775B5C70C5FD9DF560442ABD9E8AD34F4DFCF  : public RuntimeObject
{
};
struct Cubic_t4D410E9A23C187E4D8B2CC8E177739E1E1D92267  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct OverwriteManager_t25D8819D33516851D7144DBEE90D7FF232BAE825  : public RuntimeObject
{
	bool ___enabled;
	bool ___logWarnings;
	List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* ___runningTweens;
};
struct Quint_t16F6181AFE80B97B23EF114F693ED70B510DF3F7  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct TweenDelegate_t98C51F719714F2564925A5022DF8E270D12CA7EE  : public RuntimeObject
{
};
struct TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18  : public RuntimeObject
{
	RuntimeObject* ____tween;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____parms;
	ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* ____plugin;
};
struct TweenWarning_t0E96CCE0A2DBB37BEAA7A2908FB618C43C8E0986  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79  : public RuntimeObject
{
	String_t* ___propName;
	RuntimeObject* ___endValOrPlugin;
	bool ___isRelative;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int32_t ___rgba;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___rgba_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			uint8_t ___r;
		};
		#pragma pack(pop, tp)
		struct
		{
			uint8_t ___r_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___g_OffsetPadding[1];
			uint8_t ___g;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___g_OffsetPadding_forAlignmentOnly[1];
			uint8_t ___g_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___b_OffsetPadding[2];
			uint8_t ___b;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___b_OffsetPadding_forAlignmentOnly[2];
			uint8_t ___b_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___a_OffsetPadding[3];
			uint8_t ___a;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___a_OffsetPadding_forAlignmentOnly[3];
			uint8_t ___a_forAlignmentOnly;
		};
	};
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct FieldInfo_t  : public MemberInfo_t
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct PropertyInfo_t  : public MemberInfo_t
{
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
};
struct AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354_marshaled_com
{
	intptr_t ___m_Ptr;
};
struct Axis_t2ACE185AF936C449FCE7F4F518B02045907CC92E 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct EaseType_t10BF487776FC4E75AEE2F4201D2F1C8EDAD886E7 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct LoopType_tBDD352D32A5B27E8727592B5415D356D30E13A55 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PathType_t6B670AEECF0026B822D1EA2E8F7876FA56D6DF62 
{
	int32_t ___value__;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct SendMessageOptions_t8C6881C01B06BF874EE578D27D8CF237EC2BFD54 
{
	int32_t ___value__;
};
struct UpdateType_t8AC06247C9908AE54821910EDA25C966B22C0709 
{
	int32_t ___value__;
};
struct WarningLevel_t159BC3C4256708CB1725830243153EC4E59A3F5E 
{
	int32_t ___value__;
};
struct OrientType_t16B009BF3EEF7B7F0443E456ACBACCAC87651D31 
{
	int32_t ___value__;
};
struct ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737  : public RuntimeObject
{
	String_t* ____id;
	int32_t ____intId;
	bool ____autoKillOnComplete;
	bool ____enabled;
	float ____timeScale;
	int32_t ____loops;
	int32_t ____loopType;
	int32_t ____updateType;
	bool ____isPaused;
	bool ___ignoreCallbacks;
	bool ____steadyIgnoreCallbacks;
	Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* ___contSequence;
	bool ___startupDone;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onStart;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onStartWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onStartParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onUpdate;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onUpdateWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onUpdateParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPluginUpdated;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPluginUpdatedWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPluginUpdatedParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPause;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPauseWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPauseParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPlay;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPlayWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPlayParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onRewinded;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onRewindedWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onRewindedParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onStepComplete;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onStepCompleteWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onStepCompleteParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onComplete;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onCompleteWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onCompleteParms;
	int32_t ____completedLoops;
	float ____duration;
	float ____originalDuration;
	float ____originalNonSpeedBasedDuration;
	float ____fullDuration;
	float ____elapsed;
	float ____fullElapsed;
	bool ____destroyed;
	bool ____isEmpty;
	bool ____isReversed;
	bool ____isLoopingBack;
	bool ____hasStarted;
	bool ____isComplete;
	float ___prevFullElapsed;
	int32_t ___prevCompletedLoops;
	bool ___manageBehaviours;
	bool ___manageGameObjects;
	BehaviourU5BU5D_t18066727E4902C04B4FFBCEEAB25AAC13418F9AA* ___managedBehavioursOn;
	BehaviourU5BU5D_t18066727E4902C04B4FFBCEEAB25AAC13418F9AA* ___managedBehavioursOff;
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___managedGameObjectsOn;
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___managedGameObjectsOff;
	BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4* ___managedBehavioursOriginalState;
	BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4* ___managedGameObjectsOriginalState;
};
struct ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E  : public RuntimeObject
{
	String_t* ___id;
	int32_t ___intId;
	bool ___autoKillOnComplete;
	int32_t ___updateType;
	float ___timeScale;
	int32_t ___loops;
	int32_t ___loopType;
	bool ___isPaused;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onStart;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onStartWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onStartParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onUpdate;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onUpdateWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onUpdateParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPluginUpdated;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPluginUpdatedWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPluginUpdatedParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPause;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPauseWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPauseParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPlay;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPlayWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPlayParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onRewinded;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onRewindedWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onRewindedParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onStepComplete;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onStepCompleteWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onStepCompleteParms;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onComplete;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onCompleteWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onCompleteParms;
	bool ___manageBehaviours;
	bool ___manageGameObjects;
	BehaviourU5BU5D_t18066727E4902C04B4FFBCEEAB25AAC13418F9AA* ___managedBehavioursOn;
	BehaviourU5BU5D_t18066727E4902C04B4FFBCEEAB25AAC13418F9AA* ___managedBehavioursOff;
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___managedGameObjectsOn;
	GameObjectU5BU5D_tFF67550DFCE87096D7A3734EA15B75896B2722CF* ___managedGameObjectsOff;
};
struct ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A  : public RuntimeObject
{
	RuntimeObject* ____startVal;
	RuntimeObject* ____endVal;
	float ____duration;
	bool ____initialized;
	bool ____easeReversed;
	String_t* ____propName;
	Type_t* ___targetType;
	EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* ___ease;
	bool ___isRelative;
	bool ___ignoreAccessor;
	int32_t ___easeType;
	EaseInfo_tCF78178CA81F33CDDB727DB6FBFDF917CD3BA51F* ___easeInfo;
	EaseCurve_tF0DFACE7D4AAA5781F27DE34E72E81660958CE61* ___easeCurve;
	RuntimeObject* ___valAccessor;
	bool ___wasStarted;
	bool ___speedBasedDurationWasSet;
	int32_t ___prevCompletedLoops;
	bool ____useSpeedTransformAccessors;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ____transformTarget;
	HOAction_1_t3C819BD915A5AB9ABC2CFC13278E30C26EC42BF2* ____setTransformVector3;
	HOFunc_1_t41E04A0D95A65CF2022480C7F7AE604DD8E5A32C* ____getTransformVector3;
	HOAction_1_tFB2FBEE2362B9EA5FB3B490AF7DCB29F69FB49DB* ____setTransformQuaternion;
	HOFunc_1_t40C6A6BEC4C5E783BF6FCDC4F38C337734402C44* ____getTransformQuaternion;
	PropertyInfo_t* ___propInfo;
	FieldInfo_t* ___fieldInfo;
	Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___tweenObj;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF  : public RuntimeObject
{
	float ___pathLength;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___waypointsLength;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___timesTable;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___lengthsTable;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___path;
	bool ___changed;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___drawPs;
	int32_t ___pathType;
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___typedStartVal;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___typedEndVal;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___diffChangeVal;
};
struct PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___typedStartVal;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___typedEndVal;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___diffChangeVal;
};
struct PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	float ___typedStartVal;
	float ___typedEndVal;
	float ___changeVal;
};
struct PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	float ___typedStartVal;
	float ___typedEndVal;
	float ___changeVal;
};
struct PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___typedStartVal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___typedEndVal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___changeVal;
	bool ___beyond360;
};
struct PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___typedStartVal;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___typedEndVal;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___diffChangeVal;
};
struct PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	String_t* ___typedStartVal;
	String_t* ___typedEndVal;
	float ___changeVal;
};
struct PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___typedStartVal;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___typedEndVal;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___changeVal;
};
struct PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___typedStartVal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___typedEndVal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___changeVal;
};
struct PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* ___path;
	float ___pathPerc;
	bool ___hasAdditionalStartingP;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___typedStartVal;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___points;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___diffChangeVal;
	bool ___isClosedPath;
	int32_t ___orientType;
	float ___lookAheadVal;
	int32_t ___lockPositionAxis;
	int32_t ___lockRotationAxis;
	bool ___isPartialPath;
	bool ___usesLocalPosition;
	float ___startPerc;
	float ___changePerc;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___lookPos;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___lookTrans;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___orientTrans;
	int32_t ___U3CpathTypeU3Ek__BackingField;
};
struct PlugVector4_t182247639032B73333E7055ED1105099DEED99DF  : public ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___typedStartVal;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___typedEndVal;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___changeVal;
};
struct Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279  : public ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737
{
	bool ___hasCallbacks;
	int32_t ___prevIncrementalCompletedLoops;
	float ___prevElapsed;
	List_1_t698497CF3874D0FD4985709B1BF189D35D6EA4B0* ___items;
};
struct SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA  : public ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D  : public ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E
{
	bool ___pixelPerfect;
	bool ___speedBased;
	bool ___easeSet;
	int32_t ___easeType;
	AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* ___easeAnimCurve;
	float ___easeOvershootOrAmplitude;
	float ___easePeriod;
	float ___delay;
	List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* ___propDatas;
	bool ___isFrom;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPluginOverwritten;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPluginOverwrittenWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPluginOverwrittenParms;
};
struct Tweener_t99074CD44759EE1C18B018744C9E38243A40871A  : public ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737
{
	float ____elapsedDelay;
	int32_t ____easeType;
	AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* ____easeAnimationCurve;
	float ____easeOvershootOrAmplitude;
	float ____easePeriod;
	bool ____pixelPerfect;
	bool ____speedBased;
	float ____delay;
	float ___delayCount;
	TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___onPluginOverwritten;
	TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* ___onPluginOverwrittenWParms;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___onPluginOverwrittenParms;
	List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* ___plugins;
	RuntimeObject* ____target;
	bool ___isPartialled;
	int32_t ____originalEaseType;
	PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* ___pv3Path;
	bool ___U3CisFromU3Ek__BackingField;
};
struct EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75  : public MulticastDelegate_t
{
};
struct FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707  : public MulticastDelegate_t
{
};
struct TweenCallback_t636681A33D249FB51EB356E0746B53250D607704  : public MulticastDelegate_t
{
};
struct TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF  : public MulticastDelegate_t
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A_StaticFields
{
	ABSTweenPluginU5BU5D_t475F412450955BB856940F6D8BD8088B8CF930C4* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F_StaticFields
{
	TweenerU5BU5D_t1772BFD4FB12F62941EC6F73B9B8E495B8B22EC5* ___s_emptyArray;
};
struct List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD_StaticFields
{
	HOTPropDataU5BU5D_t06113F571F1402CE0E0D0D2DD1D465216AFE51BC* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_StaticFields
{
	Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* ___U24U24method0x60002b7U2D1;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___zeroVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___oneVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___positiveInfinityVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___negativeInfinityVector;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
struct PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validPropTypes;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___validValueTypes;
};
struct TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields
{
	Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* ____TypeToShortString;
};
struct HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields
{
	String_t* ___VERSION;
	int32_t ___defUpdateType;
	float ___defTimeScale;
	int32_t ___defEaseType;
	float ___defEaseOvershootOrAmplitude;
	float ___defEasePeriod;
	int32_t ___defLoopType;
	bool ___showPathGizmos;
	int32_t ___warningLevel;
	bool ___isIOS;
	bool ___isEditor;
	List_1_t49F91546A5E6849CD21CAF9281555E44FBD71FFC* ___onCompletes;
	bool ___initialized;
	bool ___isPermanent;
	bool ___renameInstToCountTw;
	float ___time;
	bool ___isQuitting;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___tweensToRemoveIndexes;
	OverwriteManager_t25D8819D33516851D7144DBEE90D7FF232BAE825* ___overwriteManager;
	List_1_t49F91546A5E6849CD21CAF9281555E44FBD71FFC* ___tweens;
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___tweenGOInstance;
	HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC* ___it;
	bool ___U3CisUpdateLoopU3Ek__BackingField;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C  : public RuntimeArray
{
	ALIGN_FIELD (8) Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 m_Items[1];

	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 value)
	{
		m_Items[index] = value;
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB  : public RuntimeArray
{
	ALIGN_FIELD (8) Type_t* m_Items[1];

	inline Type_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Type_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Type_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Type_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Type_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Type_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C  : public RuntimeArray
{
	ALIGN_FIELD (8) float m_Items[1];

	inline float GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline float* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, float value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline float GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline float* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, float value)
	{
		m_Items[index] = value;
	}
};
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771  : public RuntimeArray
{
	ALIGN_FIELD (8) Delegate_t* m_Items[1];

	inline Delegate_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Delegate_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Delegate_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Delegate_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Dictionary_2_ContainsKey_m703047C213F7AB55C9DC346596287773A1F670CD_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Dictionary_2_get_Item_m4AAAECBE902A211BF2126E6AFA280AEF73A3E0D6_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m129B1E1EDDABF00B402C93841CCA7169B8963D83_gshared (Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1* __this, int32_t ___0_capacity, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Add_m63897227AFA7035F1772315ABBBE7FD0A250E10C_gshared (Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1* __this, RuntimeObject* ___0_key, int32_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Dictionary_2_TryGetValue_m4B8EE45640C70BBFD6F3EFF1040983404C098342_gshared (Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1* __this, RuntimeObject* ___0_key, int32_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Array_IndexOf_TisRuntimeObject_m69589B2C5A44BA495E1A2B1170931D92F9BB6BF1_gshared (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_mF225F49F6BE54C39563CECD7C693F0AE4F0530E8_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, int32_t ___0_capacity, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Add_m93FFFABE8FCE7FA9793F0915E2A8842C7CD0C0C1_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Copy_m4233828B4E6288B6D815F539AAA38575DE627900 (RuntimeArray* ___0_sourceArray, RuntimeArray* ___1_destinationArray, int32_t ___2_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Reverse_m464993603E0F56B4A68F70113212032FE7381B6C (RuntimeArray* ___0_array, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482 (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, RuntimeObject* ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void PlugVector3Path_set_pathType_mB72EF8EB3956A20D2D45AA4DCEBB3727603CFABE_inline (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36 (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_args, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B (String_t* ___0_p_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ABSTweenPlugin_Init_mA17A13339EA1B9D8A939B5E8144C57FE9342CC29 (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___0_p_tweenObj, String_t* ___1_p_propertyName, int32_t ___2_p_easeType, Type_t* ___3_p_targetType, PropertyInfo_t* ___4_p_propertyInfo, FieldInfo_t* ___5_p_fieldInfo, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_ClosePath_mFF30CD58A7ADBE3860716938AE1D0B590EECE6D2 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, bool ___0_p_close, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_OrientToPath_mDD2FD17CAE023690D586637E863EAB2F75BEDCAA (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, bool ___0_p_orient, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_OrientToPath_mD952BB4DB29845EC1FBA4A84CFAC615054204F0A (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, bool ___0_p_orient, float ___1_p_lookAhead, int32_t ___2_p_lockRotationAxis, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186_inline (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Path__ctor_mB08F108F59563B544D546B8A9EB2105FD46D4588 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, int32_t ___0_p_type, Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___1_p_path, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Path_StoreTimeToLenTables_mF3AFBB4D067AB81A9B6EE85D91D340361D198EC9 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, int32_t ___0_p_subdivisions, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_inline (EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 PlugVector3Path_GetConstPointOnPath_m00566EF01E12762EB32F6317A830E61461A670E4 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, float ___0_t, bool ___1_p_updatePathPerc, Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* ___2_p_path, int32_t* ___3_out_waypointIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Transform_get_parent_m65354E28A4C94EC00EBCF03532F7B0718380791E (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_LookAt_mBD38EDB5E915C5DA6C5A79D191DEE2C826A9FC2C (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_worldUp, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_TransformPoint_m05BFF013DB830D7BFE44A007703694AE1062EE44 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_position, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_position, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ABSTweenPlugin_Rewind_m738D072B63A84CA9B808B389B7DF22F35A3E4FBC (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ABSTweenPlugin_Complete_m25B5AD6CE114E6224C2BC80D575E5148753855DB (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetConstPoint_mC350B3F2078D6AB8F49B9C5B063BDD6C79B0654C (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, float* ___1_out_pathPerc, int32_t* ___2_out_waypointIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetConstPoint_m1DADD874A6EC9E06D13C398963002B81F9017653 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
inline void List_1__ctor_m39186FF5CA6EEBF0401FCC8D454A147188082B45 (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline int32_t List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_inline (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline int32_t List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_inline (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411 (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* (*) (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* (*) (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB_inline (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F (ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ABSTweenComponent_get_isComplete_m709E527B954A24C4FC9BFA6AAEAF82332441991F_inline (ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* __this, const RuntimeMethod* method) ;
inline void List_1_RemoveAt_mB0AE72F0CAE49940457AFDC332ED7869B9EADA8E (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	((  void (*) (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A*, int32_t, const RuntimeMethod*))List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared)(__this, ___0_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t String_LastIndexOf_m8923DBD89F2B3E5A34190B038B48F402E0C17E40 (String_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Substring_m6BA4A3FA3800FE92662D0847CC8E1EEF940DF472 (String_t* __this, int32_t ___0_startIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Sequence_Remove_mC0A8D195AF01D4D8514D7515286352256C677E31 (Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* __this, ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* ___0_p_tween, const RuntimeMethod* method) ;
inline void List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44 (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	((  void (*) (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F*, int32_t, const RuntimeMethod*))List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared)(__this, ___0_index, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_inline (TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenEvent__ctor_m20EB08AE4E804741D72FBED05DE8925CC9C132EF (TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* __this, RuntimeObject* ___0_p_tween, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___1_p_parms, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_inline (TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ABSTweenComponent_get_destroyed_m4FE7ACE9A38BE5BED05C117B3F147838083CFC01_inline (ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* __this, const RuntimeMethod* method) ;
inline void List_1_Add_m18CB12DF523FE98B674A0D93FA002E47704F555E_inline (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* __this, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F*, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_op_Addition_mA7A51CACA49ED8D23D3D9CA3A0092D32F657E053_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___0_c, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_op_Subtraction_mF003448D819F2A41405BB6D85F1563CDA900B07F_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_op_Multiply_m379B20A820266ACF82A21425B9CAE8DCD773CFBB_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, float ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_c, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenWarning_Log_mDD27E543707A5EFEDCBE8A709413D3156D9A938F (String_t* ___0_p_message, bool ___1_p_verbose, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetPoint_m931F8C934DA00412C36CCE7D011F45F2F4F80555 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, int32_t* ___1_out_waypointIndex, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_ClampMagnitude_mF83675F19744F58E97CF24D8359A810634DC031F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, float ___1_maxLength, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline (float ___0_d, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797 (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_from, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_to, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Gizmos_DrawSphere_mC7B2862BBDB3141A63B83F0F1E56E30101D4F472 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_center, float ___1_radius, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3_Normalize_mC749B887A4C74BA0A2E13E6377F17CCAEB0AADA8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Cross_mF93A280558BCE756D13B6CC5DCD7DE8A43148987_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_blue_mF04A26CE61D6DA3C0D8B1C4720901B1028C7AB87_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Path_GetConstPathPercFromTimePerc_m05DF6CE5DEE89C0D965748560E2D3E701C9D01B5 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ABSTweenComponentParms_InitializeOwner_mF88937400BEA35A760F2DC698CA459C44FE82327 (ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E* __this, ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* ___0_p_owner, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Tweener_set_isFrom_m3E5ABBC9B076D66C6006F2E422A6B15C0899CD24_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, bool ___0_value, const RuntimeMethod* method) ;
inline void List_1__ctor_m805576DBB9A4E83729241F9A56D3E75202DF9014 (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline int32_t List_1_get_Count_mE437070E1C414F54A661124CFD73BAE04C1D0CC8_inline (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* List_1_get_Item_m68F0E22360E0088E4149CBCBDAE6A1E67C16CD6C (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* (*) (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PropertyInfo_t* Type_GetProperty_mD183124FC8A89121E8368058B327A7750B14281D (Type_t* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FieldInfo_t* Type_GetField_m0BF55B1A27A1B6AB6D3477E7F9E1CF2A3451E1E0 (Type_t* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ABSTweenPlugin_get_initialized_mBDDF3D1051BAFBF04CAAF5600D799AE51D452397_inline (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* ABSTweenPlugin_CloneBasic_mCA9249440372C5ECD0B8A07D357C7D005CBDF22E (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Utils_SimpleClassName_m04D18EADDE8255C2C1DDB00067B4F55C8EB8F5FA (Type_t* ___0_p_class, const RuntimeMethod* method) ;
inline bool Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* __this, Type_t* ___0_key, const RuntimeMethod* method)
{
	return ((  bool (*) (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE*, Type_t*, const RuntimeMethod*))Dictionary_2_ContainsKey_m703047C213F7AB55C9DC346596287773A1F670CD_gshared)(__this, ___0_key, method);
}
inline String_t* Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008 (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* __this, Type_t* ___0_key, const RuntimeMethod* method)
{
	return ((  String_t* (*) (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE*, Type_t*, const RuntimeMethod*))Dictionary_2_get_Item_m4AAAECBE902A211BF2126E6AFA280AEF73A3E0D6_gshared)(__this, ___0_key, method);
}
inline void Dictionary_2__ctor_mBB2DBA9ECB2AD6046CB4CFB717FDD7E474A439AB (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* __this, int32_t ___0_capacity, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588*, int32_t, const RuntimeMethod*))Dictionary_2__ctor_m129B1E1EDDABF00B402C93841CCA7169B8963D83_gshared)(__this, ___0_capacity, method);
}
inline void Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883 (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* __this, String_t* ___0_key, int32_t ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588*, String_t*, int32_t, const RuntimeMethod*))Dictionary_2_Add_m63897227AFA7035F1772315ABBBE7FD0A250E10C_gshared)(__this, ___0_key, ___1_value, method);
}
inline bool Dictionary_2_TryGetValue_m835BB1E6EA8A8BF1242B51E28FD65B43FEF68E2A (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* __this, String_t* ___0_key, int32_t* ___1_value, const RuntimeMethod* method)
{
	return ((  bool (*) (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588*, String_t*, int32_t*, const RuntimeMethod*))Dictionary_2_TryGetValue_m4B8EE45640C70BBFD6F3EFF1040983404C098342_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B (RuntimeObject* ___0_p_val, TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___1_p_validVals, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector2__ctor_mD38E3F80476EF22E23B0D6902C1EBFBE597E50DD (PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3__ctor_mFAEE32D17D68FA03776ED57F2C2A351D19A2621B (PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector4__ctor_m348E95DFFA753B9E5A4DF1A5AB25DEA5DBD84E81 (PlugVector4_t182247639032B73333E7055ED1105099DEED99DF* __this, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion__ctor_m653333B63186F7A0F1430587FAF26EE4A67302D8 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion__ctor_m46BD79B83263F7486AA657F2BDB40E50A2198049 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor__ctor_m9587F07E6E13DF59F6DBB8795BC7408688ABF745 (PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32__ctor_mA746143BEC963C76BB01E625BE07D6E7B6D83E4E (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugRect__ctor_m1CCAC707C847323D566B4B359BD492E0368C1750 (PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugString__ctor_mBC5CF13283AEDED7546061AFDDCD1BC3049D9D12 (PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580* __this, String_t* ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt__ctor_m36BBA904D1AA75C2195D945C7D808BB4404D404D (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, float ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Convert_ToSingle_m6B47C78A7DFD7825B4361BCA8AB6748FC82165E9 (RuntimeObject* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugFloat__ctor_m7F3FBD710426F3E263968ABEA94E1083679AB401 (PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B* __this, float ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) ;
inline void List_1_Add_m0C336245737552A850BF98B9B62610882672A341_inline (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* __this, ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A*, ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Ease_mB302FD168B34BF99116AA23AC925761871053D9B (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_easeType, float ___1_p_amplitude, float ___2_p_period, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Prop_m52667C136BA3A423786787A2E8B27D3BB1E25BA0 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, String_t* ___0_p_propName, RuntimeObject* ___1_p_endVal, bool ___2_p_isRelative, const RuntimeMethod* method) ;
inline void List_1__ctor_m5D2B3DB01D3330882450D6B77EB81FBDA75042CA (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HOTPropData__ctor_mEB72EC44DC80528C9615FBB1580D2208C1C27DEA (HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* __this, String_t* ___0_p_propName, RuntimeObject* ___1_p_endValOrPlugin, bool ___2_p_isRelative, const RuntimeMethod* method) ;
inline void List_1_Add_m1CBA8A3D48739CC5AF6BCBBD86D0086BB762DE1A_inline (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* __this, HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD*, HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Loops_mFD3B261B9B6C37DD20528F6E622E0145F0B23974 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_loops, int32_t ___1_p_loopType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenCallbackWParms__ctor_mB37CAD56CA9F34BDAC55ED611104A2DBBE80B520 (TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
inline int32_t Array_IndexOf_TisType_t_m2923AB55EE8374E8CABFAD02C349A1C742E82B8A (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___0_array, Type_t* ___1_value, const RuntimeMethod* method)
{
	return ((  int32_t (*) (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*, Type_t*, const RuntimeMethod*))Array_IndexOf_TisRuntimeObject_m69589B2C5A44BA495E1A2B1170931D92F9BB6BF1_gshared)(___0_array, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ABSTweenComponentParms__ctor_m689C96ED2202D6F626DB88BBF1F031D265508270 (ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E* __this, const RuntimeMethod* method) ;
inline void Dictionary_2__ctor_mD41ECDF321C38DCCF6A9FFC5CC98C0D1D8E2764C (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* __this, int32_t ___0_capacity, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE*, int32_t, const RuntimeMethod*))Dictionary_2__ctor_mF225F49F6BE54C39563CECD7C693F0AE4F0530E8_gshared)(__this, ___0_capacity, method);
}
inline void Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* __this, Type_t* ___0_key, String_t* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE*, Type_t*, String_t*, const RuntimeMethod*))Dictionary_2_Add_m93FFFABE8FCE7FA9793F0915E2A8842C7CD0C0C1_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_magnitude_mF0D6017E90B345F1F52D1CC564C640F1A847AF2D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* __this, uint8_t ___0_r, uint8_t ___1_g, uint8_t ___2_b, uint8_t ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_Internal_ToEulerRad_m5BD0EEC543120C320DC77FCCDFD2CE2E6BD3F1A8 (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_rotation, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_Internal_MakePositive_m73E2D01920CB0DFE661A55022C129E8617F0C9A8 (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CpathTypeU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_set_pathType_mB72EF8EB3956A20D2D45AA4DCEBB3727603CFABE (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CpathTypeU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlugVector3Path_get_startVal_m4B0C3A1FD61836CB35759B84E5C97CE5CEDA4DED (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_set_startVal_m8D9E885781CE7F85A304FB17058D4B60A5A48B60 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* V_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_0);
		bool L_1;
		L_1 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_0, NULL);
		if (!L_1)
		{
			goto IL_0044;
		}
	}
	{
		RuntimeObject* L_2 = ___0_value;
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal), (void*)L_2);
		RuntimeObject* L_3 = ___0_value;
		V_0 = ((Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)Castclass((RuntimeObject*)L_3, Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var));
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_4 = V_0;
		NullCheck(L_4);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_5 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length)));
		__this->___points = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___points), (void*)L_5);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_6 = V_0;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_7 = __this->___points;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_8 = V_0;
		NullCheck(L_8);
		Array_Copy_m4233828B4E6288B6D815F539AAA38575DE627900((RuntimeArray*)L_6, (RuntimeArray*)L_7, ((int32_t)(((RuntimeArray*)L_8)->max_length)), NULL);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_9 = __this->___points;
		Array_Reverse_m464993603E0F56B4A68F70113212032FE7381B6C((RuntimeArray*)L_9, NULL);
		return;
	}

IL_0044:
	{
		RuntimeObject* L_10 = ___0_value;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_10, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))));
		V_1 = L_11;
		__this->___typedStartVal = L_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = L_12;
		RuntimeObject* L_14 = Box(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var, &L_13);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_14;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_14);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_set_endVal_m105459D2CDA35CD6650A04F2CFB690BE1188200E (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* V_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_0);
		bool L_1;
		L_1 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_0, NULL);
		if (!L_1)
		{
			goto IL_0028;
		}
	}
	{
		RuntimeObject* L_2 = ___0_value;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_2, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))));
		V_1 = L_3;
		__this->___typedStartVal = L_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = L_4;
		RuntimeObject* L_6 = Box(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var, &L_5);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_6);
		return;
	}

IL_0028:
	{
		RuntimeObject* L_7 = ___0_value;
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal), (void*)L_7);
		RuntimeObject* L_8 = ___0_value;
		V_0 = ((Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)Castclass((RuntimeObject*)L_8, Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var));
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_9 = V_0;
		NullCheck(L_9);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_10 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)(((RuntimeArray*)L_9)->max_length)));
		__this->___points = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___points), (void*)L_10);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_11 = V_0;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_12 = __this->___points;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_13 = V_0;
		NullCheck(L_13);
		Array_Copy_m4233828B4E6288B6D815F539AAA38575DE627900((RuntimeArray*)L_11, (RuntimeArray*)L_12, ((int32_t)(((RuntimeArray*)L_13)->max_length)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path__ctor_m3263C04904CBAF1C7F7B41FCA8433F2DAF755798 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___0_p_path, bool ___1_p_isRelative, int32_t ___2_p_type, const RuntimeMethod* method) 
{
	{
		__this->___lookAheadVal = (9.99999975E-05f);
		__this->___changePerc = (1.0f);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_0 = ___0_p_path;
		bool L_1 = ___1_p_isRelative;
		ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482(__this, (RuntimeObject*)L_0, L_1, NULL);
		int32_t L_2 = ___2_p_type;
		PlugVector3Path_set_pathType_mB72EF8EB3956A20D2D45AA4DCEBB3727603CFABE_inline(__this, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_Init_m85E520A75C4F71095E071D4B01BAE0008A06A651 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___0_p_tweenObj, String_t* ___1_p_propertyName, int32_t ___2_p_easeType, Type_t* ___3_p_targetType, PropertyInfo_t* ___4_p_propertyInfo, FieldInfo_t* ___5_p_fieldInfo, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral22019CCE5271D6EB84252727A240AB258D6BE609);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8A9E9F41FB83E43385B4BF4AA395DC6C61CEF5AD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		s_Il2CppMethodInitialized = true;
	}
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	{
		bool L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_0)
		{
			goto IL_004e;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_1 = ___0_p_tweenObj;
		NullCheck(L_1);
		bool L_2;
		L_2 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_1, NULL);
		if (!L_2)
		{
			goto IL_004e;
		}
	}
	{
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative = (bool)0;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_3 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)5);
		V_0 = L_3;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, _stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)_stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_5 = V_0;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_6 = ___0_p_tweenObj;
		NullCheck(L_6);
		RuntimeObject* L_7;
		L_7 = Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline(L_6, NULL);
		NullCheck(L_5);
		ArrayElementTypeCheck (L_5, L_7);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_7);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_8 = V_0;
		NullCheck(L_8);
		ArrayElementTypeCheck (L_8, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_9 = V_0;
		String_t* L_10 = ___1_p_propertyName;
		NullCheck(L_9);
		ArrayElementTypeCheck (L_9, L_10);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_10);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_11 = V_0;
		NullCheck(L_11);
		ArrayElementTypeCheck (L_11, _stringLiteral22019CCE5271D6EB84252727A240AB258D6BE609);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)_stringLiteral22019CCE5271D6EB84252727A240AB258D6BE609);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_12 = V_0;
		String_t* L_13;
		L_13 = String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36(L_12, NULL);
		TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B(L_13, NULL);
	}

IL_004e:
	{
		String_t* L_14 = ___1_p_propertyName;
		bool L_15;
		L_15 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_14, _stringLiteral8A9E9F41FB83E43385B4BF4AA395DC6C61CEF5AD, NULL);
		__this->___usesLocalPosition = L_15;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_16 = ___0_p_tweenObj;
		String_t* L_17 = ___1_p_propertyName;
		int32_t L_18 = ___2_p_easeType;
		Type_t* L_19 = ___3_p_targetType;
		PropertyInfo_t* L_20 = ___4_p_propertyInfo;
		FieldInfo_t* L_21 = ___5_p_fieldInfo;
		ABSTweenPlugin_Init_mA17A13339EA1B9D8A939B5E8144C57FE9342CC29(__this, L_16, L_17, L_18, L_19, L_20, L_21, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_ClosePath_m0832EA5BB568B5780C96EFA67DD46D636CAEED70 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* L_0;
		L_0 = PlugVector3Path_ClosePath_mFF30CD58A7ADBE3860716938AE1D0B590EECE6D2(__this, (bool)1, NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_ClosePath_mFF30CD58A7ADBE3860716938AE1D0B590EECE6D2 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, bool ___0_p_close, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_p_close;
		__this->___isClosedPath = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_OrientToPath_mD014C263C462386E51EB32BFE838C361BF2F6358 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* L_0;
		L_0 = PlugVector3Path_OrientToPath_mDD2FD17CAE023690D586637E863EAB2F75BEDCAA(__this, (bool)1, NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_OrientToPath_mDD2FD17CAE023690D586637E863EAB2F75BEDCAA (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, bool ___0_p_orient, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_p_orient;
		PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* L_1;
		L_1 = PlugVector3Path_OrientToPath_mD952BB4DB29845EC1FBA4A84CFAC615054204F0A(__this, L_0, (9.99999975E-05f), 0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* PlugVector3Path_OrientToPath_mD952BB4DB29845EC1FBA4A84CFAC615054204F0A (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, bool ___0_p_orient, float ___1_p_lookAhead, int32_t ___2_p_lockRotationAxis, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_p_orient;
		if (!L_0)
		{
			goto IL_000a;
		}
	}
	{
		__this->___orientType = 1;
	}

IL_000a:
	{
		float L_1 = ___1_p_lookAhead;
		__this->___lookAheadVal = L_1;
		float L_2 = __this->___lookAheadVal;
		if ((!(((float)L_2) < ((float)(9.99999975E-05f)))))
		{
			goto IL_002b;
		}
	}
	{
		__this->___lookAheadVal = (9.99999975E-05f);
		goto IL_0043;
	}

IL_002b:
	{
		float L_3 = __this->___lookAheadVal;
		if ((!(((float)L_3) > ((float)(0.999899983f)))))
		{
			goto IL_0043;
		}
	}
	{
		__this->___lookAheadVal = (0.999899983f);
	}

IL_0043:
	{
		int32_t L_4 = ___2_p_lockRotationAxis;
		__this->___lockRotationAxis = L_4;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlugVector3Path_GetSpeedBasedDuration_mF385E24D04D41BCA0487141CC86AAC2DF468594E (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, float ___0_p_speed, const RuntimeMethod* method) 
{
	{
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_0 = __this->___path;
		NullCheck(L_0);
		float L_1 = L_0->___pathLength;
		float L_2 = ___0_p_speed;
		return ((float)(L_1/L_2));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_SetChangeVal_mED72B4F145B086DCAA6008DDA4F396A507EC444D (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* V_0 = NULL;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_4;
	memset((&V_4), 0, sizeof(V_4));
	int32_t V_5 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_6;
	memset((&V_6), 0, sizeof(V_6));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_7;
	memset((&V_7), 0, sizeof(V_7));
	int32_t V_8 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_9;
	memset((&V_9), 0, sizeof(V_9));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_10;
	memset((&V_10), 0, sizeof(V_10));
	bool V_11 = false;
	bool V_12 = false;
	bool V_13 = false;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_14;
	memset((&V_14), 0, sizeof(V_14));
	int32_t V_15 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_16;
	memset((&V_16), 0, sizeof(V_16));
	int32_t G_B6_0 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B36_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B35_0 = NULL;
	float G_B37_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B37_1 = NULL;
	float G_B39_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B39_1 = NULL;
	float G_B38_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B38_1 = NULL;
	float G_B40_0 = 0.0f;
	float G_B40_1 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B40_2 = NULL;
	float G_B42_0 = 0.0f;
	float G_B42_1 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B42_2 = NULL;
	float G_B41_0 = 0.0f;
	float G_B41_1 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B41_2 = NULL;
	float G_B43_0 = 0.0f;
	float G_B43_1 = 0.0f;
	float G_B43_2 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B43_3 = NULL;
	{
		int32_t L_0 = __this->___orientType;
		if (!L_0)
		{
			goto IL_002c;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1 = __this->___orientTrans;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_002c;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_3 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline(L_3, NULL);
		__this->___orientTrans = ((Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*)IsInstClass((RuntimeObject*)L_4, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&__this->___orientTrans), (void*)((Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*)IsInstClass((RuntimeObject*)L_4, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1_il2cpp_TypeInfo_var)));
	}

IL_002c:
	{
		V_1 = 1;
		bool L_5 = __this->___isClosedPath;
		if (L_5)
		{
			goto IL_0039;
		}
	}
	{
		G_B6_0 = 0;
		goto IL_003a;
	}

IL_0039:
	{
		G_B6_0 = 1;
	}

IL_003a:
	{
		V_2 = G_B6_0;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_6 = __this->___points;
		NullCheck(L_6);
		V_3 = ((int32_t)(((RuntimeArray*)L_6)->max_length));
		bool L_7 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_7)
		{
			goto IL_00b9;
		}
	}
	{
		__this->___hasAdditionalStartingP = (bool)0;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_8 = __this->___points;
		NullCheck(L_8);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_8)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = __this->___typedStartVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_9, L_10, NULL);
		V_4 = L_11;
		int32_t L_12 = V_3;
		int32_t L_13 = V_2;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_14 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(L_12, 2)), L_13)));
		V_0 = L_14;
		V_5 = 0;
		goto IL_00af;
	}

IL_0081:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_15 = V_0;
		int32_t L_16 = V_5;
		int32_t L_17 = V_1;
		NullCheck(L_15);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_18 = __this->___points;
		int32_t L_19 = V_5;
		NullCheck(L_18);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_18)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_19))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21 = V_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22;
		L_22 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_20, L_21, NULL);
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_15)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(L_16, L_17))))) = L_22;
		int32_t L_23 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_23, 1));
	}

IL_00af:
	{
		int32_t L_24 = V_5;
		int32_t L_25 = V_3;
		if ((((int32_t)L_24) < ((int32_t)L_25)))
		{
			goto IL_0081;
		}
	}
	{
		goto IL_01e9;
	}

IL_00b9:
	{
		RuntimeObject* L_26;
		L_26 = VirtualFuncInvoker0< RuntimeObject* >::Invoke(17, __this);
		V_6 = ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_26, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_27 = V_6;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_28 = __this->___points;
		NullCheck(L_28);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_29 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_28)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_30;
		L_30 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_27, L_29, NULL);
		V_7 = L_30;
		float L_31 = (&V_7)->___x;
		if ((!(((float)L_31) < ((float)(0.0f)))))
		{
			goto IL_00fd;
		}
	}
	{
		float L_32 = (&V_7)->___x;
		(&V_7)->___x = ((-L_32));
	}

IL_00fd:
	{
		float L_33 = (&V_7)->___y;
		if ((!(((float)L_33) < ((float)(0.0f)))))
		{
			goto IL_011a;
		}
	}
	{
		float L_34 = (&V_7)->___y;
		(&V_7)->___y = ((-L_34));
	}

IL_011a:
	{
		float L_35 = (&V_7)->___z;
		if ((!(((float)L_35) < ((float)(0.0f)))))
		{
			goto IL_0137;
		}
	}
	{
		float L_36 = (&V_7)->___z;
		(&V_7)->___z = ((-L_36));
	}

IL_0137:
	{
		float L_37 = (&V_7)->___x;
		if ((!(((float)L_37) < ((float)(0.00100000005f)))))
		{
			goto IL_0175;
		}
	}
	{
		float L_38 = (&V_7)->___y;
		if ((!(((float)L_38) < ((float)(0.00100000005f)))))
		{
			goto IL_0175;
		}
	}
	{
		float L_39 = (&V_7)->___z;
		if ((!(((float)L_39) < ((float)(0.00100000005f)))))
		{
			goto IL_0175;
		}
	}
	{
		__this->___hasAdditionalStartingP = (bool)0;
		int32_t L_40 = V_3;
		int32_t L_41 = V_2;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_42 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(L_40, 2)), L_41)));
		V_0 = L_42;
		goto IL_01b8;
	}

IL_0175:
	{
		__this->___hasAdditionalStartingP = (bool)1;
		int32_t L_43 = V_3;
		int32_t L_44 = V_2;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_45 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(L_43, 3)), L_44)));
		V_0 = L_45;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_46 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_46);
		bool L_47;
		L_47 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_46, NULL);
		if (!L_47)
		{
			goto IL_01a8;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_48 = V_0;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_49 = V_0;
		NullCheck(L_49);
		NullCheck(L_48);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_50 = V_6;
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_48)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_49)->max_length)), 2))))) = L_50;
		goto IL_01b8;
	}

IL_01a8:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_51 = V_0;
		NullCheck(L_51);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_52 = V_6;
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_51)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))) = L_52;
		V_1 = 2;
	}

IL_01b8:
	{
		V_8 = 0;
		goto IL_01e4;
	}

IL_01bd:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_53 = V_0;
		int32_t L_54 = V_8;
		int32_t L_55 = V_1;
		NullCheck(L_53);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_56 = __this->___points;
		int32_t L_57 = V_8;
		NullCheck(L_56);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_58 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_56)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_57))));
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_53)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(L_54, L_55))))) = L_58;
		int32_t L_59 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add(L_59, 1));
	}

IL_01e4:
	{
		int32_t L_60 = V_8;
		int32_t L_61 = V_3;
		if ((((int32_t)L_60) < ((int32_t)L_61)))
		{
			goto IL_01bd;
		}
	}

IL_01e9:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_62 = V_0;
		NullCheck(L_62);
		V_3 = ((int32_t)(((RuntimeArray*)L_62)->max_length));
		bool L_63 = __this->___isClosedPath;
		if (!L_63)
		{
			goto IL_020f;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_64 = V_0;
		int32_t L_65 = V_3;
		NullCheck(L_64);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_66 = V_0;
		NullCheck(L_66);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_67 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_66)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))));
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_64)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_65, 2))))) = L_67;
	}

IL_020f:
	{
		bool L_68 = __this->___isClosedPath;
		if (!L_68)
		{
			goto IL_024d;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_69 = V_0;
		NullCheck(L_69);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_70 = V_0;
		int32_t L_71 = V_3;
		NullCheck(L_70);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_72 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_70)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_71, 3))))));
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_69)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))) = L_72;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_73 = V_0;
		int32_t L_74 = V_3;
		NullCheck(L_73);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_75 = V_0;
		NullCheck(L_75);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_76 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_75)->GetAddressAt(static_cast<il2cpp_array_size_t>(2))));
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_73)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_74, 1))))) = L_76;
		goto IL_02a3;
	}

IL_024d:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_77 = V_0;
		NullCheck(L_77);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_78 = V_0;
		NullCheck(L_78);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_79 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_78)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))));
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_77)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))) = L_79;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_80 = V_0;
		int32_t L_81 = V_3;
		NullCheck(L_80);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_82 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_80)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_81, 2))))));
		V_9 = L_82;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_83 = V_9;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_84 = V_0;
		int32_t L_85 = V_3;
		NullCheck(L_84);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_86 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_84)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_85, 3))))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_87;
		L_87 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_83, L_86, NULL);
		V_10 = L_87;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_88 = V_0;
		int32_t L_89 = V_3;
		NullCheck(L_88);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_90 = V_9;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_91 = V_10;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_92;
		L_92 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_90, L_91, NULL);
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_88)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_89, 1))))) = L_92;
	}

IL_02a3:
	{
		int32_t L_93 = __this->___lockPositionAxis;
		if (!L_93)
		{
			goto IL_034a;
		}
	}
	{
		int32_t L_94 = __this->___lockPositionAxis;
		V_11 = (bool)((((int32_t)((int32_t)((int32_t)L_94&2))) == ((int32_t)2))? 1 : 0);
		int32_t L_95 = __this->___lockPositionAxis;
		V_12 = (bool)((((int32_t)((int32_t)((int32_t)L_95&4))) == ((int32_t)4))? 1 : 0);
		int32_t L_96 = __this->___lockPositionAxis;
		V_13 = (bool)((((int32_t)((int32_t)((int32_t)L_96&8))) == ((int32_t)8))? 1 : 0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_97 = __this->___typedStartVal;
		V_14 = L_97;
		V_15 = 0;
		goto IL_0345;
	}

IL_02e2:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_98 = V_0;
		int32_t L_99 = V_15;
		NullCheck(L_98);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_100 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_98)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_99))));
		V_16 = L_100;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_101 = V_0;
		int32_t L_102 = V_15;
		NullCheck(L_101);
		bool L_103 = V_11;
		if (L_103)
		{
			G_B36_0 = ((L_101)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_102)));
			goto IL_0306;
		}
		G_B35_0 = ((L_101)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_102)));
	}
	{
		float L_104 = (&V_16)->___x;
		G_B37_0 = L_104;
		G_B37_1 = G_B35_0;
		goto IL_030d;
	}

IL_0306:
	{
		float L_105 = (&V_14)->___x;
		G_B37_0 = L_105;
		G_B37_1 = G_B36_0;
	}

IL_030d:
	{
		bool L_106 = V_12;
		if (L_106)
		{
			G_B39_0 = G_B37_0;
			G_B39_1 = G_B37_1;
			goto IL_031a;
		}
		G_B38_0 = G_B37_0;
		G_B38_1 = G_B37_1;
	}
	{
		float L_107 = (&V_16)->___y;
		G_B40_0 = L_107;
		G_B40_1 = G_B38_0;
		G_B40_2 = G_B38_1;
		goto IL_0321;
	}

IL_031a:
	{
		float L_108 = (&V_14)->___y;
		G_B40_0 = L_108;
		G_B40_1 = G_B39_0;
		G_B40_2 = G_B39_1;
	}

IL_0321:
	{
		bool L_109 = V_13;
		if (L_109)
		{
			G_B42_0 = G_B40_0;
			G_B42_1 = G_B40_1;
			G_B42_2 = G_B40_2;
			goto IL_032e;
		}
		G_B41_0 = G_B40_0;
		G_B41_1 = G_B40_1;
		G_B41_2 = G_B40_2;
	}
	{
		float L_110 = (&V_16)->___z;
		G_B43_0 = L_110;
		G_B43_1 = G_B41_0;
		G_B43_2 = G_B41_1;
		G_B43_3 = G_B41_2;
		goto IL_0335;
	}

IL_032e:
	{
		float L_111 = (&V_14)->___z;
		G_B43_0 = L_111;
		G_B43_1 = G_B42_0;
		G_B43_2 = G_B42_1;
		G_B43_3 = G_B42_2;
	}

IL_0335:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_112;
		memset((&L_112), 0, sizeof(L_112));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_112), G_B43_2, G_B43_1, G_B43_0, NULL);
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)G_B43_3 = L_112;
		int32_t L_113 = V_15;
		V_15 = ((int32_t)il2cpp_codegen_add(L_113, 1));
	}

IL_0345:
	{
		int32_t L_114 = V_15;
		int32_t L_115 = V_3;
		if ((((int32_t)L_114) < ((int32_t)L_115)))
		{
			goto IL_02e2;
		}
	}

IL_034a:
	{
		int32_t L_116;
		L_116 = PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186_inline(__this, NULL);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_117 = V_0;
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_118 = (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF*)il2cpp_codegen_object_new(Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF_il2cpp_TypeInfo_var);
		Path__ctor_mB08F108F59563B544D546B8A9EB2105FD46D4588(L_118, L_116, L_117, NULL);
		__this->___path = L_118;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___path), (void*)L_118);
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_119 = __this->___path;
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_120 = __this->___path;
		NullCheck(L_120);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_121 = L_120->___path;
		NullCheck(L_121);
		NullCheck(L_119);
		Path_StoreTimeToLenTables_mF3AFBB4D067AB81A9B6EE85D91D340361D198EC9(L_119, ((int32_t)il2cpp_codegen_multiply(((int32_t)(((RuntimeArray*)L_121)->max_length)), ((int32_t)16))), NULL);
		bool L_122 = __this->___isClosedPath;
		if (L_122)
		{
			goto IL_03a4;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_123 = V_0;
		int32_t L_124 = V_3;
		NullCheck(L_123);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_125 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_123)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_124, 2))))));
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_126 = V_0;
		NullCheck(L_126);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_127 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_126)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_128;
		L_128 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_125, L_127, NULL);
		__this->___diffChangeVal = L_128;
	}

IL_03a4:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_SetIncremental_m4BDBAA71BDBA0EE9727AB9D0AADB8CCD2E85A828 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, int32_t ___0_p_diffIncr, const RuntimeMethod* method) 
{
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* V_0 = NULL;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	{
		bool L_0 = __this->___isClosedPath;
		if (!L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_1 = __this->___path;
		NullCheck(L_1);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_2 = L_1->___path;
		V_0 = L_2;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_3 = V_0;
		NullCheck(L_3);
		V_1 = ((int32_t)(((RuntimeArray*)L_3)->max_length));
		V_2 = 0;
		goto IL_0045;
	}

IL_001d:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_4 = V_0;
		int32_t L_5 = V_2;
		NullCheck(L_4);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_6 = ((L_4)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_5)));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)L_6);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = __this->___diffChangeVal;
		int32_t L_9 = ___0_p_diffIncr;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_8, ((float)L_9), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_7, L_10, NULL);
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)L_6 = L_11;
		int32_t L_12 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_12, 1));
	}

IL_0045:
	{
		int32_t L_13 = V_2;
		int32_t L_14 = V_1;
		if ((((int32_t)L_13) < ((int32_t)L_14)))
		{
			goto IL_001d;
		}
	}
	{
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_15 = __this->___path;
		NullCheck(L_15);
		L_15->___changed = (bool)1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_DoUpdate_m48865BA96D2EEC21A396216C47D336A71EBD7AC4 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, float ___0_p_totElapsed, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* V_2 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_3;
	memset((&V_3), 0, sizeof(V_3));
	float V_4 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_5;
	memset((&V_5), 0, sizeof(V_5));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_6;
	memset((&V_6), 0, sizeof(V_6));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_7;
	memset((&V_7), 0, sizeof(V_7));
	int32_t V_8 = 0;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* G_B6_0 = NULL;
	float G_B19_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 G_B31_0;
	memset((&G_B31_0), 0, sizeof(G_B31_0));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 G_B41_0;
	memset((&G_B41_0), 0, sizeof(G_B41_0));
	{
		EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___ease;
		float L_1 = ___0_p_totElapsed;
		float L_2 = __this->___startPerc;
		float L_3 = __this->___changePerc;
		float L_4 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____duration;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_5 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_5);
		float L_6;
		L_6 = Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612_inline(L_5, NULL);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_7 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_7);
		float L_8;
		L_8 = Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA_inline(L_7, NULL);
		NullCheck(L_0);
		float L_9;
		L_9 = EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_inline(L_0, L_1, L_2, L_3, L_4, L_6, L_8, NULL);
		__this->___pathPerc = L_9;
		float L_10 = __this->___pathPerc;
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_11 = __this->___path;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = PlugVector3Path_GetConstPointOnPath_m00566EF01E12762EB32F6317A830E61461A670E4(__this, L_10, (bool)1, L_11, (&V_0), NULL);
		V_1 = L_12;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14 = L_13;
		RuntimeObject* L_15 = Box(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var, &L_14);
		VirtualActionInvoker1< RuntimeObject* >::Invoke(16, __this, L_15);
		int32_t L_16 = __this->___orientType;
		if (!L_16)
		{
			goto IL_02c1;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_17 = __this->___orientTrans;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_18;
		L_18 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_17, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_18)
		{
			goto IL_02c1;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_19 = __this->___orientTrans;
		NullCheck(L_19);
		bool L_20;
		L_20 = VirtualFuncInvoker1< bool, RuntimeObject* >::Invoke(0, L_19, NULL);
		if (L_20)
		{
			goto IL_02c1;
		}
	}
	{
		bool L_21 = __this->___usesLocalPosition;
		if (L_21)
		{
			goto IL_0094;
		}
	}
	{
		G_B6_0 = ((Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1*)(NULL));
		goto IL_009f;
	}

IL_0094:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_22 = __this->___orientTrans;
		NullCheck(L_22);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_23;
		L_23 = Transform_get_parent_m65354E28A4C94EC00EBCF03532F7B0718380791E(L_22, NULL);
		G_B6_0 = L_23;
	}

IL_009f:
	{
		V_2 = G_B6_0;
		int32_t L_24 = __this->___orientType;
		V_8 = L_24;
		int32_t L_25 = V_8;
		switch (((int32_t)il2cpp_codegen_subtract((int32_t)L_25, 1)))
		{
			case 0:
			{
				goto IL_0113;
			}
			case 1:
			{
				goto IL_00d5;
			}
			case 2:
			{
				goto IL_00be;
			}
		}
	}
	{
		return;
	}

IL_00be:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_26 = __this->___orientTrans;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_27 = __this->___lookPos;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_28;
		L_28 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		NullCheck(L_26);
		Transform_LookAt_mBD38EDB5E915C5DA6C5A79D191DEE2C826A9FC2C(L_26, L_27, L_28, NULL);
		return;
	}

IL_00d5:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_29 = __this->___orientTrans;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_30;
		L_30 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_29, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_30)
		{
			goto IL_02c1;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_31 = __this->___orientTrans;
		NullCheck(L_31);
		bool L_32;
		L_32 = VirtualFuncInvoker1< bool, RuntimeObject* >::Invoke(0, L_31, NULL);
		if (L_32)
		{
			goto IL_02c1;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_33 = __this->___orientTrans;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_34 = __this->___lookTrans;
		NullCheck(L_34);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_35;
		L_35 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_34, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_36;
		L_36 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		NullCheck(L_33);
		Transform_LookAt_mBD38EDB5E915C5DA6C5A79D191DEE2C826A9FC2C(L_33, L_35, L_36, NULL);
		return;
	}

IL_0113:
	{
		int32_t L_37;
		L_37 = PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186_inline(__this, NULL);
		if (L_37)
		{
			goto IL_0164;
		}
	}
	{
		float L_38 = __this->___lookAheadVal;
		if ((!(((float)L_38) <= ((float)(9.99999975E-05f)))))
		{
			goto IL_0164;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_39 = V_1;
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_40 = __this->___path;
		NullCheck(L_40);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_41 = L_40->___path;
		int32_t L_42 = V_0;
		NullCheck(L_41);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_41)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_42))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_44;
		L_44 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_39, L_43, NULL);
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_45 = __this->___path;
		NullCheck(L_45);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_46 = L_45->___path;
		int32_t L_47 = V_0;
		NullCheck(L_46);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_48 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_46)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_47, 1))))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_49;
		L_49 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_44, L_48, NULL);
		V_3 = L_49;
		goto IL_01a3;
	}

IL_0164:
	{
		float L_50 = __this->___pathPerc;
		float L_51 = __this->___lookAheadVal;
		V_4 = ((float)il2cpp_codegen_add(L_50, L_51));
		float L_52 = V_4;
		if ((!(((float)L_52) > ((float)(1.0f)))))
		{
			goto IL_0195;
		}
	}
	{
		bool L_53 = __this->___isClosedPath;
		if (L_53)
		{
			goto IL_018b;
		}
	}
	{
		G_B19_0 = (1.00000095f);
		goto IL_0193;
	}

IL_018b:
	{
		float L_54 = V_4;
		G_B19_0 = ((float)il2cpp_codegen_subtract(L_54, (1.0f)));
	}

IL_0193:
	{
		V_4 = G_B19_0;
	}

IL_0195:
	{
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_55 = __this->___path;
		float L_56 = V_4;
		NullCheck(L_55);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_57;
		L_57 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(L_55, L_56, NULL);
		V_3 = L_57;
	}

IL_01a3:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_58 = __this->___orientTrans;
		NullCheck(L_58);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_59;
		L_59 = Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2(L_58, NULL);
		V_5 = L_59;
		bool L_60 = __this->___usesLocalPosition;
		if (!L_60)
		{
			goto IL_01c9;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_61 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_62;
		L_62 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_61, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_62)
		{
			goto IL_01c9;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_63 = V_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_64 = V_3;
		NullCheck(L_63);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_65;
		L_65 = Transform_TransformPoint_m05BFF013DB830D7BFE44A007703694AE1062EE44(L_63, L_64, NULL);
		V_3 = L_65;
	}

IL_01c9:
	{
		int32_t L_66 = __this->___lockRotationAxis;
		if (!L_66)
		{
			goto IL_02b3;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_67 = __this->___orientTrans;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_68;
		L_68 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_67, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_68)
		{
			goto IL_02b3;
		}
	}
	{
		int32_t L_69 = __this->___lockRotationAxis;
		if ((!(((uint32_t)((int32_t)((int32_t)L_69&2))) == ((uint32_t)2))))
		{
			goto IL_0238;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_70 = __this->___orientTrans;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_71 = V_3;
		NullCheck(L_70);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_72;
		L_72 = Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D(L_70, L_71, NULL);
		V_6 = L_72;
		(&V_6)->___y = (0.0f);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_73 = __this->___orientTrans;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_74 = V_6;
		NullCheck(L_73);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_75;
		L_75 = Transform_TransformPoint_m05BFF013DB830D7BFE44A007703694AE1062EE44(L_73, L_74, NULL);
		V_3 = L_75;
		bool L_76 = __this->___usesLocalPosition;
		if (!L_76)
		{
			goto IL_0229;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_77 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_78;
		L_78 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_77, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (L_78)
		{
			goto IL_0230;
		}
	}

IL_0229:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_79;
		L_79 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		G_B31_0 = L_79;
		goto IL_0236;
	}

IL_0230:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_80 = V_2;
		NullCheck(L_80);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_81;
		L_81 = Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2(L_80, NULL);
		G_B31_0 = L_81;
	}

IL_0236:
	{
		V_5 = G_B31_0;
	}

IL_0238:
	{
		int32_t L_82 = __this->___lockRotationAxis;
		if ((!(((uint32_t)((int32_t)((int32_t)L_82&4))) == ((uint32_t)4))))
		{
			goto IL_0288;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_83 = __this->___orientTrans;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_84 = V_3;
		NullCheck(L_83);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_85;
		L_85 = Transform_InverseTransformPoint_m18CD395144D9C78F30E15A5B82B6670E792DBA5D(L_83, L_84, NULL);
		V_7 = L_85;
		float L_86 = (&V_7)->___z;
		if ((!(((float)L_86) < ((float)(0.0f)))))
		{
			goto IL_026e;
		}
	}
	{
		float L_87 = (&V_7)->___z;
		(&V_7)->___z = ((-L_87));
	}

IL_026e:
	{
		(&V_7)->___x = (0.0f);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_88 = __this->___orientTrans;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_89 = V_7;
		NullCheck(L_88);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_90;
		L_90 = Transform_TransformPoint_m05BFF013DB830D7BFE44A007703694AE1062EE44(L_88, L_89, NULL);
		V_3 = L_90;
	}

IL_0288:
	{
		int32_t L_91 = __this->___lockRotationAxis;
		if ((!(((uint32_t)((int32_t)((int32_t)L_91&8))) == ((uint32_t)8))))
		{
			goto IL_02b3;
		}
	}
	{
		bool L_92 = __this->___usesLocalPosition;
		if (!L_92)
		{
			goto IL_02a4;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_93 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_94;
		L_94 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_93, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (L_94)
		{
			goto IL_02ab;
		}
	}

IL_02a4:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_95;
		L_95 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		G_B41_0 = L_95;
		goto IL_02b1;
	}

IL_02ab:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_96 = V_2;
		NullCheck(L_96);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_97;
		L_97 = Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2(L_96, NULL);
		G_B41_0 = L_97;
	}

IL_02b1:
	{
		V_5 = G_B41_0;
	}

IL_02b3:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_98 = __this->___orientTrans;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_99 = V_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_100 = V_5;
		NullCheck(L_98);
		Transform_LookAt_mBD38EDB5E915C5DA6C5A79D191DEE2C826A9FC2C(L_98, L_99, L_100, NULL);
	}

IL_02c1:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_Rewind_m1F677A68251CCFA24A43639E764E1BBC763B003C (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isPartialPath;
		if (!L_0)
		{
			goto IL_0014;
		}
	}
	{
		VirtualActionInvoker1< float >::Invoke(10, __this, (0.0f));
		return;
	}

IL_0014:
	{
		ABSTweenPlugin_Rewind_m738D072B63A84CA9B808B389B7DF22F35A3E4FBC(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path_Complete_m23E64294CE7F24812B449861F669C82231288B4C (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isPartialPath;
		if (!L_0)
		{
			goto IL_0015;
		}
	}
	{
		float L_1 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____duration;
		VirtualActionInvoker1< float >::Invoke(10, __this, L_1);
		return;
	}

IL_0015:
	{
		ABSTweenPlugin_Complete_m25B5AD6CE114E6224C2BC80D575E5148753855DB(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 PlugVector3Path_GetConstPointOnPath_m00566EF01E12762EB32F6317A830E61461A670E4 (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, float ___0_t, bool ___1_p_updatePathPerc, Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* ___2_p_path, int32_t* ___3_out_waypointIndex, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___1_p_updatePathPerc;
		if (!L_0)
		{
			goto IL_0013;
		}
	}
	{
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_1 = ___2_p_path;
		float L_2 = ___0_t;
		float* L_3 = (float*)(&__this->___pathPerc);
		int32_t* L_4 = ___3_out_waypointIndex;
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5;
		L_5 = Path_GetConstPoint_mC350B3F2078D6AB8F49B9C5B063BDD6C79B0654C(L_1, L_2, L_3, L_4, NULL);
		return L_5;
	}

IL_0013:
	{
		int32_t* L_6 = ___3_out_waypointIndex;
		*((int32_t*)L_6) = (int32_t)(-1);
		Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* L_7 = __this->___path;
		float L_8 = ___0_t;
		NullCheck(L_7);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		L_9 = Path_GetConstPoint_m1DADD874A6EC9E06D13C398963002B81F9017653(L_7, L_8, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugVector3Path__cctor_m40152CCEDE327C8DD3FD458F8A06426653DA0C35 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_0 = NULL;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_1 = NULL;
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_0 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		V_0 = L_0;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_1 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_3);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_3);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = V_0;
		((PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_il2cpp_TypeInfo_var))->___validPropTypes = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_il2cpp_TypeInfo_var))->___validPropTypes), (void*)L_4);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_5 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		V_1 = L_5;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_6 = V_1;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_7 = { reinterpret_cast<intptr_t> (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_0_0_0_var) };
		Type_t* L_8;
		L_8 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_7, NULL);
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_8);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_8);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_9 = V_1;
		((PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_il2cpp_TypeInfo_var))->___validValueTypes = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8_il2cpp_TypeInfo_var))->___validValueTypes), (void*)L_9);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OverwriteManager__ctor_mEFBC7A321BD74D1CD40B2F02991D1C41DF99FE2C (OverwriteManager_t25D8819D33516851D7144DBEE90D7FF232BAE825* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m39186FF5CA6EEBF0401FCC8D454A147188082B45_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_0 = (List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F*)il2cpp_codegen_object_new(List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F_il2cpp_TypeInfo_var);
		List_1__ctor_m39186FF5CA6EEBF0401FCC8D454A147188082B45(L_0, List_1__ctor_m39186FF5CA6EEBF0401FCC8D454A147188082B45_RuntimeMethod_var);
		__this->___runningTweens = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___runningTweens), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OverwriteManager_AddTween_mB998E62A5D1E0C495C718F07B805E4E5B51D01E3 (OverwriteManager_t25D8819D33516851D7144DBEE90D7FF232BAE825* __this, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___0_p_tween, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m18CB12DF523FE98B674A0D93FA002E47704F555E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_mB0AE72F0CAE49940457AFDC332ED7869B9EADA8E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7BC2733BAEC60A24A610EE1518219446E759790F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF422850993212057809CBD984B2F3DAEC17A02ED);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* V_0 = NULL;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* V_4 = NULL;
	List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* V_5 = NULL;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* V_8 = NULL;
	int32_t V_9 = 0;
	ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* V_10 = NULL;
	String_t* V_11 = NULL;
	String_t* V_12 = NULL;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_13 = NULL;
	{
		bool L_0 = __this->___enabled;
		if (!L_0)
		{
			goto IL_026b;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_1 = ___0_p_tween;
		NullCheck(L_1);
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_2 = L_1->___plugins;
		V_0 = L_2;
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_3 = __this->___runningTweens;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_inline(L_3, List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_RuntimeMethod_var);
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_4, 1));
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_5 = V_0;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_inline(L_5, List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_RuntimeMethod_var);
		V_2 = L_6;
		int32_t L_7 = V_1;
		V_3 = L_7;
		goto IL_0264;
	}

IL_002e:
	{
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_8 = __this->___runningTweens;
		int32_t L_9 = V_3;
		NullCheck(L_8);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_10;
		L_10 = List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411(L_8, L_9, List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411_RuntimeMethod_var);
		V_4 = L_10;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_11 = V_4;
		NullCheck(L_11);
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_12 = L_11->___plugins;
		V_5 = L_12;
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_13 = V_5;
		NullCheck(L_13);
		int32_t L_14;
		L_14 = List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_inline(L_13, List_1_get_Count_mCDD99745CD3A2987A64B2EA65B81818761B6AD32_RuntimeMethod_var);
		V_6 = L_14;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_15 = V_4;
		NullCheck(L_15);
		RuntimeObject* L_16;
		L_16 = Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline(L_15, NULL);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_17 = ___0_p_tween;
		NullCheck(L_17);
		RuntimeObject* L_18;
		L_18 = Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline(L_17, NULL);
		if ((!(((RuntimeObject*)(RuntimeObject*)L_16) == ((RuntimeObject*)(RuntimeObject*)L_18))))
		{
			goto IL_0260;
		}
	}
	{
		V_7 = 0;
		goto IL_0258;
	}

IL_0068:
	{
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_19 = V_0;
		int32_t L_20 = V_7;
		NullCheck(L_19);
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_21;
		L_21 = List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F(L_19, L_20, List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F_RuntimeMethod_var);
		V_8 = L_21;
		int32_t L_22 = V_6;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_22, 1));
		goto IL_024a;
	}

IL_007d:
	{
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_23 = V_5;
		int32_t L_24 = V_9;
		NullCheck(L_23);
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_25;
		L_25 = List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F(L_23, L_24, List_1_get_Item_mA2060A9D0EB3616B4076B851CCFFE8874BD1708F_RuntimeMethod_var);
		V_10 = L_25;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_26 = V_10;
		NullCheck(L_26);
		String_t* L_27;
		L_27 = ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB_inline(L_26, NULL);
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_28 = V_8;
		NullCheck(L_28);
		String_t* L_29;
		L_29 = ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB_inline(L_28, NULL);
		bool L_30;
		L_30 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_27, L_29, NULL);
		if (!L_30)
		{
			goto IL_0244;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_31 = V_8;
		NullCheck(L_31);
		int32_t L_32;
		L_32 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_31);
		if ((((int32_t)L_32) == ((int32_t)(-1))))
		{
			goto IL_00c7;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_33 = V_10;
		NullCheck(L_33);
		int32_t L_34;
		L_34 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_33);
		if ((((int32_t)L_34) == ((int32_t)(-1))))
		{
			goto IL_00c7;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_35 = V_10;
		NullCheck(L_35);
		int32_t L_36;
		L_36 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_35);
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_37 = V_8;
		NullCheck(L_37);
		int32_t L_38;
		L_38 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_37);
		if ((!(((uint32_t)L_36) == ((uint32_t)L_38))))
		{
			goto IL_0244;
		}
	}

IL_00c7:
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_39 = V_4;
		NullCheck(L_39);
		bool L_40;
		L_40 = ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F(L_39, NULL);
		if (!L_40)
		{
			goto IL_00ea;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_41 = ___0_p_tween;
		NullCheck(L_41);
		bool L_42;
		L_42 = ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F(L_41, NULL);
		if (!L_42)
		{
			goto IL_00ea;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_43 = V_4;
		NullCheck(L_43);
		Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* L_44 = ((ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737*)L_43)->___contSequence;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_45 = ___0_p_tween;
		NullCheck(L_45);
		Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* L_46 = ((ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737*)L_45)->___contSequence;
		if ((((RuntimeObject*)(Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279*)L_44) == ((RuntimeObject*)(Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279*)L_46)))
		{
			goto IL_0260;
		}
	}

IL_00ea:
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_47 = V_4;
		NullCheck(L_47);
		bool L_48 = ((ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737*)L_47)->____isPaused;
		if (L_48)
		{
			goto IL_0244;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_49 = V_4;
		NullCheck(L_49);
		bool L_50;
		L_50 = ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F(L_49, NULL);
		if (!L_50)
		{
			goto IL_010b;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_51 = V_4;
		NullCheck(L_51);
		bool L_52;
		L_52 = ABSTweenComponent_get_isComplete_m709E527B954A24C4FC9BFA6AAEAF82332441991F_inline(L_51, NULL);
		if (L_52)
		{
			goto IL_0244;
		}
	}

IL_010b:
	{
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_53 = V_5;
		int32_t L_54 = V_9;
		NullCheck(L_53);
		List_1_RemoveAt_mB0AE72F0CAE49940457AFDC332ED7869B9EADA8E(L_53, L_54, List_1_RemoveAt_mB0AE72F0CAE49940457AFDC332ED7869B9EADA8E_RuntimeMethod_var);
		int32_t L_55 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_55, 1));
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		bool L_56 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___isEditor;
		if (!L_56)
		{
			goto IL_01d2;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		int32_t L_57 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___warningLevel;
		if ((!(((uint32_t)L_57) == ((uint32_t)2))))
		{
			goto IL_01d2;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_58 = V_8;
		NullCheck(L_58);
		Type_t* L_59;
		L_59 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_58, NULL);
		NullCheck(L_59);
		String_t* L_60;
		L_60 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_59);
		V_11 = L_60;
		String_t* L_61 = V_11;
		String_t* L_62 = V_11;
		NullCheck(L_62);
		int32_t L_63;
		L_63 = String_LastIndexOf_m8923DBD89F2B3E5A34190B038B48F402E0C17E40(L_62, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D, NULL);
		NullCheck(L_61);
		String_t* L_64;
		L_64 = String_Substring_m6BA4A3FA3800FE92662D0847CC8E1EEF940DF472(L_61, ((int32_t)il2cpp_codegen_add(L_63, 1)), NULL);
		V_11 = L_64;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_65 = V_10;
		NullCheck(L_65);
		Type_t* L_66;
		L_66 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_65, NULL);
		NullCheck(L_66);
		String_t* L_67;
		L_67 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_66);
		V_12 = L_67;
		String_t* L_68 = V_12;
		String_t* L_69 = V_12;
		NullCheck(L_69);
		int32_t L_70;
		L_70 = String_LastIndexOf_m8923DBD89F2B3E5A34190B038B48F402E0C17E40(L_69, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D, NULL);
		NullCheck(L_68);
		String_t* L_71;
		L_71 = String_Substring_m6BA4A3FA3800FE92662D0847CC8E1EEF940DF472(L_68, ((int32_t)il2cpp_codegen_add(L_70, 1)), NULL);
		V_12 = L_71;
		bool L_72 = __this->___logWarnings;
		if (!L_72)
		{
			goto IL_01d2;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_73 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)7);
		V_13 = L_73;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_74 = V_13;
		String_t* L_75 = V_11;
		NullCheck(L_74);
		ArrayElementTypeCheck (L_74, L_75);
		(L_74)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_75);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_76 = V_13;
		NullCheck(L_76);
		ArrayElementTypeCheck (L_76, _stringLiteral7BC2733BAEC60A24A610EE1518219446E759790F);
		(L_76)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)_stringLiteral7BC2733BAEC60A24A610EE1518219446E759790F);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_77 = V_13;
		String_t* L_78 = V_12;
		NullCheck(L_77);
		ArrayElementTypeCheck (L_77, L_78);
		(L_77)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_78);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_79 = V_13;
		NullCheck(L_79);
		ArrayElementTypeCheck (L_79, _stringLiteralF422850993212057809CBD984B2F3DAEC17A02ED);
		(L_79)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)_stringLiteralF422850993212057809CBD984B2F3DAEC17A02ED);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_80 = V_13;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_81 = V_4;
		NullCheck(L_81);
		RuntimeObject* L_82;
		L_82 = Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline(L_81, NULL);
		NullCheck(L_80);
		ArrayElementTypeCheck (L_80, L_82);
		(L_80)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)L_82);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_83 = V_13;
		NullCheck(L_83);
		ArrayElementTypeCheck (L_83, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		(L_83)->SetAt(static_cast<il2cpp_array_size_t>(5), (RuntimeObject*)_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_84 = V_13;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_85 = V_10;
		NullCheck(L_85);
		String_t* L_86;
		L_86 = ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB_inline(L_85, NULL);
		NullCheck(L_84);
		ArrayElementTypeCheck (L_84, L_86);
		(L_84)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)L_86);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_87 = V_13;
		String_t* L_88;
		L_88 = String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36(L_87, NULL);
		TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B(L_88, NULL);
	}

IL_01d2:
	{
		int32_t L_89 = V_6;
		if (L_89)
		{
			goto IL_0201;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_90 = V_4;
		NullCheck(L_90);
		bool L_91;
		L_91 = ABSTweenComponent_get_isSequenced_mE341F3D7751CC291E9E5A64FB576CDBE2AC4BA5F(L_90, NULL);
		if (!L_91)
		{
			goto IL_01ed;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_92 = V_4;
		NullCheck(L_92);
		Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* L_93 = ((ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737*)L_92)->___contSequence;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_94 = V_4;
		NullCheck(L_93);
		Sequence_Remove_mC0A8D195AF01D4D8514D7515286352256C677E31(L_93, L_94, NULL);
	}

IL_01ed:
	{
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_95 = __this->___runningTweens;
		int32_t L_96 = V_3;
		NullCheck(L_95);
		List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44(L_95, L_96, List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44_RuntimeMethod_var);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_97 = V_4;
		NullCheck(L_97);
		VirtualActionInvoker1< bool >::Invoke(21, L_97, (bool)0);
	}

IL_0201:
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_98 = V_4;
		NullCheck(L_98);
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_99 = L_98->___onPluginOverwritten;
		if (!L_99)
		{
			goto IL_0218;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_100 = V_4;
		NullCheck(L_100);
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_101 = L_100->___onPluginOverwritten;
		NullCheck(L_101);
		TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_inline(L_101, NULL);
		goto IL_023b;
	}

IL_0218:
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_102 = V_4;
		NullCheck(L_102);
		TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* L_103 = L_102->___onPluginOverwrittenWParms;
		if (!L_103)
		{
			goto IL_023b;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_104 = V_4;
		NullCheck(L_104);
		TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* L_105 = L_104->___onPluginOverwrittenWParms;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_106 = V_4;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_107 = V_4;
		NullCheck(L_107);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_108 = L_107->___onPluginOverwrittenParms;
		TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* L_109 = (TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18*)il2cpp_codegen_object_new(TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18_il2cpp_TypeInfo_var);
		TweenEvent__ctor_m20EB08AE4E804741D72FBED05DE8925CC9C132EF(L_109, L_106, L_108, NULL);
		NullCheck(L_105);
		TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_inline(L_105, L_109, NULL);
	}

IL_023b:
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_110 = V_4;
		NullCheck(L_110);
		bool L_111;
		L_111 = ABSTweenComponent_get_destroyed_m4FE7ACE9A38BE5BED05C117B3F147838083CFC01_inline(L_110, NULL);
		if (L_111)
		{
			goto IL_0260;
		}
	}

IL_0244:
	{
		int32_t L_112 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_112, 1));
	}

IL_024a:
	{
		int32_t L_113 = V_9;
		if ((((int32_t)L_113) > ((int32_t)(-1))))
		{
			goto IL_007d;
		}
	}
	{
		int32_t L_114 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_add(L_114, 1));
	}

IL_0258:
	{
		int32_t L_115 = V_7;
		int32_t L_116 = V_2;
		if ((((int32_t)L_115) < ((int32_t)L_116)))
		{
			goto IL_0068;
		}
	}

IL_0260:
	{
		int32_t L_117 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_117, 1));
	}

IL_0264:
	{
		int32_t L_118 = V_3;
		if ((((int32_t)L_118) > ((int32_t)(-1))))
		{
			goto IL_002e;
		}
	}

IL_026b:
	{
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_119 = __this->___runningTweens;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_120 = ___0_p_tween;
		NullCheck(L_119);
		List_1_Add_m18CB12DF523FE98B674A0D93FA002E47704F555E_inline(L_119, L_120, List_1_Add_m18CB12DF523FE98B674A0D93FA002E47704F555E_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OverwriteManager_RemoveTween_m45DEDD84C3EDC7D2EF90E5DF5772201EB4C22F0F (OverwriteManager_t25D8819D33516851D7144DBEE90D7FF232BAE825* __this, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___0_p_tween, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_0 = __this->___runningTweens;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_inline(L_0, List_1_get_Count_m03D809BA2D51AA36B88B17993142D1EE099C9BD2_RuntimeMethod_var);
		V_0 = L_1;
		V_1 = 0;
		goto IL_0030;
	}

IL_0010:
	{
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_2 = __this->___runningTweens;
		int32_t L_3 = V_1;
		NullCheck(L_2);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_4;
		L_4 = List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411(L_2, L_3, List_1_get_Item_m69F009D7AFF7671AD5FE03A47E16A8C822270411_RuntimeMethod_var);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_5 = ___0_p_tween;
		if ((!(((RuntimeObject*)(Tweener_t99074CD44759EE1C18B018744C9E38243A40871A*)L_4) == ((RuntimeObject*)(Tweener_t99074CD44759EE1C18B018744C9E38243A40871A*)L_5))))
		{
			goto IL_002c;
		}
	}
	{
		List_1_tEC58583E33FFBCCB6A81DD554C40BC69808A701F* L_6 = __this->___runningTweens;
		int32_t L_7 = V_1;
		NullCheck(L_6);
		List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44(L_6, L_7, List_1_RemoveAt_mF1540910232343DD1D8FE562E02D93DC14C94B44_RuntimeMethod_var);
		return;
	}

IL_002c:
	{
		int32_t L_8 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_8, 1));
	}

IL_0030:
	{
		int32_t L_9 = V_1;
		int32_t L_10 = V_0;
		if ((((int32_t)L_9) < ((int32_t)L_10)))
		{
			goto IL_0010;
		}
	}
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Quint_EaseIn_m6C3211561F2D84CDC0E99DB5F74C961EE3EFCF4E (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)(L_1/L_2));
		___0_time = L_3;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___0_time;
		float L_7 = ___0_time;
		float L_8 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_0, L_3)), L_4)), L_5)), L_6)), L_7)), L_8));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Quint_EaseOut_m89CBF1324AC66BB28842633E0AA7751E98DE3DEB (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)il2cpp_codegen_subtract(((float)(L_1/L_2)), (1.0f)));
		___0_time = L_3;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___0_time;
		float L_7 = ___0_time;
		float L_8 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_3, L_4)), L_5)), L_6)), L_7)), (1.0f))))), L_8));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Quint_EaseInOut_m279B4513D0474EC373DA9C86641607FFD6950507 (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_time;
		float L_1 = ___3_duration;
		float L_2 = ((float)(L_0/((float)il2cpp_codegen_multiply(L_1, (0.5f)))));
		___0_time = L_2;
		if ((!(((float)L_2) < ((float)(1.0f)))))
		{
			goto IL_0027;
		}
	}
	{
		float L_3 = ___2_changeValue;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___0_time;
		float L_7 = ___0_time;
		float L_8 = ___0_time;
		float L_9 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_3, (0.5f))), L_4)), L_5)), L_6)), L_7)), L_8)), L_9));
	}

IL_0027:
	{
		float L_10 = ___2_changeValue;
		float L_11 = ___0_time;
		float L_12 = ((float)il2cpp_codegen_subtract(L_11, (2.0f)));
		___0_time = L_12;
		float L_13 = ___0_time;
		float L_14 = ___0_time;
		float L_15 = ___0_time;
		float L_16 = ___0_time;
		float L_17 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_10, (0.5f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_12, L_13)), L_14)), L_15)), L_16)), (2.0f))))), L_17));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlugColor32_get_startVal_mD9C0E957F190B58BF9AF2F3874F7464E0E7261AB (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32_set_startVal_m3D8B40246FD173C803497DC4A94C6C5490FD5EEE (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_0);
		bool L_1;
		L_1 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_0, NULL);
		if (!L_1)
		{
			goto IL_003b;
		}
	}
	{
		bool L_2 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_2)
		{
			goto IL_003b;
		}
	}
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = __this->___typedEndVal;
		RuntimeObject* L_4 = ___0_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5;
		L_5 = Color_op_Addition_mA7A51CACA49ED8D23D3D9CA3A0092D32F657E053_inline(L_3, ((*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)((Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)UnBox(L_4, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var)))), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = L_5;
		V_0 = L_6;
		__this->___typedStartVal = L_6;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7 = V_0;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = L_7;
		RuntimeObject* L_9 = Box(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var, &L_8);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_9);
		return;
	}

IL_003b:
	{
		RuntimeObject* L_10 = ___0_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_11;
		L_11 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(((*(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)UnBox(L_10, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var)))), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = L_11;
		V_1 = L_12;
		__this->___typedStartVal = L_12;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_13 = V_1;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_14 = L_13;
		RuntimeObject* L_15 = Box(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var, &L_14);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_15;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_15);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32_set_endVal_mEFFB93CBFEC846BF6102A9A4C377146950333311 (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		RuntimeObject* L_0 = ___0_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline(((*(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)UnBox(L_0, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var)))), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = L_1;
		V_0 = L_2;
		__this->___typedEndVal = L_2;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = V_0;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = L_3;
		RuntimeObject* L_5 = Box(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var, &L_4);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal), (void*)L_5);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32__ctor_mA746143BEC963C76BB01E625BE07D6E7B6D83E4E (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_0 = ___0_p_endVal;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_1 = L_0;
		RuntimeObject* L_2 = Box(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var, &L_1);
		bool L_3 = ___1_p_isRelative;
		ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482(__this, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlugColor32_GetSpeedBasedDuration_m1845E1E00E9BBD94FA4AF9A02E4774C3FDD8FCC1 (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, float ___0_p_speed, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_p_speed;
		V_0 = ((float)((1.0f)/L_0));
		float L_1 = V_0;
		if ((!(((float)L_1) < ((float)(0.0f)))))
		{
			goto IL_0013;
		}
	}
	{
		float L_2 = V_0;
		V_0 = ((-L_2));
	}

IL_0013:
	{
		float L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32_SetChangeVal_m0F2E1D66423E4F3CF74DA63D0D8002E5CD7831C0 (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_0)
		{
			goto IL_002c;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_1 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_1);
		bool L_2;
		L_2 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_1, NULL);
		if (L_2)
		{
			goto IL_002c;
		}
	}
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = __this->___typedStartVal;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = __this->___typedEndVal;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5;
		L_5 = Color_op_Addition_mA7A51CACA49ED8D23D3D9CA3A0092D32F657E053_inline(L_3, L_4, NULL);
		__this->___typedEndVal = L_5;
	}

IL_002c:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = __this->___typedEndVal;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7 = __this->___typedStartVal;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8;
		L_8 = Color_op_Subtraction_mF003448D819F2A41405BB6D85F1563CDA900B07F_inline(L_6, L_7, NULL);
		__this->___diffChangeVal = L_8;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32_SetIncremental_m0D105D6FD4A79E01E0E94F9A2E3A060189AE3371 (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, int32_t ___0_p_diffIncr, const RuntimeMethod* method) 
{
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = __this->___typedStartVal;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = __this->___diffChangeVal;
		int32_t L_2 = ___0_p_diffIncr;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3;
		L_3 = Color_op_Multiply_m379B20A820266ACF82A21425B9CAE8DCD773CFBB_inline(L_1, ((float)L_2), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4;
		L_4 = Color_op_Addition_mA7A51CACA49ED8D23D3D9CA3A0092D32F657E053_inline(L_0, L_3, NULL);
		__this->___typedStartVal = L_4;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5 = __this->___typedEndVal;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = __this->___diffChangeVal;
		int32_t L_7 = ___0_p_diffIncr;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8;
		L_8 = Color_op_Multiply_m379B20A820266ACF82A21425B9CAE8DCD773CFBB_inline(L_6, ((float)L_7), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9;
		L_9 = Color_op_Addition_mA7A51CACA49ED8D23D3D9CA3A0092D32F657E053_inline(L_5, L_8, NULL);
		__this->___typedEndVal = L_9;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32_DoUpdate_mA43CD82817D34A333A0E49381E01BCC37AFB0C63 (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* __this, float ___0_p_totElapsed, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___ease;
		float L_1 = ___0_p_totElapsed;
		float L_2 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____duration;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_3 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_3);
		float L_4;
		L_4 = Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612_inline(L_3, NULL);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_5 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_5);
		float L_6;
		L_6 = Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA_inline(L_5, NULL);
		NullCheck(L_0);
		float L_7;
		L_7 = EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_inline(L_0, L_1, (0.0f), (1.0f), L_2, L_4, L_6, NULL);
		V_0 = L_7;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_8 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___typedStartVal);
		float L_9 = L_8->___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_10 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___diffChangeVal);
		float L_11 = L_10->___r;
		float L_12 = V_0;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_13 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___typedStartVal);
		float L_14 = L_13->___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_15 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___diffChangeVal);
		float L_16 = L_15->___g;
		float L_17 = V_0;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_18 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___typedStartVal);
		float L_19 = L_18->___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_20 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___diffChangeVal);
		float L_21 = L_20->___b;
		float L_22 = V_0;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_23 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___typedStartVal);
		float L_24 = L_23->___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_25 = (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(&__this->___diffChangeVal);
		float L_26 = L_25->___a;
		float L_27 = V_0;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_28;
		memset((&L_28), 0, sizeof(L_28));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_28), ((float)il2cpp_codegen_add(L_9, ((float)il2cpp_codegen_multiply(L_11, L_12)))), ((float)il2cpp_codegen_add(L_14, ((float)il2cpp_codegen_multiply(L_16, L_17)))), ((float)il2cpp_codegen_add(L_19, ((float)il2cpp_codegen_multiply(L_21, L_22)))), ((float)il2cpp_codegen_add(L_24, ((float)il2cpp_codegen_multiply(L_26, L_27)))), NULL);
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_29;
		L_29 = Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline(L_28, NULL);
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_30 = L_29;
		RuntimeObject* L_31 = Box(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var, &L_30);
		VirtualActionInvoker1< RuntimeObject* >::Invoke(16, __this, L_31);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugColor32__cctor_mA54543A3A75DEBA7711695153EE0560717E5FB15 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_0 = NULL;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_1 = NULL;
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_0 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		V_0 = L_0;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_1 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_3);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_3);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = V_0;
		((PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_StaticFields*)il2cpp_codegen_static_fields_for(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var))->___validPropTypes = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_StaticFields*)il2cpp_codegen_static_fields_for(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var))->___validPropTypes), (void*)L_4);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_5 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		V_1 = L_5;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_6 = V_1;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_7 = { reinterpret_cast<intptr_t> (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var) };
		Type_t* L_8;
		L_8 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_7, NULL);
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_8);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_8);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_9 = V_1;
		((PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_StaticFields*)il2cpp_codegen_static_fields_for(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var))->___validValueTypes = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_StaticFields*)il2cpp_codegen_static_fields_for(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var))->___validValueTypes), (void*)L_9);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B (String_t* ___0_p_message, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_p_message;
		TweenWarning_Log_mDD27E543707A5EFEDCBE8A709413D3156D9A938F(L_0, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenWarning_Log_mDD27E543707A5EFEDCBE8A709413D3156D9A938F (String_t* ___0_p_message, bool ___1_p_verbose, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0082802CB33D711591EB7173923DE71C91BF6CBE);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		int32_t L_0 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___warningLevel;
		if (L_0)
		{
			goto IL_0008;
		}
	}
	{
		return;
	}

IL_0008:
	{
		bool L_1 = ___1_p_verbose;
		if (!L_1)
		{
			goto IL_0013;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		int32_t L_2 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___warningLevel;
		if ((!(((uint32_t)L_2) == ((uint32_t)2))))
		{
			goto IL_0023;
		}
	}

IL_0013:
	{
		String_t* L_3 = ___0_p_message;
		String_t* L_4;
		L_4 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral0082802CB33D711591EB7173923DE71C91BF6CBE, L_3, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(L_4, NULL);
	}

IL_0023:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Path__ctor_mB08F108F59563B544D546B8A9EB2105FD46D4588 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, int32_t ___0_p_type, Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___1_p_path, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_p_type;
		__this->___pathType = L_0;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_1 = ___1_p_path;
		NullCheck(L_1);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_2 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)(((RuntimeArray*)L_1)->max_length)));
		__this->___path = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___path), (void*)L_2);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_3 = ___1_p_path;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_4 = __this->___path;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_5 = __this->___path;
		NullCheck(L_5);
		Array_Copy_m4233828B4E6288B6D815F539AAA38575DE627900((RuntimeArray*)L_3, (RuntimeArray*)L_4, ((int32_t)(((RuntimeArray*)L_5)->max_length)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		float L_0 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Path_GetPoint_m931F8C934DA00412C36CCE7D011F45F2F4F80555(__this, L_0, (&V_0), NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetPoint_m931F8C934DA00412C36CCE7D011F45F2F4F80555 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, int32_t* ___1_out_waypointIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_7;
	memset((&V_7), 0, sizeof(V_7));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_8;
	memset((&V_8), 0, sizeof(V_8));
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	int32_t V_11 = 0;
	float V_12 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_13;
	memset((&V_13), 0, sizeof(V_13));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_14;
	memset((&V_14), 0, sizeof(V_14));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_15;
	memset((&V_15), 0, sizeof(V_15));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_16;
	memset((&V_16), 0, sizeof(V_16));
	int32_t V_17 = 0;
	{
		int32_t L_0 = __this->___pathType;
		V_17 = L_0;
		int32_t L_1 = V_17;
		if ((!(((uint32_t)L_1) == ((uint32_t)0))))
		{
			goto IL_00c8;
		}
	}
	{
		float L_2 = ___0_t;
		if ((!(((float)L_2) <= ((float)(0.0f)))))
		{
			goto IL_002d;
		}
	}
	{
		int32_t* L_3 = ___1_out_waypointIndex;
		*((int32_t*)L_3) = (int32_t)1;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_4 = __this->___path;
		NullCheck(L_4);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_4)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))));
		return L_5;
	}

IL_002d:
	{
		V_0 = 0;
		V_1 = 0;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_6 = __this->___timesTable;
		NullCheck(L_6);
		V_2 = ((int32_t)(((RuntimeArray*)L_6)->max_length));
		V_3 = 1;
		goto IL_0055;
	}

IL_003e:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_7 = __this->___timesTable;
		int32_t L_8 = V_3;
		NullCheck(L_7);
		int32_t L_9 = L_8;
		float L_10 = (L_7)->GetAt(static_cast<il2cpp_array_size_t>(L_9));
		float L_11 = ___0_t;
		if ((!(((float)L_10) >= ((float)L_11))))
		{
			goto IL_0051;
		}
	}
	{
		int32_t L_12 = V_3;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_12, 1));
		int32_t L_13 = V_3;
		V_1 = L_13;
		goto IL_0059;
	}

IL_0051:
	{
		int32_t L_14 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_14, 1));
	}

IL_0055:
	{
		int32_t L_15 = V_3;
		int32_t L_16 = V_2;
		if ((((int32_t)L_15) < ((int32_t)L_16)))
		{
			goto IL_003e;
		}
	}

IL_0059:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_17 = __this->___timesTable;
		int32_t L_18 = V_0;
		NullCheck(L_17);
		int32_t L_19 = L_18;
		float L_20 = (L_17)->GetAt(static_cast<il2cpp_array_size_t>(L_19));
		V_4 = L_20;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_21 = __this->___timesTable;
		int32_t L_22 = V_1;
		NullCheck(L_21);
		int32_t L_23 = L_22;
		float L_24 = (L_21)->GetAt(static_cast<il2cpp_array_size_t>(L_23));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_25 = __this->___timesTable;
		int32_t L_26 = V_0;
		NullCheck(L_25);
		int32_t L_27 = L_26;
		float L_28 = (L_25)->GetAt(static_cast<il2cpp_array_size_t>(L_27));
		V_5 = ((float)il2cpp_codegen_subtract(L_24, L_28));
		float L_29 = ___0_t;
		float L_30 = V_4;
		V_5 = ((float)il2cpp_codegen_subtract(L_29, L_30));
		float L_31 = __this->___pathLength;
		float L_32 = V_5;
		V_6 = ((float)il2cpp_codegen_multiply(L_31, L_32));
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_33 = __this->___path;
		int32_t L_34 = V_0;
		NullCheck(L_33);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_35 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_33)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_34))));
		V_7 = L_35;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_36 = __this->___path;
		int32_t L_37 = V_1;
		NullCheck(L_36);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_38 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_36)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_37))));
		V_8 = L_38;
		int32_t* L_39 = ___1_out_waypointIndex;
		int32_t L_40 = V_1;
		*((int32_t*)L_39) = (int32_t)L_40;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_41 = V_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_42 = V_8;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43 = V_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_44;
		L_44 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_42, L_43, NULL);
		float L_45 = V_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_46;
		L_46 = Vector3_ClampMagnitude_mF83675F19744F58E97CF24D8359A810634DC031F_inline(L_44, L_45, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_47;
		L_47 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_41, L_46, NULL);
		return L_47;
	}

IL_00c8:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_48 = __this->___path;
		NullCheck(L_48);
		V_9 = ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_48)->max_length)), 3));
		float L_49 = ___0_t;
		int32_t L_50 = V_9;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_51;
		L_51 = floor(((double)((float)il2cpp_codegen_multiply(L_49, ((float)L_50)))));
		V_10 = il2cpp_codegen_cast_double_to_int<int32_t>(L_51);
		int32_t L_52 = V_9;
		V_11 = ((int32_t)il2cpp_codegen_subtract(L_52, 1));
		int32_t L_53 = V_11;
		int32_t L_54 = V_10;
		if ((((int32_t)L_53) <= ((int32_t)L_54)))
		{
			goto IL_00f2;
		}
	}
	{
		int32_t L_55 = V_10;
		V_11 = L_55;
	}

IL_00f2:
	{
		float L_56 = ___0_t;
		int32_t L_57 = V_9;
		int32_t L_58 = V_11;
		V_12 = ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(L_56, ((float)L_57))), ((float)L_58)));
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_59 = __this->___path;
		int32_t L_60 = V_11;
		NullCheck(L_59);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_61 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_59)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_60))));
		V_13 = L_61;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_62 = __this->___path;
		int32_t L_63 = V_11;
		NullCheck(L_62);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_64 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_62)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(L_63, 1))))));
		V_14 = L_64;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_65 = __this->___path;
		int32_t L_66 = V_11;
		NullCheck(L_65);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_67 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_65)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(L_66, 2))))));
		V_15 = L_67;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_68 = __this->___path;
		int32_t L_69 = V_11;
		NullCheck(L_68);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_70 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_68)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(L_69, 3))))));
		V_16 = L_70;
		int32_t* L_71 = ___1_out_waypointIndex;
		*((int32_t*)L_71) = (int32_t)(-1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_72 = V_13;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_73;
		L_73 = Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline(L_72, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_74 = V_14;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_75;
		L_75 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((3.0f), L_74, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_76;
		L_76 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_73, L_75, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_77 = V_15;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_78;
		L_78 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((3.0f), L_77, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_79;
		L_79 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_76, L_78, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_80 = V_16;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_81;
		L_81 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_79, L_80, NULL);
		float L_82 = V_12;
		float L_83 = V_12;
		float L_84 = V_12;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_85;
		L_85 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_81, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_82, L_83)), L_84)), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_86 = V_13;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_87;
		L_87 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((2.0f), L_86, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_88 = V_14;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_89;
		L_89 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((5.0f), L_88, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_90;
		L_90 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_87, L_89, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_91 = V_15;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_92;
		L_92 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((4.0f), L_91, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_93;
		L_93 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_90, L_92, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_94 = V_16;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_95;
		L_95 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_93, L_94, NULL);
		float L_96 = V_12;
		float L_97 = V_12;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_98;
		L_98 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_95, ((float)il2cpp_codegen_multiply(L_96, L_97)), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_99;
		L_99 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_85, L_98, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_100 = V_13;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_101;
		L_101 = Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline(L_100, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_102 = V_15;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_103;
		L_103 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_101, L_102, NULL);
		float L_104 = V_12;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_105;
		L_105 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_103, L_104, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_106;
		L_106 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_99, L_105, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_107 = V_14;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_108;
		L_108 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((2.0f), L_107, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_109;
		L_109 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_106, L_108, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_110;
		L_110 = Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline((0.5f), L_109, NULL);
		return L_110;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Path_GizmoDraw_mC7CF7E5C1B7567B315E5F7BAAF2EEEC671E9A7B7 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, bool ___1_p_drawTrig, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	float V_3 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_4;
	memset((&V_4), 0, sizeof(V_4));
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_11;
	memset((&V_11), 0, sizeof(V_11));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_12;
	memset((&V_12), 0, sizeof(V_12));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_13;
	memset((&V_13), 0, sizeof(V_13));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_14;
	memset((&V_14), 0, sizeof(V_14));
	float V_15 = 0.0f;
	float V_16 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_17;
	memset((&V_17), 0, sizeof(V_17));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_18;
	memset((&V_18), 0, sizeof(V_18));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_19;
	memset((&V_19), 0, sizeof(V_19));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_20;
	memset((&V_20), 0, sizeof(V_20));
	int32_t V_21 = 0;
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (0.600000024f), (0.600000024f), (0.600000024f), (0.600000024f), NULL);
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_0, NULL);
		bool L_1 = __this->___changed;
		if (L_1)
		{
			goto IL_0037;
		}
	}
	{
		int32_t L_2 = __this->___pathType;
		if ((!(((uint32_t)L_2) == ((uint32_t)1))))
		{
			goto IL_008d;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_3 = __this->___drawPs;
		if (L_3)
		{
			goto IL_008d;
		}
	}

IL_0037:
	{
		__this->___changed = (bool)0;
		int32_t L_4 = __this->___pathType;
		if ((!(((uint32_t)L_4) == ((uint32_t)1))))
		{
			goto IL_008d;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_5 = __this->___path;
		NullCheck(L_5);
		V_1 = ((int32_t)il2cpp_codegen_multiply(((int32_t)(((RuntimeArray*)L_5)->max_length)), ((int32_t)10)));
		int32_t L_6 = V_1;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_7 = (Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C*)SZArrayNew(Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)il2cpp_codegen_add(L_6, 1)));
		__this->___drawPs = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___drawPs), (void*)L_7);
		V_2 = 0;
		goto IL_0089;
	}

IL_0065:
	{
		int32_t L_8 = V_2;
		int32_t L_9 = V_1;
		V_3 = ((float)(((float)L_8)/((float)L_9)));
		float L_10 = V_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_10, NULL);
		V_0 = L_11;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_12 = __this->___drawPs;
		int32_t L_13 = V_2;
		NullCheck(L_12);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14 = V_0;
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_12)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_13))) = L_14;
		int32_t L_15 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_15, 1));
	}

IL_0089:
	{
		int32_t L_16 = V_2;
		int32_t L_17 = V_1;
		if ((((int32_t)L_16) <= ((int32_t)L_17)))
		{
			goto IL_0065;
		}
	}

IL_008d:
	{
		int32_t L_18 = __this->___pathType;
		V_21 = L_18;
		int32_t L_19 = V_21;
		if ((!(((uint32_t)L_19) == ((uint32_t)0))))
		{
			goto IL_00ea;
		}
	}
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_20 = __this->___path;
		NullCheck(L_20);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_20)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))));
		V_4 = L_21;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_22 = __this->___path;
		NullCheck(L_22);
		V_5 = ((int32_t)(((RuntimeArray*)L_22)->max_length));
		V_6 = 1;
		goto IL_00e0;
	}

IL_00bc:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_23 = __this->___path;
		int32_t L_24 = V_6;
		NullCheck(L_23);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_23)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_24))));
		V_0 = L_25;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_26 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_27 = V_4;
		Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A(L_26, L_27, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_28 = V_0;
		V_4 = L_28;
		int32_t L_29 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_29, 1));
	}

IL_00e0:
	{
		int32_t L_30 = V_6;
		int32_t L_31 = V_5;
		if ((((int32_t)L_30) < ((int32_t)((int32_t)il2cpp_codegen_subtract(L_31, 1)))))
		{
			goto IL_00bc;
		}
	}
	{
		goto IL_0136;
	}

IL_00ea:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_32 = __this->___drawPs;
		NullCheck(L_32);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_33 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_32)->GetAddressAt(static_cast<il2cpp_array_size_t>(0))));
		V_4 = L_33;
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_34 = __this->___drawPs;
		NullCheck(L_34);
		V_7 = ((int32_t)(((RuntimeArray*)L_34)->max_length));
		V_8 = 1;
		goto IL_0130;
	}

IL_010c:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_35 = __this->___drawPs;
		int32_t L_36 = V_8;
		NullCheck(L_35);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_37 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_35)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_36))));
		V_0 = L_37;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_38 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_39 = V_4;
		Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A(L_38, L_39, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_40 = V_0;
		V_4 = L_40;
		int32_t L_41 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add(L_41, 1));
	}

IL_0130:
	{
		int32_t L_42 = V_8;
		int32_t L_43 = V_7;
		if ((((int32_t)L_42) < ((int32_t)L_43)))
		{
			goto IL_010c;
		}
	}

IL_0136:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_44;
		L_44 = Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline(NULL);
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_44, NULL);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_45 = __this->___path;
		NullCheck(L_45);
		V_9 = ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_45)->max_length)), 1));
		V_10 = 1;
		goto IL_0173;
	}

IL_0151:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_46 = __this->___path;
		int32_t L_47 = V_10;
		NullCheck(L_46);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_48 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_46)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_47))));
		Gizmos_DrawSphere_mC7B2862BBDB3141A63B83F0F1E56E30101D4F472(L_48, (0.100000001f), NULL);
		int32_t L_49 = V_10;
		V_10 = ((int32_t)il2cpp_codegen_add(L_49, 1));
	}

IL_0173:
	{
		int32_t L_50 = V_10;
		int32_t L_51 = V_9;
		if ((((int32_t)L_50) < ((int32_t)L_51)))
		{
			goto IL_0151;
		}
	}
	{
		bool L_52 = ___1_p_drawTrig;
		if (!L_52)
		{
			goto IL_02ad;
		}
	}
	{
		float L_53 = ___0_t;
		if ((((float)L_53) == ((float)(-1.0f))))
		{
			goto IL_02ad;
		}
	}
	{
		float L_54 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_55;
		L_55 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_54, NULL);
		V_11 = L_55;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_56 = V_11;
		V_13 = L_56;
		float L_57 = ___0_t;
		V_15 = ((float)il2cpp_codegen_add(L_57, (9.99999975E-05f)));
		float L_58 = V_15;
		if ((!(((float)L_58) > ((float)(1.0f)))))
		{
			goto IL_01cd;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_59 = V_11;
		V_14 = L_59;
		float L_60 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_61;
		L_61 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, ((float)il2cpp_codegen_subtract(L_60, (9.99999975E-05f))), NULL);
		V_13 = L_61;
		float L_62 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_63;
		L_63 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, ((float)il2cpp_codegen_subtract(L_62, (0.000199999995f))), NULL);
		V_12 = L_63;
		goto IL_0217;
	}

IL_01cd:
	{
		float L_64 = ___0_t;
		V_16 = ((float)il2cpp_codegen_subtract(L_64, (9.99999975E-05f)));
		float L_65 = V_16;
		if ((!(((float)L_65) < ((float)(0.0f)))))
		{
			goto IL_0203;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_66 = V_11;
		V_12 = L_66;
		float L_67 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_68;
		L_68 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, ((float)il2cpp_codegen_add(L_67, (9.99999975E-05f))), NULL);
		V_13 = L_68;
		float L_69 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_70;
		L_70 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, ((float)il2cpp_codegen_add(L_69, (0.000199999995f))), NULL);
		V_14 = L_70;
		goto IL_0217;
	}

IL_0203:
	{
		float L_71 = V_16;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_72;
		L_72 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_71, NULL);
		V_12 = L_72;
		float L_73 = V_15;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_74;
		L_74 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_73, NULL);
		V_14 = L_74;
	}

IL_0217:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_75 = V_14;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_76 = V_13;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_77;
		L_77 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_75, L_76, NULL);
		V_17 = L_77;
		Vector3_Normalize_mC749B887A4C74BA0A2E13E6377F17CCAEB0AADA8_inline((&V_17), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_78 = V_13;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_79 = V_12;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_80;
		L_80 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_78, L_79, NULL);
		V_18 = L_80;
		Vector3_Normalize_mC749B887A4C74BA0A2E13E6377F17CCAEB0AADA8_inline((&V_18), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_81 = V_17;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_82 = V_18;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_83;
		L_83 = Vector3_Cross_mF93A280558BCE756D13B6CC5DCD7DE8A43148987_inline(L_81, L_82, NULL);
		V_19 = L_83;
		Vector3_Normalize_mC749B887A4C74BA0A2E13E6377F17CCAEB0AADA8_inline((&V_19), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_84 = V_17;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_85 = V_19;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_86;
		L_86 = Vector3_Cross_mF93A280558BCE756D13B6CC5DCD7DE8A43148987_inline(L_84, L_85, NULL);
		V_20 = L_86;
		Vector3_Normalize_mC749B887A4C74BA0A2E13E6377F17CCAEB0AADA8_inline((&V_20), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_87;
		L_87 = Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline(NULL);
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_87, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_88 = V_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_89 = V_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_90 = V_17;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_91;
		L_91 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_89, L_90, NULL);
		Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A(L_88, L_91, NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_92;
		L_92 = Color_get_blue_mF04A26CE61D6DA3C0D8B1C4720901B1028C7AB87_inline(NULL);
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_92, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_93 = V_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_94 = V_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_95 = V_19;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_96;
		L_96 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_94, L_95, NULL);
		Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A(L_93, L_96, NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_97;
		L_97 = Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D_inline(NULL);
		Gizmos_set_color_m53927A2741937484180B20B55F7F20F8F60C5797(L_97, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_98 = V_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_99 = V_11;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_100 = V_20;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_101;
		L_101 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_99, L_100, NULL);
		Gizmos_DrawLine_mB139054F55D615637A39A3127AADB16043387F8A(L_98, L_101, NULL);
	}

IL_02ad:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetConstPoint_m1DADD874A6EC9E06D13C398963002B81F9017653 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->___pathType;
		V_1 = L_0;
		int32_t L_1 = V_1;
		if ((!(((uint32_t)L_1) == ((uint32_t)0))))
		{
			goto IL_0013;
		}
	}
	{
		float L_2 = ___0_t;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_2, NULL);
		return L_3;
	}

IL_0013:
	{
		float L_4 = ___0_t;
		float L_5;
		L_5 = Path_GetConstPathPercFromTimePerc_m05DF6CE5DEE89C0D965748560E2D3E701C9D01B5(__this, L_4, NULL);
		V_0 = L_5;
		float L_6 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_6, NULL);
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Path_GetConstPoint_mC350B3F2078D6AB8F49B9C5B063BDD6C79B0654C (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, float* ___1_out_pathPerc, int32_t* ___2_out_waypointIndex, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->___pathType;
		V_1 = L_0;
		int32_t L_1 = V_1;
		if ((!(((uint32_t)L_1) == ((uint32_t)0))))
		{
			goto IL_0017;
		}
	}
	{
		float* L_2 = ___1_out_pathPerc;
		float L_3 = ___0_t;
		*((float*)L_2) = (float)L_3;
		float L_4 = ___0_t;
		int32_t* L_5 = ___2_out_waypointIndex;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Path_GetPoint_m931F8C934DA00412C36CCE7D011F45F2F4F80555(__this, L_4, L_5, NULL);
		return L_6;
	}

IL_0017:
	{
		float L_7 = ___0_t;
		float L_8;
		L_8 = Path_GetConstPathPercFromTimePerc_m05DF6CE5DEE89C0D965748560E2D3E701C9D01B5(__this, L_7, NULL);
		V_0 = L_8;
		float* L_9 = ___1_out_pathPerc;
		float L_10 = V_0;
		*((float*)L_9) = (float)L_10;
		int32_t* L_11 = ___2_out_waypointIndex;
		*((int32_t*)L_11) = (int32_t)(-1);
		float L_12 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_12, NULL);
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Path_StoreTimeToLenTables_mF3AFBB4D067AB81A9B6EE85D91D340361D198EC9 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, int32_t ___0_p_subdivisions, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	float V_2 = 0.0f;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	float V_9 = 0.0f;
	int32_t V_10 = 0;
	{
		int32_t L_0 = __this->___pathType;
		V_10 = L_0;
		int32_t L_1 = V_10;
		if ((!(((uint32_t)L_1) == ((uint32_t)0))))
		{
			goto IL_00d5;
		}
	}
	{
		__this->___pathLength = (0.0f);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_2 = __this->___path;
		NullCheck(L_2);
		V_3 = ((int32_t)(((RuntimeArray*)L_2)->max_length));
		int32_t L_3 = V_3;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_4 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)L_3);
		__this->___waypointsLength = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___waypointsLength), (void*)L_4);
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_5 = __this->___path;
		NullCheck(L_5);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_5)->GetAddressAt(static_cast<il2cpp_array_size_t>(1))));
		V_0 = L_6;
		V_4 = 1;
		goto IL_008c;
	}

IL_0047:
	{
		Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* L_7 = __this->___path;
		int32_t L_8 = V_4;
		NullCheck(L_7);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((L_7)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_8))));
		V_1 = L_9;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = V_0;
		float L_12;
		L_12 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_10, L_11, NULL);
		V_5 = L_12;
		int32_t L_13 = V_4;
		int32_t L_14 = V_3;
		if ((((int32_t)L_13) >= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_14, 1)))))
		{
			goto IL_0079;
		}
	}
	{
		float L_15 = __this->___pathLength;
		float L_16 = V_5;
		__this->___pathLength = ((float)il2cpp_codegen_add(L_15, L_16));
	}

IL_0079:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_17 = V_1;
		V_0 = L_17;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_18 = __this->___waypointsLength;
		int32_t L_19 = V_4;
		float L_20 = V_5;
		NullCheck(L_18);
		(L_18)->SetAt(static_cast<il2cpp_array_size_t>(L_19), (float)L_20);
		int32_t L_21 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_21, 1));
	}

IL_008c:
	{
		int32_t L_22 = V_4;
		int32_t L_23 = V_3;
		if ((((int32_t)L_22) < ((int32_t)L_23)))
		{
			goto IL_0047;
		}
	}
	{
		int32_t L_24 = V_3;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_25 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)L_24);
		__this->___timesTable = L_25;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___timesTable), (void*)L_25);
		V_6 = (0.0f);
		V_7 = 2;
		goto IL_00cf;
	}

IL_00a9:
	{
		float L_26 = V_6;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_27 = __this->___waypointsLength;
		int32_t L_28 = V_7;
		NullCheck(L_27);
		int32_t L_29 = L_28;
		float L_30 = (L_27)->GetAt(static_cast<il2cpp_array_size_t>(L_29));
		V_6 = ((float)il2cpp_codegen_add(L_26, L_30));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_31 = __this->___timesTable;
		int32_t L_32 = V_7;
		float L_33 = V_6;
		float L_34 = __this->___pathLength;
		NullCheck(L_31);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(L_32), (float)((float)(L_33/L_34)));
		int32_t L_35 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_add(L_35, 1));
	}

IL_00cf:
	{
		int32_t L_36 = V_7;
		int32_t L_37 = V_3;
		if ((((int32_t)L_36) < ((int32_t)L_37)))
		{
			goto IL_00a9;
		}
	}
	{
		return;
	}

IL_00d5:
	{
		__this->___pathLength = (0.0f);
		int32_t L_38 = ___0_p_subdivisions;
		V_2 = ((float)((1.0f)/((float)L_38)));
		int32_t L_39 = ___0_p_subdivisions;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_40 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)L_39);
		__this->___timesTable = L_40;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___timesTable), (void*)L_40);
		int32_t L_41 = ___0_p_subdivisions;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_42 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)L_41);
		__this->___lengthsTable = L_42;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___lengthsTable), (void*)L_42);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43;
		L_43 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, (0.0f), NULL);
		V_0 = L_43;
		V_8 = 1;
		goto IL_015c;
	}

IL_0112:
	{
		float L_44 = V_2;
		int32_t L_45 = V_8;
		V_9 = ((float)il2cpp_codegen_multiply(L_44, ((float)L_45)));
		float L_46 = V_9;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_47;
		L_47 = Path_GetPoint_m39ECC109111994C94F070D36A0DC1B52B7061E74(__this, L_46, NULL);
		V_1 = L_47;
		float L_48 = __this->___pathLength;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_49 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_50 = V_0;
		float L_51;
		L_51 = Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline(L_49, L_50, NULL);
		__this->___pathLength = ((float)il2cpp_codegen_add(L_48, L_51));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_52 = V_1;
		V_0 = L_52;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_53 = __this->___timesTable;
		int32_t L_54 = V_8;
		float L_55 = V_9;
		NullCheck(L_53);
		(L_53)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_54, 1))), (float)L_55);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_56 = __this->___lengthsTable;
		int32_t L_57 = V_8;
		float L_58 = __this->___pathLength;
		NullCheck(L_56);
		(L_56)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_57, 1))), (float)L_58);
		int32_t L_59 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add(L_59, 1));
	}

IL_015c:
	{
		int32_t L_60 = V_8;
		int32_t L_61 = ___0_p_subdivisions;
		if ((((int32_t)L_60) < ((int32_t)((int32_t)il2cpp_codegen_add(L_61, 1)))))
		{
			goto IL_0112;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Path_GetConstPathPercFromTimePerc_m05DF6CE5DEE89C0D965748560E2D3E701C9D01B5 (Path_tF32AD8DAA5F5FA3E86ABFAC091C92B61A0385DCF* __this, float ___0_t, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	{
		float L_0 = ___0_t;
		if ((!(((float)L_0) > ((float)(0.0f)))))
		{
			goto IL_00a1;
		}
	}
	{
		float L_1 = ___0_t;
		if ((!(((float)L_1) < ((float)(1.0f)))))
		{
			goto IL_00a1;
		}
	}
	{
		float L_2 = __this->___pathLength;
		float L_3 = ___0_t;
		V_0 = ((float)il2cpp_codegen_multiply(L_2, L_3));
		V_1 = (0.0f);
		V_2 = (0.0f);
		V_3 = (0.0f);
		V_4 = (0.0f);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_4 = __this->___lengthsTable;
		NullCheck(L_4);
		V_5 = ((int32_t)(((RuntimeArray*)L_4)->max_length));
		V_6 = 0;
		goto IL_008b;
	}

IL_0047:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_5 = __this->___lengthsTable;
		int32_t L_6 = V_6;
		NullCheck(L_5);
		int32_t L_7 = L_6;
		float L_8 = (L_5)->GetAt(static_cast<il2cpp_array_size_t>(L_7));
		float L_9 = V_0;
		if ((!(((float)L_8) > ((float)L_9))))
		{
			goto IL_007b;
		}
	}
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_10 = __this->___timesTable;
		int32_t L_11 = V_6;
		NullCheck(L_10);
		int32_t L_12 = L_11;
		float L_13 = (L_10)->GetAt(static_cast<il2cpp_array_size_t>(L_12));
		V_3 = L_13;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_14 = __this->___lengthsTable;
		int32_t L_15 = V_6;
		NullCheck(L_14);
		int32_t L_16 = L_15;
		float L_17 = (L_14)->GetAt(static_cast<il2cpp_array_size_t>(L_16));
		V_4 = L_17;
		int32_t L_18 = V_6;
		if ((((int32_t)L_18) <= ((int32_t)0)))
		{
			goto IL_0091;
		}
	}
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_19 = __this->___lengthsTable;
		int32_t L_20 = V_6;
		NullCheck(L_19);
		int32_t L_21 = ((int32_t)il2cpp_codegen_subtract(L_20, 1));
		float L_22 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_21));
		V_2 = L_22;
		goto IL_0091;
	}

IL_007b:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_23 = __this->___timesTable;
		int32_t L_24 = V_6;
		NullCheck(L_23);
		int32_t L_25 = L_24;
		float L_26 = (L_23)->GetAt(static_cast<il2cpp_array_size_t>(L_25));
		V_1 = L_26;
		int32_t L_27 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_27, 1));
	}

IL_008b:
	{
		int32_t L_28 = V_6;
		int32_t L_29 = V_5;
		if ((((int32_t)L_28) < ((int32_t)L_29)))
		{
			goto IL_0047;
		}
	}

IL_0091:
	{
		float L_30 = V_1;
		float L_31 = V_0;
		float L_32 = V_2;
		float L_33 = V_4;
		float L_34 = V_2;
		float L_35 = V_3;
		float L_36 = V_1;
		___0_t = ((float)il2cpp_codegen_add(L_30, ((float)il2cpp_codegen_multiply(((float)(((float)il2cpp_codegen_subtract(L_31, L_32))/((float)il2cpp_codegen_subtract(L_33, L_34)))), ((float)il2cpp_codegen_subtract(L_35, L_36))))));
	}

IL_00a1:
	{
		float L_37 = ___0_t;
		if ((!(((float)L_37) > ((float)(1.0f)))))
		{
			goto IL_00b2;
		}
	}
	{
		___0_t = (1.0f);
		goto IL_00c1;
	}

IL_00b2:
	{
		float L_38 = ___0_t;
		if ((!(((float)L_38) < ((float)(0.0f)))))
		{
			goto IL_00c1;
		}
	}
	{
		___0_t = (0.0f);
	}

IL_00c1:
	{
		float L_39 = ___0_t;
		return L_39;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TweenParms_get_hasProps_mD3C0CFB119ABC3898AC73225309DF329FDBF0952 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, const RuntimeMethod* method) 
{
	{
		List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* L_0 = __this->___propDatas;
		return (bool)((((int32_t)((((RuntimeObject*)(List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD*)L_0) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenParms_InitializeObject_m0B9ABD1E886C5141D7B5742E4D5A9240E15ABAA8 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* ___0_p_tweenObj, RuntimeObject* ___1_p_target, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m835BB1E6EA8A8BF1242B51E28FD65B43FEF68E2A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_mBB2DBA9ECB2AD6046CB4CFB717FDD7E474A439AB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m0C336245737552A850BF98B9B62610882672A341_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m805576DBB9A4E83729241F9A56D3E75202DF9014_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mE437070E1C414F54A661124CFD73BAE04C1D0CC8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m68F0E22360E0088E4149CBCBDAE6A1E67C16CD6C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral14C4F2807068D9640EE91247145D17939966A293);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral16B1A560D0508AB021624167CB1F87B6D48B02D6);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral19B7D722FFCBB1EBCC95DE76FB16F022050F3CC8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral27D9B7EF612AEB12509925B54604A1C6C9199F88);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3B53C838334DF89B87164B8A5EE26C8FD470850B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4B64ECB86CB3E3562CA21F15EDF2E19D670A51ED);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5F43C61FF910780A25E22CD0232290820C30BA1D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral82B1FFF171100778CEDD884A0E4A65666906E7EE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB375D52F58ABA319072C6F9F1880BCB36A59233C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBCA7DDD073AD5DB21CC612ADB1833BF1A5D32261);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBED41A93D53C57A40BB6B79662E6D00E6BF4EFB1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCFA73882EBCB16AE44454CACF911EC21EF0A579C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDB47297909F3BD6EDB8AD67A8511975233214355);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEB60F7CAA481E19A64B444094946BAD0787BCE63);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		s_Il2CppMethodInitialized = true;
	}
	Type_t* V_0 = NULL;
	FieldInfo_t* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* V_4 = NULL;
	PropertyInfo_t* V_5 = NULL;
	ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* V_6 = NULL;
	ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* V_7 = NULL;
	String_t* V_8 = NULL;
	float V_9 = 0.0f;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_10 = NULL;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_11 = NULL;
	String_t* V_12 = NULL;
	int32_t V_13 = 0;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_14 = NULL;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_15 = NULL;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	String_t* G_B19_0 = NULL;
	int32_t G_B48_0 = 0;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* G_B48_1 = NULL;
	int32_t G_B47_0 = 0;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* G_B47_1 = NULL;
	Type_t* G_B49_0 = NULL;
	int32_t G_B49_1 = 0;
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* G_B49_2 = NULL;
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_0 = ___0_p_tweenObj;
		ABSTweenComponentParms_InitializeOwner_mF88937400BEA35A760F2DC698CA459C44FE82327(__this, L_0, NULL);
		bool L_1 = __this->___speedBased;
		if (!L_1)
		{
			goto IL_001e;
		}
	}
	{
		bool L_2 = __this->___easeSet;
		if (L_2)
		{
			goto IL_001e;
		}
	}
	{
		__this->___easeType = 0;
	}

IL_001e:
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_3 = ___0_p_tweenObj;
		bool L_4 = __this->___pixelPerfect;
		NullCheck(L_3);
		L_3->____pixelPerfect = L_4;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_5 = ___0_p_tweenObj;
		bool L_6 = __this->___speedBased;
		NullCheck(L_5);
		L_5->____speedBased = L_6;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_7 = ___0_p_tweenObj;
		int32_t L_8 = __this->___easeType;
		NullCheck(L_7);
		L_7->____easeType = L_8;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_9 = ___0_p_tweenObj;
		AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* L_10 = __this->___easeAnimCurve;
		NullCheck(L_9);
		L_9->____easeAnimationCurve = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&L_9->____easeAnimationCurve), (void*)L_10);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_11 = ___0_p_tweenObj;
		float L_12 = __this->___easeOvershootOrAmplitude;
		NullCheck(L_11);
		L_11->____easeOvershootOrAmplitude = L_12;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_13 = ___0_p_tweenObj;
		float L_14 = __this->___easePeriod;
		NullCheck(L_13);
		L_13->____easePeriod = L_14;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_15 = ___0_p_tweenObj;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_16 = ___0_p_tweenObj;
		float L_17 = __this->___delay;
		float L_18 = L_17;
		V_9 = L_18;
		NullCheck(L_16);
		L_16->___delayCount = L_18;
		float L_19 = V_9;
		NullCheck(L_15);
		L_15->____delay = L_19;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_20 = ___0_p_tweenObj;
		bool L_21 = __this->___isFrom;
		NullCheck(L_20);
		Tweener_set_isFrom_m3E5ABBC9B076D66C6006F2E422A6B15C0899CD24_inline(L_20, L_21, NULL);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_22 = ___0_p_tweenObj;
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_23 = __this->___onPluginOverwritten;
		NullCheck(L_22);
		L_22->___onPluginOverwritten = L_23;
		Il2CppCodeGenWriteBarrier((void**)(&L_22->___onPluginOverwritten), (void*)L_23);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_24 = ___0_p_tweenObj;
		TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* L_25 = __this->___onPluginOverwrittenWParms;
		NullCheck(L_24);
		L_24->___onPluginOverwrittenWParms = L_25;
		Il2CppCodeGenWriteBarrier((void**)(&L_24->___onPluginOverwrittenWParms), (void*)L_25);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_26 = ___0_p_tweenObj;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_27 = __this->___onPluginOverwrittenParms;
		NullCheck(L_26);
		L_26->___onPluginOverwrittenParms = L_27;
		Il2CppCodeGenWriteBarrier((void**)(&L_26->___onPluginOverwrittenParms), (void*)L_27);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_28 = ___0_p_tweenObj;
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_29 = (List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A*)il2cpp_codegen_object_new(List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A_il2cpp_TypeInfo_var);
		List_1__ctor_m805576DBB9A4E83729241F9A56D3E75202DF9014(L_29, List_1__ctor_m805576DBB9A4E83729241F9A56D3E75202DF9014_RuntimeMethod_var);
		NullCheck(L_28);
		L_28->___plugins = L_29;
		Il2CppCodeGenWriteBarrier((void**)(&L_28->___plugins), (void*)L_29);
		RuntimeObject* L_30 = ___1_p_target;
		NullCheck(L_30);
		Type_t* L_31;
		L_31 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_30, NULL);
		V_0 = L_31;
		V_1 = (FieldInfo_t*)NULL;
		List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* L_32 = __this->___propDatas;
		NullCheck(L_32);
		int32_t L_33;
		L_33 = List_1_get_Count_mE437070E1C414F54A661124CFD73BAE04C1D0CC8_inline(L_32, List_1_get_Count_mE437070E1C414F54A661124CFD73BAE04C1D0CC8_RuntimeMethod_var);
		V_2 = L_33;
		V_3 = 0;
		goto IL_05ea;
	}

IL_00d4:
	{
		List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* L_34 = __this->___propDatas;
		int32_t L_35 = V_3;
		NullCheck(L_34);
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_36;
		L_36 = List_1_get_Item_m68F0E22360E0088E4149CBCBDAE6A1E67C16CD6C(L_34, L_35, List_1_get_Item_m68F0E22360E0088E4149CBCBDAE6A1E67C16CD6C_RuntimeMethod_var);
		V_4 = L_36;
		Type_t* L_37 = V_0;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_38 = V_4;
		NullCheck(L_38);
		String_t* L_39 = L_38->___propName;
		NullCheck(L_37);
		PropertyInfo_t* L_40;
		L_40 = Type_GetProperty_mD183124FC8A89121E8368058B327A7750B14281D(L_37, L_39, NULL);
		V_5 = L_40;
		PropertyInfo_t* L_41 = V_5;
		if (L_41)
		{
			goto IL_014a;
		}
	}
	{
		Type_t* L_42 = V_0;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_43 = V_4;
		NullCheck(L_43);
		String_t* L_44 = L_43->___propName;
		NullCheck(L_42);
		FieldInfo_t* L_45;
		L_45 = Type_GetField_m0BF55B1A27A1B6AB6D3477E7F9E1CF2A3451E1E0(L_42, L_44, NULL);
		V_1 = L_45;
		FieldInfo_t* L_46 = V_1;
		if (L_46)
		{
			goto IL_014a;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_47 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)5);
		V_10 = L_47;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_48 = V_10;
		NullCheck(L_48);
		ArrayElementTypeCheck (L_48, _stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677);
		(L_48)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)_stringLiteralC62C64F00567C5368CAE37F4E64E1E82FF785677);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_49 = V_10;
		RuntimeObject* L_50 = ___1_p_target;
		NullCheck(L_49);
		ArrayElementTypeCheck (L_49, L_50);
		(L_49)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_50);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_51 = V_10;
		NullCheck(L_51);
		ArrayElementTypeCheck (L_51, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		(L_51)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_52 = V_10;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_53 = V_4;
		NullCheck(L_53);
		String_t* L_54 = L_53->___propName;
		NullCheck(L_52);
		ArrayElementTypeCheck (L_52, L_54);
		(L_52)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_54);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_55 = V_10;
		NullCheck(L_55);
		ArrayElementTypeCheck (L_55, _stringLiteralBED41A93D53C57A40BB6B79662E6D00E6BF4EFB1);
		(L_55)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)_stringLiteralBED41A93D53C57A40BB6B79662E6D00E6BF4EFB1);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_56 = V_10;
		String_t* L_57;
		L_57 = String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36(L_56, NULL);
		TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B(L_57, NULL);
		goto IL_05e6;
	}

IL_014a:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_58 = V_4;
		NullCheck(L_58);
		RuntimeObject* L_59 = L_58->___endValOrPlugin;
		V_7 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)IsInstClass((RuntimeObject*)L_59, ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A_il2cpp_TypeInfo_var));
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_60 = V_7;
		if (!L_60)
		{
			goto IL_01c4;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_61 = V_7;
		V_6 = L_61;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_62 = V_6;
		RuntimeObject* L_63 = ___1_p_target;
		NullCheck(L_62);
		bool L_64;
		L_64 = VirtualFuncInvoker1< bool, RuntimeObject* >::Invoke(9, L_62, L_63);
		if (!L_64)
		{
			goto IL_0184;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_65 = V_6;
		NullCheck(L_65);
		bool L_66;
		L_66 = ABSTweenPlugin_get_initialized_mBDDF3D1051BAFBF04CAAF5600D799AE51D452397_inline(L_65, NULL);
		if (!L_66)
		{
			goto IL_05c0;
		}
	}
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_67 = V_6;
		NullCheck(L_67);
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_68;
		L_68 = ABSTweenPlugin_CloneBasic_mCA9249440372C5ECD0B8A07D357C7D005CBDF22E(L_67, NULL);
		V_6 = L_68;
		goto IL_05c0;
	}

IL_0184:
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_69 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)4);
		V_11 = L_69;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_70 = V_11;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_71 = V_6;
		NullCheck(L_71);
		Type_t* L_72;
		L_72 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_71, NULL);
		String_t* L_73;
		L_73 = Utils_SimpleClassName_m04D18EADDE8255C2C1DDB00067B4F55C8EB8F5FA(L_72, NULL);
		NullCheck(L_70);
		ArrayElementTypeCheck (L_70, L_73);
		(L_70)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_73);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_74 = V_11;
		NullCheck(L_74);
		ArrayElementTypeCheck (L_74, _stringLiteral3B53C838334DF89B87164B8A5EE26C8FD470850B);
		(L_74)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)_stringLiteral3B53C838334DF89B87164B8A5EE26C8FD470850B);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_75 = V_11;
		RuntimeObject* L_76 = ___1_p_target;
		NullCheck(L_75);
		ArrayElementTypeCheck (L_75, L_76);
		(L_75)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_76);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_77 = V_11;
		NullCheck(L_77);
		ArrayElementTypeCheck (L_77, _stringLiteral4B64ECB86CB3E3562CA21F15EDF2E19D670A51ED);
		(L_77)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)_stringLiteral4B64ECB86CB3E3562CA21F15EDF2E19D670A51ED);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_78 = V_11;
		String_t* L_79;
		L_79 = String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36(L_78, NULL);
		TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B(L_79, NULL);
		goto IL_05e6;
	}

IL_01c4:
	{
		V_6 = (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)NULL;
		PropertyInfo_t* L_80 = V_5;
		if (L_80)
		{
			goto IL_01f6;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_81 = ((TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields*)il2cpp_codegen_static_fields_for(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var))->____TypeToShortString;
		FieldInfo_t* L_82 = V_1;
		NullCheck(L_82);
		Type_t* L_83;
		L_83 = VirtualFuncInvoker0< Type_t* >::Invoke(17, L_82);
		NullCheck(L_81);
		bool L_84;
		L_84 = Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F(L_81, L_83, Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F_RuntimeMethod_var);
		if (L_84)
		{
			goto IL_01e4;
		}
	}
	{
		G_B19_0 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		goto IL_0221;
	}

IL_01e4:
	{
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_85 = ((TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields*)il2cpp_codegen_static_fields_for(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var))->____TypeToShortString;
		FieldInfo_t* L_86 = V_1;
		NullCheck(L_86);
		Type_t* L_87;
		L_87 = VirtualFuncInvoker0< Type_t* >::Invoke(17, L_86);
		NullCheck(L_85);
		String_t* L_88;
		L_88 = Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008(L_85, L_87, Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008_RuntimeMethod_var);
		G_B19_0 = L_88;
		goto IL_0221;
	}

IL_01f6:
	{
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_89 = ((TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields*)il2cpp_codegen_static_fields_for(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var))->____TypeToShortString;
		PropertyInfo_t* L_90 = V_5;
		NullCheck(L_90);
		Type_t* L_91;
		L_91 = VirtualFuncInvoker0< Type_t* >::Invoke(16, L_90);
		NullCheck(L_89);
		bool L_92;
		L_92 = Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F(L_89, L_91, Dictionary_2_ContainsKey_m5AF1FF54C84FB97FFB85E559036AB80013342C4F_RuntimeMethod_var);
		if (L_92)
		{
			goto IL_0210;
		}
	}
	{
		G_B19_0 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		goto IL_0221;
	}

IL_0210:
	{
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_93 = ((TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields*)il2cpp_codegen_static_fields_for(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var))->____TypeToShortString;
		PropertyInfo_t* L_94 = V_5;
		NullCheck(L_94);
		Type_t* L_95;
		L_95 = VirtualFuncInvoker0< Type_t* >::Invoke(16, L_94);
		NullCheck(L_93);
		String_t* L_96;
		L_96 = Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008(L_93, L_95, Dictionary_2_get_Item_m3359894DA1EF277B87D6220E9C380C4C01AE6008_RuntimeMethod_var);
		G_B19_0 = L_96;
	}

IL_0221:
	{
		V_8 = G_B19_0;
		String_t* L_97 = V_8;
		String_t* L_98 = L_97;
		V_12 = L_98;
		if (!L_98)
		{
			goto IL_04fd;
		}
	}
	{
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_99 = ((U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_StaticFields*)il2cpp_codegen_static_fields_for(U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_il2cpp_TypeInfo_var))->___U24U24method0x60002b7U2D1;
		il2cpp_codegen_memory_barrier();
		if (L_99)
		{
			goto IL_02b0;
		}
	}
	{
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_100 = (Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588*)il2cpp_codegen_object_new(Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_mBB2DBA9ECB2AD6046CB4CFB717FDD7E474A439AB(L_100, ((int32_t)9), Dictionary_2__ctor_mBB2DBA9ECB2AD6046CB4CFB717FDD7E474A439AB_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_101 = L_100;
		NullCheck(L_101);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_101, _stringLiteralCFA73882EBCB16AE44454CACF911EC21EF0A579C, 0, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_102 = L_101;
		NullCheck(L_102);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_102, _stringLiteralB375D52F58ABA319072C6F9F1880BCB36A59233C, 1, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_103 = L_102;
		NullCheck(L_103);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_103, _stringLiteral82B1FFF171100778CEDD884A0E4A65666906E7EE, 2, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_104 = L_103;
		NullCheck(L_104);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_104, _stringLiteral27D9B7EF612AEB12509925B54604A1C6C9199F88, 3, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_105 = L_104;
		NullCheck(L_105);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_105, _stringLiteral19B7D722FFCBB1EBCC95DE76FB16F022050F3CC8, 4, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_106 = L_105;
		NullCheck(L_106);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_106, _stringLiteral16B1A560D0508AB021624167CB1F87B6D48B02D6, 5, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_107 = L_106;
		NullCheck(L_107);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_107, _stringLiteral5F43C61FF910780A25E22CD0232290820C30BA1D, 6, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_108 = L_107;
		NullCheck(L_108);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_108, _stringLiteralBCA7DDD073AD5DB21CC612ADB1833BF1A5D32261, 7, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_109 = L_108;
		NullCheck(L_109);
		Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883(L_109, _stringLiteralDB47297909F3BD6EDB8AD67A8511975233214355, 8, Dictionary_2_Add_m2FE98C9C3763E31D7CB55207ED3A46B33BF64883_RuntimeMethod_var);
		il2cpp_codegen_memory_barrier();
		((U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_StaticFields*)il2cpp_codegen_static_fields_for(U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_il2cpp_TypeInfo_var))->___U24U24method0x60002b7U2D1 = L_109;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_StaticFields*)il2cpp_codegen_static_fields_for(U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_il2cpp_TypeInfo_var))->___U24U24method0x60002b7U2D1), (void*)L_109);
	}

IL_02b0:
	{
		Dictionary_2_t5C8F46F5D57502270DD9E1DA8303B23C7FE85588* L_110 = ((U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_StaticFields*)il2cpp_codegen_static_fields_for(U3CPrivateImplementationDetailsU3EU7BD0F30737U2DDDE4U2D49F0U2D85CFU2D94F27798FC12U7D_t10BCC32585957BC226A1DF6ADA4B88598B1542FF_il2cpp_TypeInfo_var))->___U24U24method0x60002b7U2D1;
		il2cpp_codegen_memory_barrier();
		String_t* L_111 = V_12;
		NullCheck(L_110);
		bool L_112;
		L_112 = Dictionary_2_TryGetValue_m835BB1E6EA8A8BF1242B51E28FD65B43FEF68E2A(L_110, L_111, (&V_13), Dictionary_2_TryGetValue_m835BB1E6EA8A8BF1242B51E28FD65B43FEF68E2A_RuntimeMethod_var);
		if (!L_112)
		{
			goto IL_04fd;
		}
	}
	{
		int32_t L_113 = V_13;
		switch (L_113)
		{
			case 0:
			{
				goto IL_02f5;
			}
			case 1:
			{
				goto IL_032a;
			}
			case 2:
			{
				goto IL_035f;
			}
			case 3:
			{
				goto IL_0394;
			}
			case 4:
			{
				goto IL_03f6;
			}
			case 5:
			{
				goto IL_042b;
			}
			case 6:
			{
				goto IL_0460;
			}
			case 7:
			{
				goto IL_0495;
			}
			case 8:
			{
				goto IL_04ca;
			}
		}
	}
	{
		goto IL_04fd;
	}

IL_02f5:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_114 = V_4;
		NullCheck(L_114);
		RuntimeObject* L_115 = L_114->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_116 = ((PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_117;
		L_117 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_115, L_116, NULL);
		if (!L_117)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_118 = V_4;
		NullCheck(L_118);
		RuntimeObject* L_119 = L_118->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_120 = V_4;
		NullCheck(L_120);
		bool L_121 = L_120->___isRelative;
		PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8* L_122 = (PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8*)il2cpp_codegen_object_new(PlugVector2_t1833992ECF7D55CA00358CF7512F9E89FB0C48C8_il2cpp_TypeInfo_var);
		PlugVector2__ctor_mD38E3F80476EF22E23B0D6902C1EBFBE597E50DD(L_122, ((*(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)((Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)UnBox(L_119, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var)))), L_121, NULL);
		V_6 = L_122;
		goto IL_057b;
	}

IL_032a:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_123 = V_4;
		NullCheck(L_123);
		RuntimeObject* L_124 = L_123->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_125 = ((PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_126;
		L_126 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_124, L_125, NULL);
		if (!L_126)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_127 = V_4;
		NullCheck(L_127);
		RuntimeObject* L_128 = L_127->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_129 = V_4;
		NullCheck(L_129);
		bool L_130 = L_129->___isRelative;
		PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E* L_131 = (PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E*)il2cpp_codegen_object_new(PlugVector3_tC11284528716A47F8BDB7B404DE18F28FC53E82E_il2cpp_TypeInfo_var);
		PlugVector3__ctor_mFAEE32D17D68FA03776ED57F2C2A351D19A2621B(L_131, ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_128, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var)))), L_130, NULL);
		V_6 = L_131;
		goto IL_057b;
	}

IL_035f:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_132 = V_4;
		NullCheck(L_132);
		RuntimeObject* L_133 = L_132->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_134 = ((PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_StaticFields*)il2cpp_codegen_static_fields_for(PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_135;
		L_135 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_133, L_134, NULL);
		if (!L_135)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_136 = V_4;
		NullCheck(L_136);
		RuntimeObject* L_137 = L_136->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_138 = V_4;
		NullCheck(L_138);
		bool L_139 = L_138->___isRelative;
		PlugVector4_t182247639032B73333E7055ED1105099DEED99DF* L_140 = (PlugVector4_t182247639032B73333E7055ED1105099DEED99DF*)il2cpp_codegen_object_new(PlugVector4_t182247639032B73333E7055ED1105099DEED99DF_il2cpp_TypeInfo_var);
		PlugVector4__ctor_m348E95DFFA753B9E5A4DF1A5AB25DEA5DBD84E81(L_140, ((*(Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)((Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)(Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)UnBox(L_137, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_il2cpp_TypeInfo_var)))), L_139, NULL);
		V_6 = L_140;
		goto IL_057b;
	}

IL_0394:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_141 = V_4;
		NullCheck(L_141);
		RuntimeObject* L_142 = L_141->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_143 = ((PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_StaticFields*)il2cpp_codegen_static_fields_for(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_144;
		L_144 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_142, L_143, NULL);
		if (!L_144)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_145 = V_4;
		NullCheck(L_145);
		RuntimeObject* L_146 = L_145->___endValOrPlugin;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_146, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var)))
		{
			goto IL_03d7;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_147 = V_4;
		NullCheck(L_147);
		RuntimeObject* L_148 = L_147->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_149 = V_4;
		NullCheck(L_149);
		bool L_150 = L_149->___isRelative;
		PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* L_151 = (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1*)il2cpp_codegen_object_new(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var);
		PlugQuaternion__ctor_m653333B63186F7A0F1430587FAF26EE4A67302D8(L_151, ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_148, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var)))), L_150, NULL);
		V_6 = L_151;
		goto IL_057b;
	}

IL_03d7:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_152 = V_4;
		NullCheck(L_152);
		RuntimeObject* L_153 = L_152->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_154 = V_4;
		NullCheck(L_154);
		bool L_155 = L_154->___isRelative;
		PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* L_156 = (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1*)il2cpp_codegen_object_new(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var);
		PlugQuaternion__ctor_m46BD79B83263F7486AA657F2BDB40E50A2198049(L_156, ((*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)((Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)UnBox(L_153, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var)))), L_155, NULL);
		V_6 = L_156;
		goto IL_057b;
	}

IL_03f6:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_157 = V_4;
		NullCheck(L_157);
		RuntimeObject* L_158 = L_157->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_159 = ((PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_StaticFields*)il2cpp_codegen_static_fields_for(PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_160;
		L_160 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_158, L_159, NULL);
		if (!L_160)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_161 = V_4;
		NullCheck(L_161);
		RuntimeObject* L_162 = L_161->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_163 = V_4;
		NullCheck(L_163);
		bool L_164 = L_163->___isRelative;
		PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF* L_165 = (PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF*)il2cpp_codegen_object_new(PlugColor_t6AB8BACA97784733D72CD239B3E2BD5AC3B2B8BF_il2cpp_TypeInfo_var);
		PlugColor__ctor_m9587F07E6E13DF59F6DBB8795BC7408688ABF745(L_165, ((*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)((Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)UnBox(L_162, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_il2cpp_TypeInfo_var)))), L_164, NULL);
		V_6 = L_165;
		goto IL_057b;
	}

IL_042b:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_166 = V_4;
		NullCheck(L_166);
		RuntimeObject* L_167 = L_166->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_168 = ((PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_StaticFields*)il2cpp_codegen_static_fields_for(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_169;
		L_169 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_167, L_168, NULL);
		if (!L_169)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_170 = V_4;
		NullCheck(L_170);
		RuntimeObject* L_171 = L_170->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_172 = V_4;
		NullCheck(L_172);
		bool L_173 = L_172->___isRelative;
		PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19* L_174 = (PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19*)il2cpp_codegen_object_new(PlugColor32_tE4C23DC55F6C37D2646186A4367A3324A714BD19_il2cpp_TypeInfo_var);
		PlugColor32__ctor_mA746143BEC963C76BB01E625BE07D6E7B6D83E4E(L_174, ((*(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)((Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)(Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B*)UnBox(L_171, Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_il2cpp_TypeInfo_var)))), L_173, NULL);
		V_6 = L_174;
		goto IL_057b;
	}

IL_0460:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_175 = V_4;
		NullCheck(L_175);
		RuntimeObject* L_176 = L_175->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_177 = ((PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_StaticFields*)il2cpp_codegen_static_fields_for(PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_178;
		L_178 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_176, L_177, NULL);
		if (!L_178)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_179 = V_4;
		NullCheck(L_179);
		RuntimeObject* L_180 = L_179->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_181 = V_4;
		NullCheck(L_181);
		bool L_182 = L_181->___isRelative;
		PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888* L_183 = (PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888*)il2cpp_codegen_object_new(PlugRect_tF76294752A03DC508D606336D3CC6B766CDF0888_il2cpp_TypeInfo_var);
		PlugRect__ctor_m1CCAC707C847323D566B4B359BD492E0368C1750(L_183, ((*(Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D*)((Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D*)(Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D*)UnBox(L_180, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_il2cpp_TypeInfo_var)))), L_182, NULL);
		V_6 = L_183;
		goto IL_057b;
	}

IL_0495:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_184 = V_4;
		NullCheck(L_184);
		RuntimeObject* L_185 = L_184->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_186 = ((PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_StaticFields*)il2cpp_codegen_static_fields_for(PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_187;
		L_187 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_185, L_186, NULL);
		if (!L_187)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_188 = V_4;
		NullCheck(L_188);
		RuntimeObject* L_189 = L_188->___endValOrPlugin;
		NullCheck(L_189);
		String_t* L_190;
		L_190 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_189);
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_191 = V_4;
		NullCheck(L_191);
		bool L_192 = L_191->___isRelative;
		PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580* L_193 = (PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580*)il2cpp_codegen_object_new(PlugString_t93DF9CFF9BB85A95AB002DCB3FEB3B4ACC55A580_il2cpp_TypeInfo_var);
		PlugString__ctor_mBC5CF13283AEDED7546061AFDDCD1BC3049D9D12(L_193, L_190, L_192, NULL);
		V_6 = L_193;
		goto IL_057b;
	}

IL_04ca:
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_194 = V_4;
		NullCheck(L_194);
		RuntimeObject* L_195 = L_194->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_196 = ((PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_StaticFields*)il2cpp_codegen_static_fields_for(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var))->___validValueTypes;
		il2cpp_codegen_runtime_class_init_inline(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		bool L_197;
		L_197 = TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B(L_195, L_196, NULL);
		if (!L_197)
		{
			goto IL_057b;
		}
	}
	{
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_198 = V_4;
		NullCheck(L_198);
		RuntimeObject* L_199 = L_198->___endValOrPlugin;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_200 = V_4;
		NullCheck(L_200);
		bool L_201 = L_200->___isRelative;
		PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* L_202 = (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5*)il2cpp_codegen_object_new(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var);
		PlugInt__ctor_m36BBA904D1AA75C2195D945C7D808BB4404D404D(L_202, ((float)((*(int32_t*)((int32_t*)(int32_t*)UnBox(L_199, Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var))))), L_201, NULL);
		V_6 = L_202;
		goto IL_057b;
	}

IL_04fd:
	try
	{

IL_04fd_1:
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_203 = V_4;
		NullCheck(L_203);
		RuntimeObject* L_204 = L_203->___endValOrPlugin;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		float L_205;
		L_205 = Convert_ToSingle_m6B47C78A7DFD7825B4361BCA8AB6748FC82165E9(L_204, NULL);
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_206 = V_4;
		NullCheck(L_206);
		bool L_207 = L_206->___isRelative;
		PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B* L_208 = (PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B*)il2cpp_codegen_object_new(PlugFloat_t93A397BF2C4A2CF36C2B5CCB774BBB0EA2FA9F3B_il2cpp_TypeInfo_var);
		PlugFloat__ctor_m7F3FBD710426F3E263968ABEA94E1083679AB401(L_208, L_205, L_207, NULL);
		V_6 = L_208;
		goto IL_057b;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_0519;
		}
		throw e;
	}

CATCH_0519:
	{
		{
			Exception_t* L_209 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_210 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var)), (uint32_t)7);
			V_14 = L_210;
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_211 = V_14;
			NullCheck(L_211);
			ArrayElementTypeCheck (L_211, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralA98C7A22AA6A1C57588D0F7FF2DA7969390ED248)));
			(L_211)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralA98C7A22AA6A1C57588D0F7FF2DA7969390ED248)));
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_212 = V_14;
			RuntimeObject* L_213 = ___1_p_target;
			NullCheck(L_212);
			ArrayElementTypeCheck (L_212, L_213);
			(L_212)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_213);
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_214 = V_14;
			NullCheck(L_214);
			ArrayElementTypeCheck (L_214, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D)));
			(L_214)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D)));
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_215 = V_14;
			HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_216 = V_4;
			NullCheck(L_216);
			String_t* L_217 = L_216->___propName;
			NullCheck(L_215);
			ArrayElementTypeCheck (L_215, L_217);
			(L_215)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_217);
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_218 = V_14;
			NullCheck(L_218);
			ArrayElementTypeCheck (L_218, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral2F49C847A1A5CEB5577FEA54212488B3D7D0B825)));
			(L_218)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral2F49C847A1A5CEB5577FEA54212488B3D7D0B825)));
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_219 = V_14;
			PropertyInfo_t* L_220 = V_5;
			if (L_220)
			{
				G_B48_0 = 5;
				G_B48_1 = L_219;
				goto IL_055c;
			}
			G_B47_0 = 5;
			G_B47_1 = L_219;
		}
		{
			FieldInfo_t* L_221 = V_1;
			NullCheck(L_221);
			Type_t* L_222;
			L_222 = VirtualFuncInvoker0< Type_t* >::Invoke(17, L_221);
			G_B49_0 = L_222;
			G_B49_1 = G_B47_0;
			G_B49_2 = G_B47_1;
			goto IL_0563;
		}

IL_055c:
		{
			PropertyInfo_t* L_223 = V_5;
			NullCheck(L_223);
			Type_t* L_224;
			L_224 = VirtualFuncInvoker0< Type_t* >::Invoke(16, L_223);
			G_B49_0 = L_224;
			G_B49_1 = G_B48_0;
			G_B49_2 = G_B48_1;
		}

IL_0563:
		{
			NullCheck(G_B49_2);
			ArrayElementTypeCheck (G_B49_2, G_B49_0);
			(G_B49_2)->SetAt(static_cast<il2cpp_array_size_t>(G_B49_1), (RuntimeObject*)G_B49_0);
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_225 = V_14;
			NullCheck(L_225);
			ArrayElementTypeCheck (L_225, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral4B64ECB86CB3E3562CA21F15EDF2E19D670A51ED)));
			(L_225)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral4B64ECB86CB3E3562CA21F15EDF2E19D670A51ED)));
			ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_226 = V_14;
			String_t* L_227;
			L_227 = String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36(L_226, NULL);
			TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B(L_227, NULL);
			IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
			goto IL_05e6;
		}
	}

IL_057b:
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_228 = V_6;
		if (L_228)
		{
			goto IL_05c0;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_229 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)5);
		V_15 = L_229;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_230 = V_15;
		NullCheck(L_230);
		ArrayElementTypeCheck (L_230, _stringLiteralEB60F7CAA481E19A64B444094946BAD0787BCE63);
		(L_230)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)_stringLiteralEB60F7CAA481E19A64B444094946BAD0787BCE63);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_231 = V_15;
		RuntimeObject* L_232 = ___1_p_target;
		NullCheck(L_231);
		ArrayElementTypeCheck (L_231, L_232);
		(L_231)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_232);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_233 = V_15;
		NullCheck(L_233);
		ArrayElementTypeCheck (L_233, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		(L_233)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_234 = V_15;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_235 = V_4;
		NullCheck(L_235);
		String_t* L_236 = L_235->___propName;
		NullCheck(L_234);
		ArrayElementTypeCheck (L_234, L_236);
		(L_234)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_236);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_237 = V_15;
		NullCheck(L_237);
		ArrayElementTypeCheck (L_237, _stringLiteral14C4F2807068D9640EE91247145D17939966A293);
		(L_237)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)_stringLiteral14C4F2807068D9640EE91247145D17939966A293);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_238 = V_15;
		String_t* L_239;
		L_239 = String_Concat_m9EB826D3BC0EF2322AA8E55DF0D20EE41B1E5A36(L_238, NULL);
		TweenWarning_Log_mD858AE1285DA74AD38B19D90625472F7C087356B(L_239, NULL);
		goto IL_05e6;
	}

IL_05c0:
	{
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_240 = V_6;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_241 = ___0_p_tweenObj;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_242 = V_4;
		NullCheck(L_242);
		String_t* L_243 = L_242->___propName;
		int32_t L_244 = __this->___easeType;
		Type_t* L_245 = V_0;
		PropertyInfo_t* L_246 = V_5;
		FieldInfo_t* L_247 = V_1;
		NullCheck(L_240);
		VirtualActionInvoker6< Tweener_t99074CD44759EE1C18B018744C9E38243A40871A*, String_t*, int32_t, Type_t*, PropertyInfo_t*, FieldInfo_t* >::Invoke(8, L_240, L_241, L_243, L_244, L_245, L_246, L_247);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_248 = ___0_p_tweenObj;
		NullCheck(L_248);
		List_1_t696500FAD911AE9CC1F61D3C277AF6093BF4B16A* L_249 = L_248->___plugins;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_250 = V_6;
		NullCheck(L_249);
		List_1_Add_m0C336245737552A850BF98B9B62610882672A341_inline(L_249, L_250, List_1_Add_m0C336245737552A850BF98B9B62610882672A341_RuntimeMethod_var);
	}

IL_05e6:
	{
		int32_t L_251 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_251, 1));
	}

IL_05ea:
	{
		int32_t L_252 = V_3;
		int32_t L_253 = V_2;
		if ((((int32_t)L_252) < ((int32_t)L_253)))
		{
			goto IL_00d4;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Ease_m2239F2056CC81A905ED58A77D51E87293B229BAC (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_easeType, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_p_easeType;
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		float L_1 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___defEaseOvershootOrAmplitude;
		float L_2 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___defEasePeriod;
		TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* L_3;
		L_3 = TweenParms_Ease_mB302FD168B34BF99116AA23AC925761871053D9B(__this, L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Ease_mB302FD168B34BF99116AA23AC925761871053D9B (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_easeType, float ___1_p_amplitude, float ___2_p_period, const RuntimeMethod* method) 
{
	{
		__this->___easeSet = (bool)1;
		int32_t L_0 = ___0_p_easeType;
		__this->___easeType = L_0;
		float L_1 = ___1_p_amplitude;
		__this->___easeOvershootOrAmplitude = L_1;
		float L_2 = ___2_p_period;
		__this->___easePeriod = L_2;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Ease_m72F10CB93D8FC98D43D9FA9672DACE62F687F81D (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* ___0_p_easeAnimationCurve, const RuntimeMethod* method) 
{
	{
		__this->___easeSet = (bool)1;
		__this->___easeType = ((int32_t)31);
		AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* L_0 = ___0_p_easeAnimationCurve;
		__this->___easeAnimCurve = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___easeAnimCurve), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Delay_mBAFB272EC1B21EAF7FBA48BC96E636977A6D6643 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, float ___0_p_delay, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_p_delay;
		__this->___delay = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Pause_mA62F1F2E657D0A048F6EB4A437ECC48EE58FA18C (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, bool ___0_p_pause, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_p_pause;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___isPaused = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Prop_m1E6374BA7365EFA50DBA6654C21A3E2D89FD8C81 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, String_t* ___0_p_propName, ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* ___1_p_plugin, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_p_propName;
		ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* L_1 = ___1_p_plugin;
		TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* L_2;
		L_2 = TweenParms_Prop_m52667C136BA3A423786787A2E8B27D3BB1E25BA0(__this, L_0, L_1, (bool)0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Prop_mADE8C79AEFF46387BD66FD1281A5F4871A977D89 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, String_t* ___0_p_propName, RuntimeObject* ___1_p_endVal, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_p_propName;
		RuntimeObject* L_1 = ___1_p_endVal;
		TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* L_2;
		L_2 = TweenParms_Prop_m52667C136BA3A423786787A2E8B27D3BB1E25BA0(__this, L_0, L_1, (bool)0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Prop_m52667C136BA3A423786787A2E8B27D3BB1E25BA0 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, String_t* ___0_p_propName, RuntimeObject* ___1_p_endVal, bool ___2_p_isRelative, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m1CBA8A3D48739CC5AF6BCBBD86D0086BB762DE1A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m5D2B3DB01D3330882450D6B77EB81FBDA75042CA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* L_0 = __this->___propDatas;
		if (L_0)
		{
			goto IL_0013;
		}
	}
	{
		List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* L_1 = (List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD*)il2cpp_codegen_object_new(List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD_il2cpp_TypeInfo_var);
		List_1__ctor_m5D2B3DB01D3330882450D6B77EB81FBDA75042CA(L_1, List_1__ctor_m5D2B3DB01D3330882450D6B77EB81FBDA75042CA_RuntimeMethod_var);
		__this->___propDatas = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___propDatas), (void*)L_1);
	}

IL_0013:
	{
		List_1_t046A226EC5EE4BD84CD4514A2655CC859E3AADDD* L_2 = __this->___propDatas;
		String_t* L_3 = ___0_p_propName;
		RuntimeObject* L_4 = ___1_p_endVal;
		bool L_5 = ___2_p_isRelative;
		HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* L_6 = (HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79*)il2cpp_codegen_object_new(HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79_il2cpp_TypeInfo_var);
		HOTPropData__ctor_mEB72EC44DC80528C9615FBB1580D2208C1C27DEA(L_6, L_3, L_4, L_5, NULL);
		NullCheck(L_2);
		List_1_Add_m1CBA8A3D48739CC5AF6BCBBD86D0086BB762DE1A_inline(L_2, L_6, List_1_Add_m1CBA8A3D48739CC5AF6BCBBD86D0086BB762DE1A_RuntimeMethod_var);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Id_m3F778CAACB05B79BF6D06E875CB4186154FEDEB4 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, String_t* ___0_p_id, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_p_id;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___id = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___id), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_AutoKill_m336EF4095BD04F73271FC6BC18172531EF63AB58 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, bool ___0_p_active, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_p_active;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___autoKillOnComplete = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_UpdateType_m94959C570D78097687980C2EB04908F0145076D4 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_updateType, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_p_updateType;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___updateType = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_TimeScale_m3B26C008E3C0F957F044A4FC0AB97ED8ABBA9FB8 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, float ___0_p_timeScale, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_p_timeScale;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___timeScale = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Loops_mE2C2F106984F8D49D02CFA2B2EC9A09AF3BBB632 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_loops, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_p_loops;
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		int32_t L_1 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___defLoopType;
		TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* L_2;
		L_2 = TweenParms_Loops_mFD3B261B9B6C37DD20528F6E622E0145F0B23974(__this, L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_Loops_mFD3B261B9B6C37DD20528F6E622E0145F0B23974 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, int32_t ___0_p_loops, int32_t ___1_p_loopType, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_p_loops;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___loops = L_0;
		int32_t L_1 = ___1_p_loopType;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___loopType = L_1;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_OnStart_m9044C94DE290F1657821BF07ACF465D6A704BCEA (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___0_p_function, const RuntimeMethod* method) 
{
	{
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_0 = ___0_p_function;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onStart = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onStart), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_OnStepComplete_m1561B57D3EFDA3B2EF0D9A8D39716DBD838909CE (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___0_p_function, const RuntimeMethod* method) 
{
	{
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_0 = ___0_p_function;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onStepComplete = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onStepComplete), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_OnComplete_mDFB601AC949292EA7FE01D75074307CC233B3559 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___0_p_function, const RuntimeMethod* method) 
{
	{
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_0 = ___0_p_function;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onComplete = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onComplete), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_OnComplete_m96A7E52367591089EB46EF61EC3C19ED84EFDBA4 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* ___0_p_sendMessageTarget, String_t* ___1_p_methodName, RuntimeObject* ___2_p_value, int32_t ___3_p_options, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTween_DoSendMessage_m88B006E16146E8559219FE3BF4553AF0A5B91BB8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SendMessageOptions_t8C6881C01B06BF874EE578D27D8CF237EC2BFD54_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	{
		TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* L_0 = (TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF*)il2cpp_codegen_object_new(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF_il2cpp_TypeInfo_var);
		TweenCallbackWParms__ctor_mB37CAD56CA9F34BDAC55ED611104A2DBBE80B520(L_0, NULL, (intptr_t)((void*)HOTween_DoSendMessage_m88B006E16146E8559219FE3BF4553AF0A5B91BB8_RuntimeMethod_var), NULL);
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onCompleteWParms = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onCompleteWParms), (void*)L_0);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)4);
		V_0 = L_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_2 = V_0;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3 = ___0_p_sendMessageTarget;
		NullCheck(L_2);
		ArrayElementTypeCheck (L_2, L_3);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_3);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		String_t* L_5 = ___1_p_methodName;
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, L_5);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_5);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		RuntimeObject* L_7 = ___2_p_value;
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_7);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_7);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_8 = V_0;
		int32_t L_9 = ___3_p_options;
		int32_t L_10 = L_9;
		RuntimeObject* L_11 = Box(SendMessageOptions_t8C6881C01B06BF874EE578D27D8CF237EC2BFD54_il2cpp_TypeInfo_var, &L_10);
		NullCheck(L_8);
		ArrayElementTypeCheck (L_8, L_11);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_11);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_12 = V_0;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onCompleteParms = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onCompleteParms), (void*)L_12);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* TweenParms_IsFrom_m03B51C6DE24F9912B052954DDDACD644E13BAC39 (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, const RuntimeMethod* method) 
{
	{
		__this->___isFrom = (bool)1;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TweenParms_ValidateValue_mBBCB88F963881CE49CA2DCB64FEF14C26F147A9B (RuntimeObject* ___0_p_val, TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___1_p_validVals, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Array_IndexOf_TisType_t_m2923AB55EE8374E8CABFAD02C349A1C742E82B8A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_0 = ___1_p_validVals;
		RuntimeObject* L_1 = ___0_p_val;
		NullCheck(L_1);
		Type_t* L_2;
		L_2 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_1, NULL);
		int32_t L_3;
		L_3 = Array_IndexOf_TisType_t_m2923AB55EE8374E8CABFAD02C349A1C742E82B8A(L_0, L_2, Array_IndexOf_TisType_t_m2923AB55EE8374E8CABFAD02C349A1C742E82B8A_RuntimeMethod_var);
		return (bool)((((int32_t)((((int32_t)L_3) == ((int32_t)(-1)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenParms__ctor_mBBE01A0AC0D7F5D39B15749CAD6F18A84A9A013E (TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var);
		int32_t L_0 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___defEaseType;
		__this->___easeType = L_0;
		float L_1 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___defEaseOvershootOrAmplitude;
		__this->___easeOvershootOrAmplitude = L_1;
		float L_2 = ((HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_StaticFields*)il2cpp_codegen_static_fields_for(HOTween_t015F57AB854A98B5723BC4B1F1F16DA374F256DC_il2cpp_TypeInfo_var))->___defEasePeriod;
		__this->___easePeriod = L_2;
		ABSTweenComponentParms__ctor_m689C96ED2202D6F626DB88BBF1F031D265508270(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenParms__cctor_m18FB70B742F40C9BFBF1AA63DEE2863FE67F3686 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_mD41ECDF321C38DCCF6A9FFC5CC98C0D1D8E2764C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral16B1A560D0508AB021624167CB1F87B6D48B02D6);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral19B7D722FFCBB1EBCC95DE76FB16F022050F3CC8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral27D9B7EF612AEB12509925B54604A1C6C9199F88);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5F43C61FF910780A25E22CD0232290820C30BA1D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral82B1FFF171100778CEDD884A0E4A65666906E7EE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB375D52F58ABA319072C6F9F1880BCB36A59233C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBCA7DDD073AD5DB21CC612ADB1833BF1A5D32261);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCFA73882EBCB16AE44454CACF911EC21EF0A579C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDB47297909F3BD6EDB8AD67A8511975233214355);
		s_Il2CppMethodInitialized = true;
	}
	Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* V_0 = NULL;
	{
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_0 = (Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE*)il2cpp_codegen_object_new(Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_mD41ECDF321C38DCCF6A9FFC5CC98C0D1D8E2764C(L_0, 8, Dictionary_2__ctor_mD41ECDF321C38DCCF6A9FFC5CC98C0D1D8E2764C_RuntimeMethod_var);
		V_0 = L_0;
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_1 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		NullCheck(L_1);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_1, L_3, _stringLiteralCFA73882EBCB16AE44454CACF911EC21EF0A579C, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_4 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_5 = { reinterpret_cast<intptr_t> (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var) };
		Type_t* L_6;
		L_6 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_5, NULL);
		NullCheck(L_4);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_4, L_6, _stringLiteralB375D52F58ABA319072C6F9F1880BCB36A59233C, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_7 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_8 = { reinterpret_cast<intptr_t> (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_0_0_0_var) };
		Type_t* L_9;
		L_9 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_8, NULL);
		NullCheck(L_7);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_7, L_9, _stringLiteral82B1FFF171100778CEDD884A0E4A65666906E7EE, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_10 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_11 = { reinterpret_cast<intptr_t> (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_0_0_0_var) };
		Type_t* L_12;
		L_12 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_11, NULL);
		NullCheck(L_10);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_10, L_12, _stringLiteral27D9B7EF612AEB12509925B54604A1C6C9199F88, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_13 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_14 = { reinterpret_cast<intptr_t> (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F_0_0_0_var) };
		Type_t* L_15;
		L_15 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_14, NULL);
		NullCheck(L_13);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_13, L_15, _stringLiteral19B7D722FFCBB1EBCC95DE76FB16F022050F3CC8, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_16 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_17 = { reinterpret_cast<intptr_t> (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B_0_0_0_var) };
		Type_t* L_18;
		L_18 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_17, NULL);
		NullCheck(L_16);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_16, L_18, _stringLiteral16B1A560D0508AB021624167CB1F87B6D48B02D6, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_19 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_20 = { reinterpret_cast<intptr_t> (Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_0_0_0_var) };
		Type_t* L_21;
		L_21 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_20, NULL);
		NullCheck(L_19);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_19, L_21, _stringLiteral5F43C61FF910780A25E22CD0232290820C30BA1D, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_22 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_23 = { reinterpret_cast<intptr_t> (String_t_0_0_0_var) };
		Type_t* L_24;
		L_24 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_23, NULL);
		NullCheck(L_22);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_22, L_24, _stringLiteralBCA7DDD073AD5DB21CC612ADB1833BF1A5D32261, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_25 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_26 = { reinterpret_cast<intptr_t> (Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var) };
		Type_t* L_27;
		L_27 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_26, NULL);
		NullCheck(L_25);
		Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC(L_25, L_27, _stringLiteralDB47297909F3BD6EDB8AD67A8511975233214355, Dictionary_2_Add_m7371147962E855B8E8BE002A226B0EE34E37B0CC_RuntimeMethod_var);
		Dictionary_2_tCAAF57FF731CF7E9CEC738A6E8400D208C1066EE* L_28 = V_0;
		((TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields*)il2cpp_codegen_static_fields_for(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var))->____TypeToShortString = L_28;
		Il2CppCodeGenWriteBarrier((void**)(&((TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_StaticFields*)il2cpp_codegen_static_fields_for(TweenParms_tC2C9DD2644457A03D75AF9C15B4CF78ACBF68D3D_il2cpp_TypeInfo_var))->____TypeToShortString), (void*)L_28);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HOTPropData__ctor_mEB72EC44DC80528C9615FBB1580D2208C1C27DEA (HOTPropData_t20C0DB5CD048AE843BD2A4BCEB3BB35A53CAFC79* __this, String_t* ___0_p_propName, RuntimeObject* ___1_p_endValOrPlugin, bool ___2_p_isRelative, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		String_t* L_0 = ___0_p_propName;
		__this->___propName = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___propName), (void*)L_0);
		RuntimeObject* L_1 = ___1_p_endValOrPlugin;
		__this->___endValOrPlugin = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___endValOrPlugin), (void*)L_1);
		bool L_2 = ___2_p_isRelative;
		__this->___isRelative = L_2;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cubic_EaseIn_mF68A8ADE66D4E5173A9738D5590AB972781CE169 (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)(L_1/L_2));
		___0_time = L_3;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_0, L_3)), L_4)), L_5)), L_6));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cubic_EaseOut_m76FCBA54077D20DB2AADF0670AFCC518C33922C9 (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)il2cpp_codegen_subtract(((float)(L_1/L_2)), (1.0f)));
		___0_time = L_3;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_3, L_4)), L_5)), (1.0f))))), L_6));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Cubic_EaseInOut_m9D8AEF7EDC6B0F59FFB2E03FB46440E491D41112 (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_time;
		float L_1 = ___3_duration;
		float L_2 = ((float)(L_0/((float)il2cpp_codegen_multiply(L_1, (0.5f)))));
		___0_time = L_2;
		if ((!(((float)L_2) < ((float)(1.0f)))))
		{
			goto IL_0023;
		}
	}
	{
		float L_3 = ___2_changeValue;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___0_time;
		float L_7 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_3, (0.5f))), L_4)), L_5)), L_6)), L_7));
	}

IL_0023:
	{
		float L_8 = ___2_changeValue;
		float L_9 = ___0_time;
		float L_10 = ((float)il2cpp_codegen_subtract(L_9, (2.0f)));
		___0_time = L_10;
		float L_11 = ___0_time;
		float L_12 = ___0_time;
		float L_13 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_8, (0.5f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_10, L_11)), L_12)), (2.0f))))), L_13));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Circ_EaseIn_mCA6A087D56618ADEC9B3CEFB23DFBE2050B3D5C2 (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)(L_1/L_2));
		___0_time = L_3;
		float L_4 = ___0_time;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_5;
		L_5 = sqrt(((double)((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(L_3, L_4))))));
		float L_6 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((-L_0)), ((float)il2cpp_codegen_subtract(((float)L_5), (1.0f))))), L_6));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Circ_EaseOut_mFF24BECA2B1B493A21CD525869E6A9C854DB51EB (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)il2cpp_codegen_subtract(((float)(L_1/L_2)), (1.0f)));
		___0_time = L_3;
		float L_4 = ___0_time;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_5;
		L_5 = sqrt(((double)((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(L_3, L_4))))));
		float L_6 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, ((float)L_5))), L_6));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Circ_EaseInOut_m93524D3A4D7315DAD226CF7CBA838B0FEEBB88D4 (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_unusedOvershootOrAmplitude, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_time;
		float L_1 = ___3_duration;
		float L_2 = ((float)(L_0/((float)il2cpp_codegen_multiply(L_1, (0.5f)))));
		___0_time = L_2;
		if ((!(((float)L_2) < ((float)(1.0f)))))
		{
			goto IL_0035;
		}
	}
	{
		float L_3 = ___2_changeValue;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_6;
		L_6 = sqrt(((double)((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(L_4, L_5))))));
		float L_7 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((-L_3)), (0.5f))), ((float)il2cpp_codegen_subtract(((float)L_6), (1.0f))))), L_7));
	}

IL_0035:
	{
		float L_8 = ___2_changeValue;
		float L_9 = ___0_time;
		float L_10 = ((float)il2cpp_codegen_subtract(L_9, (2.0f)));
		___0_time = L_10;
		float L_11 = ___0_time;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_12;
		L_12 = sqrt(((double)((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(L_10, L_11))))));
		float L_13 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_8, (0.5f))), ((float)il2cpp_codegen_add(((float)L_12), (1.0f))))), L_13));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Back_EaseIn_m6BFA78FC66458D32BFAB1D2FD39602A6D28D001A (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshoot, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)(L_1/L_2));
		___0_time = L_3;
		float L_4 = ___0_time;
		float L_5 = ___4_overshoot;
		float L_6 = ___0_time;
		float L_7 = ___4_overshoot;
		float L_8 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_0, L_3)), L_4)), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_5, (1.0f))), L_6)), L_7)))), L_8));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Back_EaseOut_m7D6F1FE29C491DBF471231363C3D4781548509DE (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshoot, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___2_changeValue;
		float L_1 = ___0_time;
		float L_2 = ___3_duration;
		float L_3 = ((float)il2cpp_codegen_subtract(((float)(L_1/L_2)), (1.0f)));
		___0_time = L_3;
		float L_4 = ___0_time;
		float L_5 = ___4_overshoot;
		float L_6 = ___0_time;
		float L_7 = ___4_overshoot;
		float L_8 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_3, L_4)), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_5, (1.0f))), L_6)), L_7)))), (1.0f))))), L_8));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Back_EaseInOut_m6714F2F1366BA14CF68B317C646B8AC04015835D (float ___0_time, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshoot, float ___5_unusedPeriod, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_time;
		float L_1 = ___3_duration;
		float L_2 = ((float)(L_0/((float)il2cpp_codegen_multiply(L_1, (0.5f)))));
		___0_time = L_2;
		if ((!(((float)L_2) < ((float)(1.0f)))))
		{
			goto IL_0038;
		}
	}
	{
		float L_3 = ___2_changeValue;
		float L_4 = ___0_time;
		float L_5 = ___0_time;
		float L_6 = ___4_overshoot;
		float L_7 = ((float)il2cpp_codegen_multiply(L_6, (1.52499998f)));
		___4_overshoot = L_7;
		float L_8 = ___0_time;
		float L_9 = ___4_overshoot;
		float L_10 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_3, (0.5f))), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_7, (1.0f))), L_8)), L_9)))))), L_10));
	}

IL_0038:
	{
		float L_11 = ___2_changeValue;
		float L_12 = ___0_time;
		float L_13 = ((float)il2cpp_codegen_subtract(L_12, (2.0f)));
		___0_time = L_13;
		float L_14 = ___0_time;
		float L_15 = ___4_overshoot;
		float L_16 = ((float)il2cpp_codegen_multiply(L_15, (1.52499998f)));
		___4_overshoot = L_16;
		float L_17 = ___0_time;
		float L_18 = ___4_overshoot;
		float L_19 = ___1_startValue;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)(L_11/(2.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply(L_13, L_14)), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_16, (1.0f))), L_17)), L_18)))), (2.0f))))), L_19));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlugQuaternion_get_startVal_mC0E8AA76C47F7E0766D894BEAD22B7C3BFE910F0 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion_set_startVal_m50A46CF17B0B042F15212C2A6DEEFEE04A161C00 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 G_B4_0;
	memset((&G_B4_0), 0, sizeof(G_B4_0));
	PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* G_B4_1 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 G_B3_0;
	memset((&G_B3_0), 0, sizeof(G_B3_0));
	PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* G_B3_1 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 G_B5_0;
	memset((&G_B5_0), 0, sizeof(G_B5_0));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 G_B5_1;
	memset((&G_B5_1), 0, sizeof(G_B5_1));
	PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* G_B5_2 = NULL;
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_0);
		bool L_1;
		L_1 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_0, NULL);
		if (!L_1)
		{
			goto IL_005b;
		}
	}
	{
		bool L_2 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_2)
		{
			goto IL_005b;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = __this->___typedEndVal;
		RuntimeObject* L_4 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_4, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var)))
		{
			G_B4_0 = L_3;
			G_B4_1 = __this;
			goto IL_002c;
		}
		G_B3_0 = L_3;
		G_B3_1 = __this;
	}
	{
		RuntimeObject* L_5 = ___0_value;
		G_B5_0 = ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_5, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))));
		G_B5_1 = G_B3_0;
		G_B5_2 = G_B3_1;
		goto IL_003a;
	}

IL_002c:
	{
		RuntimeObject* L_6 = ___0_value;
		V_0 = ((*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)((Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)UnBox(L_6, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_0), NULL);
		G_B5_0 = L_7;
		G_B5_1 = G_B4_0;
		G_B5_2 = G_B4_1;
	}

IL_003a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(G_B5_1, G_B5_0, NULL);
		NullCheck(G_B5_2);
		G_B5_2->___typedStartVal = L_8;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = __this->___typedStartVal;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_10;
		L_10 = Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline(L_9, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_11 = L_10;
		RuntimeObject* L_12 = Box(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var, &L_11);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_12);
		return;
	}

IL_005b:
	{
		RuntimeObject* L_13 = ___0_value;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_13, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var)))
		{
			goto IL_007f;
		}
	}
	{
		RuntimeObject* L_14 = ___0_value;
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_14;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_14);
		RuntimeObject* L_15 = ___0_value;
		V_1 = ((*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)((Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)UnBox(L_15, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16;
		L_16 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_1), NULL);
		__this->___typedStartVal = L_16;
		return;
	}

IL_007f:
	{
		RuntimeObject* L_17 = ___0_value;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_18;
		L_18 = Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline(((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_17, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var)))), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_19 = L_18;
		RuntimeObject* L_20 = Box(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var, &L_19);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_20;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_20);
		RuntimeObject* L_21 = ___0_value;
		__this->___typedStartVal = ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_21, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion_set_endVal_m1BD6F0B44D962C4932367EC99B2E822AA904A0BB (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		RuntimeObject* L_0 = ___0_value;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var)))
		{
			goto IL_0024;
		}
	}
	{
		RuntimeObject* L_1 = ___0_value;
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal), (void*)L_1);
		RuntimeObject* L_2 = ___0_value;
		V_0 = ((*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)((Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)UnBox(L_2, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var))));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline((&V_0), NULL);
		__this->___typedEndVal = L_3;
		return;
	}

IL_0024:
	{
		RuntimeObject* L_4 = ___0_value;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_5;
		L_5 = Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline(((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_4, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var)))), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_6 = L_5;
		RuntimeObject* L_7 = Box(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var, &L_6);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal), (void*)L_7);
		RuntimeObject* L_8 = ___0_value;
		__this->___typedEndVal = ((*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)UnBox(L_8, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion__ctor_m46BD79B83263F7486AA657F2BDB40E50A2198049 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = ___0_p_endVal;
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_1 = L_0;
		RuntimeObject* L_2 = Box(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var, &L_1);
		bool L_3 = ___1_p_isRelative;
		ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482(__this, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion__ctor_mE3BC50FE78C20B2554A668FCC648878459D4A608 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_p_endVal, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_p_endVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = L_0;
		RuntimeObject* L_2 = Box(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var, &L_1);
		ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482(__this, L_2, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion__ctor_m653333B63186F7A0F1430587FAF26EE4A67302D8 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_p_endVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = L_0;
		RuntimeObject* L_2 = Box(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var, &L_1);
		bool L_3 = ___1_p_isRelative;
		ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482(__this, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* PlugQuaternion_Beyond360_m6130714D6E69D9FB0D5F7B24158EC0D84F42EF22 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, bool ___0_p_beyond360, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_p_beyond360;
		__this->___beyond360 = L_0;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlugQuaternion_GetSpeedBasedDuration_m118AC301EE4847507E6A5623AFEB9BC793750601 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, float ___0_p_speed, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_0 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_1;
		L_1 = Vector3_get_magnitude_mF0D6017E90B345F1F52D1CC564C640F1A847AF2D_inline(L_0, NULL);
		float L_2 = ___0_p_speed;
		V_0 = ((float)(L_1/((float)il2cpp_codegen_multiply(L_2, (360.0f)))));
		float L_3 = V_0;
		if ((!(((float)L_3) < ((float)(0.0f)))))
		{
			goto IL_001f;
		}
	}
	{
		float L_4 = V_0;
		V_0 = ((-L_4));
	}

IL_001f:
	{
		float L_5 = V_0;
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion_SetChangeVal_m365CBC37FAECB8923EFF1499567CB1C8A320660F (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	float V_1 = 0.0f;
	float G_B14_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B17_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B16_0 = NULL;
	float G_B18_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B18_1 = NULL;
	float G_B22_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B25_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B24_0 = NULL;
	float G_B26_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B26_1 = NULL;
	float G_B30_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B33_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B32_0 = NULL;
	float G_B34_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* G_B34_1 = NULL;
	{
		bool L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_0)
		{
			goto IL_003e;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_1 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_1);
		bool L_2;
		L_2 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_1, NULL);
		if (L_2)
		{
			goto IL_003e;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = __this->___typedEndVal;
		__this->___changeVal = L_3;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = __this->___typedStartVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = __this->___typedEndVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_4, L_5, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = L_6;
		RuntimeObject* L_8 = Box(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var, &L_7);
		VirtualActionInvoker1< RuntimeObject* >::Invoke(6, __this, L_8);
		return;
	}

IL_003e:
	{
		bool L_9 = __this->___beyond360;
		if (!L_9)
		{
			goto IL_005e;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = __this->___typedEndVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = __this->___typedStartVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_10, L_11, NULL);
		__this->___changeVal = L_12;
		return;
	}

IL_005e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = __this->___typedEndVal;
		V_0 = L_13;
		float L_14 = (&V_0)->___x;
		if ((!(((float)L_14) > ((float)(360.0f)))))
		{
			goto IL_0087;
		}
	}
	{
		float L_15 = (&V_0)->___x;
		(&V_0)->___x = (fmodf(L_15, (360.0f)));
	}

IL_0087:
	{
		float L_16 = (&V_0)->___y;
		if ((!(((float)L_16) > ((float)(360.0f)))))
		{
			goto IL_00a9;
		}
	}
	{
		float L_17 = (&V_0)->___y;
		(&V_0)->___y = (fmodf(L_17, (360.0f)));
	}

IL_00a9:
	{
		float L_18 = (&V_0)->___z;
		if ((!(((float)L_18) > ((float)(360.0f)))))
		{
			goto IL_00cb;
		}
	}
	{
		float L_19 = (&V_0)->___z;
		(&V_0)->___z = (fmodf(L_19, (360.0f)));
	}

IL_00cb:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21 = __this->___typedStartVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22;
		L_22 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_20, L_21, NULL);
		__this->___changeVal = L_22;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_23 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_24 = L_23->___x;
		if ((((float)L_24) > ((float)(0.0f))))
		{
			goto IL_00fd;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_25 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_26 = L_25->___x;
		G_B14_0 = ((-L_26));
		goto IL_0108;
	}

IL_00fd:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_27 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_28 = L_27->___x;
		G_B14_0 = L_28;
	}

IL_0108:
	{
		V_1 = G_B14_0;
		float L_29 = V_1;
		if ((!(((float)L_29) > ((float)(180.0f)))))
		{
			goto IL_013f;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_30 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_31 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_32 = L_31->___x;
		if ((((float)L_32) > ((float)(0.0f))))
		{
			G_B17_0 = L_30;
			goto IL_0132;
		}
		G_B16_0 = L_30;
	}
	{
		float L_33 = V_1;
		G_B18_0 = ((float)il2cpp_codegen_subtract((360.0f), L_33));
		G_B18_1 = G_B16_0;
		goto IL_013a;
	}

IL_0132:
	{
		float L_34 = V_1;
		G_B18_0 = ((-((float)il2cpp_codegen_subtract((360.0f), L_34))));
		G_B18_1 = G_B17_0;
	}

IL_013a:
	{
		G_B18_1->___x = G_B18_0;
	}

IL_013f:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_35 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_36 = L_35->___y;
		if ((((float)L_36) > ((float)(0.0f))))
		{
			goto IL_015f;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_37 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_38 = L_37->___y;
		G_B22_0 = ((-L_38));
		goto IL_016a;
	}

IL_015f:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_39 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_40 = L_39->___y;
		G_B22_0 = L_40;
	}

IL_016a:
	{
		V_1 = G_B22_0;
		float L_41 = V_1;
		if ((!(((float)L_41) > ((float)(180.0f)))))
		{
			goto IL_01a1;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_42 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_43 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_44 = L_43->___y;
		if ((((float)L_44) > ((float)(0.0f))))
		{
			G_B25_0 = L_42;
			goto IL_0194;
		}
		G_B24_0 = L_42;
	}
	{
		float L_45 = V_1;
		G_B26_0 = ((float)il2cpp_codegen_subtract((360.0f), L_45));
		G_B26_1 = G_B24_0;
		goto IL_019c;
	}

IL_0194:
	{
		float L_46 = V_1;
		G_B26_0 = ((-((float)il2cpp_codegen_subtract((360.0f), L_46))));
		G_B26_1 = G_B25_0;
	}

IL_019c:
	{
		G_B26_1->___y = G_B26_0;
	}

IL_01a1:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_47 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_48 = L_47->___z;
		if ((((float)L_48) > ((float)(0.0f))))
		{
			goto IL_01c1;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_49 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_50 = L_49->___z;
		G_B30_0 = ((-L_50));
		goto IL_01cc;
	}

IL_01c1:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_51 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_52 = L_51->___z;
		G_B30_0 = L_52;
	}

IL_01cc:
	{
		V_1 = G_B30_0;
		float L_53 = V_1;
		if ((!(((float)L_53) > ((float)(180.0f)))))
		{
			goto IL_0203;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_54 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_55 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_56 = L_55->___z;
		if ((((float)L_56) > ((float)(0.0f))))
		{
			G_B33_0 = L_54;
			goto IL_01f6;
		}
		G_B32_0 = L_54;
	}
	{
		float L_57 = V_1;
		G_B34_0 = ((float)il2cpp_codegen_subtract((360.0f), L_57));
		G_B34_1 = G_B32_0;
		goto IL_01fe;
	}

IL_01f6:
	{
		float L_58 = V_1;
		G_B34_0 = ((-((float)il2cpp_codegen_subtract((360.0f), L_58))));
		G_B34_1 = G_B33_0;
	}

IL_01fe:
	{
		G_B34_1->___z = G_B34_0;
	}

IL_0203:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion_SetIncremental_m8E6A96935BE3ABB1E13BEB7A3709C58125A9E0E2 (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, int32_t ___0_p_diffIncr, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___typedStartVal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = __this->___changeVal;
		int32_t L_2 = ___0_p_diffIncr;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_1, ((float)L_2), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_0, L_3, NULL);
		__this->___typedStartVal = L_4;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion_DoUpdate_m32C0D2E921B842C4D4D4DB98754D8845D7736B8E (PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1* __this, float ___0_p_totElapsed, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___ease;
		float L_1 = ___0_p_totElapsed;
		float L_2 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____duration;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_3 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_3);
		float L_4;
		L_4 = Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612_inline(L_3, NULL);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_5 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_5);
		float L_6;
		L_6 = Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA_inline(L_5, NULL);
		NullCheck(L_0);
		float L_7;
		L_7 = EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_inline(L_0, L_1, (0.0f), (1.0f), L_2, L_4, L_6, NULL);
		V_0 = L_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_8 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___typedStartVal);
		float L_9 = L_8->___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_10 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_11 = L_10->___x;
		float L_12 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_13 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___typedStartVal);
		float L_14 = L_13->___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_15 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_16 = L_15->___y;
		float L_17 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_18 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___typedStartVal);
		float L_19 = L_18->___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_20 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___changeVal);
		float L_21 = L_20->___z;
		float L_22 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_23;
		memset((&L_23), 0, sizeof(L_23));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_23), ((float)il2cpp_codegen_add(L_9, ((float)il2cpp_codegen_multiply(L_11, L_12)))), ((float)il2cpp_codegen_add(L_14, ((float)il2cpp_codegen_multiply(L_16, L_17)))), ((float)il2cpp_codegen_add(L_19, ((float)il2cpp_codegen_multiply(L_21, L_22)))), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_24;
		L_24 = Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline(L_23, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_25 = L_24;
		RuntimeObject* L_26 = Box(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_il2cpp_TypeInfo_var, &L_25);
		VirtualActionInvoker1< RuntimeObject* >::Invoke(16, __this, L_26);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugQuaternion__cctor_m890A83C1A49BDDFA299C50F6D10781E623E1ED31 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_0 = NULL;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_1 = NULL;
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_0 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		V_0 = L_0;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_1 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_3);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_3);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = V_0;
		((PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_StaticFields*)il2cpp_codegen_static_fields_for(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var))->___validPropTypes = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_StaticFields*)il2cpp_codegen_static_fields_for(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var))->___validPropTypes), (void*)L_4);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_5 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)2);
		V_1 = L_5;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_6 = V_1;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_7 = { reinterpret_cast<intptr_t> (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_0_0_0_var) };
		Type_t* L_8;
		L_8 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_7, NULL);
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_8);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_8);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_9 = V_1;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_10 = { reinterpret_cast<intptr_t> (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_0_0_0_var) };
		Type_t* L_11;
		L_11 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_10, NULL);
		NullCheck(L_9);
		ArrayElementTypeCheck (L_9, L_11);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(1), (Type_t*)L_11);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_12 = V_1;
		((PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_StaticFields*)il2cpp_codegen_static_fields_for(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var))->___validValueTypes = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_StaticFields*)il2cpp_codegen_static_fields_for(PlugQuaternion_t2C70BF395E8D422D9227D5C02A7B44D4ED6710C1_il2cpp_TypeInfo_var))->___validValueTypes), (void*)L_12);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_Multicast(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* currentDelegate = reinterpret_cast<TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_p_callbackData, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenInst(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	NullCheck(___0_p_callbackData);
	typedef void (*FunctionPointerType) (TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_p_callbackData, method);
}
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenStatic(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_p_callbackData, method);
}
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenVirtual(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	NullCheck(___0_p_callbackData);
	VirtualActionInvoker0::Invoke(il2cpp_codegen_method_get_slot(method), ___0_p_callbackData);
}
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenInterface(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	NullCheck(___0_p_callbackData);
	InterfaceActionInvoker0::Invoke(il2cpp_codegen_method_get_slot(method), il2cpp_codegen_method_get_declaring_type(method), ___0_p_callbackData);
}
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenGenericVirtual(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	NullCheck(___0_p_callbackData);
	GenericVirtualActionInvoker0::Invoke(method, ___0_p_callbackData);
}
void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenGenericInterface(TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method)
{
	NullCheck(___0_p_callbackData);
	GenericInterfaceActionInvoker0::Invoke(method, ___0_p_callbackData);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenCallbackWParms__ctor_mB37CAD56CA9F34BDAC55ED611104A2DBBE80B520 (TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
		{
			if (__this->___method_is_virtual)
			{
				if (il2cpp_codegen_method_is_generic_instance_method((RuntimeMethod*)___1_method))
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenGenericInterface;
					else
						__this->___invoke_impl = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenGenericVirtual;
				else
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenInterface;
					else
						__this->___invoke_impl = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenVirtual;
			}
			else
			{
				__this->___invoke_impl = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_OpenInst;
			}
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB (TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_p_callbackData, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_Multicast(TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* currentDelegate = reinterpret_cast<TweenCallback_t636681A33D249FB51EB356E0746B53250D607704*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_OpenInst(TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_OpenStatic(TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_TweenCallback_t636681A33D249FB51EB356E0746B53250D607704 (TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)();
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	il2cppPInvokeFunc();

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenCallback__ctor_mBD3FF0903457762300B12CB3AEA092B04F2BFD94 (TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2 (TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
float EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_Multicast(EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	float retVal = 0.0f;
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* currentDelegate = reinterpret_cast<EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75*>(delegatesToInvoke[i]);
		typedef float (*FunctionPointerType) (RuntimeObject*, float, float, float, float, float, float, const RuntimeMethod*);
		retVal = ((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_elapsed, ___1_startValue, ___2_changeValue, ___3_duration, ___4_overshootOrAmplitude, ___5_period, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
	return retVal;
}
float EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_OpenInst(EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method)
{
	typedef float (*FunctionPointerType) (float, float, float, float, float, float, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___method_ptr)(___0_elapsed, ___1_startValue, ___2_changeValue, ___3_duration, ___4_overshootOrAmplitude, ___5_period, method);
}
float EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_OpenStatic(EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method)
{
	typedef float (*FunctionPointerType) (float, float, float, float, float, float, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___method_ptr)(___0_elapsed, ___1_startValue, ___2_changeValue, ___3_duration, ___4_overshootOrAmplitude, ___5_period, method);
}
IL2CPP_EXTERN_C  float DelegatePInvokeWrapper_EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75 (EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method)
{
	typedef float (DEFAULT_CALL *PInvokeFunc)(float, float, float, float, float, float);
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	float returnValue = il2cppPInvokeFunc(___0_elapsed, ___1_startValue, ___2_changeValue, ___3_duration, ___4_overshootOrAmplitude, ___5_period);

	return returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EaseFunc__ctor_m258028586FD5AF6078A75793226DE7D379A13EA3 (EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 6;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A (EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method) 
{
	typedef float (*FunctionPointerType) (RuntimeObject*, float, float, float, float, float, float, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_elapsed, ___1_startValue, ___2_changeValue, ___3_duration, ___4_overshootOrAmplitude, ___5_period, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC_Multicast(FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* __this, int32_t ___0_p_index, bool ___1_p_optionalBool, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* currentDelegate = reinterpret_cast<FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, int32_t, bool, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_p_index, ___1_p_optionalBool, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC_OpenInst(FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* __this, int32_t ___0_p_index, bool ___1_p_optionalBool, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (int32_t, bool, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_p_index, ___1_p_optionalBool, method);
}
void FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC_OpenStatic(FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* __this, int32_t ___0_p_index, bool ___1_p_optionalBool, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (int32_t, bool, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_p_index, ___1_p_optionalBool, method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707 (FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* __this, int32_t ___0_p_index, bool ___1_p_optionalBool, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)(int32_t, int32_t);
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	il2cppPInvokeFunc(___0_p_index, static_cast<int32_t>(___1_p_optionalBool));

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FilterFunc__ctor_m09E23A301B8AAC21FA70732CB97E2E6EB0086B04 (FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 2;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FilterFunc_Invoke_m5AD6C6A70C725C13D2B53EF98639AE1EFF3412FC (FilterFunc_t3341966A27D968EF1148A7F47EBDA5C83D7CE707* __this, int32_t ___0_p_index, bool ___1_p_optionalBool, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, int32_t, bool, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_p_index, ___1_p_optionalBool, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SequenceParms_InitializeSequence_m8210CDA3A53CC6BD8A0035A2831BE4848C26B287 (SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* __this, Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* ___0_p_sequence, const RuntimeMethod* method) 
{
	{
		Sequence_t8FD9C6B20DA9C35125E186FE2A70F2B918CB3279* L_0 = ___0_p_sequence;
		ABSTweenComponentParms_InitializeOwner_mF88937400BEA35A760F2DC698CA459C44FE82327(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* SequenceParms_Id_mD12A9F7430E0AA3A442D933E730A099FB8C87497 (SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* __this, String_t* ___0_p_id, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_p_id;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___id = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___id), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* SequenceParms_Loops_mB8A56A26FF1C3FE24291B3E3FC89829191978C0F (SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* __this, int32_t ___0_p_loops, int32_t ___1_p_loopType, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_p_loops;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___loops = L_0;
		int32_t L_1 = ___1_p_loopType;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___loopType = L_1;
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* SequenceParms_OnComplete_mD046B01C9C44C8A7DD4DB3F8E41B962148831AF8 (SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* __this, TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* ___0_p_function, const RuntimeMethod* method) 
{
	{
		TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* L_0 = ___0_p_function;
		((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onComplete = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenComponentParms_tCD06661093639AB136C0C8BB7E786EC82F83075E*)__this)->___onComplete), (void*)L_0);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SequenceParms__ctor_mEA6154BD45B02D2D0E5FF50B0EFA93ACC4BB6CDD (SequenceParms_t8B5C03F571245C19D25158D966984C2ED23620FA* __this, const RuntimeMethod* method) 
{
	{
		ABSTweenComponentParms__ctor_m689C96ED2202D6F626DB88BBF1F031D265508270(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PlugInt_get_startVal_mEC395E74FAAAFE265631ABEEBDEFA84C82F1006D (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt_set_startVal_m7CFC9E6A628F0BD6AB817471F2F73510359C91E8 (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_0);
		bool L_1;
		L_1 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_0, NULL);
		if (!L_1)
		{
			goto IL_0037;
		}
	}
	{
		bool L_2 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_2)
		{
			goto IL_0037;
		}
	}
	{
		float L_3 = __this->___typedEndVal;
		RuntimeObject* L_4 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		float L_5;
		L_5 = Convert_ToSingle_m6B47C78A7DFD7825B4361BCA8AB6748FC82165E9(L_4, NULL);
		float L_6 = ((float)il2cpp_codegen_add(L_3, L_5));
		V_0 = L_6;
		__this->___typedStartVal = L_6;
		float L_7 = V_0;
		float L_8 = L_7;
		RuntimeObject* L_9 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_8);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_9);
		return;
	}

IL_0037:
	{
		RuntimeObject* L_10 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		float L_11;
		L_11 = Convert_ToSingle_m6B47C78A7DFD7825B4361BCA8AB6748FC82165E9(L_10, NULL);
		float L_12 = L_11;
		V_1 = L_12;
		__this->___typedStartVal = L_12;
		float L_13 = V_1;
		float L_14 = L_13;
		RuntimeObject* L_15 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_14);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal = L_15;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____startVal), (void*)L_15);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt_set_endVal_mE7D91C5706E4D4FF356FFBEF59AE62BE2852AA41 (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		RuntimeObject* L_0 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		float L_1;
		L_1 = Convert_ToSingle_m6B47C78A7DFD7825B4361BCA8AB6748FC82165E9(L_0, NULL);
		float L_2 = L_1;
		V_0 = L_2;
		__this->___typedEndVal = L_2;
		float L_3 = V_0;
		float L_4 = L_3;
		RuntimeObject* L_5 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_4);
		((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____endVal), (void*)L_5);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt__ctor_m36BBA904D1AA75C2195D945C7D808BB4404D404D (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, float ___0_p_endVal, bool ___1_p_isRelative, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_p_endVal;
		float L_1 = L_0;
		RuntimeObject* L_2 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_1);
		bool L_3 = ___1_p_isRelative;
		ABSTweenPlugin__ctor_m21D90130D40C028B8D49294F1664B217A8FB3482(__this, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float PlugInt_GetSpeedBasedDuration_m6C3D8EAE856E47198C2C95EE5ADBD2C439BA4FC5 (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, float ___0_p_speed, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___changeVal;
		float L_1 = ___0_p_speed;
		V_0 = ((float)(L_0/L_1));
		float L_2 = V_0;
		if ((!(((float)L_2) < ((float)(0.0f)))))
		{
			goto IL_0014;
		}
	}
	{
		float L_3 = V_0;
		V_0 = ((-L_3));
	}

IL_0014:
	{
		float L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt_SetChangeVal_m1F3BA173BD58844E40013DBEF1CED2F0059AAEBC (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___isRelative;
		if (!L_0)
		{
			goto IL_003a;
		}
	}
	{
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_1 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_1);
		bool L_2;
		L_2 = Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline(L_1, NULL);
		if (L_2)
		{
			goto IL_003a;
		}
	}
	{
		float L_3 = __this->___typedEndVal;
		__this->___changeVal = L_3;
		float L_4 = __this->___typedStartVal;
		float L_5 = __this->___typedEndVal;
		float L_6 = ((float)il2cpp_codegen_add(L_4, L_5));
		RuntimeObject* L_7 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_6);
		VirtualActionInvoker1< RuntimeObject* >::Invoke(6, __this, L_7);
		return;
	}

IL_003a:
	{
		float L_8 = __this->___typedEndVal;
		float L_9 = __this->___typedStartVal;
		__this->___changeVal = ((float)il2cpp_codegen_subtract(L_8, L_9));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt_SetIncremental_m8E4454E97CD02F65FC741ECEB005B9F83722C434 (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, int32_t ___0_p_diffIncr, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___typedStartVal;
		float L_1 = __this->___changeVal;
		int32_t L_2 = ___0_p_diffIncr;
		__this->___typedStartVal = ((float)il2cpp_codegen_add(L_0, ((float)il2cpp_codegen_multiply(L_1, ((float)L_2)))));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt_DoUpdate_m6B8283DFE8E57B477FAF53BC743D4D05D69717DA (PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5* __this, float ___0_p_totElapsed, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* L_0 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___ease;
		float L_1 = ___0_p_totElapsed;
		float L_2 = __this->___typedStartVal;
		float L_3 = __this->___changeVal;
		float L_4 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->____duration;
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_5 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_5);
		float L_6;
		L_6 = Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612_inline(L_5, NULL);
		Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* L_7 = ((ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A*)__this)->___tweenObj;
		NullCheck(L_7);
		float L_8;
		L_8 = Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA_inline(L_7, NULL);
		NullCheck(L_0);
		float L_9;
		L_9 = EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_inline(L_0, L_1, L_2, L_3, L_4, L_6, L_8, NULL);
		V_0 = L_9;
		float L_10 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_11;
		L_11 = bankers_round(((double)L_10));
		float L_12 = ((float)L_11);
		RuntimeObject* L_13 = Box(Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var, &L_12);
		VirtualActionInvoker1< RuntimeObject* >::Invoke(16, __this, L_13);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlugInt__cctor_m4F1377CB8036E178D51448051BDE98A26504B115 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_0 = NULL;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* V_1 = NULL;
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_0 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)2);
		V_0 = L_0;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_1 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_3);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_3);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = V_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_5 = { reinterpret_cast<intptr_t> (Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var) };
		Type_t* L_6;
		L_6 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_5, NULL);
		NullCheck(L_4);
		ArrayElementTypeCheck (L_4, L_6);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(1), (Type_t*)L_6);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_7 = V_0;
		((PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_StaticFields*)il2cpp_codegen_static_fields_for(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var))->___validPropTypes = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_StaticFields*)il2cpp_codegen_static_fields_for(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var))->___validPropTypes), (void*)L_7);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_8 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		V_1 = L_8;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_9 = V_1;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_10 = { reinterpret_cast<intptr_t> (Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var) };
		Type_t* L_11;
		L_11 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_10, NULL);
		NullCheck(L_9);
		ArrayElementTypeCheck (L_9, L_11);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_11);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_12 = V_1;
		((PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_StaticFields*)il2cpp_codegen_static_fields_for(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var))->___validValueTypes = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&((PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_StaticFields*)il2cpp_codegen_static_fields_for(PlugInt_t120301D625B2A3367D33ACB9E84FEDF6FCD6F7C5_il2cpp_TypeInfo_var))->___validValueTypes), (void*)L_12);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Tweener_get_isFrom_m97B6EDB4673323EF33565DF80650EDA4B6BB7A39_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CisFromU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void PlugVector3Path_set_pathType_mB72EF8EB3956A20D2D45AA4DCEBB3727603CFABE_inline (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CpathTypeU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Tweener_get_target_m7B0C8C1210C5EF4CDCB888B22F58499C1AE55A6E_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____target;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t PlugVector3Path_get_pathType_mD3B75D3F8F5ED3957F00115EC8CDBA3394DB0186_inline (PlugVector3Path_t4D72C9A0B3A8580FB67EBFD73ED77C5F47637DA8* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CpathTypeU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Tweener_get_easeOvershootOrAmplitude_mBB1487C1793BCBA8C3AA28A0A5B033B98BDC4612_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->____easeOvershootOrAmplitude;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Tweener_get_easePeriod_m8DE25C17D661AD05FA04DE6037D5BBA9C8E0A1BA_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->____easePeriod;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float EaseFunc_Invoke_m37BA904CBA2A9E8A86D8E00ADBF27F042526192A_inline (EaseFunc_t653EE39FFDCC3665B1FE38FB38C98350DBDFAF75* __this, float ___0_elapsed, float ___1_startValue, float ___2_changeValue, float ___3_duration, float ___4_overshootOrAmplitude, float ___5_period, const RuntimeMethod* method) 
{
	typedef float (*FunctionPointerType) (RuntimeObject*, float, float, float, float, float, float, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_elapsed, ___1_startValue, ___2_changeValue, ___3_duration, ___4_overshootOrAmplitude, ___5_period, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___upVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* ABSTweenPlugin_get_propName_m66440F63ADB38E6AEB81E90E0E7C0D44B2450AFB_inline (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->____propName;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ABSTweenComponent_get_isComplete_m709E527B954A24C4FC9BFA6AAEAF82332441991F_inline (ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____isComplete;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TweenCallback_Invoke_m9089E9ED78C555CB94BFBB7E31A1A9A786E4A0E2_inline (TweenCallback_t636681A33D249FB51EB356E0746B53250D607704* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TweenCallbackWParms_Invoke_m0CC0B213A8B2100D174C8BF254573DFD31B4FCDB_inline (TweenCallbackWParms_t70B215F2CBEE4D12A79EC740C4FC503253147FAF* __this, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18* ___0_p_callbackData, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, TweenEvent_t95DA77C03B3C6F6DFAF3BCBF6186C5B89B6A7D18*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_p_callbackData, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ABSTweenComponent_get_destroyed_m4FE7ACE9A38BE5BED05C117B3F147838083CFC01_inline (ABSTweenComponent_t32CCEC48173667C95B9B27178AEA570EEC8FC737* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____destroyed;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_op_Addition_mA7A51CACA49ED8D23D3D9CA3A0092D32F657E053_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_b, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = ___0_a;
		float L_1 = L_0.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = ___1_b;
		float L_3 = L_2.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = ___0_a;
		float L_5 = L_4.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = ___1_b;
		float L_7 = L_6.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = ___0_a;
		float L_9 = L_8.___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_10 = ___1_b;
		float L_11 = L_10.___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = ___0_a;
		float L_13 = L_12.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_14 = ___1_b;
		float L_15 = L_14.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_16;
		memset((&L_16), 0, sizeof(L_16));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_16), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), ((float)il2cpp_codegen_add(L_13, L_15)), NULL);
		V_0 = L_16;
		goto IL_003d;
	}

IL_003d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_17 = V_0;
		return L_17;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color32_op_Implicit_m47CBB138122B400E0B1F4BFD7C30A6C2C00FCA3E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___0_c, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_0 = ___0_c;
		uint8_t L_1 = L_0.___r;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_2 = ___0_c;
		uint8_t L_3 = L_2.___g;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_4 = ___0_c;
		uint8_t L_5 = L_4.___b;
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_6 = ___0_c;
		uint8_t L_7 = L_6.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8;
		memset((&L_8), 0, sizeof(L_8));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_8), ((float)(((float)L_1)/(255.0f))), ((float)(((float)L_3)/(255.0f))), ((float)(((float)L_5)/(255.0f))), ((float)(((float)L_7)/(255.0f))), NULL);
		V_0 = L_8;
		goto IL_003d;
	}

IL_003d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9 = V_0;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_op_Subtraction_mF003448D819F2A41405BB6D85F1563CDA900B07F_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_b, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = ___0_a;
		float L_1 = L_0.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = ___1_b;
		float L_3 = L_2.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = ___0_a;
		float L_5 = L_4.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = ___1_b;
		float L_7 = L_6.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = ___0_a;
		float L_9 = L_8.___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_10 = ___1_b;
		float L_11 = L_10.___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = ___0_a;
		float L_13 = L_12.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_14 = ___1_b;
		float L_15 = L_14.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_16;
		memset((&L_16), 0, sizeof(L_16));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_16), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), ((float)il2cpp_codegen_subtract(L_13, L_15)), NULL);
		V_0 = L_16;
		goto IL_003d;
	}

IL_003d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_17 = V_0;
		return L_17;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_op_Multiply_m379B20A820266ACF82A21425B9CAE8DCD773CFBB_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, float ___1_b, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = ___0_a;
		float L_1 = L_0.___r;
		float L_2 = ___1_b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = ___0_a;
		float L_4 = L_3.___g;
		float L_5 = ___1_b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = ___0_a;
		float L_7 = L_6.___b;
		float L_8 = ___1_b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9 = ___0_a;
		float L_10 = L_9.___a;
		float L_11 = ___1_b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12;
		memset((&L_12), 0, sizeof(L_12));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_12), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), ((float)il2cpp_codegen_multiply(L_10, L_11)), NULL);
		V_0 = L_12;
		goto IL_0029;
	}

IL_0029:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_r;
		__this->___r = L_0;
		float L_1 = ___1_g;
		__this->___g = L_1;
		float L_2 = ___2_b;
		__this->___b = L_2;
		float L_3 = ___3_a;
		__this->___a = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B Color32_op_Implicit_m79AF5E0BDE9CE041CAC4D89CBFA66E71C6DD1B70_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_c, const RuntimeMethod* method) 
{
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = ___0_c;
		float L_1 = L_0.___r;
		float L_2;
		L_2 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_1, NULL);
		float L_3;
		L_3 = bankers_roundf(((float)il2cpp_codegen_multiply(L_2, (255.0f))));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = ___0_c;
		float L_5 = L_4.___g;
		float L_6;
		L_6 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_5, NULL);
		float L_7;
		L_7 = bankers_roundf(((float)il2cpp_codegen_multiply(L_6, (255.0f))));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_8 = ___0_c;
		float L_9 = L_8.___b;
		float L_10;
		L_10 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_9, NULL);
		float L_11;
		L_11 = bankers_roundf(((float)il2cpp_codegen_multiply(L_10, (255.0f))));
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12 = ___0_c;
		float L_13 = L_12.___a;
		float L_14;
		L_14 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_13, NULL);
		float L_15;
		L_15 = bankers_roundf(((float)il2cpp_codegen_multiply(L_14, (255.0f))));
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_16;
		memset((&L_16), 0, sizeof(L_16));
		Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline((&L_16), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_3), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_7), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_11), (uint8_t)il2cpp_codegen_cast_floating_point<uint8_t, int32_t, float>(L_15), NULL);
		V_0 = L_16;
		goto IL_0065;
	}

IL_0065:
	{
		Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B L_17 = V_0;
		return L_17;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_ClampMagnitude_mF83675F19744F58E97CF24D8359A810634DC031F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, float ___1_maxLength, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	bool V_1 = false;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_6;
	memset((&V_6), 0, sizeof(V_6));
	{
		float L_0;
		L_0 = Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline((&___0_vector), NULL);
		V_0 = L_0;
		float L_1 = V_0;
		float L_2 = ___1_maxLength;
		float L_3 = ___1_maxLength;
		V_1 = (bool)((((float)L_1) > ((float)((float)il2cpp_codegen_multiply(L_2, L_3))))? 1 : 0);
		bool L_4 = V_1;
		if (!L_4)
		{
			goto IL_004e;
		}
	}
	{
		float L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_6;
		L_6 = sqrt(((double)L_5));
		V_2 = ((float)L_6);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = ___0_vector;
		float L_8 = L_7.___x;
		float L_9 = V_2;
		V_3 = ((float)(L_8/L_9));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___0_vector;
		float L_11 = L_10.___y;
		float L_12 = V_2;
		V_4 = ((float)(L_11/L_12));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = ___0_vector;
		float L_14 = L_13.___z;
		float L_15 = V_2;
		V_5 = ((float)(L_14/L_15));
		float L_16 = V_3;
		float L_17 = ___1_maxLength;
		float L_18 = V_4;
		float L_19 = ___1_maxLength;
		float L_20 = V_5;
		float L_21 = ___1_maxLength;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22;
		memset((&L_22), 0, sizeof(L_22));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_22), ((float)il2cpp_codegen_multiply(L_16, L_17)), ((float)il2cpp_codegen_multiply(L_18, L_19)), ((float)il2cpp_codegen_multiply(L_20, L_21)), NULL);
		V_6 = L_22;
		goto IL_0053;
	}

IL_004e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_23 = ___0_vector;
		V_6 = L_23;
		goto IL_0053;
	}

IL_0053:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24 = V_6;
		return L_24;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_a;
		float L_3 = L_2.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_6), ((-L_1)), ((-L_3)), ((-L_5)), NULL);
		V_0 = L_6;
		goto IL_001e;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m7F3B0FA9256CE368D7636558EFEFC4AB0E1A0F41_inline (float ___0_d, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_a, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___1_a;
		float L_1 = L_0.___x;
		float L_2 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___1_a;
		float L_4 = L_3.___y;
		float L_5 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_a;
		float L_7 = L_6.___z;
		float L_8 = ___0_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_white_m068F5AF879B0FCA584E3693F762EA41BB65532C6_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (1.0f), (1.0f), (1.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3_Normalize_mC749B887A4C74BA0A2E13E6377F17CCAEB0AADA8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	bool V_1 = false;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this);
		float L_1;
		L_1 = Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline(L_0, NULL);
		V_0 = L_1;
		float L_2 = V_0;
		V_1 = (bool)((((float)L_2) > ((float)(9.99999975E-06f)))? 1 : 0);
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_002d;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this);
		float L_5 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline(L_4, L_5, NULL);
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this = L_6;
		goto IL_0038;
	}

IL_002d:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this = L_7;
	}

IL_0038:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Cross_mF93A280558BCE756D13B6CC5DCD7DE8A43148987_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = ___0_lhs;
		float L_13 = L_12.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14 = ___1_rhs;
		float L_15 = L_14.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = ___0_lhs;
		float L_17 = L_16.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18 = ___1_rhs;
		float L_19 = L_18.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20 = ___0_lhs;
		float L_21 = L_20.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22 = ___1_rhs;
		float L_23 = L_22.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24;
		memset((&L_24), 0, sizeof(L_24));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_24), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(L_9, L_11)), ((float)il2cpp_codegen_multiply(L_13, L_15)))), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(L_17, L_19)), ((float)il2cpp_codegen_multiply(L_21, L_23)))), NULL);
		V_0 = L_24;
		goto IL_005a;
	}

IL_005a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25 = V_0;
		return L_25;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (0.0f), (0.0f), (0.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_blue_mF04A26CE61D6DA3C0D8B1C4720901B1028C7AB87_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (0.0f), (0.0f), (1.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_red_mA2E53E7173FDC97E68E335049AB0FAAEE43A844D_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (1.0f), (0.0f), (0.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Distance_m2314DB9B8BD01157E013DF87BEA557375C7F9FF9_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		V_0 = ((float)il2cpp_codegen_subtract(L_1, L_3));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, L_7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		V_2 = ((float)il2cpp_codegen_subtract(L_9, L_11));
		float L_12 = V_0;
		float L_13 = V_0;
		float L_14 = V_1;
		float L_15 = V_1;
		float L_16 = V_2;
		float L_17 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_18;
		L_18 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_12, L_13)), ((float)il2cpp_codegen_multiply(L_14, L_15)))), ((float)il2cpp_codegen_multiply(L_16, L_17))))));
		V_3 = ((float)L_18);
		goto IL_0040;
	}

IL_0040:
	{
		float L_19 = V_3;
		return L_19;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Tweener_set_isFrom_m3E5ABBC9B076D66C6006F2E422A6B15C0899CD24_inline (Tweener_t99074CD44759EE1C18B018744C9E38243A40871A* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CisFromU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ABSTweenPlugin_get_initialized_mBDDF3D1051BAFBF04CAAF5600D799AE51D452397_inline (ABSTweenPlugin_t569D8CBBE01E5375128235DBEDDC873429FB7C2A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____initialized;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Quaternion_get_eulerAngles_m2DB5158B5C3A71FD60FC8A6EE43D3AAA1CFED122_inline (Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_0 = (*(Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974*)__this);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Quaternion_Internal_ToEulerRad_m5BD0EEC543120C320DC77FCCDFD2CE2E6BD3F1A8(L_0, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_1, (57.2957802f), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Quaternion_Internal_MakePositive_m73E2D01920CB0DFE661A55022C129E8617F0C9A8(L_2, NULL);
		V_0 = L_3;
		goto IL_001e;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_Euler_m5BCCC19216CFAD2426F15BC51A30421880D27B73_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_euler, const RuntimeMethod* method) 
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_euler;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_0, (0.0174532924f), NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_2;
		L_2 = Quaternion_Internal_FromEulerRad_m66D4475341F53949471E6870FB5C5E4A5E9BA93E(L_1, NULL);
		V_0 = L_2;
		goto IL_0014;
	}

IL_0014:
	{
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_3 = V_0;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_magnitude_mF0D6017E90B345F1F52D1CC564C640F1A847AF2D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___y;
		float L_3 = __this->___y;
		float L_4 = __this->___z;
		float L_5 = __this->___z;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_6;
		L_6 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3)))), ((float)il2cpp_codegen_multiply(L_4, L_5))))));
		V_0 = ((float)L_6);
		goto IL_0034;
	}

IL_0034:
	{
		float L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	{
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		float L_5 = V_1;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color32__ctor_mC9C6B443F0C7CA3F8B174158B2AF6F05E18EAC4E_inline (Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B* __this, uint8_t ___0_r, uint8_t ___1_g, uint8_t ___2_b, uint8_t ___3_a, const RuntimeMethod* method) 
{
	{
		__this->___rgba = 0;
		uint8_t L_0 = ___0_r;
		__this->___r = L_0;
		uint8_t L_1 = ___1_g;
		__this->___g = L_1;
		uint8_t L_2 = ___2_b;
		__this->___b = L_2;
		uint8_t L_3 = ___3_a;
		__this->___a = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_get_sqrMagnitude_m43C27DEC47C4811FB30AB474FF2131A963B66FC8_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___y;
		float L_3 = __this->___y;
		float L_4 = __this->___z;
		float L_5 = __this->___z;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3)))), ((float)il2cpp_codegen_multiply(L_4, L_5))));
		goto IL_002d;
	}

IL_002d:
	{
		float L_6 = V_0;
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_vector;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_vector;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_vector;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_vector;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___0_vector;
		float L_11 = L_10.___z;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_12;
		L_12 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))))));
		V_0 = ((float)L_12);
		goto IL_0034;
	}

IL_0034:
	{
		float L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)(L_1/L_2)), ((float)(L_4/L_5)), ((float)(L_7/L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
