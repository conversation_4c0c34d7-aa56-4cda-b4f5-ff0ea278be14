﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m596437BE24172A9892B69483DC857AFB7EEC69E7 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6FB3FAA869510661EC6401C0C8A7BBE2138C3DA8 (void);
extern void BasePC2D_get_ProCamera2D_mE277F562D541662C2098DA32B2CAB8173E8F771D (void);
extern void BasePC2D_set_ProCamera2D_mD686DD8B337DD0179A94ACB3A1BE706A81781EB4 (void);
extern void BasePC2D_Awake_mB771BD4A2E3B653557FF55A4EE79EE08477CA51A (void);
extern void BasePC2D_OnEnable_m7A5D6C19C2C2EBC039CF77FA71E2E3301234A94B (void);
extern void BasePC2D_OnDisable_m0D133CC1FE265D36A005B7CB23139CBBCB61A574 (void);
extern void BasePC2D_OnDestroy_mE7F7AB62E67BC1AF00CC4F3E949B1BDEBCB638AE (void);
extern void BasePC2D_OnReset_m2ECC8F3B185DAD9BCD9283251C87E4FA71124C85 (void);
extern void BasePC2D_Enable_m046A4A78B3F5522481DCAB28495EBDDF6F81B972 (void);
extern void BasePC2D_Disable_m8149FB64FB4C4FB883B33AE21D2144A7EC1B059A (void);
extern void BasePC2D_ResetAxisFunctions_mD9A52EE6D0E6FE12D5382344F87252EB27571B71 (void);
extern void BasePC2D_OnBeforeSerialize_m8F8B6C404A81C68782282EEBB00AB4697C7C9902 (void);
extern void BasePC2D_OnAfterDeserialize_m795B5938A4A475DD63AE98C31548C0E0AAF1703D (void);
extern void BasePC2D__ctor_m67A04FFBDFB943E220923E9798543D007A8D9431 (void);
extern void U3CU3Ec__cctor_m9BB22E1A4D1CD4FC13BF1343DCEC13A25253A91E (void);
extern void U3CU3Ec__ctor_m35E8FDC339AF854AB587C00C09D68819882B472E (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_0_mF890D6EB7F51D2FA51A2D6BBCB23616B61FB88F1 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_1_mABDBCA2DB3F60AB34B4C4A7F2BF429693CA19D60 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_2_m2D73F633D5403D8294E3601EF5E2A74F4BCF88F3 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_3_m7BB51F7820B0CEEEE769E629C2506F964052C028 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_4_mE9468505EEECD9432648157323877186CC827542 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_5_m853D84C3B1120FEB80F10C25138620D978611EFE (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_6_m0ACA50D48B3289B93F64978DF13672C973D067C3 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_7_m30C7F597DD8CD52484C698A467F7D83E7A0D0E4C (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_8_mB9A44F63273EF24FCAE191FDE0A818B641E2A8E9 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_9_m476DCF2F903DB61CE825074094CDBF1E0C29BBDF (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_10_mBA4BFB39D407199F5C18D99C1A48D7D412EC5826 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_11_m85F852E71F362E885AD199B7035F2A0D2EE09CEF (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_12_mDD52C6F7705DED077952F924C1E7D7A0756FB701 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_13_m7332D560B5A6EEFD1B9B7B5ACC80D89C9383C22A (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_14_mD0DBC3FDE1D48C95CC4DEBE6AF41FF9FD2AAE219 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_0_m8E36E151FC662E6E376AB40FED8C5E74F64EBB20 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_1_m2E89B350F51983C0B1B9C883242844B3677F5307 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_2_mEB8BA710A2B7ED3A230CBF63E78B36573CDA18DB (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_3_m6C521BF0E8B4F8DDF4D95E72761D52A5799F5CD0 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_4_mEF035B068C62A1A4A430674FF1A3DAC089DB9013 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_5_mB05140CFB353430D4416C92B47749499C5DA8E24 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_6_mA32E41B1B774DDE535A138B9A9C5D20183DBCB35 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_7_m2BBA97B5B4B5BBCAF4881CC6EEB8105372080FC2 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_8_mB29412C365FF6EB218D100301AA5ECBC9D7F2903 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_9_m515A675D579328A2253A4A4CA78E0DB7F6C89A86 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_10_mE3338736369BBFDAA7471BA194AE12BF6C8A4D6B (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_11_mD42222D06BD5A9C9E6961273D25BF70C71B831A8 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_12_mC463F356B8432E0931EE0C259A2AC8552F092D63 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_13_m4B1003ACCB8C516D4B545A0D69805739CF138174 (void);
extern void U3CU3Ec_U3COnAfterDeserializeU3Eb__21_14_mAE591C329771C978CE70FE7E615ABCB8614CF255 (void);
extern void BaseTrigger_Awake_mEA036379F719B8760F3D57D96E99ACD0D7F119CC (void);
extern void BaseTrigger_OnEnable_mE5CF32D35E0110BEFF50E06547B3367CA82B04EE (void);
extern void BaseTrigger_OnDisable_m9B44B240C3C3796F5AB3D8B7C8AD8C8C3025E6E9 (void);
extern void BaseTrigger_Toggle_mF3BD1210D59B5DFC6171FA7BDAEE9885D63E8FEE (void);
extern void BaseTrigger_TestTrigger_mD6DF866002FCE0396AC826FA8715875EE8014B4A (void);
extern void BaseTrigger_EnteredTrigger_m47403F05C8AB195D87920EF17C43CBF86B9E1BB5 (void);
extern void BaseTrigger_ExitedTrigger_mCBA28315BC4F19428A209196D3957EB4D83A481C (void);
extern void BaseTrigger_TestTriggerRoutine_m0D2B7A97A473D72F3B8B4E5032CA1E2B222C71D5 (void);
extern void BaseTrigger_GetDistanceToCenterPercentage_mD39589D99050BD34D3BB5820513BD8B461483297 (void);
extern void BaseTrigger__ctor_m2394C6025AEB83FBB4349D4A10604599FC0E389D (void);
extern void U3CTestTriggerRoutineU3Ed__19__ctor_mE3EDDABFA7A49A8D836E28EAF2EB139A1F48839D (void);
extern void U3CTestTriggerRoutineU3Ed__19_System_IDisposable_Dispose_mA9BA4987A07B240853380D0749AE5BE81210D3DC (void);
extern void U3CTestTriggerRoutineU3Ed__19_MoveNext_m4C080020FA821554F864F274BDB4C5D3F773BCBE (void);
extern void U3CTestTriggerRoutineU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFE52099888EB12EBB24A6B6993DD0B7D31B450FD (void);
extern void U3CTestTriggerRoutineU3Ed__19_System_Collections_IEnumerator_Reset_m507A0AF6C51DC6E59A3C8CAEA08CE05B735ABFC7 (void);
extern void U3CTestTriggerRoutineU3Ed__19_System_Collections_IEnumerator_get_Current_m32781BBCD8F2F720A9B8B9B81317A843913A4470 (void);
extern void BoundariesAnimator__ctor_m51D92BFAF4E242DC5D8CAA5A77054DEBFBD8EA9F (void);
extern void BoundariesAnimator_GetAnimsCount_m1DCE3302A8F5237B1E4BC957CF9F493C3FA33D3E (void);
extern void BoundariesAnimator_Transition_m04CED9453CD36202CAAC840185B3A54846A0157E (void);
extern void BoundariesAnimator_LeftTransitionRoutine_m0A2E26D592D536962DAD5ED30D3912C8E882588A (void);
extern void BoundariesAnimator_RightTransitionRoutine_mB5100648BBFED0018166DBCF0C046064652E6326 (void);
extern void BoundariesAnimator_TopTransitionRoutine_m2B6233157608A645EEE6E356DEFF0C0C2B893A72 (void);
extern void BoundariesAnimator_BottomTransitionRoutine_mBA6DD346FD8549BF2F0FB074A583AB0A57ACC642 (void);
extern void U3CU3Ec__cctor_m7571A90F4C3F85DFADE6725E069A6E8012CE3767 (void);
extern void U3CU3Ec__ctor_m7DD7CB33E10A9BA06CF3A795B1877D09149E1CF3 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__16_0_mC1BE5CFF3E576381B6FEDE82260120246A91FB66 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__16_1_m81E6B070815F19A7C2B093FD35492D2A085ECF82 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__16_2_m6A525F4F7AFE65C0DC04FAD2D71AA705E96870E7 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__16_3_m3CE831DDF6BC75F8362B2343BAFFD40B309A3473 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__16_4_m5F57F9BB4E4F30665B2A50FB9EDB6ADEC9C5A356 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__16_5_mA1FBA45094202BA315083426BEFD6BB4AD97314B (void);
extern void U3CBottomTransitionRoutineU3Ed__22__ctor_m603B93CE916FE4D84A5471B8E4E2586749C67E53 (void);
extern void U3CBottomTransitionRoutineU3Ed__22_System_IDisposable_Dispose_m641FD03E5046396812223F873E8063250F8E120B (void);
extern void U3CBottomTransitionRoutineU3Ed__22_MoveNext_mBEE19069AE5919B292E9D65D14A19C168293A594 (void);
extern void U3CBottomTransitionRoutineU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5D3F6C3E838BC17FAD0E280DD5212FE8BD758BED (void);
extern void U3CBottomTransitionRoutineU3Ed__22_System_Collections_IEnumerator_Reset_m97B231CC53B239B1CFA5AD25D49194EE86C40C39 (void);
extern void U3CBottomTransitionRoutineU3Ed__22_System_Collections_IEnumerator_get_Current_m11CAB1A569CFBB7CD36EA076F67E7EF94EC63A33 (void);
extern void U3CLeftTransitionRoutineU3Ed__19__ctor_mDE4BBF10AAED14136B574F978B28B38F42B053F4 (void);
extern void U3CLeftTransitionRoutineU3Ed__19_System_IDisposable_Dispose_m2C94E72020281E822968ABFA48539ECD558E4826 (void);
extern void U3CLeftTransitionRoutineU3Ed__19_MoveNext_mA8E66911B1253A3066F3D24D4C2F47E8F5D784D1 (void);
extern void U3CLeftTransitionRoutineU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2B5FBE6D4C36A70509D39CBCE7A6BA58ABEAF6B0 (void);
extern void U3CLeftTransitionRoutineU3Ed__19_System_Collections_IEnumerator_Reset_m2064671E122CB62A2316AA52389D0A321E5ADE06 (void);
extern void U3CLeftTransitionRoutineU3Ed__19_System_Collections_IEnumerator_get_Current_mA6D08B35FFEDF57E3EE34006BB7C85B9E564647B (void);
extern void U3CRightTransitionRoutineU3Ed__20__ctor_mD13B024E8894BB8512D8762C56ABB1A0C6FBBD20 (void);
extern void U3CRightTransitionRoutineU3Ed__20_System_IDisposable_Dispose_mA7CE0EC824432E70316845039AF06B01FD286758 (void);
extern void U3CRightTransitionRoutineU3Ed__20_MoveNext_mA4A12B5679A51490211DA1CEBCE226671446E30A (void);
extern void U3CRightTransitionRoutineU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m12E89C41DCDD9493ED3AA28407ABB5F4AA83B1E0 (void);
extern void U3CRightTransitionRoutineU3Ed__20_System_Collections_IEnumerator_Reset_m8881A7649956E5C5706FB3D27DC7D958A3AE5109 (void);
extern void U3CRightTransitionRoutineU3Ed__20_System_Collections_IEnumerator_get_Current_m1568D752E5978A50945AA27474420136880D9002 (void);
extern void U3CTopTransitionRoutineU3Ed__21__ctor_m03555579F94445D9549F22F1237103C042706990 (void);
extern void U3CTopTransitionRoutineU3Ed__21_System_IDisposable_Dispose_m2529E4B6610538D00F82E5C765FC056EBBE9BD53 (void);
extern void U3CTopTransitionRoutineU3Ed__21_MoveNext_m3C092BBBD2F1ED94062F81B1F7BCC0D80068BC91 (void);
extern void U3CTopTransitionRoutineU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m26DB3126148A37CFD8DA2732D2073F0B7D97FCA2 (void);
extern void U3CTopTransitionRoutineU3Ed__21_System_Collections_IEnumerator_Reset_m9A1DE95CE6E934BF496E2E8D71B05AD2EFD4B5B9 (void);
extern void U3CTopTransitionRoutineU3Ed__21_System_Collections_IEnumerator_get_Current_mF2CC9323437B9C0833E6F5B8B9D38CDFCEF9562C (void);
extern void CameraTarget_set_TargetInfluence_m17653AA6807C23388E530760A70B8B5E4039E3AE (void);
extern void CameraTarget_get_TargetPosition_mFF1699BD2ABC41C4B52CA18FD38CA0CB0F75E815 (void);
extern void CameraTarget__ctor_m516B7C0DD4A54DD247CA10A0E4FCFBF7BEB3F476 (void);
extern void ConstantShakePreset__ctor_mE91C65FCD4029129C2BAB3AA91F75C2EA5A02AD9 (void);
extern void KDTree__ctor_m6FA50EE284097CCE0166C38F5C685AD894F5BD56 (void);
extern void KDTree_MakeFromPoints_m922A2E4092B912ABBCEE0BFCAB866A468C906152 (void);
extern void KDTree_MakeFromPointsInner_m8BB1148B655AC031FAC7212DA5D725331F7F880C (void);
extern void KDTree_SwapElements_mEA190B87CE35BC1EAC2500CB1FA87F47F751BC41 (void);
extern void KDTree_FindSplitPoint_m93764A33B1E8EFE411C86A12C52EF17527E7551B (void);
extern void KDTree_FindPivotIndex_mB8A9BF49F84EFB75BB8A4287691B6FEC13A17BBF (void);
extern void KDTree_Iota_m16A7A3DC03D94537B3D48B86D203A26190E4CFB0 (void);
extern void KDTree_FindNearest_m730196511444DD1001311F774AF55CBD4509C231 (void);
extern void KDTree_Search_m0E2D1D220B0C960D0A730D6E7F604E0D1EED67FC (void);
extern void KDTree_DistFromSplitPlane_mD0405BB7FA130B40B3E0A9FC0F258BBE43FC88FF (void);
extern void KDTree_Dump_m07B716EFDB41E0582244C7794E1722C1BC12EB46 (void);
extern void MoveInColliderBoundaries_get_RaycastOrigins_mE90FB1BA5D8DCB06251C8D0019118601D13C58E9 (void);
extern void MoveInColliderBoundaries_get_CameraCollisionState_mC28D9E2082C028FE7EB1052B868D9C3C42CA9AF8 (void);
extern void MoveInColliderBoundaries__ctor_m15C5C94F2FF75B87F7932E48F654293849A4034C (void);
extern void MoveInColliderBoundaries_Move_m8A4D989991B75EF5AE3BE24B8E7CDD0C6872F885 (void);
extern void MoveInColliderBoundaries_UpdateRaycastOrigins_m98B00B327CC32BBBB35973F12A680D3472F064FC (void);
extern void MoveInColliderBoundaries_GetOffsetAndForceMovement_m49102541A73BDDBEF81F68D2272975FC9B57B70D (void);
extern void MoveInColliderBoundaries_MoveInAxis_mDDDB1CDC5DF1F22462B0140265B9776114CF00DA (void);
extern void MoveInColliderBoundaries_DrawRay_m6EED3106A6C472E2AE9208698E2915641466CF23 (void);
extern void U3CU3Ec__cctor_mBA00A7CB33C2EA6A1B284D4674E03082EFDD889D (void);
extern void U3CU3Ec__ctor_m5EE5F9CB0A6039423C47C4FE4BBE44EB4CC6AE80 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_0_m7BEF874DC2DBBAB18AA2E9728741BCBACE81E32F (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_1_mAE9405A199C4B6F1D042625874E12C936A031E56 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_2_m881BD841D4891F0627B59A0419981D6741627044 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_3_m280FF3F00162D3B31E265F124BD9D28AE28EA019 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_4_m5C3557BB205826B3C0B656F8E04A13F2A10C7B8B (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_5_m578C4FE71C43D4A4F14BFB5B9ED977671F491A70 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_6_m41F843AE8B8AA32B9B6AA14D30715CE968B8B865 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_7_mE1718F2B273559DA2EE45B3563C6D6E8FD5340DB (void);
extern void U3CU3Ec_U3C_ctorU3Eb__20_8_m6E5A54572B8FC1FB891C3A55E4DA9A218DD12892 (void);
extern void ShakePreset__ctor_m91B602317816603CCB36EA98408CF5BBF6BEE297 (void);
extern void EditorPrefsX_SetBool_mFFA96A11C6724E90DF184A9C9A2210375A9648B0 (void);
extern void EditorPrefsX_GetBool_mE8459FD1694113E647A3FD9884A779BD2A8553D8 (void);
extern void EditorPrefsX_GetBool_m9AE9D029705D57302602BFC9571A2386D3390661 (void);
extern void EditorPrefsX_GetLong_mA8039F38781D6092601E10339626A2B09465D7C0 (void);
extern void EditorPrefsX_GetLong_mDF03D08A9DC3255FC88336CF2EC192B204365764 (void);
extern void EditorPrefsX_SplitLong_m028F8AD5102068E585022FAB7F60B5757419DB07 (void);
extern void EditorPrefsX_SetLong_m3931F1635D05CD4C40857189B55CC34BDB23A2C5 (void);
extern void EditorPrefsX_SetVector2_m97A19D7AC2EC67BD17E33BAE5301FE18D4746E99 (void);
extern void EditorPrefsX_GetVector2_mA633749FFD981821890FC4C6E37792E500696EE9 (void);
extern void EditorPrefsX_GetVector2_m3A1092E7B86B5C6F3EC428B3DC9993555205594A (void);
extern void EditorPrefsX_SetVector3_m7ECDBFA41DA1319717CB68A5AA5418CB970E714A (void);
extern void EditorPrefsX_GetVector3_mF23FDA21C203682E3F107FC3DEA43F39DE1AB384 (void);
extern void EditorPrefsX_GetVector3_m59F3C4F89BBB8F428A267814687871F18F46D6FD (void);
extern void EditorPrefsX_SetQuaternion_m9952D975CF7A23351146F6B372096F46666B3A44 (void);
extern void EditorPrefsX_GetQuaternion_mC8E102BD0486C43963F95424E32AA4282F5725EC (void);
extern void EditorPrefsX_GetQuaternion_m164B0D1326E6B911ED74F577819328AEEB97ED96 (void);
extern void EditorPrefsX_SetColor_m5E3C6C2DE1199A4ABD6779580BE7C03192A08637 (void);
extern void EditorPrefsX_GetColor_m0C14013FEFE17E3E5B78EDF4BA8AFA6DC137292F (void);
extern void EditorPrefsX_GetColor_m11D3BD48FB802175BD4E77938CF2254A0775EDAB (void);
extern void EditorPrefsX_SetBoolArray_mA5BE38CEEA812B7A4487C8BBBA40F4269E36E18B (void);
extern void EditorPrefsX_GetBoolArray_mE1197E16725ECA787EFD67607F38C9BC54B8CC6A (void);
extern void EditorPrefsX_GetBoolArray_m889ED1F5F6A05847F15AA1EF72247E728AD95C96 (void);
extern void EditorPrefsX_SetStringArray_m60312156A5699E63A27CC018FC106748150457AE (void);
extern void EditorPrefsX_GetStringArray_m310DD8967E93F56E91F69AD10749AD09930A5075 (void);
extern void EditorPrefsX_GetStringArray_m1847650220DC6F6FA98C02C08011F555C7EA2258 (void);
extern void EditorPrefsX_SetIntArray_m9B9342EDD4CEBE84E4D77D4B84AC8DC8690DADF8 (void);
extern void EditorPrefsX_SetFloatArray_m85C2273C4BC130D14FB928054FA0072ED51AF426 (void);
extern void EditorPrefsX_SetVector2Array_m4662F78962146DA0714D875C275329B9AFEFCED5 (void);
extern void EditorPrefsX_SetVector3Array_mB6994D228359533E346C50D99EA24C4DC1B32EAE (void);
extern void EditorPrefsX_SetQuaternionArray_mD885FFD5BC57FF039DE48D21530BED6217D5CB38 (void);
extern void EditorPrefsX_SetColorArray_mD2B0DB84720118EE487C591E4B0D80332BEE08A0 (void);
extern void EditorPrefsX_ConvertFromInt_m3A459D1D9D20BCA6D9BC9A2334732D56C34A1C18 (void);
extern void EditorPrefsX_ConvertFromFloat_m9996A5BEBBFFE0F5670E5E3A914997A8B85A342D (void);
extern void EditorPrefsX_ConvertFromVector2_m8E006252F547D39B2B5F8DB0103EE8BB4CC20A29 (void);
extern void EditorPrefsX_ConvertFromVector3_m3407CD5A21DFB7445446803F1EAF5CD7274F5F58 (void);
extern void EditorPrefsX_ConvertFromQuaternion_mB81CFCE33E71A759829100E9DCBC9BAF9188E0EA (void);
extern void EditorPrefsX_ConvertFromColor_m87C217DA0919C8DE8D2B6F47B65BED5D20B9C3CA (void);
extern void EditorPrefsX_GetIntArray_m9CEDD488CFF8FDD176253A55417B72DE4EA6EE7A (void);
extern void EditorPrefsX_GetIntArray_mF0A2660D2B219C4E3721C9B8DD28AF4FAC876BD6 (void);
extern void EditorPrefsX_GetFloatArray_m238A23E7D121F9EBD3BA9A99FF90DC08613344F8 (void);
extern void EditorPrefsX_GetFloatArray_m751B5DF425567DCDF1026077E89D74DD575F51C3 (void);
extern void EditorPrefsX_GetVector2Array_mE5E037CF30C4F5F70F9561CE9E00713D819893A0 (void);
extern void EditorPrefsX_GetVector2Array_mB8C2AD28C391E9907D17B28A0B9878ABFD19826F (void);
extern void EditorPrefsX_GetVector3Array_m80C6FD48CF930E2177463E66E2D10202DA1012A9 (void);
extern void EditorPrefsX_GetVector3Array_m20FDBA039608BB752BAA1D301CEE0E14A72EE199 (void);
extern void EditorPrefsX_GetQuaternionArray_mB5DDD2A161F0256947ACEEAB97ECFB68FF1F3A64 (void);
extern void EditorPrefsX_GetQuaternionArray_m18F714B572C14648FA43972D69CF5045B0CBD6EB (void);
extern void EditorPrefsX_GetColorArray_m18A7D72BEC5894D1053DCDD0F7D4E5D92B2B5F0D (void);
extern void EditorPrefsX_GetColorArray_mD7A2049208BA5EDF5FE0DDC7060DBA871856C600 (void);
extern void EditorPrefsX_ConvertToInt_mE8DBC3E665EAEC3228AA90F2ACE82A553A7E3D17 (void);
extern void EditorPrefsX_ConvertToFloat_mABE102EA7EB1A2D964214AFB7F0F009CAF6BC8E0 (void);
extern void EditorPrefsX_ConvertToVector2_m775944FAEAA2ACFAEFD68B4585E8D780FD6FA0F8 (void);
extern void EditorPrefsX_ConvertToVector3_mF084877DE4627DCA3EA20847B35483D2F229B87C (void);
extern void EditorPrefsX_ConvertToQuaternion_m8645663BB291FA477BB0BB3986C8E44651B41BF6 (void);
extern void EditorPrefsX_ConvertToColor_mBFBCC9E8CD40CCE17DB8B8DB3F86377AE0467200 (void);
extern void EditorPrefsX_ShowArrayType_mBB213714F61C3B95B37970077FF043204B6E9782 (void);
extern void EditorPrefsX_Initialize_m65B919801B48F50A7CB9B5E71AD44001878B75C1 (void);
extern void EditorPrefsX_SaveBytes_m69687D9371DCE8DE836532495BF8DE6DFC26C547 (void);
extern void EditorPrefsX_ConvertFloatToBytes_mF3B013447A0952CF46F4507B0C1D836B6F26E16B (void);
extern void EditorPrefsX_ConvertBytesToFloat_m6EDC627FEC969BC0EFA12FFE50A3443D2BE90B2B (void);
extern void EditorPrefsX_ConvertInt32ToBytes_mF3ACBAF66A81AF139346EA9BA051796B384995FD (void);
extern void EditorPrefsX_ConvertBytesToInt32_m211BF8CC41BA2213C250DD99D21BE3B753047E0B (void);
extern void EditorPrefsX_ConvertTo4Bytes_m089C0B865F0E07BFFDB1836E2285162E2C1ECC48 (void);
extern void EditorPrefsX_ConvertFrom4Bytes_m6FA7473662F259B61902BFCC209F119B0434CD4C (void);
extern void MinMaxSliderAttribute__ctor_mDB9C19C9A31D70B0C2FF560D59FA1A5B0F2CB131 (void);
extern void PrefsData__cctor_m0A23CC7D2F7AAF2D722BC9F4554343E82A3818A4 (void);
extern void Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31 (void);
extern void Utils_SmoothApproach_m9C76660CA0215DC9B7E89A70CF52188E75203853 (void);
extern void Utils_Remap_m8A4E0068BD805F72E231B3D2967EBAA329D35DB8 (void);
extern void Utils_DrawArrowForGizmo_mEF337C54E619E992F262D9B66105CBB313458808 (void);
extern void Utils_DrawArrowForGizmo_mF59BCC1F3CC03AB0A2613B1739F9F44F7240DF09 (void);
extern void Utils_DrawArrowForDebug_m4206695D40D1C30CCAC6D31ABBEA8F0927ED6A2A (void);
extern void Utils_DrawArrowForDebug_m30EB3441F70FE55061CA44C647A95BDA46DDB80E (void);
extern void Utils_DrawArrowEnd_m2B427B47E9A58A6C232EA0AA58DB9EBA486F2332 (void);
extern void Utils_AreNearlyEqual_mE44BE42133D875615050D46D728D4CF86A303AFA (void);
extern void Utils_GetScreenSizeInWorldCoords_m8EF6AEF0C48BFC424DF586E5A4838C5F53E83A9B (void);
extern void Utils_GetVectorsSum_m08EDA3327A9964C15C39B9C9598F1280F9FFF5F1 (void);
extern void Utils_AlignToGrid_m27553769AD00695CEB245011EAA53BEF0011161B (void);
extern void Utils_IsInsideRectangle_m7D8E812B437518C7EEF3AD8F2519185F09E44934 (void);
extern void Utils_IsInsideCircle_m**************************************** (void);
extern void ProCamera2DCameraWindow_Awake_m4CD6E4B2731DD2443C5640A59F8E8F90A2BABBBC (void);
extern void ProCamera2DCameraWindow_OnDestroy_m9E9E3E3409D171D150D43DA8470C825C20221224 (void);
extern void ProCamera2DCameraWindow_AdjustDelta_mF37319D46490601CC8C071FCDE31E6752DAC70FA (void);
extern void ProCamera2DCameraWindow_get_PDCOrder_mFD37AAC5E940ED9B4B66747C7D5BB543BF644AF2 (void);
extern void ProCamera2DCameraWindow_set_PDCOrder_m36FE06ACA5B78780D816669AC3E3590C7E52DA70 (void);
extern void ProCamera2DCameraWindow_GetRectAroundTransf_mF5C08DB92035A120C57D6EC2E2F5760B86335AF2 (void);
extern void ProCamera2DCameraWindow__ctor_m8ED927E958FB1C5E79AD6A91B0A04740E3A289E0 (void);
extern void ProCamera2DCameraWindow__cctor_mA2FC913354B25ACAA8ACAE8CC77954D7B9B212B8 (void);
extern void CinematicTarget__ctor_m669A02758754944BD68D9D8945CB0E2F5B6EB674 (void);
extern void CinematicEvent__ctor_mD80D459193825A9A2FDEDAE9197FD38EA4B1E891 (void);
extern void ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5 (void);
extern void ProCamera2DCinematics_Awake_mD466A492452F9A6B777B690A180287D9006E494D (void);
extern void ProCamera2DCinematics_OnDestroy_m15090E65CE0E06B15E1C382CF92DCFCB78CC4DBF (void);
extern void ProCamera2DCinematics_OverridePosition_m6E066608FA5F9389550B62BCAAA75A14DF55F2F4 (void);
extern void ProCamera2DCinematics_get_POOrder_m51006B5D7DBEBF9E655DBBAF6285AFD08062341B (void);
extern void ProCamera2DCinematics_set_POOrder_m03FDC20D666C2D27ACB950DE525AAB2AC76063EE (void);
extern void ProCamera2DCinematics_OverrideSize_m7886783343FE2959BA0158749B691902434523C0 (void);
extern void ProCamera2DCinematics_get_SOOrder_mC2151DCDDE9281A4597ED31007CCD9A2F9AE79A8 (void);
extern void ProCamera2DCinematics_set_SOOrder_mFB16A63286FF854F3B82596F9ADE87307A3F99F3 (void);
extern void ProCamera2DCinematics_Play_m0EC4378CCBB85B084497DBB3D3E6D3A0487A084B (void);
extern void ProCamera2DCinematics_Stop_m5430BE4DC9FB51CAEA12AB1A0403031966BE7415 (void);
extern void ProCamera2DCinematics_Toggle_mF3D11E8BBE8CFE7C0B02B9908F223ACEB6D98995 (void);
extern void ProCamera2DCinematics_GoToNextTarget_m1F8BCC330356EBD1818DB49BE438D136B2CCDED6 (void);
extern void ProCamera2DCinematics_Pause_m9619E298983FD2F6F21E40362E529A587A29C70C (void);
extern void ProCamera2DCinematics_Unpause_m43053CE978FC7488DEF5CD3E58935D523FFAAB56 (void);
extern void ProCamera2DCinematics_AddCinematicTarget_m94C0A7BA9CBBA679FC8B791AD566FD20397BDFF9 (void);
extern void ProCamera2DCinematics_RemoveCinematicTarget_mEC4CCCC4D1D17F608E754F5A54A762DC0B22C7D0 (void);
extern void ProCamera2DCinematics_StartCinematicRoutine_m687880F52D71C3FAC16EF3016A3739DEBBCDEE04 (void);
extern void ProCamera2DCinematics_GoToCinematicTargetRoutine_m79BADD42264D4ACE18DAD817E25C4FF1BA47D792 (void);
extern void ProCamera2DCinematics_EndCinematicRoutine_m11A1009894476C42AC58A142B95254FDB30C8DA5 (void);
extern void ProCamera2DCinematics_SetupLetterbox_m7EC5D2FCD782946DFD1D1ADDF041783F7045197D (void);
extern void ProCamera2DCinematics_LimitToNumericBoundaries_mA8A4E56FCFE608BEF6AAA30091825B8AC2EA7F6D (void);
extern void ProCamera2DCinematics__ctor_mFA08EC00A9C696170EE2A9E5FB51980F9D3EBEBE (void);
extern void ProCamera2DCinematics__cctor_m8623F94D329CB19B0D9B1936B1918D15FAA5199A (void);
extern void U3CU3Ec__cctor_mD03A41A71CECA059A2AEB73D0FC36CE5D6EE0ABE (void);
extern void U3CU3Ec__ctor_m4F8397BEBBD2B9591D4309D266AEE98084E54B01 (void);
extern void U3CU3Ec_U3CSetupLetterboxU3Eb__51_0_m62CD38E39D8C3FA756C39AA5E8DAFF9B57E5F624 (void);
extern void U3CEndCinematicRoutineU3Ed__50__ctor_m40D767478ED58E953B81AE4F4F083B2574D2C87F (void);
extern void U3CEndCinematicRoutineU3Ed__50_System_IDisposable_Dispose_mC209994DBC29E595158E4EEBCC3D4B2DB40034AF (void);
extern void U3CEndCinematicRoutineU3Ed__50_MoveNext_m4B1299A879F332F8DF396C588DF017B939871D44 (void);
extern void U3CEndCinematicRoutineU3Ed__50_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6A230898FDD0349EE0B73AD62D17014E7A2B214C (void);
extern void U3CEndCinematicRoutineU3Ed__50_System_Collections_IEnumerator_Reset_mA2F99BC76A44418C31647AFDEDBC2867D389E1DB (void);
extern void U3CEndCinematicRoutineU3Ed__50_System_Collections_IEnumerator_get_Current_m171167F79FD04430C2C613C82984AF54B5C48A30 (void);
extern void U3CGoToCinematicTargetRoutineU3Ed__49__ctor_m1E5C8FFBE8D22AB2A2706DACD0A9D5A795B5BBDB (void);
extern void U3CGoToCinematicTargetRoutineU3Ed__49_System_IDisposable_Dispose_mFBF60FED8C4E271C8AD69D0FCD2F0891A5BB205B (void);
extern void U3CGoToCinematicTargetRoutineU3Ed__49_MoveNext_m02938D8479C1664A5DD6C72FF3D04D537B0D1373 (void);
extern void U3CGoToCinematicTargetRoutineU3Ed__49_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1F81438D2DEB34EAD4D91A1AA6707094E63951B0 (void);
extern void U3CGoToCinematicTargetRoutineU3Ed__49_System_Collections_IEnumerator_Reset_m56DACA9A2B87DC0A076F5A598896B61EF4DBD4A3 (void);
extern void U3CGoToCinematicTargetRoutineU3Ed__49_System_Collections_IEnumerator_get_Current_m8FA8E02A1E6180F9C87BE989772C61A9A86DB678 (void);
extern void U3CStartCinematicRoutineU3Ed__48__ctor_mCAEECA20C52EA2104501264B23807407207B3266 (void);
extern void U3CStartCinematicRoutineU3Ed__48_System_IDisposable_Dispose_m364850BCB664CBA01E595D39C82B35A026B7AD82 (void);
extern void U3CStartCinematicRoutineU3Ed__48_MoveNext_m641F0665DAC5A7A9F6A1481F6E3EA0576069141E (void);
extern void U3CStartCinematicRoutineU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2215F0C68D9B6E7D2957C30B404ABB74E4560682 (void);
extern void U3CStartCinematicRoutineU3Ed__48_System_Collections_IEnumerator_Reset_m68CD0B32E1745B8A3299F3A4D064B52EBA9DA4D5 (void);
extern void U3CStartCinematicRoutineU3Ed__48_System_Collections_IEnumerator_get_Current_m51625671113B14FFC5E3CCF58F772A80751E1B2C (void);
extern void ProCamera2DContentFitter_set_ContentFitterMode_mFB2206019C9E20B58794ECF99E1E8B9440727B5D (void);
extern void ProCamera2DContentFitter_get_ContentFitterMode_m5AA94788EF1B0F6F420D3EC436AE110EF7240DD5 (void);
extern void ProCamera2DContentFitter_set_UseLetterOrPillarboxing_m080D28A0E1C202EF1DA730BF64F2043811DF03DF (void);
extern void ProCamera2DContentFitter_get_UseLetterOrPillarboxing_m015C987A21F46DE8B187F7C927B52C2EC64C3E8F (void);
extern void ProCamera2DContentFitter_get_ScreenAspectRatio_mE27056FAF160AD508B7C006A640F8D5EBE32A2E7 (void);
extern void ProCamera2DContentFitter_get_TargetHeight_mF3378C53AF976BB2C32032B4D43E243FA2AF76C8 (void);
extern void ProCamera2DContentFitter_set_TargetHeight_mBAA751419CABEEB9C33CDEA2859C7F03995DA08E (void);
extern void ProCamera2DContentFitter_get_TargetWidth_mCDFA37768BB13AAE5784B0B81153A148991FF1D5 (void);
extern void ProCamera2DContentFitter_set_TargetWidth_m0E8FEEAA1A77731299D8436336861B5D4B852D6E (void);
extern void ProCamera2DContentFitter_get_TargetAspectRatio_m86181154E7AF97491F7C38A51EBCFD846F8EC9A0 (void);
extern void ProCamera2DContentFitter_set_TargetAspectRatio_mD1652BE0995533C048E25D975468F524C1DFE0EE (void);
extern void ProCamera2DContentFitter_Awake_mEF13A655ED198CA8FFCF6B810E5B816E09DF00F0 (void);
extern void ProCamera2DContentFitter_Start_mDD468167A117B1A028CAACD7DAB2D377C6CBB9DF (void);
extern void ProCamera2DContentFitter_OnDestroy_mE761A8261C404FB164C2F5C0AA9DCD9226435153 (void);
extern void ProCamera2DContentFitter_OnDisable_m2D14876485B16BDA3835CC03C55A50022D551F1A (void);
extern void ProCamera2DContentFitter_OverrideSize_mAE05780F160D726833E4C6BBF4FD087C583599D8 (void);
extern void ProCamera2DContentFitter_get_SOOrder_m60F307E32277F2FB6A133AE413AB61D604F1B4D1 (void);
extern void ProCamera2DContentFitter_set_SOOrder_m0100E8BD08199231E4F08C6E291B2109C850ACBF (void);
extern void ProCamera2DContentFitter_GetSize_mC245EE94EC24B126BE720C53F34A392416D620C6 (void);
extern void ProCamera2DContentFitter_UpdateFixedAspectRatio_m673BAC83FF79F11F11F3966F9BC7A072D7AFD092 (void);
extern void ProCamera2DContentFitter_UpdateCameraAlignment_m7F3D670871D4FA197EEDA61F3AD2EA6157E5E9FF (void);
extern void ProCamera2DContentFitter_GetScissorRect_mFAA544C31D5B28D66AC22E39BE267473A72C178F (void);
extern void ProCamera2DContentFitter_UpdateLetterPillarbox_m1617EC2429E036394DD75F543C35DEEBE2257511 (void);
extern void ProCamera2DContentFitter_ToggleLetterPillarboxing_mCD8429FE56CFF9012F0281701F76B862D245132D (void);
extern void ProCamera2DContentFitter_CreateLetterPillarboxingCamera_m6F0B7E568A79E3A3A3F53EA22950F290C31D2FE3 (void);
extern void ProCamera2DContentFitter_DrawGizmoRectangle_mD06B184D05698BAB0CE3B1CC4CBB5AD9CE0C1FE4 (void);
extern void ProCamera2DContentFitter__ctor_m4025A3B4ED94B16F646E4748C9A00D52C56D3015 (void);
extern void ProCamera2DContentFitter__cctor_m21369E776E9C8DA6E7191F4FE9427D18EC712B32 (void);
extern void U3CStartU3Ed__34__ctor_m385FCF6FE44EF348F09DF88AEB804F1B95DFC8F4 (void);
extern void U3CStartU3Ed__34_System_IDisposable_Dispose_mF093418EB3417C42FC0F7D42B85E1D367DAA3E5D (void);
extern void U3CStartU3Ed__34_MoveNext_m141E771161689591B1109585BFF11A4C536C79AC (void);
extern void U3CStartU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6B96B517A7026330251A8436AE69CDFE062AE6EC (void);
extern void U3CStartU3Ed__34_System_Collections_IEnumerator_Reset_mE762A87762213032701F072120268175F417CB02 (void);
extern void U3CStartU3Ed__34_System_Collections_IEnumerator_get_Current_mFE65E57C78E899A08A8A84729569D95B496F6413 (void);
extern void U3CUpdateFixedAspectRatioU3Ed__43__ctor_mD033CFE941283B79E9A142F5968E204B39087DEF (void);
extern void U3CUpdateFixedAspectRatioU3Ed__43_System_IDisposable_Dispose_m7E144E06B61C64AC271183481AE66DC0B1E33760 (void);
extern void U3CUpdateFixedAspectRatioU3Ed__43_MoveNext_mEEF2B70E8DB5F000683D6B6737B05D8EAC8E094C (void);
extern void U3CUpdateFixedAspectRatioU3Ed__43_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m629B9926A27F6FE770EAF396EFAE557FA9F051B3 (void);
extern void U3CUpdateFixedAspectRatioU3Ed__43_System_Collections_IEnumerator_Reset_mAA723B7773636BCBE184339CC6139B460DB611A4 (void);
extern void U3CUpdateFixedAspectRatioU3Ed__43_System_Collections_IEnumerator_get_Current_mC5C22C79D1A8B4D92A89D5FD41CC95D08E8D20C2 (void);
extern void ProCamera2DForwardFocus_Awake_mF83ADE633EB448561C52020E85AF314EE7AA91DF (void);
extern void ProCamera2DForwardFocus_OnDestroy_m86F24ACE676FAC7B6737952D186381093A8C3B74 (void);
extern void ProCamera2DForwardFocus_PreMove_mBA34BE2F5BF0E39BB8B480366377A0568B5835F2 (void);
extern void ProCamera2DForwardFocus_get_PrMOrder_mE52D46089E0CD9CDDA09E230580FEBA2574898AD (void);
extern void ProCamera2DForwardFocus_set_PrMOrder_mD78BAB68A978A8B3F22A2997FEE08DB700F686BF (void);
extern void ProCamera2DForwardFocus_OnReset_mC5BD5270430DEFC0E7D160FC841BEDD339104B6E (void);
extern void ProCamera2DForwardFocus_Enable_m6C4A703D8972F6217F78ED148B4C9AC2A8074B53 (void);
extern void ProCamera2DForwardFocus_ApplyInfluence_m954A660621DFC62C5228A91A5E21CD8E23B66094 (void);
extern void ProCamera2DForwardFocus__ctor_mD5E68DE0C612A39AD47B2A8FF3079A959493D6DD (void);
extern void ProCamera2DForwardFocus__cctor_mAC5D2DAD1964BA1D3723FEC598F342BA4BF6265F (void);
extern void U3CEnableU3Ed__28__ctor_m7B4AC9783C3A2AC2B0C94EEA1D71E173C03B03A9 (void);
extern void U3CEnableU3Ed__28_System_IDisposable_Dispose_mAD6F265684EEBD7D7055BBFFDED582958C0EAE1D (void);
extern void U3CEnableU3Ed__28_MoveNext_mF4CD0BFF0ED89572C67D8775EBF7A9975D9E7A7A (void);
extern void U3CEnableU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD5176F91C71ADD25B202F4F0463AD7E9F43915E9 (void);
extern void U3CEnableU3Ed__28_System_Collections_IEnumerator_Reset_m2889E01377D0A30B49F90BBC195E6887FE37647F (void);
extern void U3CEnableU3Ed__28_System_Collections_IEnumerator_get_Current_m1554383DCED17C132CB070E5B425FD08227B9CB5 (void);
extern void ProCamera2DGeometryBoundaries_Awake_m3389F2B7991487DF4F61C6C62DD3C3BE78AB71CD (void);
extern void ProCamera2DGeometryBoundaries_OnDestroy_mBECA53E967D07E68BF3CA10D91E7E399A00BB2E0 (void);
extern void ProCamera2DGeometryBoundaries_AdjustDelta_mCEA3863492A3190C1956E6B0410911B279E87D8E (void);
extern void ProCamera2DGeometryBoundaries_get_PDCOrder_mCCC5F51589D313B04DCB81F404402A531FBC7972 (void);
extern void ProCamera2DGeometryBoundaries_set_PDCOrder_m53AD94533CA87E3F3968DBB4A73AE53108D35956 (void);
extern void ProCamera2DGeometryBoundaries__ctor_m1E26344DD2679EE491D1237BCD824E5170D289B2 (void);
extern void ProCamera2DGeometryBoundaries__cctor_m70690CC0D708B82BB653D66F49251B77AEE94627 (void);
extern void ProCamera2DLimitDistance_Awake_m9E381D5F8928B68A32E76643ED619D709EC64B28 (void);
extern void ProCamera2DLimitDistance_OnDestroy_m40F474AF4750614D2A72CF03A84AA2F7D007E422 (void);
extern void ProCamera2DLimitDistance_AdjustDelta_m75BE0BD72F942384C86DD902DF9C1045ADE67523 (void);
extern void ProCamera2DLimitDistance_get_PDCOrder_m9B3566BF0E54F997247D85CB7D9BC793BD01E122 (void);
extern void ProCamera2DLimitDistance_set_PDCOrder_m6DD6A7DA3E109F52B50D7E133F0A44B879BD3619 (void);
extern void ProCamera2DLimitDistance__ctor_m88855A0EB32B310A1BA29142ACC8FB587B6E8E87 (void);
extern void ProCamera2DLimitDistance__cctor_m8ED08521099D7B69422F865EC812A69202A7D20D (void);
extern void ProCamera2DLimitSpeed_Awake_mA89DBC45EE90D3755579A0CE72CEE1A6096CA8D8 (void);
extern void ProCamera2DLimitSpeed_OnDestroy_m8D77A5E95CED5B5782C2B83833526CB5CF8BF1D4 (void);
extern void ProCamera2DLimitSpeed_AdjustDelta_mF69312B8937E3186480335D6031B41380EA0E14C (void);
extern void ProCamera2DLimitSpeed_get_PDCOrder_mDC7B1B85DC26C1AF6CBA375C181BFD700FB912A5 (void);
extern void ProCamera2DLimitSpeed_set_PDCOrder_m93173310931D569F4CED1469CC30E28375A3E0E1 (void);
extern void ProCamera2DLimitSpeed__ctor_m9FEFC040CB328C98E1977C84B1F91C7E271A451D (void);
extern void ProCamera2DLimitSpeed__cctor_mD1872617ECF3811E49781ACB60D229489E35E2CA (void);
extern void ProCamera2DNumericBoundaries_get_Settings_m98E920A3D3BC50A810E9F1C189F0827CD260835F (void);
extern void ProCamera2DNumericBoundaries_set_Settings_m173B5D02265AC55D136B968FB88C22A69FAB1FF3 (void);
extern void ProCamera2DNumericBoundaries_Awake_m578F6026D19ABF007082CD4F79057E0E4CC127A0 (void);
extern void ProCamera2DNumericBoundaries_OnEnable_mD848F747FFA65257AE01D10E01E06DF10B2348FC (void);
extern void ProCamera2DNumericBoundaries_OnDestroy_m7AD1A446814E1FA6B042DCDCC675D98BC3A54A90 (void);
extern void ProCamera2DNumericBoundaries_AdjustDelta_mAC6886D173E4801461AE5FAB1F5108C7B67D518D (void);
extern void ProCamera2DNumericBoundaries_get_PDCOrder_m217DF340EF8B7605DEC52E20485C9230A46F14B5 (void);
extern void ProCamera2DNumericBoundaries_set_PDCOrder_m4587BEA15A13B8C12A568E71F46A984354AB3EC5 (void);
extern void ProCamera2DNumericBoundaries_OverrideSize_m49C9638739D4F9431E4B83CF18D05B9FB4A991D8 (void);
extern void ProCamera2DNumericBoundaries_get_SOOrder_mDF4E29302165B049801017BF05145C149CAFF493 (void);
extern void ProCamera2DNumericBoundaries_set_SOOrder_mF427689CE1091219B4523D32DBB32E6C1E50375A (void);
extern void ProCamera2DNumericBoundaries__ctor_m5F9E436F283327BEFFEBD032C5ABC362E0DA9090 (void);
extern void ProCamera2DNumericBoundaries__cctor_m56E98B498576677A51BA6BB74404BEC5F33A7273 (void);
extern void ProCamera2DPanAndZoom_Awake_m6BC01D37AC5F77A61DF05180C2E5A17314F4A596 (void);
extern void ProCamera2DPanAndZoom_OnDestroy_mE3B9B6C8D22B4909F7A50ECF587E081924164A36 (void);
extern void ProCamera2DPanAndZoom_Start_m731D61552514FB8A983B1609512BF7A9F10A2915 (void);
extern void ProCamera2DPanAndZoom_OnEnable_mCBAC3D2736FB451910CA9F4D3E79B70FFC23056A (void);
extern void ProCamera2DPanAndZoom_OnDisable_mC31A9ADBFE1D9BF1261CCE1AF636B3F7184BA55D (void);
extern void ProCamera2DPanAndZoom_PreMove_m4CA6D396BD55DC9A850C6A32226D03AF0F8CD545 (void);
extern void ProCamera2DPanAndZoom_get_PrMOrder_mB20BB8CBA528FAB5B7B299E8EF0627F3FB154910 (void);
extern void ProCamera2DPanAndZoom_set_PrMOrder_mEF50EE6CC89CCD4EDA151D04F49FCD008052297C (void);
extern void ProCamera2DPanAndZoom_Pan_mAFEF6F12C8DCCDCDC6ED5952744F549DAE763413 (void);
extern void ProCamera2DPanAndZoom_StartPanning_m7575B63D0D445D6A4A4173F66B6BA32BBA5E9C09 (void);
extern void ProCamera2DPanAndZoom_StopPanning_mDE941EE02B035B120D4CD710020D1D762AE62926 (void);
extern void ProCamera2DPanAndZoom_Zoom_mF7AAAE47E56A1BE6CCBE9FB19D91B729D1C22172 (void);
extern void ProCamera2DPanAndZoom_UpdateCurrentFollowSmoothness_mF61BF85FEAA72EABB0D44837CFBCA47C3269111B (void);
extern void ProCamera2DPanAndZoom_CenterPanTargetOnCamera_m516835E6A733549F29D39E582CBD4C89C53CACA2 (void);
extern void ProCamera2DPanAndZoom_CancelZoom_m774D96FC197DA3C2086CD1DBCE69CA40E264C9B5 (void);
extern void ProCamera2DPanAndZoom_RestoreFollowSmoothness_m6982CB0C40ED41985010F2C017D62BAF11199EEA (void);
extern void ProCamera2DPanAndZoom_RemoveFollowSmoothness_m4521D2385206356C3DAA671785FB68F21625DEA2 (void);
extern void ProCamera2DPanAndZoom_InsideDraggableArea_mC63B221DE56B1F76F2BCB4E87277B24BE5141542 (void);
extern void ProCamera2DPanAndZoom__ctor_m392374D54D284DA3502626BCBC74CF501F9951E3 (void);
extern void ProCamera2DPanAndZoom__cctor_mDC0132726582A95C4AB55AD9889B962C0F924C85 (void);
extern void U3CStartU3Ed__52__ctor_mD92E1D608C73AA8DA78DBB381A9F84A59456DC44 (void);
extern void U3CStartU3Ed__52_System_IDisposable_Dispose_mED9D294EBA91E017D90AA901EA5C55E620E82AF5 (void);
extern void U3CStartU3Ed__52_MoveNext_m07A83EA7DDB65D114C55F599F9D9AF8CCFD6DEA0 (void);
extern void U3CStartU3Ed__52_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m270B7E059111F31DD9B9952560BA84D608FB664D (void);
extern void U3CStartU3Ed__52_System_Collections_IEnumerator_Reset_m6AD8652989DEF47ACC7161C483E5045064EBAE29 (void);
extern void U3CStartU3Ed__52_System_Collections_IEnumerator_get_Current_mA718778E852612DEB1E10F3B1193B30FD4B637F5 (void);
extern void ProCamera2DParallaxLayer__ctor_m4FA2A8CD11B18CEA3FA3A9667599D3108ACE81E8 (void);
extern void ProCamera2DParallax_Awake_m065C295DFDDA28EB962FB866C944BCA3ADCD5495 (void);
extern void ProCamera2DParallax_OnDestroy_mFBB41796F39490224265D0D3DB59FC149AA93687 (void);
extern void ProCamera2DParallax_PostMove_mAA8B4CF00EC43ED15C0C343F0597B4CAC9549DB3 (void);
extern void ProCamera2DParallax_get_PMOrder_mFAAEC5B7ACCF7B055A52815369B1D0BABECF19CB (void);
extern void ProCamera2DParallax_set_PMOrder_mFDBE78DAC1707108F2D5F8A8863339A2BFFCB943 (void);
extern void ProCamera2DParallax_CalculateParallaxObjectsOffset_m96D1B112733FEFF1AA76B6367B26D84281149D87 (void);
extern void ProCamera2DParallax_Move_mE791D72268CAB06CEE48B89D9825314E86F2873C (void);
extern void ProCamera2DParallax_ToggleParallax_mFC94278568AE72B61552474C99330CEEFAF16627 (void);
extern void ProCamera2DParallax_Animate_mE40313CDE2DD9776A3AD15637DDC50995EB15F5F (void);
extern void ProCamera2DParallax__ctor_m4280BC7810CFD0C6755F64137764E1C8E4836420 (void);
extern void ProCamera2DParallax__cctor_mF568B5F5CA1F6DFB156AC07A415142D058766187 (void);
extern void U3CAnimateU3Ed__23__ctor_m25B854B3751C687AA6F5F552B0A6396F1FA31476 (void);
extern void U3CAnimateU3Ed__23_System_IDisposable_Dispose_mC42462AA29441C2706C8068C4AE8DF53B9DF5F24 (void);
extern void U3CAnimateU3Ed__23_MoveNext_m56350DA494B587ED37D401209CF45DC24342B0BC (void);
extern void U3CAnimateU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC8DB99E3889FBF634C6D35EF1BBDCB948CED5B0D (void);
extern void U3CAnimateU3Ed__23_System_Collections_IEnumerator_Reset_m6C25A48A470812AE898ACA1EB485205C2A399821 (void);
extern void U3CAnimateU3Ed__23_System_Collections_IEnumerator_get_Current_m385EF76D2756C2EF6C5489F6C3D58A012A3E38CF (void);
extern void ProCamera2DPixelPerfect_get_Zoom_m631BAE7D5B19521E884B8D22AD2FD833A0AD30F1 (void);
extern void ProCamera2DPixelPerfect_set_Zoom_m9796B518DF24E4F136D682B39F1CB1B8E276C542 (void);
extern void ProCamera2DPixelPerfect_get_ViewportScale_mAB68DBA757F8626D273A32F0083B6C92DA8A1F84 (void);
extern void ProCamera2DPixelPerfect_set_ViewportScale_m92BE71D08E9C0C13F6F0A0A8354BCCBF2C20F94E (void);
extern void ProCamera2DPixelPerfect_get_PixelStep_m1CEB173B55E0D43A4F8F917FD12876017E4235F6 (void);
extern void ProCamera2DPixelPerfect_Awake_mEC80E6BFA17E532BA03B02D6DB43E54C6AC320CA (void);
extern void ProCamera2DPixelPerfect_OnDestroy_m362C7216A8B610154AB62084A74B2676E5F2CC00 (void);
extern void ProCamera2DPixelPerfect_OverridePosition_m6211C4FE6CADF4489144A74C25DF5B17729BC160 (void);
extern void ProCamera2DPixelPerfect_get_POOrder_m15DC4559F2E22E8A2BDA87555E716EB9A1CE29EA (void);
extern void ProCamera2DPixelPerfect_set_POOrder_m390D55D1D78355C5EF4671C6CAF7CEBB703D1BC1 (void);
extern void ProCamera2DPixelPerfect_ResizeCameraToPixelPerfect_m40B5698F1F770793DF93D15C4EB861A1F66859AE (void);
extern void ProCamera2DPixelPerfect_CalculateViewportScale_m7254352966451BFEAC1D788E23E0C03F80D6084C (void);
extern void ProCamera2DPixelPerfect_CalculatePixelStep_m5F0533B91702424A31FA2C9E0ED78A9CC516A61D (void);
extern void ProCamera2DPixelPerfect__ctor_m5EEDD72F08C9B7BFACF645126919FE7009FC5BD6 (void);
extern void ProCamera2DPixelPerfect__cctor_m2D1F1C0250C135CCB2AF20186CFE406CDF53D445 (void);
extern void ProCamera2DPointerInfluence_Awake_m4B858F264F8B3760E6E1F9093B02100866B5D9B4 (void);
extern void ProCamera2DPointerInfluence_OnDestroy_m1C4F6B062CA9EBDE3FBB9131C1E04BB4FAF8AEA3 (void);
extern void ProCamera2DPointerInfluence_OnReset_mA52080E84D14C2F69B5889B122A16325FDF17C48 (void);
extern void ProCamera2DPointerInfluence_PreMove_m6581E8589B7EFAF080A321D3C6AE2A33E48FF129 (void);
extern void ProCamera2DPointerInfluence_get_PrMOrder_mCE3AD56C74BD7EB4AFF8159A70E0157770B6682C (void);
extern void ProCamera2DPointerInfluence_set_PrMOrder_mD384F739D740324448E71E9A1DE3C9CDDD2DA374 (void);
extern void ProCamera2DPointerInfluence_ApplyInfluence_mA9A3C99121925F3FC2632BE5FC8B1C6BBF34F2F2 (void);
extern void ProCamera2DPointerInfluence__ctor_m13A1EF8FB36062E9EFFB39FCDB87F503584A1BF5 (void);
extern void ProCamera2DPointerInfluence__cctor_mCD4E02A90A6BEFB7682085CEEC3C3F74536F647E (void);
extern void ProCamera2DRails_Awake_m41C2E3C60167F221CC581836C983D42C23ED20A1 (void);
extern void ProCamera2DRails_OnDestroy_m27CF39547E5CEC6A6F9AE48A8025E8EABD7EBCFC (void);
extern void ProCamera2DRails_PreMove_m57931C6F00A914F39EBBF0BFF270DC46956A647C (void);
extern void ProCamera2DRails_get_PrMOrder_mE3A8D17EF7EEB637D1AE91DBCDEAB789BDB99B26 (void);
extern void ProCamera2DRails_set_PrMOrder_m5B08496B76006A5AC01DFE86F4313D75C1533204 (void);
extern void ProCamera2DRails_Step_mC85476CBF88C327356388E00F63363338D47F842 (void);
extern void ProCamera2DRails_AddRailsTarget_m74A3DF1079D806E2DDB51DAFD768940E12554D97 (void);
extern void ProCamera2DRails_RemoveRailsTarget_mD13CBDB16E5E94313D1B8231AD698C1021F867CE (void);
extern void ProCamera2DRails_GetRailsTarget_m66A59B09C54F89CBC7744C7F094B0FC103425E68 (void);
extern void ProCamera2DRails_DisableTargets_m50366B4D5F7AEF172AAC18D4BD60CC5DC49E4812 (void);
extern void ProCamera2DRails_EnableTargets_m9F4057DF3A73B41890852EE93CC075990CFDF936 (void);
extern void ProCamera2DRails_GetPositionOnRail_m6F5C857AAC80B56126E7DF94A097D6D5231D8609 (void);
extern void ProCamera2DRails_GetPositionOnRailSegment_mB6EDEB3EB6EA59D550448F1AE5E358672592B8E4 (void);
extern void ProCamera2DRails__ctor_m3972E5C826166C72A36A5AC1CB490AA3A1C2262A (void);
extern void ProCamera2DRails__cctor_m049C7A0814B555BD98BEB6119E0387A0118A8E8C (void);
extern void ProCamera2DRepeater_get_RepeatHorizontal_mF4D0F9B030E50DE1FC4C57277E3A53A1B9C1007F (void);
extern void ProCamera2DRepeater_set_RepeatHorizontal_m5E36265249E43B7D2DF071979880941A62126C93 (void);
extern void ProCamera2DRepeater_get_RepeatVertical_mA009D2035A9AFF1CD62D67252A1CB9B0922ABB89 (void);
extern void ProCamera2DRepeater_set_RepeatVertical_mE475620B1184C121DD4E44259A0448D56DCEAB77 (void);
extern void ProCamera2DRepeater_Awake_m629FC9F4455659E8E923C0D30318E7C7E4A9B6F0 (void);
extern void ProCamera2DRepeater_OnDestroy_m3AF800F7CBC02A2B2F8E44B03F227CF3BA9E6554 (void);
extern void ProCamera2DRepeater_PostMove_mD0B658CE9623E07179D7580B34A5F33F5910F4EB (void);
extern void ProCamera2DRepeater_get_PMOrder_m0047209EF3523395312982C9FE075A08EF625F64 (void);
extern void ProCamera2DRepeater_set_PMOrder_m7F26559F0C2F31256231F999456BCC3557593519 (void);
extern void ProCamera2DRepeater_SetRepeatingObject_mCDDF77648685A07E9B2917A875B3752C936C833B (void);
extern void ProCamera2DRepeater_FreeOutOfRangeObjects_m4CDA3A64738EB856308B970EAD348372F9D96D8B (void);
extern void ProCamera2DRepeater_FillGrid_mDF37D6A974E7C640A386418E2ADA52D2C2121DB3 (void);
extern void ProCamera2DRepeater_InitCopy_m26F462B24113818D46C7006169353BB2E6D3169F (void);
extern void ProCamera2DRepeater_PositionObject_m41BAE779551237F6C1CED8E98B695E0C8B2B9429 (void);
extern void ProCamera2DRepeater_Refresh_m58FB6B3E3889462AA34CC07E91D2D3557C76F927 (void);
extern void ProCamera2DRepeater__ctor_mEFBCC03AADEFD119C118B12C7189097C9BB3D87C (void);
extern void ProCamera2DRepeater__cctor_m1C934621C9F228723C0CCA14CA097EEEB668A7CE (void);
extern void IntPoint__ctor_m9C4A5310BCBB59254735055F9344A5A6B0CCB7D5 (void);
extern void IntPoint_IsEqual_m35427735D5A80E733404C43AD10403BE41DB8328 (void);
extern void IntPoint_ToString_m8C85FBFAF05E2190667CF6EA9202BE3EA92A09FC (void);
extern void IntPoint_Equals_m74742DDABE818D5806161705E5C890FC6CB1B9FF (void);
extern void IntPoint_GetHashCode_mFB4BA17636681D2D12556AE92D86A7461171CB2D (void);
extern void IntPoint__cctor_m8E99957DD95DCC50501138760B082C2927CB52C7 (void);
extern void RepeatedObject__ctor_m20BC202C14DD1CC2DB686F8BED52B7ED1B9A3574 (void);
extern void Room__ctor_mB61B1D7757143CE57BEB1AFB1B45CDE5CF97CBD7 (void);
extern void Room__ctor_m264859649D6F7F62C13A409FCB2CF5624FA9249D (void);
extern void RoomEvent__ctor_m9058DE47F810DA8CE426E6E2E148E8ABE8EE4AE8 (void);
extern void ProCamera2DRooms_get_CurrentRoomIndex_m2872EB08913283500DA2E24C6BA4C84F336683B6 (void);
extern void ProCamera2DRooms_get_PreviousRoomIndex_m983B58C408712097E19EEDC697A6895AD3B49DF6 (void);
extern void ProCamera2DRooms_get_CurrentRoom_m9579F7C574DA8DE09CC3AE978209195844ECA56F (void);
extern void ProCamera2DRooms_get_OriginalSize_m18AB9488522597AAFEDD5D858891CFBD12949AB1 (void);
extern void ProCamera2DRooms_set_OriginalSize_m8EC167F41E08D153BCA0DC21AD996F57C5B891D8 (void);
extern void ProCamera2DRooms_Awake_mFFF50993091B03135CD38E0DC7093E7EAACFAD0B (void);
extern void ProCamera2DRooms_Start_mCA3EC509C22FB027F337DCF9021FADEC63158846 (void);
extern void ProCamera2DRooms_OnDestroy_m5EAB7DBEF6466FF92A85B810E5022E0DB6872388 (void);
extern void ProCamera2DRooms_OverridePosition_mF3E30677538999A64B8AF4CEF4FE3ADFBB51344D (void);
extern void ProCamera2DRooms_get_POOrder_m3AE25A6138380EFCEBB2DAE5EC1AC30034C75839 (void);
extern void ProCamera2DRooms_set_POOrder_mD24CCF66462F0671921D94033641B463EB0F0D7A (void);
extern void ProCamera2DRooms_OverrideSize_m16426E13C3E84EEF8D3D8992D320F4384D023CAA (void);
extern void ProCamera2DRooms_get_SOOrder_mD01487EE72B18A11C036DDC4C9CF28AE62505B82 (void);
extern void ProCamera2DRooms_set_SOOrder_mBA97C5794BCAAA5240158762865AEA3817567DAC (void);
extern void ProCamera2DRooms_TestRoom_m88475FF854A8E52AC28754CCB5D43D009B8AF0DB (void);
extern void ProCamera2DRooms_ComputeCurrentRoom_m366C0C6FF36905C867F5F2A25160DAD744A61459 (void);
extern void ProCamera2DRooms_EnterRoom_mA5B3D64E0CBC8A0CE6D21735E080540665C622D6 (void);
extern void ProCamera2DRooms_EnterRoom_mE1C8FA9757CC123FF5446C4C89EBF8ED1B1AE8F3 (void);
extern void ProCamera2DRooms_ExitRoom_mB189413FCC3A444265A93F15782588E34E526D41 (void);
extern void ProCamera2DRooms_AddRoom_mF022F1F2846E1CBE4ED70BBE4631889D68E862C8 (void);
extern void ProCamera2DRooms_RemoveRoom_m3A98C09EB9340D467C2EB4F4E40020F343598650 (void);
extern void ProCamera2DRooms_SetDefaultNumericBoundariesSettings_m9F4AA086B92D7D998B0DBBB2EE012B6FDA7B42A2 (void);
extern void ProCamera2DRooms_GetRoom_m1D323258CF3CA83B72D6903F17F4DF0D35F78503 (void);
extern void ProCamera2DRooms_GetCameraSizeForRoom_m7FD34831D8180815A7553C834ACEC11E7570E1C0 (void);
extern void ProCamera2DRooms_TestRoomRoutine_m65646DD9BDED0840B9C4D41647C9B5852609E8B4 (void);
extern void ProCamera2DRooms_TransitionToRoom_m88F5A6D0A3945E9CCCE799CE8F6DAED6CF4B3226 (void);
extern void ProCamera2DRooms_TransitionRoutine_mC90AA092E2E39061598D3E477605FEFC8A8F14EA (void);
extern void ProCamera2DRooms_LimitToNumericBoundaries_mDEDEEF49BE64C6DC6D207383D71E282D7CFF66B3 (void);
extern void ProCamera2DRooms__ctor_m106614BAF97A6164804D7836D5D8C0736F859C44 (void);
extern void U3CU3Ec__DisplayClass49_0__ctor_m094DC40D614A7E4A3E528CFA8D5AFF8BDFC52BC2 (void);
extern void U3CU3Ec__DisplayClass49_0_U3CEnterRoomU3Eb__0_m556D9979DCE73CC02B7A2F4F1E745631EC61D2E7 (void);
extern void U3CU3Ec__DisplayClass52_0__ctor_m66B2687F8E12F159D0D575D4373563C62122154D (void);
extern void U3CU3Ec__DisplayClass52_0_U3CRemoveRoomU3Eb__0_m550BF73461CBF9C45E7F402B4FCCAE7D4E80AA73 (void);
extern void U3CU3Ec__DisplayClass54_0__ctor_mB7A0FEDD02BC5B6F93CBE1F227B0D85D7784B8D3 (void);
extern void U3CU3Ec__DisplayClass54_0_U3CGetRoomU3Eb__0_mB27B203A22D140860121A0A14EAF8CD4B6DBACD4 (void);
extern void U3CTestRoomRoutineU3Ed__56__ctor_m6FDB9514500AF02FBA25A4B153BF9334FCAA0947 (void);
extern void U3CTestRoomRoutineU3Ed__56_System_IDisposable_Dispose_m86F4CC05D9A6CB734647916752D7B4A3A9AFE346 (void);
extern void U3CTestRoomRoutineU3Ed__56_MoveNext_m3ADDB51D7AF17A31CF9465D0BD6424C8AC34F9AB (void);
extern void U3CTestRoomRoutineU3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2E152DE74D6C71DDAE14DF0F996E026E0618B888 (void);
extern void U3CTestRoomRoutineU3Ed__56_System_Collections_IEnumerator_Reset_mED22D9D7ABE7105FDBFDF20C2B53B9E7C31FB9BC (void);
extern void U3CTestRoomRoutineU3Ed__56_System_Collections_IEnumerator_get_Current_m0EF13F0090627020DF0959446288C922A82B9944 (void);
extern void U3CTransitionRoutineU3Ed__58__ctor_mEA1BFC4251CAF4CA5D4DBCC9385074A7B55234FF (void);
extern void U3CTransitionRoutineU3Ed__58_System_IDisposable_Dispose_m3B9CC89333F22450D5AA1EDECE23F79A67E0807C (void);
extern void U3CTransitionRoutineU3Ed__58_MoveNext_m1D9E7FFFA04AE5461AF12CF9F85605F9FAEC01EF (void);
extern void U3CTransitionRoutineU3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2806747902CA94B5CC7A9A2542AFDC721C10FAD1 (void);
extern void U3CTransitionRoutineU3Ed__58_System_Collections_IEnumerator_Reset_mA044BAA75A59A72B38B1DFCC5D66803B16473549 (void);
extern void U3CTransitionRoutineU3Ed__58_System_Collections_IEnumerator_get_Current_m6DE41234405E344F0CA1CE5EF233B1C557B2B5B0 (void);
extern void ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C (void);
extern void ProCamera2DShake_get_Exists_m48B4D86F7A7E46A63FF4CAA8888D034FE7C9736D (void);
extern void ProCamera2DShake_Awake_m2B25CA30C19C768E774A9E1F929D809652C4E649 (void);
extern void ProCamera2DShake_Start_mA7118CBC2350FFBE9AAE9F13DDE80CEC7619B470 (void);
extern void ProCamera2DShake_Update_m5BC9FF27D6BE23D29D0DB0683EE507D876BB8827 (void);
extern void ProCamera2DShake_Shake_m1FD42A61C4EC8B2A58F35EEA7E7B14CCA2B4F257 (void);
extern void ProCamera2DShake_Shake_m41DBF5CBF18A3437316C56B12B2853BC0C4792B5 (void);
extern void ProCamera2DShake_Shake_m96A4C403647156FFF72E5EEA997EA9EFA6AA050B (void);
extern void ProCamera2DShake_Shake_mE8B4BCA0C5CDE8FF4E46B3652E3C9C3966EDB2CA (void);
extern void ProCamera2DShake_StopShaking_m6CAED85FB1681ACDAEB654CEFB3595D3B4E157C1 (void);
extern void ProCamera2DShake_ConstantShake_mC6A3FE872FF5DF20546CE0D4BDF99EFC0BD2B76E (void);
extern void ProCamera2DShake_ConstantShake_m5B386A99D697BBC33CB11D2E652B881C2869D46A (void);
extern void ProCamera2DShake_ConstantShake_mBCAFA5FC0FFFAA9C69559A48117FBAA695C4B164 (void);
extern void ProCamera2DShake_StopConstantShaking_mDE370598162F40CD59FDB341CAF76CEF6A26C190 (void);
extern void ProCamera2DShake_ApplyShakesTimed_mE54EEA471792ED1872EA7BD0B4C20D94CD8D44E9 (void);
extern void ProCamera2DShake_ApplyInfluenceIgnoringBoundaries_m6CD9C0DB0782E62E47D82B325353D81F3AC09006 (void);
extern void ProCamera2DShake_ApplyShakesTimed_m66D6A76E474311CE1EFA452559286F206A18EE89 (void);
extern void ProCamera2DShake_ShakeRoutine_m6355DC769E6C2971AF0B056911BFA80E7B13A686 (void);
extern void ProCamera2DShake_ShakeCompleted_m279C6AC487C8569DD08AA84FC448FCAF98165E22 (void);
extern void ProCamera2DShake_ApplyShakesTimedRoutine_mA3AB8BF75E78F2CE2A0529C71C3047A9A908BA08 (void);
extern void ProCamera2DShake_ApplyShakeTimedRoutine_mE5200201C5B2D3842A68D3B2055A7BC72E86D3AE (void);
extern void ProCamera2DShake_StopConstantShakeRoutine_mBD2EB8330B5FAF866ACA4A5330D7138A07635C08 (void);
extern void ProCamera2DShake_CalculateConstantShakePosition_mCB1F4CB3966BA7181812F418695B27D9F653573D (void);
extern void ProCamera2DShake_ConstantShakeRoutine_mC333456A6D80BF77E06AB6EEBD90A4A3678A4731 (void);
extern void ProCamera2DShake__ctor_m181D54568E60522D13F8E022BC9F1BEDBD3D5267 (void);
extern void ProCamera2DShake__cctor_mC822D1381917AD468E56F3A4051FEA9B19526CAF (void);
extern void U3CApplyShakeTimedRoutineU3Ed__44__ctor_m687B7C15896A81C5D8E200B609EE5669EF3A1704 (void);
extern void U3CApplyShakeTimedRoutineU3Ed__44_System_IDisposable_Dispose_mE4C420C515042D4E478C4D30A14C4578AB642C6B (void);
extern void U3CApplyShakeTimedRoutineU3Ed__44_MoveNext_mC2D10AF1C7869910BF3DFB66171185549C81C549 (void);
extern void U3CApplyShakeTimedRoutineU3Ed__44_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5DD8EB4807ED68D5DF748C00D08C60FA473047CE (void);
extern void U3CApplyShakeTimedRoutineU3Ed__44_System_Collections_IEnumerator_Reset_m278758615A631971BB0F41A62F15BEFA84D44A6F (void);
extern void U3CApplyShakeTimedRoutineU3Ed__44_System_Collections_IEnumerator_get_Current_mAB5CE84573D43B8F77C3B1914D66622DD47988BA (void);
extern void U3CApplyShakesTimedRoutineU3Ed__43__ctor_mCD34110E87E4C894813A8909F44E6AA614DF5703 (void);
extern void U3CApplyShakesTimedRoutineU3Ed__43_System_IDisposable_Dispose_mF5600C8D3897BB74F5C64E81067FBA148DE650CE (void);
extern void U3CApplyShakesTimedRoutineU3Ed__43_MoveNext_m2C77FD5E23E6BDE558D0C16F1AF19344EB0E1D70 (void);
extern void U3CApplyShakesTimedRoutineU3Ed__43_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD355AE36E25CAABC4463F14C8285625D8D9B880C (void);
extern void U3CApplyShakesTimedRoutineU3Ed__43_System_Collections_IEnumerator_Reset_m524EB72FB3F3CB567E584544D14582DE4872D0F8 (void);
extern void U3CApplyShakesTimedRoutineU3Ed__43_System_Collections_IEnumerator_get_Current_m37EEBEE74FDA97155071FBC93DEE00682816A4FB (void);
extern void U3CCalculateConstantShakePositionU3Ed__46__ctor_mE5ED188487C4EBC6A1BE9A3670C571A6F512B27A (void);
extern void U3CCalculateConstantShakePositionU3Ed__46_System_IDisposable_Dispose_mB7F0AD16CA89DE4379D2F3E055304AEF68739E89 (void);
extern void U3CCalculateConstantShakePositionU3Ed__46_MoveNext_m8501D1FC7265EA2CD043872390EFD363009F40ED (void);
extern void U3CCalculateConstantShakePositionU3Ed__46_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4780230FA9D0691158D82792A8B907AC1CC1B970 (void);
extern void U3CCalculateConstantShakePositionU3Ed__46_System_Collections_IEnumerator_Reset_m103197FF1D28C257970D4E7CD102A16D5B1382B5 (void);
extern void U3CCalculateConstantShakePositionU3Ed__46_System_Collections_IEnumerator_get_Current_mBFB5803D52C6DC89C09890ADCE16C089F8EEDE8F (void);
extern void U3CConstantShakeRoutineU3Ed__47__ctor_m0AAD63C7EFC2A02D20BF84C9AE5EA7D36706B170 (void);
extern void U3CConstantShakeRoutineU3Ed__47_System_IDisposable_Dispose_m612A77F1EDA887A3259F9FD7047AD13F760718FE (void);
extern void U3CConstantShakeRoutineU3Ed__47_MoveNext_m0709F4B063E296EA02F2590E3E926686A858F890 (void);
extern void U3CConstantShakeRoutineU3Ed__47_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2F38B74F4DEEB1E6338D5D3B6031C9A953CEE485 (void);
extern void U3CConstantShakeRoutineU3Ed__47_System_Collections_IEnumerator_Reset_m055E6E66969146B776E08F8DD798525444B5542B (void);
extern void U3CConstantShakeRoutineU3Ed__47_System_Collections_IEnumerator_get_Current_m73259E19CE4A2FCAA87C8D5A995051C8B628B973 (void);
extern void U3CShakeRoutineU3Ed__41__ctor_mE84017646673FB07996C92DA43F12BD46CBEDDC3 (void);
extern void U3CShakeRoutineU3Ed__41_System_IDisposable_Dispose_m479B006D6242E75326C4E3526034E2D593E8AB4B (void);
extern void U3CShakeRoutineU3Ed__41_MoveNext_m4D71BD993159F0C236E2936A4D559CD8BA1A6ABE (void);
extern void U3CShakeRoutineU3Ed__41_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEEA71DAD4E374786B8A49F5E301BD18F8CEEFB50 (void);
extern void U3CShakeRoutineU3Ed__41_System_Collections_IEnumerator_Reset_m441512342D413EB015826E81EFD0C3B60BA66573 (void);
extern void U3CShakeRoutineU3Ed__41_System_Collections_IEnumerator_get_Current_mE97E9D008A2CD11EEF6EC51010D2F7CD8E7D0CE7 (void);
extern void U3CStopConstantShakeRoutineU3Ed__45__ctor_m72F91CF09520C0C8CCDB2C95FE0D6D8E29A8B5EE (void);
extern void U3CStopConstantShakeRoutineU3Ed__45_System_IDisposable_Dispose_mBBEBA2AA94458C83ADD80319798D11398191DB3D (void);
extern void U3CStopConstantShakeRoutineU3Ed__45_MoveNext_m1241A7C7F4C89604333EF21EAA5176EE6FE5716B (void);
extern void U3CStopConstantShakeRoutineU3Ed__45_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6F8E21E5858268DFEEC32E67630BB4004D06B0FD (void);
extern void U3CStopConstantShakeRoutineU3Ed__45_System_Collections_IEnumerator_Reset_m8772142BFBB5892ECB4EFCE8F6E75BB76A948881 (void);
extern void U3CStopConstantShakeRoutineU3Ed__45_System_Collections_IEnumerator_get_Current_mE68C8D319C61185C60C49DDAFDF426235834B39D (void);
extern void ProCamera2DSpeedBasedZoom_Awake_mBB0821C84948E71450161CE4F702ADF7829D743F (void);
extern void ProCamera2DSpeedBasedZoom_OnDestroy_m836EA45E38763DBD5058EBFD167418D45305033D (void);
extern void ProCamera2DSpeedBasedZoom_AdjustSize_mDE874E73F005BE1F8E0E61B02416D99DAC41D548 (void);
extern void ProCamera2DSpeedBasedZoom_get_SDCOrder_mA9911E45EFD79D4BA41F6CD7F6530D2700640884 (void);
extern void ProCamera2DSpeedBasedZoom_set_SDCOrder_m20C2630AF9FE87D2010CFA9D957D8848F34AC268 (void);
extern void ProCamera2DSpeedBasedZoom_OnReset_m248A3024A62073840D02C7BA5C77F394B434C734 (void);
extern void ProCamera2DSpeedBasedZoom__ctor_m1795586099F240D33BDC6A6EB8517A4414A468B2 (void);
extern void ProCamera2DSpeedBasedZoom__cctor_mE14DAEEEE2F274D15366EE4CEDBF9573916314E6 (void);
extern void ProCamera2DTransitionsFX_get_Instance_mFF94EBFD9B64ABF81E88E2C0FE7EF919907DB1C8 (void);
extern void ProCamera2DTransitionsFX_Awake_m83747C922890F4DCF2E5AF6BFFCAC923A5258B0C (void);
extern void ProCamera2DTransitionsFX_TransitionEnter_m97589A7091603A387838B1F5AAAC32A061C90781 (void);
extern void ProCamera2DTransitionsFX_TransitionExit_m604CC64907B1AB598DA732CC2B2B8509D7B4EAB4 (void);
extern void ProCamera2DTransitionsFX_UpdateTransitionsShaders_m5DD784CEE7EC4A2DFBBEA5F33A8557AC3421090D (void);
extern void ProCamera2DTransitionsFX_UpdateTransitionsProperties_m0A71740385D815763DC2C09D8B36C7E3F99818CE (void);
extern void ProCamera2DTransitionsFX_UpdateTransitionsColor_mCF179BC5C4566B891D20E9FBA004160DF2D0E05B (void);
extern void ProCamera2DTransitionsFX_Clear_mAE1E26F9D6DB4D055DCB6645AD3C9B165643A65F (void);
extern void ProCamera2DTransitionsFX_Transition_m3F1D638C82A330DC9522708EB7CF541C59D84762 (void);
extern void ProCamera2DTransitionsFX_TransitionRoutine_mF39F8E2090B9C864404F19FEB1B6ED1B20273810 (void);
extern void ProCamera2DTransitionsFX__ctor_m4D3D4AA4577616805B822858C724A96E4E5D1235 (void);
extern void ProCamera2DTransitionsFX__cctor_mC0783168436854FDC39F15B01DB52E8AA279C27E (void);
extern void U3CTransitionRoutineU3Ed__48__ctor_mAFBDE72D273D7D7A3CE0A61EF6F442DD799F38F8 (void);
extern void U3CTransitionRoutineU3Ed__48_System_IDisposable_Dispose_m8C0D6CF81201EF10D0C23C67732C7668ECD2A415 (void);
extern void U3CTransitionRoutineU3Ed__48_MoveNext_m7F7782236269177078BC15B52FC57B3C81BAEC18 (void);
extern void U3CTransitionRoutineU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m651E1B2E4C792BAC7894F727E7FF4C54E81F089D (void);
extern void U3CTransitionRoutineU3Ed__48_System_Collections_IEnumerator_Reset_mC97D545A45CAFFBACDC95C1F1FAA24419CA3BE40 (void);
extern void U3CTransitionRoutineU3Ed__48_System_Collections_IEnumerator_get_Current_m6F402ABE06FA73C1659FA9E2D7ADA4B366509BC4 (void);
extern void ProCamera2DZoomToFitTargets_Awake_mB9D41E48BEC6D652D41C27FEDE78499D8B0E86D0 (void);
extern void ProCamera2DZoomToFitTargets_OnDestroy_m747CAE23396AABB1BED55164D787A34BBEE6FBCE (void);
extern void ProCamera2DZoomToFitTargets_OverrideSize_m48628C10546450A410485E9FC6DE321DCE84845F (void);
extern void ProCamera2DZoomToFitTargets_get_SOOrder_m93B34872D95CB62C5A61D6AF16A05B71494796A9 (void);
extern void ProCamera2DZoomToFitTargets_set_SOOrder_mC9555088E8C17F324CE59671D230F5104A3C7CBC (void);
extern void ProCamera2DZoomToFitTargets_OnReset_m7D4A7C48D0FD0A0EA6DAA5CD6FA1A397A0A2557F (void);
extern void ProCamera2DZoomToFitTargets_UpdateTargetCamSize_mA76AD1126BD094927B66C5AAA0D761C5EFF2E7B6 (void);
extern void ProCamera2DZoomToFitTargets__ctor_m3DDB2A84AF366151C2B07BC55C6C8BE3DBC3E891 (void);
extern void ProCamera2DZoomToFitTargets__cctor_mCFE44585301AA63C2AF87DB18B0602CDA04CD478 (void);
extern void ProCamera2DLetterbox_get_material_m41B5D137FB07EF0472DC82A66A621BBB43A43A1A (void);
extern void ProCamera2DLetterbox_OnEnable_mC1C7DF3FC09154C10F17EDCB04744749DBCF4B12 (void);
extern void ProCamera2DLetterbox_OnRenderImage_m3B13831E7BE8768AF6B4E2B99577A98407BA6AB8 (void);
extern void ProCamera2DLetterbox_OnDisable_mECA682388BD04DB186107DF9ABEAFCCD865CB6F2 (void);
extern void ProCamera2DLetterbox_TweenTo_m5CAF1779A917341DE5E02BF9ABE9F94898D6BB3D (void);
extern void ProCamera2DLetterbox_TweenToRoutine_m440D8BAD172FC93FBB78B3A716585285FAC42D36 (void);
extern void ProCamera2DLetterbox__ctor_m60D17EE1EF4C6CAE7E0B7F970741A2143557C2C3 (void);
extern void U3CTweenToRoutineU3Ed__13__ctor_m92526D77BAF39035104A37A0D06AE65667CD076D (void);
extern void U3CTweenToRoutineU3Ed__13_System_IDisposable_Dispose_m7429AE9B30C164E00A2FE8206F42B03FDB71B28A (void);
extern void U3CTweenToRoutineU3Ed__13_MoveNext_m5A11609ED8A972EAF6D2DC77F4C7399BDE9A586C (void);
extern void U3CTweenToRoutineU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3D2BEA18B590EA9B3A62F848571AD2779B556895 (void);
extern void U3CTweenToRoutineU3Ed__13_System_Collections_IEnumerator_Reset_m310742DC16415B7C3F57F4D2BEB21AE689010B7A (void);
extern void U3CTweenToRoutineU3Ed__13_System_Collections_IEnumerator_get_Current_mA7D416662D58A21EF2CC8D2045D36E1DA7FED915 (void);
extern void ProCamera2DParallaxObject__ctor_m0D5CE16184C882D42A7767C8770EACA2EDC0AD96 (void);
extern void ProCamera2DPixelPerfectSprite_Awake_mCE253D662B71BA07C0890199AEAD25AA6E1B43BB (void);
extern void ProCamera2DPixelPerfectSprite_Start_m61D4E4375AF700776A617837466DE63BFDD6CAC7 (void);
extern void ProCamera2DPixelPerfectSprite_PostMove_m4F3561BEEC1FA42A9A3CB3582D0EC580F1272372 (void);
extern void ProCamera2DPixelPerfectSprite_get_PMOrder_mAC15AE20886ED8FA093D08DB334AB3B69D648B88 (void);
extern void ProCamera2DPixelPerfectSprite_set_PMOrder_mFC3CCCA34102A2C493C901A148F37626DFF7A1AD (void);
extern void ProCamera2DPixelPerfectSprite_Step_m9E6564A4EDE2C697CBE0FB3FF26DC1999D14C9E3 (void);
extern void ProCamera2DPixelPerfectSprite_GetPixelPerfectPlugin_m94AA74AC683F0F3FE661C01F13FC7BC98AA9FB2F (void);
extern void ProCamera2DPixelPerfectSprite_GetSprite_m7FDFB315397CE61F18530ABE812128D31DAFC4C3 (void);
extern void ProCamera2DPixelPerfectSprite_SetAsPixelPerfect_m6606196A8BC9FCC4022947810F043C12813667CE (void);
extern void ProCamera2DPixelPerfectSprite_OnDestroy_m6EC1AFD283B7C9DD72A19FEB06C014C194824C04 (void);
extern void ProCamera2DPixelPerfectSprite__ctor_mDA349346F1ACE4828712F7E49DEA8F504299E355 (void);
extern void BasicBlit_OnRenderImage_mBC8EF27FAEC68AE6E65E7DABDD4F394DECE8F89D (void);
extern void BasicBlit__ctor_mEA495A90A3232651A90CE4FB6B4920717D29AD31 (void);
extern void ProCamera2DTriggerBoundaries_get_IsCurrentTrigger_m0F26039ECCAA4664D3C2E981C8EE6D64BF6D49A5 (void);
extern void ProCamera2DTriggerBoundaries_set_SetAsStartingBoundaries_m1D022A849F6C74463BD7C8942F9605A94930CCC3 (void);
extern void ProCamera2DTriggerBoundaries_get_SetAsStartingBoundaries_m2B63B6904DF9C8FAEEF2095B46A9D31576617A75 (void);
extern void ProCamera2DTriggerBoundaries_Awake_mE0486355D69F9CFBAD24C50D92D7549A28564736 (void);
extern void ProCamera2DTriggerBoundaries_OnDestroy_mE40AFF497571D514A2B6B51F8E3246668DEE2482 (void);
extern void ProCamera2DTriggerBoundaries_Start_mD2583B28266D1E67AA231B23BA6793D0D252FD57 (void);
extern void ProCamera2DTriggerBoundaries_OverridePosition_m8A92844E9317BC03F3854736B689A61472A46B63 (void);
extern void ProCamera2DTriggerBoundaries_get_POOrder_m4461C35F7352E0665F487CE6913BA120B0BBE9E3 (void);
extern void ProCamera2DTriggerBoundaries_set_POOrder_mE7CAD9C74EB475CA9B7828C88DEBE4EC7A5046E4 (void);
extern void ProCamera2DTriggerBoundaries_EnteredTrigger_mA2150C095983468C28AF90E4A311CED6016CDB93 (void);
extern void ProCamera2DTriggerBoundaries_TurnOffPreviousTrigger_mDDEA5B9B016B007549C4AB607782C9270DBC58A6 (void);
extern void ProCamera2DTriggerBoundaries_SetBoundaries_m24586EAFEDA671A33DE96AFB32C17835523D060C (void);
extern void ProCamera2DTriggerBoundaries_GetTargetBoundaries_mAD76686381487BA35FE2F1C6EC34D5BEDA648653 (void);
extern void ProCamera2DTriggerBoundaries_Transition_mF643E0C0669C37DF8DBBC0FAD0A8FB49D4DC7839 (void);
extern void ProCamera2DTriggerBoundaries_MoveCameraToTarget_m021320D764B2562D4421CA59B775CC42C3A15D6A (void);
extern void ProCamera2DTriggerBoundaries_LimitToNumericBoundaries_mA3C4CE46F1ED11F4C3610521F5E771A68648142D (void);
extern void ProCamera2DTriggerBoundaries__ctor_mE510F9EDDFC4D70095E9E71BE439175D76EA8FA5 (void);
extern void ProCamera2DTriggerBoundaries__cctor_mD6B98019227BF35A2C7BA27535E0B32A616C8D09 (void);
extern void ProCamera2DTriggerBoundaries_U3CStartU3Eb__32_0_m6A54C39B327C74E6B119B3925B6645125B390765 (void);
extern void ProCamera2DTriggerBoundaries_U3CStartU3Eb__32_1_m97ECC68428FD8749E33C84A8D7E1549E1CE272B4 (void);
extern void U3CMoveCameraToTargetU3Ed__43__ctor_m40DA53D2E23B1E5D35F7ED905429D47C8CBEE30F (void);
extern void U3CMoveCameraToTargetU3Ed__43_System_IDisposable_Dispose_mCF03288B774F24B2F43F65A770F64E9F7B918478 (void);
extern void U3CMoveCameraToTargetU3Ed__43_MoveNext_m3812AC5CE64A819F195F0EAE8EF0AB75F39D2E32 (void);
extern void U3CMoveCameraToTargetU3Ed__43_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1526A2F5322CD2D3119267A3CD4BF14F293DD4E6 (void);
extern void U3CMoveCameraToTargetU3Ed__43_System_Collections_IEnumerator_Reset_mE9B7FB6230CB5090A8C7DA9D3996C3154592C81D (void);
extern void U3CMoveCameraToTargetU3Ed__43_System_Collections_IEnumerator_get_Current_m20A60259478537CA397326E36DB3D61776241B10 (void);
extern void U3CTransitionU3Ed__42__ctor_m5951BDBBEC1FBC95252AB4C9C9D0CA8DDAFC89F3 (void);
extern void U3CTransitionU3Ed__42_System_IDisposable_Dispose_m268FDFB3E2A3294ED96F0E8D464C567A3124DB29 (void);
extern void U3CTransitionU3Ed__42_MoveNext_mE326F04D041D1DFF2F951E1836A7F6B26930E098 (void);
extern void U3CTransitionU3Ed__42_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8473324E157C0EDBC68DA12F9DE6DC853D752675 (void);
extern void U3CTransitionU3Ed__42_System_Collections_IEnumerator_Reset_m8075A76B6DC253E0A3D9208D153FB9C718A03789 (void);
extern void U3CTransitionU3Ed__42_System_Collections_IEnumerator_get_Current_m5329B950AEA855D0330B62C870EF9CC969258303 (void);
extern void U3CTurnOffPreviousTriggerU3Ed__39__ctor_m074BD6A748F8A8CCF82D6EE1CABCBE60EB2FFC73 (void);
extern void U3CTurnOffPreviousTriggerU3Ed__39_System_IDisposable_Dispose_mEDF9463CDA394718E972BF2C2DA91758DEFBF2C2 (void);
extern void U3CTurnOffPreviousTriggerU3Ed__39_MoveNext_m16E02A68A7FF429A3410A9339F4EEDB361D1970E (void);
extern void U3CTurnOffPreviousTriggerU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9DEE9AE44D5366788DA1603663049749D10047E3 (void);
extern void U3CTurnOffPreviousTriggerU3Ed__39_System_Collections_IEnumerator_Reset_mB267AB9C360AE66737A7A07CF6355AA9B8EE85E2 (void);
extern void U3CTurnOffPreviousTriggerU3Ed__39_System_Collections_IEnumerator_get_Current_m573ED4A91402546E25171E97CFC6CE6E764F3462 (void);
extern void ProCamera2DTriggerInfluence_Start_m389A7440586101060D862CFC6EDD880B37032CAF (void);
extern void ProCamera2DTriggerInfluence_EnteredTrigger_m7F6C298BB93A5A2D42C8FD08279DB23BE3D03C17 (void);
extern void ProCamera2DTriggerInfluence_ExitedTrigger_m0BF1AA6106E60B2AB2525EA1758D370D7D9F049D (void);
extern void ProCamera2DTriggerInfluence_InsideTriggerRoutine_m4A65010C3395325E78FBEA4ACDE4E32C17FD8282 (void);
extern void ProCamera2DTriggerInfluence_OutsideTriggerRoutine_m6AE9DC9E6802F4F998C101C96923F10EDAF43149 (void);
extern void ProCamera2DTriggerInfluence__ctor_m0EABE094E3C3DC8EF871ECFE9A380668B981E780 (void);
extern void ProCamera2DTriggerInfluence__cctor_m080BFB1AFAC00A2F83099F553BD6D2E9BCD159AF (void);
extern void U3CInsideTriggerRoutineU3Ed__13__ctor_m110B6FE874534C7F9CA66964B287F16E2DE0F110 (void);
extern void U3CInsideTriggerRoutineU3Ed__13_System_IDisposable_Dispose_m78E9F1EDFDCC0A3A98365944E6CFBFCFA67F43E4 (void);
extern void U3CInsideTriggerRoutineU3Ed__13_MoveNext_mA50A5683B352FE0FD5280E2F6E927E7D0F8C1A45 (void);
extern void U3CInsideTriggerRoutineU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDCA5AA1ED34F87D392ABF5BEF9F66EFACDD96A3B (void);
extern void U3CInsideTriggerRoutineU3Ed__13_System_Collections_IEnumerator_Reset_m822E63EE43C93039B969980B246E74344E6D2DA9 (void);
extern void U3CInsideTriggerRoutineU3Ed__13_System_Collections_IEnumerator_get_Current_m7E768CAD37D82EAC017C6C2BBD2134C35246B11B (void);
extern void U3COutsideTriggerRoutineU3Ed__14__ctor_m4DB22F34EB37949083D5ACE23703A5F6A6C74CF6 (void);
extern void U3COutsideTriggerRoutineU3Ed__14_System_IDisposable_Dispose_m646544044A17FD70909BB4168613C638C75F8992 (void);
extern void U3COutsideTriggerRoutineU3Ed__14_MoveNext_m22E117DAB1B28CB69C4ED0E92FA3044F9D12094E (void);
extern void U3COutsideTriggerRoutineU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m84AB448C8A113F53FFB257D4D409D9A38E347190 (void);
extern void U3COutsideTriggerRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m71B3218DDEF90CF9721E6AC5F067FBCA6370BC2F (void);
extern void U3COutsideTriggerRoutineU3Ed__14_System_Collections_IEnumerator_get_Current_m63514FE2E0B02005C91C27A05F7669B76038BE64 (void);
extern void ProCamera2DTriggerRails_Start_mA3D1CB8284DD13FF28B6DECF419873C9DAD13180 (void);
extern void ProCamera2DTriggerRails_EnteredTrigger_mA3674C99C64F7B32821F2C47854AE507C648C12D (void);
extern void ProCamera2DTriggerRails__ctor_mADA8CDF1FCA91F925CA27FAD8982E73AA5250012 (void);
extern void ProCamera2DTriggerRails__cctor_mCD3228061F420D17F93AAA5D71024739E0B60624 (void);
extern void ProCamera2DTriggerZoom_Start_mF4260EB2F36B75B2ABD27AE899F2F7135C35A38C (void);
extern void ProCamera2DTriggerZoom_EnteredTrigger_mE007D4657FE496171D84E059344BF8039140B917 (void);
extern void ProCamera2DTriggerZoom_ExitedTrigger_mF71BBBE905E1439E8B3659862370E2FBE28AC005 (void);
extern void ProCamera2DTriggerZoom_InsideTriggerRoutine_m2E9C8100B5F183D911835D9DC6596D3F689C6C31 (void);
extern void ProCamera2DTriggerZoom_OutsideTriggerRoutine_m24D6EA57BA7D6A5D1861F99F079D0CDFC4E6438F (void);
extern void ProCamera2DTriggerZoom_UpdateScreenSize_m2B1D86795B8EEE2D87E2F55818CC0D9DF2460F1F (void);
extern void ProCamera2DTriggerZoom__ctor_m3A9C194F4F0FEAAD004A4F32DF2510DCD4DE6D00 (void);
extern void ProCamera2DTriggerZoom__cctor_m528B49CB208D00C57E09C8DF117EE3A199FE6540 (void);
extern void U3CInsideTriggerRoutineU3Ed__17__ctor_m17EB0F9A747DF7F073EF479C72FFC63728589CE0 (void);
extern void U3CInsideTriggerRoutineU3Ed__17_System_IDisposable_Dispose_mB42B9C48488683ECDDEEE2359CA8EA5DBF73C380 (void);
extern void U3CInsideTriggerRoutineU3Ed__17_MoveNext_mE030C331B3F93DCDE0CD1AC12F77D91B10C3828E (void);
extern void U3CInsideTriggerRoutineU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6D2177118E614DEBAACC6B6B10C0E6C99BD20FC2 (void);
extern void U3CInsideTriggerRoutineU3Ed__17_System_Collections_IEnumerator_Reset_m47510A30DC69EFD69BFCCA676F84970340A8E464 (void);
extern void U3CInsideTriggerRoutineU3Ed__17_System_Collections_IEnumerator_get_Current_m4BBB1598C0DAB61D4F7A1B238FB3B94FED0D484D (void);
extern void U3COutsideTriggerRoutineU3Ed__18__ctor_mC0D3AA336840C93A236645C30DD4EC70C0AEAC3E (void);
extern void U3COutsideTriggerRoutineU3Ed__18_System_IDisposable_Dispose_mB9EC19A0EE4BA3383A66579BBFEE819DFCA3CE3C (void);
extern void U3COutsideTriggerRoutineU3Ed__18_MoveNext_mE28D8D56F393C05CCF7E911AD372FB2E4A3A74F8 (void);
extern void U3COutsideTriggerRoutineU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m798E326449E7771D7BA668E9C3AB8FE87EC346EF (void);
extern void U3COutsideTriggerRoutineU3Ed__18_System_Collections_IEnumerator_Reset_mB6C0241ED94ABAEDC87604F761EE137378AC71A5 (void);
extern void U3COutsideTriggerRoutineU3Ed__18_System_Collections_IEnumerator_get_Current_mDEE596CBF6843F937FC8458366F6FC238F63B8C4 (void);
extern void ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28 (void);
extern void ProCamera2D_get_Exists_m4702898858AB085D2F78DA1242FDF263FCA53D19 (void);
extern void ProCamera2D_get_IsMoving_m4FBCEF1E2C24BAC053B60EF1DBCF46C1017F3697 (void);
extern void ProCamera2D_get_Rect_mD7D77ED3B640F21706E8D0606B92C36FA6A433DF (void);
extern void ProCamera2D_set_Rect_mF8D32018B856440EA11E3312EA29B7ACFD98C6A5 (void);
extern void ProCamera2D_get_CameraTargetPositionSmoothed_m8EE960E49C580F2AF11A2F843A3764FE617883C2 (void);
extern void ProCamera2D_set_CameraTargetPositionSmoothed_m73A51A4BA3B992CD9159761FA317C72040D759C7 (void);
extern void ProCamera2D_get_LocalPosition_mA31BA2DDC21E228105F008ECBD9995BA59459455 (void);
extern void ProCamera2D_set_LocalPosition_m1A584670A06993B90F168C80E1954A45130A8603 (void);
extern void ProCamera2D_get_StartScreenSizeInWorldCoordinates_m2FFEDD76966A159EA8E1F49C60E1DB372C7EC70E (void);
extern void ProCamera2D_set_StartScreenSizeInWorldCoordinates_m76B3840F540476EDFC0E56A7A7ADFA2573DB9FC2 (void);
extern void ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4 (void);
extern void ProCamera2D_set_ScreenSizeInWorldCoordinates_mBF29DF6569154687FB9D8DF9BE120A32A1858D45 (void);
extern void ProCamera2D_get_PreviousTargetsMidPoint_m0BEC736AD8F3639DB8D0D3D841449FB1641AB2B8 (void);
extern void ProCamera2D_set_PreviousTargetsMidPoint_m4DD50C1D7B40DE2EBDFF688FC6D2DF2D4544EFB0 (void);
extern void ProCamera2D_get_TargetsMidPoint_m591ED50B4118C4D1F0B0203647587E45D7F5C917 (void);
extern void ProCamera2D_set_TargetsMidPoint_mBEBDB1FA4E967415DE743D063057ECD8D08F9F73 (void);
extern void ProCamera2D_get_CameraTargetPosition_m4CE8C22A89C1CEF55F22E74175CC63A3778DFA29 (void);
extern void ProCamera2D_set_CameraTargetPosition_m952C818F6A7F07B49E5FD163039FF4F839042FBE (void);
extern void ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1 (void);
extern void ProCamera2D_set_DeltaTime_m10BF31E3556A74382A9E4CEA341D366D018C2AA8 (void);
extern void ProCamera2D_get_ParentPosition_mF0EDE246FB6B2EFD359ECDA1C0A1567D37DB0564 (void);
extern void ProCamera2D_set_ParentPosition_m88349F9DAA10A4334D3DA0E45441C7464557385F (void);
extern void ProCamera2D_get_InfluencesSum_mED5411A2307C47E0C0C829C61E4010AF94282303 (void);
extern void ProCamera2D_Awake_m177C6CA44636442ED2EFE9B7EB65FBEC6F9DD843 (void);
extern void ProCamera2D_Start_m3A478EB09CAD73DC67FB3D90EC2E3FC319A90896 (void);
extern void ProCamera2D_LateUpdate_m6AC3EF76A3A6CF8C613C4A64A71F7BD8D1627A06 (void);
extern void ProCamera2D_FixedUpdate_mD85D5735C7F9645E86147ACEFEA43EABF906B9AA (void);
extern void ProCamera2D_OnApplicationQuit_m5A11FF5C32E64381C1617D78B5012F018BC68E4E (void);
extern void ProCamera2D_GetOffsetX_m8A5E529EC820319B59A1520EDA03FD85DBA84717 (void);
extern void ProCamera2D_GetOffsetY_mC162B9A0C0E5AC29E38E0362B90C2BC0790760BF (void);
extern void ProCamera2D_ApplyInfluence_m43513A9BCD9D112787F90B9B4A73FC54B9646139 (void);
extern void ProCamera2D_ApplyInfluencesTimed_m0F84577A587E327BE92A5FEED25472B3C6E041EF (void);
extern void ProCamera2D_AddCameraTarget_m552E7B3D6D1A458676149A0342E16A6C739667B7 (void);
extern void ProCamera2D_AddCameraTargets_m1CA93BA43B1A66E88E476A340D68E15C49188068 (void);
extern void ProCamera2D_AddCameraTargets_mCB9F42F973AE69A8B732D11BA45D7FD44A6FA50A (void);
extern void ProCamera2D_GetCameraTarget_m399CB315177C0CE2494D62A48755D1A456E1AC86 (void);
extern void ProCamera2D_RemoveCameraTarget_m1C694525D3A70E6F97E1A59887B7A355C91A2E03 (void);
extern void ProCamera2D_RemoveAllCameraTargets_mD5F5BE4CCA76309DE4F6A782E2DB5903FA00BC50 (void);
extern void ProCamera2D_AdjustCameraTargetInfluence_m7757AE12574D63C84499E9547740CBE748950655 (void);
extern void ProCamera2D_AdjustCameraTargetInfluence_mC226EA51E5BF6167784251CCD0B3D55CE725F85D (void);
extern void ProCamera2D_TranslateCamera_m99DF53F92B82C8163DE45EF8240CBFBF03E2CDB0 (void);
extern void ProCamera2D_MoveCameraInstantlyToPosition_m06A2D06E0F4DA15C8FECF6CF755FB185A69A225B (void);
extern void ProCamera2D_Reset_mDAAD96654DBE8EB0DDC246339A7CAC3AEEF1EA23 (void);
extern void ProCamera2D_ResetMovement_m40DFC1CDFE2EA63297F835AF271D4B2401080EA2 (void);
extern void ProCamera2D_ResetSize_m92A1C6E2F40AF16F35811CE5EDFC77DD7AA1B4CE (void);
extern void ProCamera2D_ResetStartSize_m72CE95AF9C0942F0C871A31761924CE2C5AE17D1 (void);
extern void ProCamera2D_ResetExtensions_mB4505B15A396C9A4AA39922893FF9872EBF89AB8 (void);
extern void ProCamera2D_CenterOnTargets_mE7E45BC1164B4D444E3BBBE3FD7A325739102FE1 (void);
extern void ProCamera2D_UpdateScreenSize_m543B2AA7607FD74DEBE09DD1E362B1D404BEC36F (void);
extern void ProCamera2D_StopUpdateScreenSizeCoroutine_mD7B0DA6227C915775F4EC0D918DF26E3F6F10D50 (void);
extern void ProCamera2D_CalculateScreenSize_mB667F894E2A01E771FC78CB901E0181DE8169CBE (void);
extern void ProCamera2D_Zoom_m1D404B5C3D895095CAD74F7D61EEAF0D2B258E89 (void);
extern void ProCamera2D_DollyZoom_mF2A6A56F4F4F55279FF3415812F9955047D58297 (void);
extern void ProCamera2D_Move_mC40528298173A335884C2B1973ED592DD7AA3D9A (void);
extern void ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F (void);
extern void ProCamera2D_ResetAxisFunctions_m056349AAD86AF9E1F15D8F0822914DEDFC34BDE1 (void);
extern void ProCamera2D_GetTargetsWeightedMidPoint_m54B55BF392BE632FA772D77A9B30C11EB02407CA (void);
extern void ProCamera2D_ApplyInfluencesTimedRoutine_m5F232822A531C4863E450C4B19DC2332ABD5D275 (void);
extern void ProCamera2D_ApplyInfluenceTimedRoutine_m339B6445B9B97C5FDB26978174E1EA7FD86E7BF5 (void);
extern void ProCamera2D_AdjustTargetInfluenceRoutine_mB65FC60E9EEA23EA3DE62E6F5EF564A408EFB6C8 (void);
extern void ProCamera2D_UpdateScreenSizeRoutine_m0C0D761B9242CD741B90025609F5E62614891246 (void);
extern void ProCamera2D_DollyZoomRoutine_m8B1A1AB1EBD0D9DB0F3402C35AD38195F302B5E1 (void);
extern void ProCamera2D_SetScreenSize_m88156B34D588B0975BDF321FDE8387FF9E6E7072 (void);
extern void ProCamera2D_GetCameraDistanceForFOV_m2FCA916D4D6C57A1A44274D78A2277AC569C4703 (void);
extern void ProCamera2D_AddPreMover_m3D9A3F718987333A6543726632F69893CAF07AE8 (void);
extern void ProCamera2D_RemovePreMover_m5D452456F35310169F53AE35B7FDD48148433BDC (void);
extern void ProCamera2D_SortPreMovers_m95A250265DFCCC252B0FC062C888BC99B053FAB4 (void);
extern void ProCamera2D_AddPositionDeltaChanger_m3682114F7AC0CA4B8994B2594E5C28F212278D55 (void);
extern void ProCamera2D_RemovePositionDeltaChanger_mEC02A9B5820D99B7025BDD0F3F2D774BDBD85812 (void);
extern void ProCamera2D_SortPositionDeltaChangers_mA0ED5B2CCA712425385F39116EE5F0B620E1E47A (void);
extern void ProCamera2D_AddPositionOverrider_m225362B7291383864FAB1753B9C262C90DA62EAB (void);
extern void ProCamera2D_RemovePositionOverrider_m9F05CDF1AAF04C47417F713B28F56DA2890618B9 (void);
extern void ProCamera2D_SortPositionOverriders_m98E6233BFD31946D460DA61D93A8BB605F2A686C (void);
extern void ProCamera2D_AddSizeDeltaChanger_m68C80B84E8827876D48ABD57D8CCB419D5140E17 (void);
extern void ProCamera2D_RemoveSizeDeltaChanger_mAECE9C00F658C19686B1141FD26FBBFBC9F50C12 (void);
extern void ProCamera2D_SortSizeDeltaChangers_m948F5ADFEA4CCA107122A1BE151DF7C67FCA1CA6 (void);
extern void ProCamera2D_AddSizeOverrider_m79B5E6F39A3148D5F77EDF20E60E20DFFF178129 (void);
extern void ProCamera2D_RemoveSizeOverrider_m6F7E1DC6397A8F75E87B0E983E0A1DB9E421BF3D (void);
extern void ProCamera2D_SortSizeOverriders_m515A2E1EAF2DD0ADE1320C378B6974E72A5D43A9 (void);
extern void ProCamera2D_AddPostMover_mB114FC26E88557D8ADFB5B131DFD33D61C3C47BE (void);
extern void ProCamera2D_RemovePostMover_mD4449D5EFB4C2A72B4B202338CDF6D864323367E (void);
extern void ProCamera2D_SortPostMovers_mCDAEB6673C18715D6650C442C149A08032AF5665 (void);
extern void ProCamera2D_OnBeforeSerialize_m1201B865E9AC53A01EFA7EAB382B59734E33B4C9 (void);
extern void ProCamera2D_OnAfterDeserialize_m8329B5E22CFDAE1E88A0D66D7EE5436C1C57DD1F (void);
extern void ProCamera2D__ctor_m8AFA40814F4ADEC1EE4B66DC06192D1BCF23AC0E (void);
extern void ProCamera2D__cctor_m0CBAE959112DF67D6D72146E6AEC2C89E8732CB6 (void);
extern void U3CU3Ec__cctor_m2CACA806FBBF16DF2FB8F6A8828F7263B626D1BA (void);
extern void U3CU3Ec__ctor_mD315A5D40510C4F060D0906D20F1573E4F17D902 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_0_m98C4A6EA221600DEA0EB0BFC56FC39E30FC645E2 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_1_m505F97B3E36295C2F8D91FF7B2A489957F716D95 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_2_m94DED5B08884FD9DAA54F16106366464B8AE9C37 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_3_m34B944A7A62CAA4608F1280C21047F380C264A2C (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_4_mAFBB04EA8528969D31E755801C9AB8A9C111B315 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_5_mBC6CC32C88B641B37D48B1859E1C266DF02460C4 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_6_m61821683F61E356BF8C6C269651CCA497DE6B87C (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_7_m0052A98D089C5B3875D03CC5ECE82BD37AB87D20 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_8_m98512A0D74D77EC23469A7FC6966E3B7E90BAAE9 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_9_m709F5BEF2F0A9AFA5AB3C247D96932B28258D2AB (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_10_m96598B4C7DC04B826B3A38D8C19950D4B69249E0 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_11_m8CA2966F66F4A767B38164CE452742B4A4229D4D (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_12_mDDD87D483B6B6AFFDC53E0835347D9158751CF5A (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_13_mDEF3D7670DF97A8BA3604D9165B65C616E19B4F4 (void);
extern void U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_14_mDEF0C08680FAEB5187CDE8257960EED9CF57163C (void);
extern void U3CU3Ec_U3CSortPreMoversU3Eb__142_0_mD9EEE20A043C09922C819C512AEFC688D2BAC5EA (void);
extern void U3CU3Ec_U3CSortPositionDeltaChangersU3Eb__145_0_mC93C34687515ABA35A0D290D89D7A2500426C09D (void);
extern void U3CU3Ec_U3CSortPositionOverridersU3Eb__148_0_mBCBF1157F9E8EF24C994B246C0D04F8F814A0FD1 (void);
extern void U3CU3Ec_U3CSortSizeDeltaChangersU3Eb__151_0_m1D3264ABC981E11D4D3A09DEF14DED70888AFA71 (void);
extern void U3CU3Ec_U3CSortSizeOverridersU3Eb__154_0_m6D5A8C79EA1D342DA47F6571AC7D4912321136E4 (void);
extern void U3CU3Ec_U3CSortPostMoversU3Eb__157_0_m9A1567248619630583D4F099F51B78340EC4507E (void);
extern void U3CAdjustTargetInfluenceRoutineU3Ed__135__ctor_m409A5B5ADD805D2BDE208CF8E4EDA32EEFA74CD5 (void);
extern void U3CAdjustTargetInfluenceRoutineU3Ed__135_System_IDisposable_Dispose_m104C961491C839A3C9776417DE0F123408AA1B44 (void);
extern void U3CAdjustTargetInfluenceRoutineU3Ed__135_MoveNext_m69DD891BF32D680F323C30BB1AB662ED99782801 (void);
extern void U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3E54B43550C7989511D73C6289AF00E4539F8CB9 (void);
extern void U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_Reset_mCC25D8F322985E1FF8D8B0211383D93A0B9951DE (void);
extern void U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_get_Current_mC8453BEE2628B21F321FF2017146FAFF03FDE2DB (void);
extern void U3CApplyInfluenceTimedRoutineU3Ed__134__ctor_m51120204F59658F8C42D1A7DA60F5FA0983504C8 (void);
extern void U3CApplyInfluenceTimedRoutineU3Ed__134_System_IDisposable_Dispose_m3BD00051FBAE7E40849BA912A5B22A1EFE8B6212 (void);
extern void U3CApplyInfluenceTimedRoutineU3Ed__134_MoveNext_mE5593442539FBC5813B0B161DFF09FCEBB3463BA (void);
extern void U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD448A87768163EA43D5653178F0DBDC60006F315 (void);
extern void U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_Reset_m4C8D8FB13B284CA12B2268B5E6DFEF45FFBA299E (void);
extern void U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_get_Current_mB679A88BC7061F08573F801056767C37EE6A14CF (void);
extern void U3CApplyInfluencesTimedRoutineU3Ed__133__ctor_mA8B4ECFBF07C3573B979B4B9B37BA42105F26EA3 (void);
extern void U3CApplyInfluencesTimedRoutineU3Ed__133_System_IDisposable_Dispose_m686AF8919E826C8AA67FD4733F65F40C49F4A442 (void);
extern void U3CApplyInfluencesTimedRoutineU3Ed__133_MoveNext_m91FA6C67DB6E6C3EC63E0E3A64AD626B7DAAC84B (void);
extern void U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0194873426BA7960743E862A7F4C57767100281 (void);
extern void U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_Reset_mB6EB8C8AF011198498FF41032C55A4FC25ECB51E (void);
extern void U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_get_Current_mBDE02E8A3BD380C22F8B33A3210D60E7ECFCE5E1 (void);
extern void U3CDollyZoomRoutineU3Ed__137__ctor_mF86B1AC195636774504B0E7AFE50740934B9E3F1 (void);
extern void U3CDollyZoomRoutineU3Ed__137_System_IDisposable_Dispose_m6C6308A5A215173B7666278C8383CB7B701BBB64 (void);
extern void U3CDollyZoomRoutineU3Ed__137_MoveNext_m3BA1B2172EA265671591C670D6A2D6925BEB90D4 (void);
extern void U3CDollyZoomRoutineU3Ed__137_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB7668F67DDEBD720BF1FD5ECB5741AE75D0C5B2E (void);
extern void U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_Reset_m8F61EFF21AD9780E02E38B110807DA2770E921D7 (void);
extern void U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_get_Current_mA5B7EE658EFDFE06CBF0CE6A6ADEA7E0C001ED8F (void);
extern void U3CUpdateScreenSizeRoutineU3Ed__136__ctor_m0C35F21342157299F0E20519FE7B8601DB070C6B (void);
extern void U3CUpdateScreenSizeRoutineU3Ed__136_System_IDisposable_Dispose_m3313BBBBC7B2FDA68B024FB518C4C523C50D3402 (void);
extern void U3CUpdateScreenSizeRoutineU3Ed__136_MoveNext_m8FF50A132F993A1E88CEBC9751FDA587ACCC19AF (void);
extern void U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m63DFE9D92CC3D757C5DC39EC31DB292CE5EE5EF8 (void);
extern void U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_Reset_mA0B540206D18E1C21A1314421A16B808A4B8B0D0 (void);
extern void U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_get_Current_mD34C3430382F0730625E618C5B542235566C7D06 (void);
static Il2CppMethodPointer s_methodPointers[884] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m596437BE24172A9892B69483DC857AFB7EEC69E7,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6FB3FAA869510661EC6401C0C8A7BBE2138C3DA8,
	BasePC2D_get_ProCamera2D_mE277F562D541662C2098DA32B2CAB8173E8F771D,
	BasePC2D_set_ProCamera2D_mD686DD8B337DD0179A94ACB3A1BE706A81781EB4,
	BasePC2D_Awake_mB771BD4A2E3B653557FF55A4EE79EE08477CA51A,
	BasePC2D_OnEnable_m7A5D6C19C2C2EBC039CF77FA71E2E3301234A94B,
	BasePC2D_OnDisable_m0D133CC1FE265D36A005B7CB23139CBBCB61A574,
	BasePC2D_OnDestroy_mE7F7AB62E67BC1AF00CC4F3E949B1BDEBCB638AE,
	BasePC2D_OnReset_m2ECC8F3B185DAD9BCD9283251C87E4FA71124C85,
	BasePC2D_Enable_m046A4A78B3F5522481DCAB28495EBDDF6F81B972,
	BasePC2D_Disable_m8149FB64FB4C4FB883B33AE21D2144A7EC1B059A,
	BasePC2D_ResetAxisFunctions_mD9A52EE6D0E6FE12D5382344F87252EB27571B71,
	BasePC2D_OnBeforeSerialize_m8F8B6C404A81C68782282EEBB00AB4697C7C9902,
	BasePC2D_OnAfterDeserialize_m795B5938A4A475DD63AE98C31548C0E0AAF1703D,
	BasePC2D__ctor_m67A04FFBDFB943E220923E9798543D007A8D9431,
	U3CU3Ec__cctor_m9BB22E1A4D1CD4FC13BF1343DCEC13A25253A91E,
	U3CU3Ec__ctor_m35E8FDC339AF854AB587C00C09D68819882B472E,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_0_mF890D6EB7F51D2FA51A2D6BBCB23616B61FB88F1,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_1_mABDBCA2DB3F60AB34B4C4A7F2BF429693CA19D60,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_2_m2D73F633D5403D8294E3601EF5E2A74F4BCF88F3,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_3_m7BB51F7820B0CEEEE769E629C2506F964052C028,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_4_mE9468505EEECD9432648157323877186CC827542,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_5_m853D84C3B1120FEB80F10C25138620D978611EFE,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_6_m0ACA50D48B3289B93F64978DF13672C973D067C3,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_7_m30C7F597DD8CD52484C698A467F7D83E7A0D0E4C,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_8_mB9A44F63273EF24FCAE191FDE0A818B641E2A8E9,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_9_m476DCF2F903DB61CE825074094CDBF1E0C29BBDF,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_10_mBA4BFB39D407199F5C18D99C1A48D7D412EC5826,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_11_m85F852E71F362E885AD199B7035F2A0D2EE09CEF,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_12_mDD52C6F7705DED077952F924C1E7D7A0756FB701,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_13_m7332D560B5A6EEFD1B9B7B5ACC80D89C9383C22A,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__19_14_mD0DBC3FDE1D48C95CC4DEBE6AF41FF9FD2AAE219,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_0_m8E36E151FC662E6E376AB40FED8C5E74F64EBB20,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_1_m2E89B350F51983C0B1B9C883242844B3677F5307,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_2_mEB8BA710A2B7ED3A230CBF63E78B36573CDA18DB,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_3_m6C521BF0E8B4F8DDF4D95E72761D52A5799F5CD0,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_4_mEF035B068C62A1A4A430674FF1A3DAC089DB9013,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_5_mB05140CFB353430D4416C92B47749499C5DA8E24,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_6_mA32E41B1B774DDE535A138B9A9C5D20183DBCB35,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_7_m2BBA97B5B4B5BBCAF4881CC6EEB8105372080FC2,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_8_mB29412C365FF6EB218D100301AA5ECBC9D7F2903,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_9_m515A675D579328A2253A4A4CA78E0DB7F6C89A86,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_10_mE3338736369BBFDAA7471BA194AE12BF6C8A4D6B,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_11_mD42222D06BD5A9C9E6961273D25BF70C71B831A8,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_12_mC463F356B8432E0931EE0C259A2AC8552F092D63,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_13_m4B1003ACCB8C516D4B545A0D69805739CF138174,
	U3CU3Ec_U3COnAfterDeserializeU3Eb__21_14_mAE591C329771C978CE70FE7E615ABCB8614CF255,
	BaseTrigger_Awake_mEA036379F719B8760F3D57D96E99ACD0D7F119CC,
	BaseTrigger_OnEnable_mE5CF32D35E0110BEFF50E06547B3367CA82B04EE,
	BaseTrigger_OnDisable_m9B44B240C3C3796F5AB3D8B7C8AD8C8C3025E6E9,
	BaseTrigger_Toggle_mF3BD1210D59B5DFC6171FA7BDAEE9885D63E8FEE,
	BaseTrigger_TestTrigger_mD6DF866002FCE0396AC826FA8715875EE8014B4A,
	BaseTrigger_EnteredTrigger_m47403F05C8AB195D87920EF17C43CBF86B9E1BB5,
	BaseTrigger_ExitedTrigger_mCBA28315BC4F19428A209196D3957EB4D83A481C,
	BaseTrigger_TestTriggerRoutine_m0D2B7A97A473D72F3B8B4E5032CA1E2B222C71D5,
	BaseTrigger_GetDistanceToCenterPercentage_mD39589D99050BD34D3BB5820513BD8B461483297,
	BaseTrigger__ctor_m2394C6025AEB83FBB4349D4A10604599FC0E389D,
	U3CTestTriggerRoutineU3Ed__19__ctor_mE3EDDABFA7A49A8D836E28EAF2EB139A1F48839D,
	U3CTestTriggerRoutineU3Ed__19_System_IDisposable_Dispose_mA9BA4987A07B240853380D0749AE5BE81210D3DC,
	U3CTestTriggerRoutineU3Ed__19_MoveNext_m4C080020FA821554F864F274BDB4C5D3F773BCBE,
	U3CTestTriggerRoutineU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFE52099888EB12EBB24A6B6993DD0B7D31B450FD,
	U3CTestTriggerRoutineU3Ed__19_System_Collections_IEnumerator_Reset_m507A0AF6C51DC6E59A3C8CAEA08CE05B735ABFC7,
	U3CTestTriggerRoutineU3Ed__19_System_Collections_IEnumerator_get_Current_m32781BBCD8F2F720A9B8B9B81317A843913A4470,
	BoundariesAnimator__ctor_m51D92BFAF4E242DC5D8CAA5A77054DEBFBD8EA9F,
	BoundariesAnimator_GetAnimsCount_m1DCE3302A8F5237B1E4BC957CF9F493C3FA33D3E,
	BoundariesAnimator_Transition_m04CED9453CD36202CAAC840185B3A54846A0157E,
	BoundariesAnimator_LeftTransitionRoutine_m0A2E26D592D536962DAD5ED30D3912C8E882588A,
	BoundariesAnimator_RightTransitionRoutine_mB5100648BBFED0018166DBCF0C046064652E6326,
	BoundariesAnimator_TopTransitionRoutine_m2B6233157608A645EEE6E356DEFF0C0C2B893A72,
	BoundariesAnimator_BottomTransitionRoutine_mBA6DD346FD8549BF2F0FB074A583AB0A57ACC642,
	U3CU3Ec__cctor_m7571A90F4C3F85DFADE6725E069A6E8012CE3767,
	U3CU3Ec__ctor_m7DD7CB33E10A9BA06CF3A795B1877D09149E1CF3,
	U3CU3Ec_U3C_ctorU3Eb__16_0_mC1BE5CFF3E576381B6FEDE82260120246A91FB66,
	U3CU3Ec_U3C_ctorU3Eb__16_1_m81E6B070815F19A7C2B093FD35492D2A085ECF82,
	U3CU3Ec_U3C_ctorU3Eb__16_2_m6A525F4F7AFE65C0DC04FAD2D71AA705E96870E7,
	U3CU3Ec_U3C_ctorU3Eb__16_3_m3CE831DDF6BC75F8362B2343BAFFD40B309A3473,
	U3CU3Ec_U3C_ctorU3Eb__16_4_m5F57F9BB4E4F30665B2A50FB9EDB6ADEC9C5A356,
	U3CU3Ec_U3C_ctorU3Eb__16_5_mA1FBA45094202BA315083426BEFD6BB4AD97314B,
	U3CBottomTransitionRoutineU3Ed__22__ctor_m603B93CE916FE4D84A5471B8E4E2586749C67E53,
	U3CBottomTransitionRoutineU3Ed__22_System_IDisposable_Dispose_m641FD03E5046396812223F873E8063250F8E120B,
	U3CBottomTransitionRoutineU3Ed__22_MoveNext_mBEE19069AE5919B292E9D65D14A19C168293A594,
	U3CBottomTransitionRoutineU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5D3F6C3E838BC17FAD0E280DD5212FE8BD758BED,
	U3CBottomTransitionRoutineU3Ed__22_System_Collections_IEnumerator_Reset_m97B231CC53B239B1CFA5AD25D49194EE86C40C39,
	U3CBottomTransitionRoutineU3Ed__22_System_Collections_IEnumerator_get_Current_m11CAB1A569CFBB7CD36EA076F67E7EF94EC63A33,
	U3CLeftTransitionRoutineU3Ed__19__ctor_mDE4BBF10AAED14136B574F978B28B38F42B053F4,
	U3CLeftTransitionRoutineU3Ed__19_System_IDisposable_Dispose_m2C94E72020281E822968ABFA48539ECD558E4826,
	U3CLeftTransitionRoutineU3Ed__19_MoveNext_mA8E66911B1253A3066F3D24D4C2F47E8F5D784D1,
	U3CLeftTransitionRoutineU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2B5FBE6D4C36A70509D39CBCE7A6BA58ABEAF6B0,
	U3CLeftTransitionRoutineU3Ed__19_System_Collections_IEnumerator_Reset_m2064671E122CB62A2316AA52389D0A321E5ADE06,
	U3CLeftTransitionRoutineU3Ed__19_System_Collections_IEnumerator_get_Current_mA6D08B35FFEDF57E3EE34006BB7C85B9E564647B,
	U3CRightTransitionRoutineU3Ed__20__ctor_mD13B024E8894BB8512D8762C56ABB1A0C6FBBD20,
	U3CRightTransitionRoutineU3Ed__20_System_IDisposable_Dispose_mA7CE0EC824432E70316845039AF06B01FD286758,
	U3CRightTransitionRoutineU3Ed__20_MoveNext_mA4A12B5679A51490211DA1CEBCE226671446E30A,
	U3CRightTransitionRoutineU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m12E89C41DCDD9493ED3AA28407ABB5F4AA83B1E0,
	U3CRightTransitionRoutineU3Ed__20_System_Collections_IEnumerator_Reset_m8881A7649956E5C5706FB3D27DC7D958A3AE5109,
	U3CRightTransitionRoutineU3Ed__20_System_Collections_IEnumerator_get_Current_m1568D752E5978A50945AA27474420136880D9002,
	U3CTopTransitionRoutineU3Ed__21__ctor_m03555579F94445D9549F22F1237103C042706990,
	U3CTopTransitionRoutineU3Ed__21_System_IDisposable_Dispose_m2529E4B6610538D00F82E5C765FC056EBBE9BD53,
	U3CTopTransitionRoutineU3Ed__21_MoveNext_m3C092BBBD2F1ED94062F81B1F7BCC0D80068BC91,
	U3CTopTransitionRoutineU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m26DB3126148A37CFD8DA2732D2073F0B7D97FCA2,
	U3CTopTransitionRoutineU3Ed__21_System_Collections_IEnumerator_Reset_m9A1DE95CE6E934BF496E2E8D71B05AD2EFD4B5B9,
	U3CTopTransitionRoutineU3Ed__21_System_Collections_IEnumerator_get_Current_mF2CC9323437B9C0833E6F5B8B9D38CDFCEF9562C,
	CameraTarget_set_TargetInfluence_m17653AA6807C23388E530760A70B8B5E4039E3AE,
	CameraTarget_get_TargetPosition_mFF1699BD2ABC41C4B52CA18FD38CA0CB0F75E815,
	CameraTarget__ctor_m516B7C0DD4A54DD247CA10A0E4FCFBF7BEB3F476,
	ConstantShakePreset__ctor_mE91C65FCD4029129C2BAB3AA91F75C2EA5A02AD9,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	KDTree__ctor_m6FA50EE284097CCE0166C38F5C685AD894F5BD56,
	KDTree_MakeFromPoints_m922A2E4092B912ABBCEE0BFCAB866A468C906152,
	KDTree_MakeFromPointsInner_m8BB1148B655AC031FAC7212DA5D725331F7F880C,
	KDTree_SwapElements_mEA190B87CE35BC1EAC2500CB1FA87F47F751BC41,
	KDTree_FindSplitPoint_m93764A33B1E8EFE411C86A12C52EF17527E7551B,
	KDTree_FindPivotIndex_mB8A9BF49F84EFB75BB8A4287691B6FEC13A17BBF,
	KDTree_Iota_m16A7A3DC03D94537B3D48B86D203A26190E4CFB0,
	KDTree_FindNearest_m730196511444DD1001311F774AF55CBD4509C231,
	KDTree_Search_m0E2D1D220B0C960D0A730D6E7F604E0D1EED67FC,
	KDTree_DistFromSplitPlane_mD0405BB7FA130B40B3E0A9FC0F258BBE43FC88FF,
	KDTree_Dump_m07B716EFDB41E0582244C7794E1722C1BC12EB46,
	MoveInColliderBoundaries_get_RaycastOrigins_mE90FB1BA5D8DCB06251C8D0019118601D13C58E9,
	MoveInColliderBoundaries_get_CameraCollisionState_mC28D9E2082C028FE7EB1052B868D9C3C42CA9AF8,
	MoveInColliderBoundaries__ctor_m15C5C94F2FF75B87F7932E48F654293849A4034C,
	MoveInColliderBoundaries_Move_m8A4D989991B75EF5AE3BE24B8E7CDD0C6872F885,
	MoveInColliderBoundaries_UpdateRaycastOrigins_m98B00B327CC32BBBB35973F12A680D3472F064FC,
	MoveInColliderBoundaries_GetOffsetAndForceMovement_m49102541A73BDDBEF81F68D2272975FC9B57B70D,
	MoveInColliderBoundaries_MoveInAxis_mDDDB1CDC5DF1F22462B0140265B9776114CF00DA,
	MoveInColliderBoundaries_DrawRay_m6EED3106A6C472E2AE9208698E2915641466CF23,
	U3CU3Ec__cctor_mBA00A7CB33C2EA6A1B284D4674E03082EFDD889D,
	U3CU3Ec__ctor_m5EE5F9CB0A6039423C47C4FE4BBE44EB4CC6AE80,
	U3CU3Ec_U3C_ctorU3Eb__20_0_m7BEF874DC2DBBAB18AA2E9728741BCBACE81E32F,
	U3CU3Ec_U3C_ctorU3Eb__20_1_mAE9405A199C4B6F1D042625874E12C936A031E56,
	U3CU3Ec_U3C_ctorU3Eb__20_2_m881BD841D4891F0627B59A0419981D6741627044,
	U3CU3Ec_U3C_ctorU3Eb__20_3_m280FF3F00162D3B31E265F124BD9D28AE28EA019,
	U3CU3Ec_U3C_ctorU3Eb__20_4_m5C3557BB205826B3C0B656F8E04A13F2A10C7B8B,
	U3CU3Ec_U3C_ctorU3Eb__20_5_m578C4FE71C43D4A4F14BFB5B9ED977671F491A70,
	U3CU3Ec_U3C_ctorU3Eb__20_6_m41F843AE8B8AA32B9B6AA14D30715CE968B8B865,
	U3CU3Ec_U3C_ctorU3Eb__20_7_mE1718F2B273559DA2EE45B3563C6D6E8FD5340DB,
	U3CU3Ec_U3C_ctorU3Eb__20_8_m6E5A54572B8FC1FB891C3A55E4DA9A218DD12892,
	ShakePreset__ctor_m91B602317816603CCB36EA98408CF5BBF6BEE297,
	EditorPrefsX_SetBool_mFFA96A11C6724E90DF184A9C9A2210375A9648B0,
	EditorPrefsX_GetBool_mE8459FD1694113E647A3FD9884A779BD2A8553D8,
	EditorPrefsX_GetBool_m9AE9D029705D57302602BFC9571A2386D3390661,
	EditorPrefsX_GetLong_mA8039F38781D6092601E10339626A2B09465D7C0,
	EditorPrefsX_GetLong_mDF03D08A9DC3255FC88336CF2EC192B204365764,
	EditorPrefsX_SplitLong_m028F8AD5102068E585022FAB7F60B5757419DB07,
	EditorPrefsX_SetLong_m3931F1635D05CD4C40857189B55CC34BDB23A2C5,
	EditorPrefsX_SetVector2_m97A19D7AC2EC67BD17E33BAE5301FE18D4746E99,
	EditorPrefsX_GetVector2_mA633749FFD981821890FC4C6E37792E500696EE9,
	EditorPrefsX_GetVector2_m3A1092E7B86B5C6F3EC428B3DC9993555205594A,
	EditorPrefsX_SetVector3_m7ECDBFA41DA1319717CB68A5AA5418CB970E714A,
	EditorPrefsX_GetVector3_mF23FDA21C203682E3F107FC3DEA43F39DE1AB384,
	EditorPrefsX_GetVector3_m59F3C4F89BBB8F428A267814687871F18F46D6FD,
	EditorPrefsX_SetQuaternion_m9952D975CF7A23351146F6B372096F46666B3A44,
	EditorPrefsX_GetQuaternion_mC8E102BD0486C43963F95424E32AA4282F5725EC,
	EditorPrefsX_GetQuaternion_m164B0D1326E6B911ED74F577819328AEEB97ED96,
	EditorPrefsX_SetColor_m5E3C6C2DE1199A4ABD6779580BE7C03192A08637,
	EditorPrefsX_GetColor_m0C14013FEFE17E3E5B78EDF4BA8AFA6DC137292F,
	EditorPrefsX_GetColor_m11D3BD48FB802175BD4E77938CF2254A0775EDAB,
	EditorPrefsX_SetBoolArray_mA5BE38CEEA812B7A4487C8BBBA40F4269E36E18B,
	EditorPrefsX_GetBoolArray_mE1197E16725ECA787EFD67607F38C9BC54B8CC6A,
	EditorPrefsX_GetBoolArray_m889ED1F5F6A05847F15AA1EF72247E728AD95C96,
	EditorPrefsX_SetStringArray_m60312156A5699E63A27CC018FC106748150457AE,
	EditorPrefsX_GetStringArray_m310DD8967E93F56E91F69AD10749AD09930A5075,
	EditorPrefsX_GetStringArray_m1847650220DC6F6FA98C02C08011F555C7EA2258,
	EditorPrefsX_SetIntArray_m9B9342EDD4CEBE84E4D77D4B84AC8DC8690DADF8,
	EditorPrefsX_SetFloatArray_m85C2273C4BC130D14FB928054FA0072ED51AF426,
	EditorPrefsX_SetVector2Array_m4662F78962146DA0714D875C275329B9AFEFCED5,
	EditorPrefsX_SetVector3Array_mB6994D228359533E346C50D99EA24C4DC1B32EAE,
	EditorPrefsX_SetQuaternionArray_mD885FFD5BC57FF039DE48D21530BED6217D5CB38,
	EditorPrefsX_SetColorArray_mD2B0DB84720118EE487C591E4B0D80332BEE08A0,
	NULL,
	EditorPrefsX_ConvertFromInt_m3A459D1D9D20BCA6D9BC9A2334732D56C34A1C18,
	EditorPrefsX_ConvertFromFloat_m9996A5BEBBFFE0F5670E5E3A914997A8B85A342D,
	EditorPrefsX_ConvertFromVector2_m8E006252F547D39B2B5F8DB0103EE8BB4CC20A29,
	EditorPrefsX_ConvertFromVector3_m3407CD5A21DFB7445446803F1EAF5CD7274F5F58,
	EditorPrefsX_ConvertFromQuaternion_mB81CFCE33E71A759829100E9DCBC9BAF9188E0EA,
	EditorPrefsX_ConvertFromColor_m87C217DA0919C8DE8D2B6F47B65BED5D20B9C3CA,
	EditorPrefsX_GetIntArray_m9CEDD488CFF8FDD176253A55417B72DE4EA6EE7A,
	EditorPrefsX_GetIntArray_mF0A2660D2B219C4E3721C9B8DD28AF4FAC876BD6,
	EditorPrefsX_GetFloatArray_m238A23E7D121F9EBD3BA9A99FF90DC08613344F8,
	EditorPrefsX_GetFloatArray_m751B5DF425567DCDF1026077E89D74DD575F51C3,
	EditorPrefsX_GetVector2Array_mE5E037CF30C4F5F70F9561CE9E00713D819893A0,
	EditorPrefsX_GetVector2Array_mB8C2AD28C391E9907D17B28A0B9878ABFD19826F,
	EditorPrefsX_GetVector3Array_m80C6FD48CF930E2177463E66E2D10202DA1012A9,
	EditorPrefsX_GetVector3Array_m20FDBA039608BB752BAA1D301CEE0E14A72EE199,
	EditorPrefsX_GetQuaternionArray_mB5DDD2A161F0256947ACEEAB97ECFB68FF1F3A64,
	EditorPrefsX_GetQuaternionArray_m18F714B572C14648FA43972D69CF5045B0CBD6EB,
	EditorPrefsX_GetColorArray_m18A7D72BEC5894D1053DCDD0F7D4E5D92B2B5F0D,
	EditorPrefsX_GetColorArray_mD7A2049208BA5EDF5FE0DDC7060DBA871856C600,
	NULL,
	EditorPrefsX_ConvertToInt_mE8DBC3E665EAEC3228AA90F2ACE82A553A7E3D17,
	EditorPrefsX_ConvertToFloat_mABE102EA7EB1A2D964214AFB7F0F009CAF6BC8E0,
	EditorPrefsX_ConvertToVector2_m775944FAEAA2ACFAEFD68B4585E8D780FD6FA0F8,
	EditorPrefsX_ConvertToVector3_mF084877DE4627DCA3EA20847B35483D2F229B87C,
	EditorPrefsX_ConvertToQuaternion_m8645663BB291FA477BB0BB3986C8E44651B41BF6,
	EditorPrefsX_ConvertToColor_mBFBCC9E8CD40CCE17DB8B8DB3F86377AE0467200,
	EditorPrefsX_ShowArrayType_mBB213714F61C3B95B37970077FF043204B6E9782,
	EditorPrefsX_Initialize_m65B919801B48F50A7CB9B5E71AD44001878B75C1,
	EditorPrefsX_SaveBytes_m69687D9371DCE8DE836532495BF8DE6DFC26C547,
	EditorPrefsX_ConvertFloatToBytes_mF3B013447A0952CF46F4507B0C1D836B6F26E16B,
	EditorPrefsX_ConvertBytesToFloat_m6EDC627FEC969BC0EFA12FFE50A3443D2BE90B2B,
	EditorPrefsX_ConvertInt32ToBytes_mF3ACBAF66A81AF139346EA9BA051796B384995FD,
	EditorPrefsX_ConvertBytesToInt32_m211BF8CC41BA2213C250DD99D21BE3B753047E0B,
	EditorPrefsX_ConvertTo4Bytes_m089C0B865F0E07BFFDB1836E2285162E2C1ECC48,
	EditorPrefsX_ConvertFrom4Bytes_m6FA7473662F259B61902BFCC209F119B0434CD4C,
	MinMaxSliderAttribute__ctor_mDB9C19C9A31D70B0C2FF560D59FA1A5B0F2CB131,
	PrefsData__cctor_m0A23CC7D2F7AAF2D722BC9F4554343E82A3818A4,
	Utils_EaseFromTo_m72E4B08297843D1983026CD01287C6F724554B31,
	Utils_SmoothApproach_m9C76660CA0215DC9B7E89A70CF52188E75203853,
	Utils_Remap_m8A4E0068BD805F72E231B3D2967EBAA329D35DB8,
	Utils_DrawArrowForGizmo_mEF337C54E619E992F262D9B66105CBB313458808,
	Utils_DrawArrowForGizmo_mF59BCC1F3CC03AB0A2613B1739F9F44F7240DF09,
	Utils_DrawArrowForDebug_m4206695D40D1C30CCAC6D31ABBEA8F0927ED6A2A,
	Utils_DrawArrowForDebug_m30EB3441F70FE55061CA44C647A95BDA46DDB80E,
	Utils_DrawArrowEnd_m2B427B47E9A58A6C232EA0AA58DB9EBA486F2332,
	Utils_AreNearlyEqual_mE44BE42133D875615050D46D728D4CF86A303AFA,
	Utils_GetScreenSizeInWorldCoords_m8EF6AEF0C48BFC424DF586E5A4838C5F53E83A9B,
	Utils_GetVectorsSum_m08EDA3327A9964C15C39B9C9598F1280F9FFF5F1,
	Utils_AlignToGrid_m27553769AD00695CEB245011EAA53BEF0011161B,
	Utils_IsInsideRectangle_m7D8E812B437518C7EEF3AD8F2519185F09E44934,
	Utils_IsInsideCircle_m****************************************,
	ProCamera2DCameraWindow_Awake_m4CD6E4B2731DD2443C5640A59F8E8F90A2BABBBC,
	ProCamera2DCameraWindow_OnDestroy_m9E9E3E3409D171D150D43DA8470C825C20221224,
	ProCamera2DCameraWindow_AdjustDelta_mF37319D46490601CC8C071FCDE31E6752DAC70FA,
	ProCamera2DCameraWindow_get_PDCOrder_mFD37AAC5E940ED9B4B66747C7D5BB543BF644AF2,
	ProCamera2DCameraWindow_set_PDCOrder_m36FE06ACA5B78780D816669AC3E3590C7E52DA70,
	ProCamera2DCameraWindow_GetRectAroundTransf_mF5C08DB92035A120C57D6EC2E2F5760B86335AF2,
	ProCamera2DCameraWindow__ctor_m8ED927E958FB1C5E79AD6A91B0A04740E3A289E0,
	ProCamera2DCameraWindow__cctor_mA2FC913354B25ACAA8ACAE8CC77954D7B9B212B8,
	CinematicTarget__ctor_m669A02758754944BD68D9D8945CB0E2F5B6EB674,
	CinematicEvent__ctor_mD80D459193825A9A2FDEDAE9197FD38EA4B1E891,
	ProCamera2DCinematics_get_IsPlaying_m5B20736D6468E89392AE123DE2A6C43D071096C5,
	ProCamera2DCinematics_Awake_mD466A492452F9A6B777B690A180287D9006E494D,
	ProCamera2DCinematics_OnDestroy_m15090E65CE0E06B15E1C382CF92DCFCB78CC4DBF,
	ProCamera2DCinematics_OverridePosition_m6E066608FA5F9389550B62BCAAA75A14DF55F2F4,
	ProCamera2DCinematics_get_POOrder_m51006B5D7DBEBF9E655DBBAF6285AFD08062341B,
	ProCamera2DCinematics_set_POOrder_m03FDC20D666C2D27ACB950DE525AAB2AC76063EE,
	ProCamera2DCinematics_OverrideSize_m7886783343FE2959BA0158749B691902434523C0,
	ProCamera2DCinematics_get_SOOrder_mC2151DCDDE9281A4597ED31007CCD9A2F9AE79A8,
	ProCamera2DCinematics_set_SOOrder_mFB16A63286FF854F3B82596F9ADE87307A3F99F3,
	ProCamera2DCinematics_Play_m0EC4378CCBB85B084497DBB3D3E6D3A0487A084B,
	ProCamera2DCinematics_Stop_m5430BE4DC9FB51CAEA12AB1A0403031966BE7415,
	ProCamera2DCinematics_Toggle_mF3D11E8BBE8CFE7C0B02B9908F223ACEB6D98995,
	ProCamera2DCinematics_GoToNextTarget_m1F8BCC330356EBD1818DB49BE438D136B2CCDED6,
	ProCamera2DCinematics_Pause_m9619E298983FD2F6F21E40362E529A587A29C70C,
	ProCamera2DCinematics_Unpause_m43053CE978FC7488DEF5CD3E58935D523FFAAB56,
	ProCamera2DCinematics_AddCinematicTarget_m94C0A7BA9CBBA679FC8B791AD566FD20397BDFF9,
	ProCamera2DCinematics_RemoveCinematicTarget_mEC4CCCC4D1D17F608E754F5A54A762DC0B22C7D0,
	ProCamera2DCinematics_StartCinematicRoutine_m687880F52D71C3FAC16EF3016A3739DEBBCDEE04,
	ProCamera2DCinematics_GoToCinematicTargetRoutine_m79BADD42264D4ACE18DAD817E25C4FF1BA47D792,
	ProCamera2DCinematics_EndCinematicRoutine_m11A1009894476C42AC58A142B95254FDB30C8DA5,
	ProCamera2DCinematics_SetupLetterbox_m7EC5D2FCD782946DFD1D1ADDF041783F7045197D,
	ProCamera2DCinematics_LimitToNumericBoundaries_mA8A4E56FCFE608BEF6AAA30091825B8AC2EA7F6D,
	ProCamera2DCinematics__ctor_mFA08EC00A9C696170EE2A9E5FB51980F9D3EBEBE,
	ProCamera2DCinematics__cctor_m8623F94D329CB19B0D9B1936B1918D15FAA5199A,
	U3CU3Ec__cctor_mD03A41A71CECA059A2AEB73D0FC36CE5D6EE0ABE,
	U3CU3Ec__ctor_m4F8397BEBBD2B9591D4309D266AEE98084E54B01,
	U3CU3Ec_U3CSetupLetterboxU3Eb__51_0_m62CD38E39D8C3FA756C39AA5E8DAFF9B57E5F624,
	U3CEndCinematicRoutineU3Ed__50__ctor_m40D767478ED58E953B81AE4F4F083B2574D2C87F,
	U3CEndCinematicRoutineU3Ed__50_System_IDisposable_Dispose_mC209994DBC29E595158E4EEBCC3D4B2DB40034AF,
	U3CEndCinematicRoutineU3Ed__50_MoveNext_m4B1299A879F332F8DF396C588DF017B939871D44,
	U3CEndCinematicRoutineU3Ed__50_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6A230898FDD0349EE0B73AD62D17014E7A2B214C,
	U3CEndCinematicRoutineU3Ed__50_System_Collections_IEnumerator_Reset_mA2F99BC76A44418C31647AFDEDBC2867D389E1DB,
	U3CEndCinematicRoutineU3Ed__50_System_Collections_IEnumerator_get_Current_m171167F79FD04430C2C613C82984AF54B5C48A30,
	U3CGoToCinematicTargetRoutineU3Ed__49__ctor_m1E5C8FFBE8D22AB2A2706DACD0A9D5A795B5BBDB,
	U3CGoToCinematicTargetRoutineU3Ed__49_System_IDisposable_Dispose_mFBF60FED8C4E271C8AD69D0FCD2F0891A5BB205B,
	U3CGoToCinematicTargetRoutineU3Ed__49_MoveNext_m02938D8479C1664A5DD6C72FF3D04D537B0D1373,
	U3CGoToCinematicTargetRoutineU3Ed__49_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1F81438D2DEB34EAD4D91A1AA6707094E63951B0,
	U3CGoToCinematicTargetRoutineU3Ed__49_System_Collections_IEnumerator_Reset_m56DACA9A2B87DC0A076F5A598896B61EF4DBD4A3,
	U3CGoToCinematicTargetRoutineU3Ed__49_System_Collections_IEnumerator_get_Current_m8FA8E02A1E6180F9C87BE989772C61A9A86DB678,
	U3CStartCinematicRoutineU3Ed__48__ctor_mCAEECA20C52EA2104501264B23807407207B3266,
	U3CStartCinematicRoutineU3Ed__48_System_IDisposable_Dispose_m364850BCB664CBA01E595D39C82B35A026B7AD82,
	U3CStartCinematicRoutineU3Ed__48_MoveNext_m641F0665DAC5A7A9F6A1481F6E3EA0576069141E,
	U3CStartCinematicRoutineU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2215F0C68D9B6E7D2957C30B404ABB74E4560682,
	U3CStartCinematicRoutineU3Ed__48_System_Collections_IEnumerator_Reset_m68CD0B32E1745B8A3299F3A4D064B52EBA9DA4D5,
	U3CStartCinematicRoutineU3Ed__48_System_Collections_IEnumerator_get_Current_m51625671113B14FFC5E3CCF58F772A80751E1B2C,
	ProCamera2DContentFitter_set_ContentFitterMode_mFB2206019C9E20B58794ECF99E1E8B9440727B5D,
	ProCamera2DContentFitter_get_ContentFitterMode_m5AA94788EF1B0F6F420D3EC436AE110EF7240DD5,
	ProCamera2DContentFitter_set_UseLetterOrPillarboxing_m080D28A0E1C202EF1DA730BF64F2043811DF03DF,
	ProCamera2DContentFitter_get_UseLetterOrPillarboxing_m015C987A21F46DE8B187F7C927B52C2EC64C3E8F,
	ProCamera2DContentFitter_get_ScreenAspectRatio_mE27056FAF160AD508B7C006A640F8D5EBE32A2E7,
	ProCamera2DContentFitter_get_TargetHeight_mF3378C53AF976BB2C32032B4D43E243FA2AF76C8,
	ProCamera2DContentFitter_set_TargetHeight_mBAA751419CABEEB9C33CDEA2859C7F03995DA08E,
	ProCamera2DContentFitter_get_TargetWidth_mCDFA37768BB13AAE5784B0B81153A148991FF1D5,
	ProCamera2DContentFitter_set_TargetWidth_m0E8FEEAA1A77731299D8436336861B5D4B852D6E,
	ProCamera2DContentFitter_get_TargetAspectRatio_m86181154E7AF97491F7C38A51EBCFD846F8EC9A0,
	ProCamera2DContentFitter_set_TargetAspectRatio_mD1652BE0995533C048E25D975468F524C1DFE0EE,
	ProCamera2DContentFitter_Awake_mEF13A655ED198CA8FFCF6B810E5B816E09DF00F0,
	ProCamera2DContentFitter_Start_mDD468167A117B1A028CAACD7DAB2D377C6CBB9DF,
	ProCamera2DContentFitter_OnDestroy_mE761A8261C404FB164C2F5C0AA9DCD9226435153,
	ProCamera2DContentFitter_OnDisable_m2D14876485B16BDA3835CC03C55A50022D551F1A,
	ProCamera2DContentFitter_OverrideSize_mAE05780F160D726833E4C6BBF4FD087C583599D8,
	ProCamera2DContentFitter_get_SOOrder_m60F307E32277F2FB6A133AE413AB61D604F1B4D1,
	ProCamera2DContentFitter_set_SOOrder_m0100E8BD08199231E4F08C6E291B2109C850ACBF,
	ProCamera2DContentFitter_GetSize_mC245EE94EC24B126BE720C53F34A392416D620C6,
	ProCamera2DContentFitter_UpdateFixedAspectRatio_m673BAC83FF79F11F11F3966F9BC7A072D7AFD092,
	ProCamera2DContentFitter_UpdateCameraAlignment_m7F3D670871D4FA197EEDA61F3AD2EA6157E5E9FF,
	ProCamera2DContentFitter_GetScissorRect_mFAA544C31D5B28D66AC22E39BE267473A72C178F,
	ProCamera2DContentFitter_UpdateLetterPillarbox_m1617EC2429E036394DD75F543C35DEEBE2257511,
	ProCamera2DContentFitter_ToggleLetterPillarboxing_mCD8429FE56CFF9012F0281701F76B862D245132D,
	ProCamera2DContentFitter_CreateLetterPillarboxingCamera_m6F0B7E568A79E3A3A3F53EA22950F290C31D2FE3,
	ProCamera2DContentFitter_DrawGizmoRectangle_mD06B184D05698BAB0CE3B1CC4CBB5AD9CE0C1FE4,
	ProCamera2DContentFitter__ctor_m4025A3B4ED94B16F646E4748C9A00D52C56D3015,
	ProCamera2DContentFitter__cctor_m21369E776E9C8DA6E7191F4FE9427D18EC712B32,
	U3CStartU3Ed__34__ctor_m385FCF6FE44EF348F09DF88AEB804F1B95DFC8F4,
	U3CStartU3Ed__34_System_IDisposable_Dispose_mF093418EB3417C42FC0F7D42B85E1D367DAA3E5D,
	U3CStartU3Ed__34_MoveNext_m141E771161689591B1109585BFF11A4C536C79AC,
	U3CStartU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6B96B517A7026330251A8436AE69CDFE062AE6EC,
	U3CStartU3Ed__34_System_Collections_IEnumerator_Reset_mE762A87762213032701F072120268175F417CB02,
	U3CStartU3Ed__34_System_Collections_IEnumerator_get_Current_mFE65E57C78E899A08A8A84729569D95B496F6413,
	U3CUpdateFixedAspectRatioU3Ed__43__ctor_mD033CFE941283B79E9A142F5968E204B39087DEF,
	U3CUpdateFixedAspectRatioU3Ed__43_System_IDisposable_Dispose_m7E144E06B61C64AC271183481AE66DC0B1E33760,
	U3CUpdateFixedAspectRatioU3Ed__43_MoveNext_mEEF2B70E8DB5F000683D6B6737B05D8EAC8E094C,
	U3CUpdateFixedAspectRatioU3Ed__43_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m629B9926A27F6FE770EAF396EFAE557FA9F051B3,
	U3CUpdateFixedAspectRatioU3Ed__43_System_Collections_IEnumerator_Reset_mAA723B7773636BCBE184339CC6139B460DB611A4,
	U3CUpdateFixedAspectRatioU3Ed__43_System_Collections_IEnumerator_get_Current_mC5C22C79D1A8B4D92A89D5FD41CC95D08E8D20C2,
	ProCamera2DForwardFocus_Awake_mF83ADE633EB448561C52020E85AF314EE7AA91DF,
	ProCamera2DForwardFocus_OnDestroy_m86F24ACE676FAC7B6737952D186381093A8C3B74,
	ProCamera2DForwardFocus_PreMove_mBA34BE2F5BF0E39BB8B480366377A0568B5835F2,
	ProCamera2DForwardFocus_get_PrMOrder_mE52D46089E0CD9CDDA09E230580FEBA2574898AD,
	ProCamera2DForwardFocus_set_PrMOrder_mD78BAB68A978A8B3F22A2997FEE08DB700F686BF,
	ProCamera2DForwardFocus_OnReset_mC5BD5270430DEFC0E7D160FC841BEDD339104B6E,
	ProCamera2DForwardFocus_Enable_m6C4A703D8972F6217F78ED148B4C9AC2A8074B53,
	ProCamera2DForwardFocus_ApplyInfluence_m954A660621DFC62C5228A91A5E21CD8E23B66094,
	ProCamera2DForwardFocus__ctor_mD5E68DE0C612A39AD47B2A8FF3079A959493D6DD,
	ProCamera2DForwardFocus__cctor_mAC5D2DAD1964BA1D3723FEC598F342BA4BF6265F,
	U3CEnableU3Ed__28__ctor_m7B4AC9783C3A2AC2B0C94EEA1D71E173C03B03A9,
	U3CEnableU3Ed__28_System_IDisposable_Dispose_mAD6F265684EEBD7D7055BBFFDED582958C0EAE1D,
	U3CEnableU3Ed__28_MoveNext_mF4CD0BFF0ED89572C67D8775EBF7A9975D9E7A7A,
	U3CEnableU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD5176F91C71ADD25B202F4F0463AD7E9F43915E9,
	U3CEnableU3Ed__28_System_Collections_IEnumerator_Reset_m2889E01377D0A30B49F90BBC195E6887FE37647F,
	U3CEnableU3Ed__28_System_Collections_IEnumerator_get_Current_m1554383DCED17C132CB070E5B425FD08227B9CB5,
	ProCamera2DGeometryBoundaries_Awake_m3389F2B7991487DF4F61C6C62DD3C3BE78AB71CD,
	ProCamera2DGeometryBoundaries_OnDestroy_mBECA53E967D07E68BF3CA10D91E7E399A00BB2E0,
	ProCamera2DGeometryBoundaries_AdjustDelta_mCEA3863492A3190C1956E6B0410911B279E87D8E,
	ProCamera2DGeometryBoundaries_get_PDCOrder_mCCC5F51589D313B04DCB81F404402A531FBC7972,
	ProCamera2DGeometryBoundaries_set_PDCOrder_m53AD94533CA87E3F3968DBB4A73AE53108D35956,
	ProCamera2DGeometryBoundaries__ctor_m1E26344DD2679EE491D1237BCD824E5170D289B2,
	ProCamera2DGeometryBoundaries__cctor_m70690CC0D708B82BB653D66F49251B77AEE94627,
	ProCamera2DLimitDistance_Awake_m9E381D5F8928B68A32E76643ED619D709EC64B28,
	ProCamera2DLimitDistance_OnDestroy_m40F474AF4750614D2A72CF03A84AA2F7D007E422,
	ProCamera2DLimitDistance_AdjustDelta_m75BE0BD72F942384C86DD902DF9C1045ADE67523,
	ProCamera2DLimitDistance_get_PDCOrder_m9B3566BF0E54F997247D85CB7D9BC793BD01E122,
	ProCamera2DLimitDistance_set_PDCOrder_m6DD6A7DA3E109F52B50D7E133F0A44B879BD3619,
	ProCamera2DLimitDistance__ctor_m88855A0EB32B310A1BA29142ACC8FB587B6E8E87,
	ProCamera2DLimitDistance__cctor_m8ED08521099D7B69422F865EC812A69202A7D20D,
	ProCamera2DLimitSpeed_Awake_mA89DBC45EE90D3755579A0CE72CEE1A6096CA8D8,
	ProCamera2DLimitSpeed_OnDestroy_m8D77A5E95CED5B5782C2B83833526CB5CF8BF1D4,
	ProCamera2DLimitSpeed_AdjustDelta_mF69312B8937E3186480335D6031B41380EA0E14C,
	ProCamera2DLimitSpeed_get_PDCOrder_mDC7B1B85DC26C1AF6CBA375C181BFD700FB912A5,
	ProCamera2DLimitSpeed_set_PDCOrder_m93173310931D569F4CED1469CC30E28375A3E0E1,
	ProCamera2DLimitSpeed__ctor_m9FEFC040CB328C98E1977C84B1F91C7E271A451D,
	ProCamera2DLimitSpeed__cctor_mD1872617ECF3811E49781ACB60D229489E35E2CA,
	ProCamera2DNumericBoundaries_get_Settings_m98E920A3D3BC50A810E9F1C189F0827CD260835F,
	ProCamera2DNumericBoundaries_set_Settings_m173B5D02265AC55D136B968FB88C22A69FAB1FF3,
	ProCamera2DNumericBoundaries_Awake_m578F6026D19ABF007082CD4F79057E0E4CC127A0,
	ProCamera2DNumericBoundaries_OnEnable_mD848F747FFA65257AE01D10E01E06DF10B2348FC,
	ProCamera2DNumericBoundaries_OnDestroy_m7AD1A446814E1FA6B042DCDCC675D98BC3A54A90,
	ProCamera2DNumericBoundaries_AdjustDelta_mAC6886D173E4801461AE5FAB1F5108C7B67D518D,
	ProCamera2DNumericBoundaries_get_PDCOrder_m217DF340EF8B7605DEC52E20485C9230A46F14B5,
	ProCamera2DNumericBoundaries_set_PDCOrder_m4587BEA15A13B8C12A568E71F46A984354AB3EC5,
	ProCamera2DNumericBoundaries_OverrideSize_m49C9638739D4F9431E4B83CF18D05B9FB4A991D8,
	ProCamera2DNumericBoundaries_get_SOOrder_mDF4E29302165B049801017BF05145C149CAFF493,
	ProCamera2DNumericBoundaries_set_SOOrder_mF427689CE1091219B4523D32DBB32E6C1E50375A,
	ProCamera2DNumericBoundaries__ctor_m5F9E436F283327BEFFEBD032C5ABC362E0DA9090,
	ProCamera2DNumericBoundaries__cctor_m56E98B498576677A51BA6BB74404BEC5F33A7273,
	ProCamera2DPanAndZoom_Awake_m6BC01D37AC5F77A61DF05180C2E5A17314F4A596,
	ProCamera2DPanAndZoom_OnDestroy_mE3B9B6C8D22B4909F7A50ECF587E081924164A36,
	ProCamera2DPanAndZoom_Start_m731D61552514FB8A983B1609512BF7A9F10A2915,
	ProCamera2DPanAndZoom_OnEnable_mCBAC3D2736FB451910CA9F4D3E79B70FFC23056A,
	ProCamera2DPanAndZoom_OnDisable_mC31A9ADBFE1D9BF1261CCE1AF636B3F7184BA55D,
	ProCamera2DPanAndZoom_PreMove_m4CA6D396BD55DC9A850C6A32226D03AF0F8CD545,
	ProCamera2DPanAndZoom_get_PrMOrder_mB20BB8CBA528FAB5B7B299E8EF0627F3FB154910,
	ProCamera2DPanAndZoom_set_PrMOrder_mEF50EE6CC89CCD4EDA151D04F49FCD008052297C,
	ProCamera2DPanAndZoom_Pan_mAFEF6F12C8DCCDCDC6ED5952744F549DAE763413,
	ProCamera2DPanAndZoom_StartPanning_m7575B63D0D445D6A4A4173F66B6BA32BBA5E9C09,
	ProCamera2DPanAndZoom_StopPanning_mDE941EE02B035B120D4CD710020D1D762AE62926,
	ProCamera2DPanAndZoom_Zoom_mF7AAAE47E56A1BE6CCBE9FB19D91B729D1C22172,
	ProCamera2DPanAndZoom_UpdateCurrentFollowSmoothness_mF61BF85FEAA72EABB0D44837CFBCA47C3269111B,
	ProCamera2DPanAndZoom_CenterPanTargetOnCamera_m516835E6A733549F29D39E582CBD4C89C53CACA2,
	ProCamera2DPanAndZoom_CancelZoom_m774D96FC197DA3C2086CD1DBCE69CA40E264C9B5,
	ProCamera2DPanAndZoom_RestoreFollowSmoothness_m6982CB0C40ED41985010F2C017D62BAF11199EEA,
	ProCamera2DPanAndZoom_RemoveFollowSmoothness_m4521D2385206356C3DAA671785FB68F21625DEA2,
	ProCamera2DPanAndZoom_InsideDraggableArea_mC63B221DE56B1F76F2BCB4E87277B24BE5141542,
	ProCamera2DPanAndZoom__ctor_m392374D54D284DA3502626BCBC74CF501F9951E3,
	ProCamera2DPanAndZoom__cctor_mDC0132726582A95C4AB55AD9889B962C0F924C85,
	U3CStartU3Ed__52__ctor_mD92E1D608C73AA8DA78DBB381A9F84A59456DC44,
	U3CStartU3Ed__52_System_IDisposable_Dispose_mED9D294EBA91E017D90AA901EA5C55E620E82AF5,
	U3CStartU3Ed__52_MoveNext_m07A83EA7DDB65D114C55F599F9D9AF8CCFD6DEA0,
	U3CStartU3Ed__52_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m270B7E059111F31DD9B9952560BA84D608FB664D,
	U3CStartU3Ed__52_System_Collections_IEnumerator_Reset_m6AD8652989DEF47ACC7161C483E5045064EBAE29,
	U3CStartU3Ed__52_System_Collections_IEnumerator_get_Current_mA718778E852612DEB1E10F3B1193B30FD4B637F5,
	ProCamera2DParallaxLayer__ctor_m4FA2A8CD11B18CEA3FA3A9667599D3108ACE81E8,
	ProCamera2DParallax_Awake_m065C295DFDDA28EB962FB866C944BCA3ADCD5495,
	ProCamera2DParallax_OnDestroy_mFBB41796F39490224265D0D3DB59FC149AA93687,
	ProCamera2DParallax_PostMove_mAA8B4CF00EC43ED15C0C343F0597B4CAC9549DB3,
	ProCamera2DParallax_get_PMOrder_mFAAEC5B7ACCF7B055A52815369B1D0BABECF19CB,
	ProCamera2DParallax_set_PMOrder_mFDBE78DAC1707108F2D5F8A8863339A2BFFCB943,
	ProCamera2DParallax_CalculateParallaxObjectsOffset_m96D1B112733FEFF1AA76B6367B26D84281149D87,
	ProCamera2DParallax_Move_mE791D72268CAB06CEE48B89D9825314E86F2873C,
	ProCamera2DParallax_ToggleParallax_mFC94278568AE72B61552474C99330CEEFAF16627,
	ProCamera2DParallax_Animate_mE40313CDE2DD9776A3AD15637DDC50995EB15F5F,
	ProCamera2DParallax__ctor_m4280BC7810CFD0C6755F64137764E1C8E4836420,
	ProCamera2DParallax__cctor_mF568B5F5CA1F6DFB156AC07A415142D058766187,
	U3CAnimateU3Ed__23__ctor_m25B854B3751C687AA6F5F552B0A6396F1FA31476,
	U3CAnimateU3Ed__23_System_IDisposable_Dispose_mC42462AA29441C2706C8068C4AE8DF53B9DF5F24,
	U3CAnimateU3Ed__23_MoveNext_m56350DA494B587ED37D401209CF45DC24342B0BC,
	U3CAnimateU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC8DB99E3889FBF634C6D35EF1BBDCB948CED5B0D,
	U3CAnimateU3Ed__23_System_Collections_IEnumerator_Reset_m6C25A48A470812AE898ACA1EB485205C2A399821,
	U3CAnimateU3Ed__23_System_Collections_IEnumerator_get_Current_m385EF76D2756C2EF6C5489F6C3D58A012A3E38CF,
	ProCamera2DPixelPerfect_get_Zoom_m631BAE7D5B19521E884B8D22AD2FD833A0AD30F1,
	ProCamera2DPixelPerfect_set_Zoom_m9796B518DF24E4F136D682B39F1CB1B8E276C542,
	ProCamera2DPixelPerfect_get_ViewportScale_mAB68DBA757F8626D273A32F0083B6C92DA8A1F84,
	ProCamera2DPixelPerfect_set_ViewportScale_m92BE71D08E9C0C13F6F0A0A8354BCCBF2C20F94E,
	ProCamera2DPixelPerfect_get_PixelStep_m1CEB173B55E0D43A4F8F917FD12876017E4235F6,
	ProCamera2DPixelPerfect_Awake_mEC80E6BFA17E532BA03B02D6DB43E54C6AC320CA,
	ProCamera2DPixelPerfect_OnDestroy_m362C7216A8B610154AB62084A74B2676E5F2CC00,
	ProCamera2DPixelPerfect_OverridePosition_m6211C4FE6CADF4489144A74C25DF5B17729BC160,
	ProCamera2DPixelPerfect_get_POOrder_m15DC4559F2E22E8A2BDA87555E716EB9A1CE29EA,
	ProCamera2DPixelPerfect_set_POOrder_m390D55D1D78355C5EF4671C6CAF7CEBB703D1BC1,
	ProCamera2DPixelPerfect_ResizeCameraToPixelPerfect_m40B5698F1F770793DF93D15C4EB861A1F66859AE,
	ProCamera2DPixelPerfect_CalculateViewportScale_m7254352966451BFEAC1D788E23E0C03F80D6084C,
	ProCamera2DPixelPerfect_CalculatePixelStep_m5F0533B91702424A31FA2C9E0ED78A9CC516A61D,
	ProCamera2DPixelPerfect__ctor_m5EEDD72F08C9B7BFACF645126919FE7009FC5BD6,
	ProCamera2DPixelPerfect__cctor_m2D1F1C0250C135CCB2AF20186CFE406CDF53D445,
	ProCamera2DPointerInfluence_Awake_m4B858F264F8B3760E6E1F9093B02100866B5D9B4,
	ProCamera2DPointerInfluence_OnDestroy_m1C4F6B062CA9EBDE3FBB9131C1E04BB4FAF8AEA3,
	ProCamera2DPointerInfluence_OnReset_mA52080E84D14C2F69B5889B122A16325FDF17C48,
	ProCamera2DPointerInfluence_PreMove_m6581E8589B7EFAF080A321D3C6AE2A33E48FF129,
	ProCamera2DPointerInfluence_get_PrMOrder_mCE3AD56C74BD7EB4AFF8159A70E0157770B6682C,
	ProCamera2DPointerInfluence_set_PrMOrder_mD384F739D740324448E71E9A1DE3C9CDDD2DA374,
	ProCamera2DPointerInfluence_ApplyInfluence_mA9A3C99121925F3FC2632BE5FC8B1C6BBF34F2F2,
	ProCamera2DPointerInfluence__ctor_m13A1EF8FB36062E9EFFB39FCDB87F503584A1BF5,
	ProCamera2DPointerInfluence__cctor_mCD4E02A90A6BEFB7682085CEEC3C3F74536F647E,
	ProCamera2DRails_Awake_m41C2E3C60167F221CC581836C983D42C23ED20A1,
	ProCamera2DRails_OnDestroy_m27CF39547E5CEC6A6F9AE48A8025E8EABD7EBCFC,
	ProCamera2DRails_PreMove_m57931C6F00A914F39EBBF0BFF270DC46956A647C,
	ProCamera2DRails_get_PrMOrder_mE3A8D17EF7EEB637D1AE91DBCDEAB789BDB99B26,
	ProCamera2DRails_set_PrMOrder_m5B08496B76006A5AC01DFE86F4313D75C1533204,
	ProCamera2DRails_Step_mC85476CBF88C327356388E00F63363338D47F842,
	ProCamera2DRails_AddRailsTarget_m74A3DF1079D806E2DDB51DAFD768940E12554D97,
	ProCamera2DRails_RemoveRailsTarget_mD13CBDB16E5E94313D1B8231AD698C1021F867CE,
	ProCamera2DRails_GetRailsTarget_m66A59B09C54F89CBC7744C7F094B0FC103425E68,
	ProCamera2DRails_DisableTargets_m50366B4D5F7AEF172AAC18D4BD60CC5DC49E4812,
	ProCamera2DRails_EnableTargets_m9F4057DF3A73B41890852EE93CC075990CFDF936,
	ProCamera2DRails_GetPositionOnRail_m6F5C857AAC80B56126E7DF94A097D6D5231D8609,
	ProCamera2DRails_GetPositionOnRailSegment_mB6EDEB3EB6EA59D550448F1AE5E358672592B8E4,
	ProCamera2DRails__ctor_m3972E5C826166C72A36A5AC1CB490AA3A1C2262A,
	ProCamera2DRails__cctor_m049C7A0814B555BD98BEB6119E0387A0118A8E8C,
	ProCamera2DRepeater_get_RepeatHorizontal_mF4D0F9B030E50DE1FC4C57277E3A53A1B9C1007F,
	ProCamera2DRepeater_set_RepeatHorizontal_m5E36265249E43B7D2DF071979880941A62126C93,
	ProCamera2DRepeater_get_RepeatVertical_mA009D2035A9AFF1CD62D67252A1CB9B0922ABB89,
	ProCamera2DRepeater_set_RepeatVertical_mE475620B1184C121DD4E44259A0448D56DCEAB77,
	ProCamera2DRepeater_Awake_m629FC9F4455659E8E923C0D30318E7C7E4A9B6F0,
	ProCamera2DRepeater_OnDestroy_m3AF800F7CBC02A2B2F8E44B03F227CF3BA9E6554,
	ProCamera2DRepeater_PostMove_mD0B658CE9623E07179D7580B34A5F33F5910F4EB,
	ProCamera2DRepeater_get_PMOrder_m0047209EF3523395312982C9FE075A08EF625F64,
	ProCamera2DRepeater_set_PMOrder_m7F26559F0C2F31256231F999456BCC3557593519,
	ProCamera2DRepeater_SetRepeatingObject_mCDDF77648685A07E9B2917A875B3752C936C833B,
	ProCamera2DRepeater_FreeOutOfRangeObjects_m4CDA3A64738EB856308B970EAD348372F9D96D8B,
	ProCamera2DRepeater_FillGrid_mDF37D6A974E7C640A386418E2ADA52D2C2121DB3,
	ProCamera2DRepeater_InitCopy_m26F462B24113818D46C7006169353BB2E6D3169F,
	ProCamera2DRepeater_PositionObject_m41BAE779551237F6C1CED8E98B695E0C8B2B9429,
	ProCamera2DRepeater_Refresh_m58FB6B3E3889462AA34CC07E91D2D3557C76F927,
	ProCamera2DRepeater__ctor_mEFBCC03AADEFD119C118B12C7189097C9BB3D87C,
	ProCamera2DRepeater__cctor_m1C934621C9F228723C0CCA14CA097EEEB668A7CE,
	IntPoint__ctor_m9C4A5310BCBB59254735055F9344A5A6B0CCB7D5,
	IntPoint_IsEqual_m35427735D5A80E733404C43AD10403BE41DB8328,
	IntPoint_ToString_m8C85FBFAF05E2190667CF6EA9202BE3EA92A09FC,
	IntPoint_Equals_m74742DDABE818D5806161705E5C890FC6CB1B9FF,
	IntPoint_GetHashCode_mFB4BA17636681D2D12556AE92D86A7461171CB2D,
	IntPoint__cctor_m8E99957DD95DCC50501138760B082C2927CB52C7,
	RepeatedObject__ctor_m20BC202C14DD1CC2DB686F8BED52B7ED1B9A3574,
	Room__ctor_mB61B1D7757143CE57BEB1AFB1B45CDE5CF97CBD7,
	Room__ctor_m264859649D6F7F62C13A409FCB2CF5624FA9249D,
	RoomEvent__ctor_m9058DE47F810DA8CE426E6E2E148E8ABE8EE4AE8,
	ProCamera2DRooms_get_CurrentRoomIndex_m2872EB08913283500DA2E24C6BA4C84F336683B6,
	ProCamera2DRooms_get_PreviousRoomIndex_m983B58C408712097E19EEDC697A6895AD3B49DF6,
	ProCamera2DRooms_get_CurrentRoom_m9579F7C574DA8DE09CC3AE978209195844ECA56F,
	ProCamera2DRooms_get_OriginalSize_m18AB9488522597AAFEDD5D858891CFBD12949AB1,
	ProCamera2DRooms_set_OriginalSize_m8EC167F41E08D153BCA0DC21AD996F57C5B891D8,
	ProCamera2DRooms_Awake_mFFF50993091B03135CD38E0DC7093E7EAACFAD0B,
	ProCamera2DRooms_Start_mCA3EC509C22FB027F337DCF9021FADEC63158846,
	ProCamera2DRooms_OnDestroy_m5EAB7DBEF6466FF92A85B810E5022E0DB6872388,
	ProCamera2DRooms_OverridePosition_mF3E30677538999A64B8AF4CEF4FE3ADFBB51344D,
	ProCamera2DRooms_get_POOrder_m3AE25A6138380EFCEBB2DAE5EC1AC30034C75839,
	ProCamera2DRooms_set_POOrder_mD24CCF66462F0671921D94033641B463EB0F0D7A,
	ProCamera2DRooms_OverrideSize_m16426E13C3E84EEF8D3D8992D320F4384D023CAA,
	ProCamera2DRooms_get_SOOrder_mD01487EE72B18A11C036DDC4C9CF28AE62505B82,
	ProCamera2DRooms_set_SOOrder_mBA97C5794BCAAA5240158762865AEA3817567DAC,
	ProCamera2DRooms_TestRoom_m88475FF854A8E52AC28754CCB5D43D009B8AF0DB,
	ProCamera2DRooms_ComputeCurrentRoom_m366C0C6FF36905C867F5F2A25160DAD744A61459,
	ProCamera2DRooms_EnterRoom_mA5B3D64E0CBC8A0CE6D21735E080540665C622D6,
	ProCamera2DRooms_EnterRoom_mE1C8FA9757CC123FF5446C4C89EBF8ED1B1AE8F3,
	ProCamera2DRooms_ExitRoom_mB189413FCC3A444265A93F15782588E34E526D41,
	ProCamera2DRooms_AddRoom_mF022F1F2846E1CBE4ED70BBE4631889D68E862C8,
	ProCamera2DRooms_RemoveRoom_m3A98C09EB9340D467C2EB4F4E40020F343598650,
	ProCamera2DRooms_SetDefaultNumericBoundariesSettings_m9F4AA086B92D7D998B0DBBB2EE012B6FDA7B42A2,
	ProCamera2DRooms_GetRoom_m1D323258CF3CA83B72D6903F17F4DF0D35F78503,
	ProCamera2DRooms_GetCameraSizeForRoom_m7FD34831D8180815A7553C834ACEC11E7570E1C0,
	ProCamera2DRooms_TestRoomRoutine_m65646DD9BDED0840B9C4D41647C9B5852609E8B4,
	ProCamera2DRooms_TransitionToRoom_m88F5A6D0A3945E9CCCE799CE8F6DAED6CF4B3226,
	ProCamera2DRooms_TransitionRoutine_mC90AA092E2E39061598D3E477605FEFC8A8F14EA,
	ProCamera2DRooms_LimitToNumericBoundaries_mDEDEEF49BE64C6DC6D207383D71E282D7CFF66B3,
	ProCamera2DRooms__ctor_m106614BAF97A6164804D7836D5D8C0736F859C44,
	U3CU3Ec__DisplayClass49_0__ctor_m094DC40D614A7E4A3E528CFA8D5AFF8BDFC52BC2,
	U3CU3Ec__DisplayClass49_0_U3CEnterRoomU3Eb__0_m556D9979DCE73CC02B7A2F4F1E745631EC61D2E7,
	U3CU3Ec__DisplayClass52_0__ctor_m66B2687F8E12F159D0D575D4373563C62122154D,
	U3CU3Ec__DisplayClass52_0_U3CRemoveRoomU3Eb__0_m550BF73461CBF9C45E7F402B4FCCAE7D4E80AA73,
	U3CU3Ec__DisplayClass54_0__ctor_mB7A0FEDD02BC5B6F93CBE1F227B0D85D7784B8D3,
	U3CU3Ec__DisplayClass54_0_U3CGetRoomU3Eb__0_mB27B203A22D140860121A0A14EAF8CD4B6DBACD4,
	U3CTestRoomRoutineU3Ed__56__ctor_m6FDB9514500AF02FBA25A4B153BF9334FCAA0947,
	U3CTestRoomRoutineU3Ed__56_System_IDisposable_Dispose_m86F4CC05D9A6CB734647916752D7B4A3A9AFE346,
	U3CTestRoomRoutineU3Ed__56_MoveNext_m3ADDB51D7AF17A31CF9465D0BD6424C8AC34F9AB,
	U3CTestRoomRoutineU3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2E152DE74D6C71DDAE14DF0F996E026E0618B888,
	U3CTestRoomRoutineU3Ed__56_System_Collections_IEnumerator_Reset_mED22D9D7ABE7105FDBFDF20C2B53B9E7C31FB9BC,
	U3CTestRoomRoutineU3Ed__56_System_Collections_IEnumerator_get_Current_m0EF13F0090627020DF0959446288C922A82B9944,
	U3CTransitionRoutineU3Ed__58__ctor_mEA1BFC4251CAF4CA5D4DBCC9385074A7B55234FF,
	U3CTransitionRoutineU3Ed__58_System_IDisposable_Dispose_m3B9CC89333F22450D5AA1EDECE23F79A67E0807C,
	U3CTransitionRoutineU3Ed__58_MoveNext_m1D9E7FFFA04AE5461AF12CF9F85605F9FAEC01EF,
	U3CTransitionRoutineU3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2806747902CA94B5CC7A9A2542AFDC721C10FAD1,
	U3CTransitionRoutineU3Ed__58_System_Collections_IEnumerator_Reset_mA044BAA75A59A72B38B1DFCC5D66803B16473549,
	U3CTransitionRoutineU3Ed__58_System_Collections_IEnumerator_get_Current_m6DE41234405E344F0CA1CE5EF233B1C557B2B5B0,
	ProCamera2DShake_get_Instance_mE5088408DA31B1DD04DD6557AF4728CA5E1DD87C,
	ProCamera2DShake_get_Exists_m48B4D86F7A7E46A63FF4CAA8888D034FE7C9736D,
	ProCamera2DShake_Awake_m2B25CA30C19C768E774A9E1F929D809652C4E649,
	ProCamera2DShake_Start_mA7118CBC2350FFBE9AAE9F13DDE80CEC7619B470,
	ProCamera2DShake_Update_m5BC9FF27D6BE23D29D0DB0683EE507D876BB8827,
	ProCamera2DShake_Shake_m1FD42A61C4EC8B2A58F35EEA7E7B14CCA2B4F257,
	ProCamera2DShake_Shake_m41DBF5CBF18A3437316C56B12B2853BC0C4792B5,
	ProCamera2DShake_Shake_m96A4C403647156FFF72E5EEA997EA9EFA6AA050B,
	ProCamera2DShake_Shake_mE8B4BCA0C5CDE8FF4E46B3652E3C9C3966EDB2CA,
	ProCamera2DShake_StopShaking_m6CAED85FB1681ACDAEB654CEFB3595D3B4E157C1,
	ProCamera2DShake_ConstantShake_mC6A3FE872FF5DF20546CE0D4BDF99EFC0BD2B76E,
	ProCamera2DShake_ConstantShake_m5B386A99D697BBC33CB11D2E652B881C2869D46A,
	ProCamera2DShake_ConstantShake_mBCAFA5FC0FFFAA9C69559A48117FBAA695C4B164,
	ProCamera2DShake_StopConstantShaking_mDE370598162F40CD59FDB341CAF76CEF6A26C190,
	ProCamera2DShake_ApplyShakesTimed_mE54EEA471792ED1872EA7BD0B4C20D94CD8D44E9,
	ProCamera2DShake_ApplyInfluenceIgnoringBoundaries_m6CD9C0DB0782E62E47D82B325353D81F3AC09006,
	ProCamera2DShake_ApplyShakesTimed_m66D6A76E474311CE1EFA452559286F206A18EE89,
	ProCamera2DShake_ShakeRoutine_m6355DC769E6C2971AF0B056911BFA80E7B13A686,
	ProCamera2DShake_ShakeCompleted_m279C6AC487C8569DD08AA84FC448FCAF98165E22,
	ProCamera2DShake_ApplyShakesTimedRoutine_mA3AB8BF75E78F2CE2A0529C71C3047A9A908BA08,
	ProCamera2DShake_ApplyShakeTimedRoutine_mE5200201C5B2D3842A68D3B2055A7BC72E86D3AE,
	ProCamera2DShake_StopConstantShakeRoutine_mBD2EB8330B5FAF866ACA4A5330D7138A07635C08,
	ProCamera2DShake_CalculateConstantShakePosition_mCB1F4CB3966BA7181812F418695B27D9F653573D,
	ProCamera2DShake_ConstantShakeRoutine_mC333456A6D80BF77E06AB6EEBD90A4A3678A4731,
	ProCamera2DShake__ctor_m181D54568E60522D13F8E022BC9F1BEDBD3D5267,
	ProCamera2DShake__cctor_mC822D1381917AD468E56F3A4051FEA9B19526CAF,
	U3CApplyShakeTimedRoutineU3Ed__44__ctor_m687B7C15896A81C5D8E200B609EE5669EF3A1704,
	U3CApplyShakeTimedRoutineU3Ed__44_System_IDisposable_Dispose_mE4C420C515042D4E478C4D30A14C4578AB642C6B,
	U3CApplyShakeTimedRoutineU3Ed__44_MoveNext_mC2D10AF1C7869910BF3DFB66171185549C81C549,
	U3CApplyShakeTimedRoutineU3Ed__44_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5DD8EB4807ED68D5DF748C00D08C60FA473047CE,
	U3CApplyShakeTimedRoutineU3Ed__44_System_Collections_IEnumerator_Reset_m278758615A631971BB0F41A62F15BEFA84D44A6F,
	U3CApplyShakeTimedRoutineU3Ed__44_System_Collections_IEnumerator_get_Current_mAB5CE84573D43B8F77C3B1914D66622DD47988BA,
	U3CApplyShakesTimedRoutineU3Ed__43__ctor_mCD34110E87E4C894813A8909F44E6AA614DF5703,
	U3CApplyShakesTimedRoutineU3Ed__43_System_IDisposable_Dispose_mF5600C8D3897BB74F5C64E81067FBA148DE650CE,
	U3CApplyShakesTimedRoutineU3Ed__43_MoveNext_m2C77FD5E23E6BDE558D0C16F1AF19344EB0E1D70,
	U3CApplyShakesTimedRoutineU3Ed__43_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD355AE36E25CAABC4463F14C8285625D8D9B880C,
	U3CApplyShakesTimedRoutineU3Ed__43_System_Collections_IEnumerator_Reset_m524EB72FB3F3CB567E584544D14582DE4872D0F8,
	U3CApplyShakesTimedRoutineU3Ed__43_System_Collections_IEnumerator_get_Current_m37EEBEE74FDA97155071FBC93DEE00682816A4FB,
	U3CCalculateConstantShakePositionU3Ed__46__ctor_mE5ED188487C4EBC6A1BE9A3670C571A6F512B27A,
	U3CCalculateConstantShakePositionU3Ed__46_System_IDisposable_Dispose_mB7F0AD16CA89DE4379D2F3E055304AEF68739E89,
	U3CCalculateConstantShakePositionU3Ed__46_MoveNext_m8501D1FC7265EA2CD043872390EFD363009F40ED,
	U3CCalculateConstantShakePositionU3Ed__46_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m4780230FA9D0691158D82792A8B907AC1CC1B970,
	U3CCalculateConstantShakePositionU3Ed__46_System_Collections_IEnumerator_Reset_m103197FF1D28C257970D4E7CD102A16D5B1382B5,
	U3CCalculateConstantShakePositionU3Ed__46_System_Collections_IEnumerator_get_Current_mBFB5803D52C6DC89C09890ADCE16C089F8EEDE8F,
	U3CConstantShakeRoutineU3Ed__47__ctor_m0AAD63C7EFC2A02D20BF84C9AE5EA7D36706B170,
	U3CConstantShakeRoutineU3Ed__47_System_IDisposable_Dispose_m612A77F1EDA887A3259F9FD7047AD13F760718FE,
	U3CConstantShakeRoutineU3Ed__47_MoveNext_m0709F4B063E296EA02F2590E3E926686A858F890,
	U3CConstantShakeRoutineU3Ed__47_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2F38B74F4DEEB1E6338D5D3B6031C9A953CEE485,
	U3CConstantShakeRoutineU3Ed__47_System_Collections_IEnumerator_Reset_m055E6E66969146B776E08F8DD798525444B5542B,
	U3CConstantShakeRoutineU3Ed__47_System_Collections_IEnumerator_get_Current_m73259E19CE4A2FCAA87C8D5A995051C8B628B973,
	U3CShakeRoutineU3Ed__41__ctor_mE84017646673FB07996C92DA43F12BD46CBEDDC3,
	U3CShakeRoutineU3Ed__41_System_IDisposable_Dispose_m479B006D6242E75326C4E3526034E2D593E8AB4B,
	U3CShakeRoutineU3Ed__41_MoveNext_m4D71BD993159F0C236E2936A4D559CD8BA1A6ABE,
	U3CShakeRoutineU3Ed__41_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEEA71DAD4E374786B8A49F5E301BD18F8CEEFB50,
	U3CShakeRoutineU3Ed__41_System_Collections_IEnumerator_Reset_m441512342D413EB015826E81EFD0C3B60BA66573,
	U3CShakeRoutineU3Ed__41_System_Collections_IEnumerator_get_Current_mE97E9D008A2CD11EEF6EC51010D2F7CD8E7D0CE7,
	U3CStopConstantShakeRoutineU3Ed__45__ctor_m72F91CF09520C0C8CCDB2C95FE0D6D8E29A8B5EE,
	U3CStopConstantShakeRoutineU3Ed__45_System_IDisposable_Dispose_mBBEBA2AA94458C83ADD80319798D11398191DB3D,
	U3CStopConstantShakeRoutineU3Ed__45_MoveNext_m1241A7C7F4C89604333EF21EAA5176EE6FE5716B,
	U3CStopConstantShakeRoutineU3Ed__45_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6F8E21E5858268DFEEC32E67630BB4004D06B0FD,
	U3CStopConstantShakeRoutineU3Ed__45_System_Collections_IEnumerator_Reset_m8772142BFBB5892ECB4EFCE8F6E75BB76A948881,
	U3CStopConstantShakeRoutineU3Ed__45_System_Collections_IEnumerator_get_Current_mE68C8D319C61185C60C49DDAFDF426235834B39D,
	ProCamera2DSpeedBasedZoom_Awake_mBB0821C84948E71450161CE4F702ADF7829D743F,
	ProCamera2DSpeedBasedZoom_OnDestroy_m836EA45E38763DBD5058EBFD167418D45305033D,
	ProCamera2DSpeedBasedZoom_AdjustSize_mDE874E73F005BE1F8E0E61B02416D99DAC41D548,
	ProCamera2DSpeedBasedZoom_get_SDCOrder_mA9911E45EFD79D4BA41F6CD7F6530D2700640884,
	ProCamera2DSpeedBasedZoom_set_SDCOrder_m20C2630AF9FE87D2010CFA9D957D8848F34AC268,
	ProCamera2DSpeedBasedZoom_OnReset_m248A3024A62073840D02C7BA5C77F394B434C734,
	ProCamera2DSpeedBasedZoom__ctor_m1795586099F240D33BDC6A6EB8517A4414A468B2,
	ProCamera2DSpeedBasedZoom__cctor_mE14DAEEEE2F274D15366EE4CEDBF9573916314E6,
	ProCamera2DTransitionsFX_get_Instance_mFF94EBFD9B64ABF81E88E2C0FE7EF919907DB1C8,
	ProCamera2DTransitionsFX_Awake_m83747C922890F4DCF2E5AF6BFFCAC923A5258B0C,
	ProCamera2DTransitionsFX_TransitionEnter_m97589A7091603A387838B1F5AAAC32A061C90781,
	ProCamera2DTransitionsFX_TransitionExit_m604CC64907B1AB598DA732CC2B2B8509D7B4EAB4,
	ProCamera2DTransitionsFX_UpdateTransitionsShaders_m5DD784CEE7EC4A2DFBBEA5F33A8557AC3421090D,
	ProCamera2DTransitionsFX_UpdateTransitionsProperties_m0A71740385D815763DC2C09D8B36C7E3F99818CE,
	ProCamera2DTransitionsFX_UpdateTransitionsColor_mCF179BC5C4566B891D20E9FBA004160DF2D0E05B,
	ProCamera2DTransitionsFX_Clear_mAE1E26F9D6DB4D055DCB6645AD3C9B165643A65F,
	ProCamera2DTransitionsFX_Transition_m3F1D638C82A330DC9522708EB7CF541C59D84762,
	ProCamera2DTransitionsFX_TransitionRoutine_mF39F8E2090B9C864404F19FEB1B6ED1B20273810,
	ProCamera2DTransitionsFX__ctor_m4D3D4AA4577616805B822858C724A96E4E5D1235,
	ProCamera2DTransitionsFX__cctor_mC0783168436854FDC39F15B01DB52E8AA279C27E,
	U3CTransitionRoutineU3Ed__48__ctor_mAFBDE72D273D7D7A3CE0A61EF6F442DD799F38F8,
	U3CTransitionRoutineU3Ed__48_System_IDisposable_Dispose_m8C0D6CF81201EF10D0C23C67732C7668ECD2A415,
	U3CTransitionRoutineU3Ed__48_MoveNext_m7F7782236269177078BC15B52FC57B3C81BAEC18,
	U3CTransitionRoutineU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m651E1B2E4C792BAC7894F727E7FF4C54E81F089D,
	U3CTransitionRoutineU3Ed__48_System_Collections_IEnumerator_Reset_mC97D545A45CAFFBACDC95C1F1FAA24419CA3BE40,
	U3CTransitionRoutineU3Ed__48_System_Collections_IEnumerator_get_Current_m6F402ABE06FA73C1659FA9E2D7ADA4B366509BC4,
	ProCamera2DZoomToFitTargets_Awake_mB9D41E48BEC6D652D41C27FEDE78499D8B0E86D0,
	ProCamera2DZoomToFitTargets_OnDestroy_m747CAE23396AABB1BED55164D787A34BBEE6FBCE,
	ProCamera2DZoomToFitTargets_OverrideSize_m48628C10546450A410485E9FC6DE321DCE84845F,
	ProCamera2DZoomToFitTargets_get_SOOrder_m93B34872D95CB62C5A61D6AF16A05B71494796A9,
	ProCamera2DZoomToFitTargets_set_SOOrder_mC9555088E8C17F324CE59671D230F5104A3C7CBC,
	ProCamera2DZoomToFitTargets_OnReset_m7D4A7C48D0FD0A0EA6DAA5CD6FA1A397A0A2557F,
	ProCamera2DZoomToFitTargets_UpdateTargetCamSize_mA76AD1126BD094927B66C5AAA0D761C5EFF2E7B6,
	ProCamera2DZoomToFitTargets__ctor_m3DDB2A84AF366151C2B07BC55C6C8BE3DBC3E891,
	ProCamera2DZoomToFitTargets__cctor_mCFE44585301AA63C2AF87DB18B0602CDA04CD478,
	ProCamera2DLetterbox_get_material_m41B5D137FB07EF0472DC82A66A621BBB43A43A1A,
	ProCamera2DLetterbox_OnEnable_mC1C7DF3FC09154C10F17EDCB04744749DBCF4B12,
	ProCamera2DLetterbox_OnRenderImage_m3B13831E7BE8768AF6B4E2B99577A98407BA6AB8,
	ProCamera2DLetterbox_OnDisable_mECA682388BD04DB186107DF9ABEAFCCD865CB6F2,
	ProCamera2DLetterbox_TweenTo_m5CAF1779A917341DE5E02BF9ABE9F94898D6BB3D,
	ProCamera2DLetterbox_TweenToRoutine_m440D8BAD172FC93FBB78B3A716585285FAC42D36,
	ProCamera2DLetterbox__ctor_m60D17EE1EF4C6CAE7E0B7F970741A2143557C2C3,
	U3CTweenToRoutineU3Ed__13__ctor_m92526D77BAF39035104A37A0D06AE65667CD076D,
	U3CTweenToRoutineU3Ed__13_System_IDisposable_Dispose_m7429AE9B30C164E00A2FE8206F42B03FDB71B28A,
	U3CTweenToRoutineU3Ed__13_MoveNext_m5A11609ED8A972EAF6D2DC77F4C7399BDE9A586C,
	U3CTweenToRoutineU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3D2BEA18B590EA9B3A62F848571AD2779B556895,
	U3CTweenToRoutineU3Ed__13_System_Collections_IEnumerator_Reset_m310742DC16415B7C3F57F4D2BEB21AE689010B7A,
	U3CTweenToRoutineU3Ed__13_System_Collections_IEnumerator_get_Current_mA7D416662D58A21EF2CC8D2045D36E1DA7FED915,
	ProCamera2DParallaxObject__ctor_m0D5CE16184C882D42A7767C8770EACA2EDC0AD96,
	ProCamera2DPixelPerfectSprite_Awake_mCE253D662B71BA07C0890199AEAD25AA6E1B43BB,
	ProCamera2DPixelPerfectSprite_Start_m61D4E4375AF700776A617837466DE63BFDD6CAC7,
	ProCamera2DPixelPerfectSprite_PostMove_m4F3561BEEC1FA42A9A3CB3582D0EC580F1272372,
	ProCamera2DPixelPerfectSprite_get_PMOrder_mAC15AE20886ED8FA093D08DB334AB3B69D648B88,
	ProCamera2DPixelPerfectSprite_set_PMOrder_mFC3CCCA34102A2C493C901A148F37626DFF7A1AD,
	ProCamera2DPixelPerfectSprite_Step_m9E6564A4EDE2C697CBE0FB3FF26DC1999D14C9E3,
	ProCamera2DPixelPerfectSprite_GetPixelPerfectPlugin_m94AA74AC683F0F3FE661C01F13FC7BC98AA9FB2F,
	ProCamera2DPixelPerfectSprite_GetSprite_m7FDFB315397CE61F18530ABE812128D31DAFC4C3,
	ProCamera2DPixelPerfectSprite_SetAsPixelPerfect_m6606196A8BC9FCC4022947810F043C12813667CE,
	ProCamera2DPixelPerfectSprite_OnDestroy_m6EC1AFD283B7C9DD72A19FEB06C014C194824C04,
	ProCamera2DPixelPerfectSprite__ctor_mDA349346F1ACE4828712F7E49DEA8F504299E355,
	BasicBlit_OnRenderImage_mBC8EF27FAEC68AE6E65E7DABDD4F394DECE8F89D,
	BasicBlit__ctor_mEA495A90A3232651A90CE4FB6B4920717D29AD31,
	ProCamera2DTriggerBoundaries_get_IsCurrentTrigger_m0F26039ECCAA4664D3C2E981C8EE6D64BF6D49A5,
	ProCamera2DTriggerBoundaries_set_SetAsStartingBoundaries_m1D022A849F6C74463BD7C8942F9605A94930CCC3,
	ProCamera2DTriggerBoundaries_get_SetAsStartingBoundaries_m2B63B6904DF9C8FAEEF2095B46A9D31576617A75,
	ProCamera2DTriggerBoundaries_Awake_mE0486355D69F9CFBAD24C50D92D7549A28564736,
	ProCamera2DTriggerBoundaries_OnDestroy_mE40AFF497571D514A2B6B51F8E3246668DEE2482,
	ProCamera2DTriggerBoundaries_Start_mD2583B28266D1E67AA231B23BA6793D0D252FD57,
	ProCamera2DTriggerBoundaries_OverridePosition_m8A92844E9317BC03F3854736B689A61472A46B63,
	ProCamera2DTriggerBoundaries_get_POOrder_m4461C35F7352E0665F487CE6913BA120B0BBE9E3,
	ProCamera2DTriggerBoundaries_set_POOrder_mE7CAD9C74EB475CA9B7828C88DEBE4EC7A5046E4,
	ProCamera2DTriggerBoundaries_EnteredTrigger_mA2150C095983468C28AF90E4A311CED6016CDB93,
	ProCamera2DTriggerBoundaries_TurnOffPreviousTrigger_mDDEA5B9B016B007549C4AB607782C9270DBC58A6,
	ProCamera2DTriggerBoundaries_SetBoundaries_m24586EAFEDA671A33DE96AFB32C17835523D060C,
	ProCamera2DTriggerBoundaries_GetTargetBoundaries_mAD76686381487BA35FE2F1C6EC34D5BEDA648653,
	ProCamera2DTriggerBoundaries_Transition_mF643E0C0669C37DF8DBBC0FAD0A8FB49D4DC7839,
	ProCamera2DTriggerBoundaries_MoveCameraToTarget_m021320D764B2562D4421CA59B775CC42C3A15D6A,
	ProCamera2DTriggerBoundaries_LimitToNumericBoundaries_mA3C4CE46F1ED11F4C3610521F5E771A68648142D,
	ProCamera2DTriggerBoundaries__ctor_mE510F9EDDFC4D70095E9E71BE439175D76EA8FA5,
	ProCamera2DTriggerBoundaries__cctor_mD6B98019227BF35A2C7BA27535E0B32A616C8D09,
	ProCamera2DTriggerBoundaries_U3CStartU3Eb__32_0_m6A54C39B327C74E6B119B3925B6645125B390765,
	ProCamera2DTriggerBoundaries_U3CStartU3Eb__32_1_m97ECC68428FD8749E33C84A8D7E1549E1CE272B4,
	U3CMoveCameraToTargetU3Ed__43__ctor_m40DA53D2E23B1E5D35F7ED905429D47C8CBEE30F,
	U3CMoveCameraToTargetU3Ed__43_System_IDisposable_Dispose_mCF03288B774F24B2F43F65A770F64E9F7B918478,
	U3CMoveCameraToTargetU3Ed__43_MoveNext_m3812AC5CE64A819F195F0EAE8EF0AB75F39D2E32,
	U3CMoveCameraToTargetU3Ed__43_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1526A2F5322CD2D3119267A3CD4BF14F293DD4E6,
	U3CMoveCameraToTargetU3Ed__43_System_Collections_IEnumerator_Reset_mE9B7FB6230CB5090A8C7DA9D3996C3154592C81D,
	U3CMoveCameraToTargetU3Ed__43_System_Collections_IEnumerator_get_Current_m20A60259478537CA397326E36DB3D61776241B10,
	U3CTransitionU3Ed__42__ctor_m5951BDBBEC1FBC95252AB4C9C9D0CA8DDAFC89F3,
	U3CTransitionU3Ed__42_System_IDisposable_Dispose_m268FDFB3E2A3294ED96F0E8D464C567A3124DB29,
	U3CTransitionU3Ed__42_MoveNext_mE326F04D041D1DFF2F951E1836A7F6B26930E098,
	U3CTransitionU3Ed__42_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8473324E157C0EDBC68DA12F9DE6DC853D752675,
	U3CTransitionU3Ed__42_System_Collections_IEnumerator_Reset_m8075A76B6DC253E0A3D9208D153FB9C718A03789,
	U3CTransitionU3Ed__42_System_Collections_IEnumerator_get_Current_m5329B950AEA855D0330B62C870EF9CC969258303,
	U3CTurnOffPreviousTriggerU3Ed__39__ctor_m074BD6A748F8A8CCF82D6EE1CABCBE60EB2FFC73,
	U3CTurnOffPreviousTriggerU3Ed__39_System_IDisposable_Dispose_mEDF9463CDA394718E972BF2C2DA91758DEFBF2C2,
	U3CTurnOffPreviousTriggerU3Ed__39_MoveNext_m16E02A68A7FF429A3410A9339F4EEDB361D1970E,
	U3CTurnOffPreviousTriggerU3Ed__39_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9DEE9AE44D5366788DA1603663049749D10047E3,
	U3CTurnOffPreviousTriggerU3Ed__39_System_Collections_IEnumerator_Reset_mB267AB9C360AE66737A7A07CF6355AA9B8EE85E2,
	U3CTurnOffPreviousTriggerU3Ed__39_System_Collections_IEnumerator_get_Current_m573ED4A91402546E25171E97CFC6CE6E764F3462,
	ProCamera2DTriggerInfluence_Start_m389A7440586101060D862CFC6EDD880B37032CAF,
	ProCamera2DTriggerInfluence_EnteredTrigger_m7F6C298BB93A5A2D42C8FD08279DB23BE3D03C17,
	ProCamera2DTriggerInfluence_ExitedTrigger_m0BF1AA6106E60B2AB2525EA1758D370D7D9F049D,
	ProCamera2DTriggerInfluence_InsideTriggerRoutine_m4A65010C3395325E78FBEA4ACDE4E32C17FD8282,
	ProCamera2DTriggerInfluence_OutsideTriggerRoutine_m6AE9DC9E6802F4F998C101C96923F10EDAF43149,
	ProCamera2DTriggerInfluence__ctor_m0EABE094E3C3DC8EF871ECFE9A380668B981E780,
	ProCamera2DTriggerInfluence__cctor_m080BFB1AFAC00A2F83099F553BD6D2E9BCD159AF,
	U3CInsideTriggerRoutineU3Ed__13__ctor_m110B6FE874534C7F9CA66964B287F16E2DE0F110,
	U3CInsideTriggerRoutineU3Ed__13_System_IDisposable_Dispose_m78E9F1EDFDCC0A3A98365944E6CFBFCFA67F43E4,
	U3CInsideTriggerRoutineU3Ed__13_MoveNext_mA50A5683B352FE0FD5280E2F6E927E7D0F8C1A45,
	U3CInsideTriggerRoutineU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDCA5AA1ED34F87D392ABF5BEF9F66EFACDD96A3B,
	U3CInsideTriggerRoutineU3Ed__13_System_Collections_IEnumerator_Reset_m822E63EE43C93039B969980B246E74344E6D2DA9,
	U3CInsideTriggerRoutineU3Ed__13_System_Collections_IEnumerator_get_Current_m7E768CAD37D82EAC017C6C2BBD2134C35246B11B,
	U3COutsideTriggerRoutineU3Ed__14__ctor_m4DB22F34EB37949083D5ACE23703A5F6A6C74CF6,
	U3COutsideTriggerRoutineU3Ed__14_System_IDisposable_Dispose_m646544044A17FD70909BB4168613C638C75F8992,
	U3COutsideTriggerRoutineU3Ed__14_MoveNext_m22E117DAB1B28CB69C4ED0E92FA3044F9D12094E,
	U3COutsideTriggerRoutineU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m84AB448C8A113F53FFB257D4D409D9A38E347190,
	U3COutsideTriggerRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m71B3218DDEF90CF9721E6AC5F067FBCA6370BC2F,
	U3COutsideTriggerRoutineU3Ed__14_System_Collections_IEnumerator_get_Current_m63514FE2E0B02005C91C27A05F7669B76038BE64,
	ProCamera2DTriggerRails_Start_mA3D1CB8284DD13FF28B6DECF419873C9DAD13180,
	ProCamera2DTriggerRails_EnteredTrigger_mA3674C99C64F7B32821F2C47854AE507C648C12D,
	ProCamera2DTriggerRails__ctor_mADA8CDF1FCA91F925CA27FAD8982E73AA5250012,
	ProCamera2DTriggerRails__cctor_mCD3228061F420D17F93AAA5D71024739E0B60624,
	ProCamera2DTriggerZoom_Start_mF4260EB2F36B75B2ABD27AE899F2F7135C35A38C,
	ProCamera2DTriggerZoom_EnteredTrigger_mE007D4657FE496171D84E059344BF8039140B917,
	ProCamera2DTriggerZoom_ExitedTrigger_mF71BBBE905E1439E8B3659862370E2FBE28AC005,
	ProCamera2DTriggerZoom_InsideTriggerRoutine_m2E9C8100B5F183D911835D9DC6596D3F689C6C31,
	ProCamera2DTriggerZoom_OutsideTriggerRoutine_m24D6EA57BA7D6A5D1861F99F079D0CDFC4E6438F,
	ProCamera2DTriggerZoom_UpdateScreenSize_m2B1D86795B8EEE2D87E2F55818CC0D9DF2460F1F,
	ProCamera2DTriggerZoom__ctor_m3A9C194F4F0FEAAD004A4F32DF2510DCD4DE6D00,
	ProCamera2DTriggerZoom__cctor_m528B49CB208D00C57E09C8DF117EE3A199FE6540,
	U3CInsideTriggerRoutineU3Ed__17__ctor_m17EB0F9A747DF7F073EF479C72FFC63728589CE0,
	U3CInsideTriggerRoutineU3Ed__17_System_IDisposable_Dispose_mB42B9C48488683ECDDEEE2359CA8EA5DBF73C380,
	U3CInsideTriggerRoutineU3Ed__17_MoveNext_mE030C331B3F93DCDE0CD1AC12F77D91B10C3828E,
	U3CInsideTriggerRoutineU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6D2177118E614DEBAACC6B6B10C0E6C99BD20FC2,
	U3CInsideTriggerRoutineU3Ed__17_System_Collections_IEnumerator_Reset_m47510A30DC69EFD69BFCCA676F84970340A8E464,
	U3CInsideTriggerRoutineU3Ed__17_System_Collections_IEnumerator_get_Current_m4BBB1598C0DAB61D4F7A1B238FB3B94FED0D484D,
	U3COutsideTriggerRoutineU3Ed__18__ctor_mC0D3AA336840C93A236645C30DD4EC70C0AEAC3E,
	U3COutsideTriggerRoutineU3Ed__18_System_IDisposable_Dispose_mB9EC19A0EE4BA3383A66579BBFEE819DFCA3CE3C,
	U3COutsideTriggerRoutineU3Ed__18_MoveNext_mE28D8D56F393C05CCF7E911AD372FB2E4A3A74F8,
	U3COutsideTriggerRoutineU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m798E326449E7771D7BA668E9C3AB8FE87EC346EF,
	U3COutsideTriggerRoutineU3Ed__18_System_Collections_IEnumerator_Reset_mB6C0241ED94ABAEDC87604F761EE137378AC71A5,
	U3COutsideTriggerRoutineU3Ed__18_System_Collections_IEnumerator_get_Current_mDEE596CBF6843F937FC8458366F6FC238F63B8C4,
	ProCamera2D_get_Instance_mD8DFABBF6CCE99E45FE7EEC8DEEE5767F97BFE28,
	ProCamera2D_get_Exists_m4702898858AB085D2F78DA1242FDF263FCA53D19,
	ProCamera2D_get_IsMoving_m4FBCEF1E2C24BAC053B60EF1DBCF46C1017F3697,
	ProCamera2D_get_Rect_mD7D77ED3B640F21706E8D0606B92C36FA6A433DF,
	ProCamera2D_set_Rect_mF8D32018B856440EA11E3312EA29B7ACFD98C6A5,
	ProCamera2D_get_CameraTargetPositionSmoothed_m8EE960E49C580F2AF11A2F843A3764FE617883C2,
	ProCamera2D_set_CameraTargetPositionSmoothed_m73A51A4BA3B992CD9159761FA317C72040D759C7,
	ProCamera2D_get_LocalPosition_mA31BA2DDC21E228105F008ECBD9995BA59459455,
	ProCamera2D_set_LocalPosition_m1A584670A06993B90F168C80E1954A45130A8603,
	ProCamera2D_get_StartScreenSizeInWorldCoordinates_m2FFEDD76966A159EA8E1F49C60E1DB372C7EC70E,
	ProCamera2D_set_StartScreenSizeInWorldCoordinates_m76B3840F540476EDFC0E56A7A7ADFA2573DB9FC2,
	ProCamera2D_get_ScreenSizeInWorldCoordinates_m1EB1BD6581436AC4926C06FE98EEAF25E2068EC4,
	ProCamera2D_set_ScreenSizeInWorldCoordinates_mBF29DF6569154687FB9D8DF9BE120A32A1858D45,
	ProCamera2D_get_PreviousTargetsMidPoint_m0BEC736AD8F3639DB8D0D3D841449FB1641AB2B8,
	ProCamera2D_set_PreviousTargetsMidPoint_m4DD50C1D7B40DE2EBDFF688FC6D2DF2D4544EFB0,
	ProCamera2D_get_TargetsMidPoint_m591ED50B4118C4D1F0B0203647587E45D7F5C917,
	ProCamera2D_set_TargetsMidPoint_mBEBDB1FA4E967415DE743D063057ECD8D08F9F73,
	ProCamera2D_get_CameraTargetPosition_m4CE8C22A89C1CEF55F22E74175CC63A3778DFA29,
	ProCamera2D_set_CameraTargetPosition_m952C818F6A7F07B49E5FD163039FF4F839042FBE,
	ProCamera2D_get_DeltaTime_mEF7472348B7127FE4B12CB998AE40AAC7C1A6BA1,
	ProCamera2D_set_DeltaTime_m10BF31E3556A74382A9E4CEA341D366D018C2AA8,
	ProCamera2D_get_ParentPosition_mF0EDE246FB6B2EFD359ECDA1C0A1567D37DB0564,
	ProCamera2D_set_ParentPosition_m88349F9DAA10A4334D3DA0E45441C7464557385F,
	ProCamera2D_get_InfluencesSum_mED5411A2307C47E0C0C829C61E4010AF94282303,
	ProCamera2D_Awake_m177C6CA44636442ED2EFE9B7EB65FBEC6F9DD843,
	ProCamera2D_Start_m3A478EB09CAD73DC67FB3D90EC2E3FC319A90896,
	ProCamera2D_LateUpdate_m6AC3EF76A3A6CF8C613C4A64A71F7BD8D1627A06,
	ProCamera2D_FixedUpdate_mD85D5735C7F9645E86147ACEFEA43EABF906B9AA,
	ProCamera2D_OnApplicationQuit_m5A11FF5C32E64381C1617D78B5012F018BC68E4E,
	ProCamera2D_GetOffsetX_m8A5E529EC820319B59A1520EDA03FD85DBA84717,
	ProCamera2D_GetOffsetY_mC162B9A0C0E5AC29E38E0362B90C2BC0790760BF,
	ProCamera2D_ApplyInfluence_m43513A9BCD9D112787F90B9B4A73FC54B9646139,
	ProCamera2D_ApplyInfluencesTimed_m0F84577A587E327BE92A5FEED25472B3C6E041EF,
	ProCamera2D_AddCameraTarget_m552E7B3D6D1A458676149A0342E16A6C739667B7,
	ProCamera2D_AddCameraTargets_m1CA93BA43B1A66E88E476A340D68E15C49188068,
	ProCamera2D_AddCameraTargets_mCB9F42F973AE69A8B732D11BA45D7FD44A6FA50A,
	ProCamera2D_GetCameraTarget_m399CB315177C0CE2494D62A48755D1A456E1AC86,
	ProCamera2D_RemoveCameraTarget_m1C694525D3A70E6F97E1A59887B7A355C91A2E03,
	ProCamera2D_RemoveAllCameraTargets_mD5F5BE4CCA76309DE4F6A782E2DB5903FA00BC50,
	ProCamera2D_AdjustCameraTargetInfluence_m7757AE12574D63C84499E9547740CBE748950655,
	ProCamera2D_AdjustCameraTargetInfluence_mC226EA51E5BF6167784251CCD0B3D55CE725F85D,
	ProCamera2D_TranslateCamera_m99DF53F92B82C8163DE45EF8240CBFBF03E2CDB0,
	ProCamera2D_MoveCameraInstantlyToPosition_m06A2D06E0F4DA15C8FECF6CF755FB185A69A225B,
	ProCamera2D_Reset_mDAAD96654DBE8EB0DDC246339A7CAC3AEEF1EA23,
	ProCamera2D_ResetMovement_m40DFC1CDFE2EA63297F835AF271D4B2401080EA2,
	ProCamera2D_ResetSize_m92A1C6E2F40AF16F35811CE5EDFC77DD7AA1B4CE,
	ProCamera2D_ResetStartSize_m72CE95AF9C0942F0C871A31761924CE2C5AE17D1,
	ProCamera2D_ResetExtensions_mB4505B15A396C9A4AA39922893FF9872EBF89AB8,
	ProCamera2D_CenterOnTargets_mE7E45BC1164B4D444E3BBBE3FD7A325739102FE1,
	ProCamera2D_UpdateScreenSize_m543B2AA7607FD74DEBE09DD1E362B1D404BEC36F,
	ProCamera2D_StopUpdateScreenSizeCoroutine_mD7B0DA6227C915775F4EC0D918DF26E3F6F10D50,
	ProCamera2D_CalculateScreenSize_mB667F894E2A01E771FC78CB901E0181DE8169CBE,
	ProCamera2D_Zoom_m1D404B5C3D895095CAD74F7D61EEAF0D2B258E89,
	ProCamera2D_DollyZoom_mF2A6A56F4F4F55279FF3415812F9955047D58297,
	ProCamera2D_Move_mC40528298173A335884C2B1973ED592DD7AA3D9A,
	ProCamera2D_GetYield_m310C0EC9FFE9070AF654303A92C4575FD451CF7F,
	ProCamera2D_ResetAxisFunctions_m056349AAD86AF9E1F15D8F0822914DEDFC34BDE1,
	ProCamera2D_GetTargetsWeightedMidPoint_m54B55BF392BE632FA772D77A9B30C11EB02407CA,
	ProCamera2D_ApplyInfluencesTimedRoutine_m5F232822A531C4863E450C4B19DC2332ABD5D275,
	ProCamera2D_ApplyInfluenceTimedRoutine_m339B6445B9B97C5FDB26978174E1EA7FD86E7BF5,
	ProCamera2D_AdjustTargetInfluenceRoutine_mB65FC60E9EEA23EA3DE62E6F5EF564A408EFB6C8,
	ProCamera2D_UpdateScreenSizeRoutine_m0C0D761B9242CD741B90025609F5E62614891246,
	ProCamera2D_DollyZoomRoutine_m8B1A1AB1EBD0D9DB0F3402C35AD38195F302B5E1,
	ProCamera2D_SetScreenSize_m88156B34D588B0975BDF321FDE8387FF9E6E7072,
	ProCamera2D_GetCameraDistanceForFOV_m2FCA916D4D6C57A1A44274D78A2277AC569C4703,
	ProCamera2D_AddPreMover_m3D9A3F718987333A6543726632F69893CAF07AE8,
	ProCamera2D_RemovePreMover_m5D452456F35310169F53AE35B7FDD48148433BDC,
	ProCamera2D_SortPreMovers_m95A250265DFCCC252B0FC062C888BC99B053FAB4,
	ProCamera2D_AddPositionDeltaChanger_m3682114F7AC0CA4B8994B2594E5C28F212278D55,
	ProCamera2D_RemovePositionDeltaChanger_mEC02A9B5820D99B7025BDD0F3F2D774BDBD85812,
	ProCamera2D_SortPositionDeltaChangers_mA0ED5B2CCA712425385F39116EE5F0B620E1E47A,
	ProCamera2D_AddPositionOverrider_m225362B7291383864FAB1753B9C262C90DA62EAB,
	ProCamera2D_RemovePositionOverrider_m9F05CDF1AAF04C47417F713B28F56DA2890618B9,
	ProCamera2D_SortPositionOverriders_m98E6233BFD31946D460DA61D93A8BB605F2A686C,
	ProCamera2D_AddSizeDeltaChanger_m68C80B84E8827876D48ABD57D8CCB419D5140E17,
	ProCamera2D_RemoveSizeDeltaChanger_mAECE9C00F658C19686B1141FD26FBBFBC9F50C12,
	ProCamera2D_SortSizeDeltaChangers_m948F5ADFEA4CCA107122A1BE151DF7C67FCA1CA6,
	ProCamera2D_AddSizeOverrider_m79B5E6F39A3148D5F77EDF20E60E20DFFF178129,
	ProCamera2D_RemoveSizeOverrider_m6F7E1DC6397A8F75E87B0E983E0A1DB9E421BF3D,
	ProCamera2D_SortSizeOverriders_m515A2E1EAF2DD0ADE1320C378B6974E72A5D43A9,
	ProCamera2D_AddPostMover_mB114FC26E88557D8ADFB5B131DFD33D61C3C47BE,
	ProCamera2D_RemovePostMover_mD4449D5EFB4C2A72B4B202338CDF6D864323367E,
	ProCamera2D_SortPostMovers_mCDAEB6673C18715D6650C442C149A08032AF5665,
	ProCamera2D_OnBeforeSerialize_m1201B865E9AC53A01EFA7EAB382B59734E33B4C9,
	ProCamera2D_OnAfterDeserialize_m8329B5E22CFDAE1E88A0D66D7EE5436C1C57DD1F,
	ProCamera2D__ctor_m8AFA40814F4ADEC1EE4B66DC06192D1BCF23AC0E,
	ProCamera2D__cctor_m0CBAE959112DF67D6D72146E6AEC2C89E8732CB6,
	U3CU3Ec__cctor_m2CACA806FBBF16DF2FB8F6A8828F7263B626D1BA,
	U3CU3Ec__ctor_mD315A5D40510C4F060D0906D20F1573E4F17D902,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_0_m98C4A6EA221600DEA0EB0BFC56FC39E30FC645E2,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_1_m505F97B3E36295C2F8D91FF7B2A489957F716D95,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_2_m94DED5B08884FD9DAA54F16106366464B8AE9C37,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_3_m34B944A7A62CAA4608F1280C21047F380C264A2C,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_4_mAFBB04EA8528969D31E755801C9AB8A9C111B315,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_5_mBC6CC32C88B641B37D48B1859E1C266DF02460C4,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_6_m61821683F61E356BF8C6C269651CCA497DE6B87C,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_7_m0052A98D089C5B3875D03CC5ECE82BD37AB87D20,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_8_m98512A0D74D77EC23469A7FC6966E3B7E90BAAE9,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_9_m709F5BEF2F0A9AFA5AB3C247D96932B28258D2AB,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_10_m96598B4C7DC04B826B3A38D8C19950D4B69249E0,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_11_m8CA2966F66F4A767B38164CE452742B4A4229D4D,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_12_mDDD87D483B6B6AFFDC53E0835347D9158751CF5A,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_13_mDEF3D7670DF97A8BA3604D9165B65C616E19B4F4,
	U3CU3Ec_U3CResetAxisFunctionsU3Eb__131_14_mDEF0C08680FAEB5187CDE8257960EED9CF57163C,
	U3CU3Ec_U3CSortPreMoversU3Eb__142_0_mD9EEE20A043C09922C819C512AEFC688D2BAC5EA,
	U3CU3Ec_U3CSortPositionDeltaChangersU3Eb__145_0_mC93C34687515ABA35A0D290D89D7A2500426C09D,
	U3CU3Ec_U3CSortPositionOverridersU3Eb__148_0_mBCBF1157F9E8EF24C994B246C0D04F8F814A0FD1,
	U3CU3Ec_U3CSortSizeDeltaChangersU3Eb__151_0_m1D3264ABC981E11D4D3A09DEF14DED70888AFA71,
	U3CU3Ec_U3CSortSizeOverridersU3Eb__154_0_m6D5A8C79EA1D342DA47F6571AC7D4912321136E4,
	U3CU3Ec_U3CSortPostMoversU3Eb__157_0_m9A1567248619630583D4F099F51B78340EC4507E,
	U3CAdjustTargetInfluenceRoutineU3Ed__135__ctor_m409A5B5ADD805D2BDE208CF8E4EDA32EEFA74CD5,
	U3CAdjustTargetInfluenceRoutineU3Ed__135_System_IDisposable_Dispose_m104C961491C839A3C9776417DE0F123408AA1B44,
	U3CAdjustTargetInfluenceRoutineU3Ed__135_MoveNext_m69DD891BF32D680F323C30BB1AB662ED99782801,
	U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m3E54B43550C7989511D73C6289AF00E4539F8CB9,
	U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_Reset_mCC25D8F322985E1FF8D8B0211383D93A0B9951DE,
	U3CAdjustTargetInfluenceRoutineU3Ed__135_System_Collections_IEnumerator_get_Current_mC8453BEE2628B21F321FF2017146FAFF03FDE2DB,
	U3CApplyInfluenceTimedRoutineU3Ed__134__ctor_m51120204F59658F8C42D1A7DA60F5FA0983504C8,
	U3CApplyInfluenceTimedRoutineU3Ed__134_System_IDisposable_Dispose_m3BD00051FBAE7E40849BA912A5B22A1EFE8B6212,
	U3CApplyInfluenceTimedRoutineU3Ed__134_MoveNext_mE5593442539FBC5813B0B161DFF09FCEBB3463BA,
	U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD448A87768163EA43D5653178F0DBDC60006F315,
	U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_Reset_m4C8D8FB13B284CA12B2268B5E6DFEF45FFBA299E,
	U3CApplyInfluenceTimedRoutineU3Ed__134_System_Collections_IEnumerator_get_Current_mB679A88BC7061F08573F801056767C37EE6A14CF,
	U3CApplyInfluencesTimedRoutineU3Ed__133__ctor_mA8B4ECFBF07C3573B979B4B9B37BA42105F26EA3,
	U3CApplyInfluencesTimedRoutineU3Ed__133_System_IDisposable_Dispose_m686AF8919E826C8AA67FD4733F65F40C49F4A442,
	U3CApplyInfluencesTimedRoutineU3Ed__133_MoveNext_m91FA6C67DB6E6C3EC63E0E3A64AD626B7DAAC84B,
	U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0194873426BA7960743E862A7F4C57767100281,
	U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_Reset_mB6EB8C8AF011198498FF41032C55A4FC25ECB51E,
	U3CApplyInfluencesTimedRoutineU3Ed__133_System_Collections_IEnumerator_get_Current_mBDE02E8A3BD380C22F8B33A3210D60E7ECFCE5E1,
	U3CDollyZoomRoutineU3Ed__137__ctor_mF86B1AC195636774504B0E7AFE50740934B9E3F1,
	U3CDollyZoomRoutineU3Ed__137_System_IDisposable_Dispose_m6C6308A5A215173B7666278C8383CB7B701BBB64,
	U3CDollyZoomRoutineU3Ed__137_MoveNext_m3BA1B2172EA265671591C670D6A2D6925BEB90D4,
	U3CDollyZoomRoutineU3Ed__137_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB7668F67DDEBD720BF1FD5ECB5741AE75D0C5B2E,
	U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_Reset_m8F61EFF21AD9780E02E38B110807DA2770E921D7,
	U3CDollyZoomRoutineU3Ed__137_System_Collections_IEnumerator_get_Current_mA5B7EE658EFDFE06CBF0CE6A6ADEA7E0C001ED8F,
	U3CUpdateScreenSizeRoutineU3Ed__136__ctor_m0C35F21342157299F0E20519FE7B8601DB070C6B,
	U3CUpdateScreenSizeRoutineU3Ed__136_System_IDisposable_Dispose_m3313BBBBC7B2FDA68B024FB518C4C523C50D3402,
	U3CUpdateScreenSizeRoutineU3Ed__136_MoveNext_m8FF50A132F993A1E88CEBC9751FDA587ACCC19AF,
	U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m63DFE9D92CC3D757C5DC39EC31DB292CE5EE5EF8,
	U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_Reset_mA0B540206D18E1C21A1314421A16B808A4B8B0D0,
	U3CUpdateScreenSizeRoutineU3Ed__136_System_Collections_IEnumerator_get_Current_mD34C3430382F0730625E618C5B542235566C7D06,
};
extern void IntPoint__ctor_m9C4A5310BCBB59254735055F9344A5A6B0CCB7D5_AdjustorThunk (void);
extern void IntPoint_IsEqual_m35427735D5A80E733404C43AD10403BE41DB8328_AdjustorThunk (void);
extern void IntPoint_ToString_m8C85FBFAF05E2190667CF6EA9202BE3EA92A09FC_AdjustorThunk (void);
extern void IntPoint_Equals_m74742DDABE818D5806161705E5C890FC6CB1B9FF_AdjustorThunk (void);
extern void IntPoint_GetHashCode_mFB4BA17636681D2D12556AE92D86A7461171CB2D_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[5] = 
{
	{ 0x060001E3, IntPoint__ctor_m9C4A5310BCBB59254735055F9344A5A6B0CCB7D5_AdjustorThunk },
	{ 0x060001E4, IntPoint_IsEqual_m35427735D5A80E733404C43AD10403BE41DB8328_AdjustorThunk },
	{ 0x060001E5, IntPoint_ToString_m8C85FBFAF05E2190667CF6EA9202BE3EA92A09FC_AdjustorThunk },
	{ 0x060001E6, IntPoint_Equals_m74742DDABE818D5806161705E5C890FC6CB1B9FF_AdjustorThunk },
	{ 0x060001E7, IntPoint_GetHashCode_mFB4BA17636681D2D12556AE92D86A7461171CB2D_AdjustorThunk },
};
static const int32_t s_InvokerIndices[884] = 
{
	10465,
	7120,
	6992,
	5806,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	10455,
	7120,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	7120,
	7120,
	7120,
	5703,
	7120,
	7120,
	7120,
	6992,
	5267,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	3363,
	6957,
	7120,
	2686,
	2686,
	2686,
	2686,
	10455,
	7120,
	5268,
	5268,
	5268,
	5268,
	5268,
	5268,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5851,
	7111,
	7120,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7120,
	10054,
	7739,
	8953,
	7717,
	7717,
	10051,
	5009,
	1967,
	1689,
	5178,
	7015,
	6888,
	5806,
	5379,
	7120,
	401,
	2723,
	1376,
	10455,
	7120,
	5268,
	5268,
	2764,
	5268,
	5268,
	2764,
	5268,
	5268,
	2764,
	7120,
	9123,
	9834,
	9123,
	9313,
	9960,
	8914,
	9624,
	9132,
	10261,
	9525,
	9133,
	10269,
	9539,
	9128,
	10076,
	9426,
	9124,
	9852,
	9207,
	9127,
	10054,
	8721,
	9127,
	10054,
	8736,
	9127,
	9127,
	9127,
	9127,
	9127,
	9127,
	0,
	8963,
	8963,
	8963,
	8963,
	8963,
	8963,
	10054,
	8731,
	10054,
	8745,
	10054,
	8752,
	10054,
	8755,
	10054,
	8741,
	10054,
	8724,
	0,
	9630,
	9630,
	9630,
	9630,
	9630,
	9630,
	10293,
	10455,
	9127,
	9649,
	10110,
	9578,
	9942,
	10293,
	10293,
	3410,
	10455,
	8313,
	7802,
	7802,
	8434,
	7929,
	8434,
	7929,
	7512,
	8553,
	9524,
	10269,
	9457,
	7429,
	7579,
	7120,
	7120,
	2765,
	6957,
	5774,
	1155,
	7120,
	10455,
	7120,
	7120,
	6887,
	7120,
	7120,
	2765,
	6957,
	5774,
	2726,
	6957,
	5774,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	152,
	5806,
	6992,
	2681,
	6992,
	7120,
	2795,
	7120,
	10455,
	10455,
	7120,
	5263,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	6957,
	5703,
	6887,
	10440,
	7043,
	5851,
	7043,
	5851,
	7043,
	5851,
	7120,
	6992,
	7120,
	7120,
	2726,
	6957,
	5774,
	5262,
	6992,
	7861,
	9332,
	7861,
	5703,
	7120,
	317,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	5851,
	6957,
	5774,
	7120,
	6992,
	5851,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	2765,
	6957,
	5774,
	7120,
	10455,
	7120,
	7120,
	2765,
	6957,
	5774,
	7120,
	10455,
	7120,
	7120,
	2765,
	6957,
	5774,
	7120,
	10455,
	6991,
	5805,
	7120,
	7120,
	7120,
	2765,
	6957,
	5774,
	2726,
	6957,
	5774,
	7120,
	10455,
	7120,
	7120,
	6992,
	7120,
	7120,
	5851,
	6957,
	5774,
	5851,
	7120,
	7120,
	5851,
	7120,
	5851,
	7120,
	7120,
	7120,
	4383,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	5851,
	6957,
	5774,
	7120,
	7120,
	1775,
	1591,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	6957,
	5774,
	7043,
	5851,
	7043,
	7120,
	7120,
	2765,
	6957,
	5774,
	7120,
	7043,
	5265,
	7120,
	10455,
	7120,
	7120,
	7120,
	5851,
	6957,
	5774,
	5851,
	7120,
	10455,
	7120,
	7120,
	5851,
	6957,
	5774,
	7120,
	707,
	5806,
	5181,
	5851,
	5851,
	5379,
	1716,
	7120,
	10455,
	6887,
	5703,
	6887,
	5703,
	7120,
	7120,
	5851,
	6957,
	5774,
	3349,
	3328,
	3328,
	3349,
	3360,
	7120,
	7120,
	10455,
	3113,
	4233,
	6992,
	4233,
	6957,
	10455,
	7120,
	5806,
	7120,
	7120,
	6957,
	6957,
	6992,
	7043,
	5851,
	7120,
	7120,
	7120,
	2765,
	6957,
	5774,
	2726,
	6957,
	5774,
	7120,
	5009,
	1821,
	1870,
	7120,
	58,
	5806,
	5805,
	5181,
	5264,
	6992,
	3349,
	1110,
	620,
	7120,
	7120,
	4261,
	7120,
	4261,
	7120,
	4261,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	10420,
	10397,
	7120,
	7120,
	7120,
	157,
	5774,
	5806,
	5806,
	7120,
	5806,
	5806,
	5774,
	5851,
	604,
	5913,
	604,
	2686,
	7120,
	1134,
	1150,
	5184,
	296,
	5184,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	2726,
	6957,
	5774,
	7120,
	7120,
	10455,
	10420,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	387,
	313,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	2726,
	6957,
	5774,
	7120,
	7120,
	7120,
	10455,
	6992,
	7120,
	3363,
	7120,
	3410,
	2688,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	5851,
	6957,
	5774,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	3363,
	7120,
	6887,
	5703,
	6887,
	7120,
	7120,
	7120,
	2765,
	6957,
	5774,
	7120,
	5181,
	7120,
	7120,
	6992,
	6992,
	2795,
	7120,
	10455,
	7120,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	6992,
	6992,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	10455,
	7120,
	7120,
	7120,
	6992,
	6992,
	5851,
	7120,
	10455,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	10420,
	10397,
	6887,
	7017,
	5831,
	7109,
	5913,
	7111,
	5915,
	7109,
	5913,
	7109,
	5913,
	7111,
	5915,
	7111,
	5915,
	7111,
	5915,
	7043,
	5851,
	7111,
	5915,
	7111,
	7120,
	7120,
	7120,
	7120,
	7120,
	7043,
	7043,
	5913,
	2682,
	608,
	706,
	5806,
	5181,
	3370,
	5851,
	1141,
	1141,
	5913,
	5913,
	1766,
	7120,
	7120,
	5913,
	7120,
	7120,
	1950,
	7120,
	7120,
	1950,
	1950,
	5851,
	6992,
	7120,
	5375,
	2682,
	2700,
	607,
	1645,
	1645,
	5851,
	2726,
	5806,
	5806,
	7120,
	5806,
	5806,
	7120,
	5806,
	5806,
	7120,
	5806,
	5806,
	7120,
	5806,
	5806,
	7120,
	5806,
	5806,
	7120,
	7120,
	7120,
	7120,
	10455,
	10455,
	7120,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	5268,
	5268,
	5268,
	2764,
	1715,
	4927,
	4927,
	4927,
	4927,
	4927,
	4927,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x060000BB, { 0, 4 } },
};
extern const uint32_t g_rgctx_T_t43FE7B12355FD0C61487669182E159B15440469B;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t43FE7B12355FD0C61487669182E159B15440469B_ICollection_get_Count_m67A35400CF1A7DA9EE8BD9064E719999B7B12F68;
extern const uint32_t g_rgctx_Action_3_tB405D0A7BD918A261387E4D118DC5F45FAB2412C;
extern const uint32_t g_rgctx_Action_3_Invoke_m8BAA4966B2775AF907C6794350BCC240138485FB;
static const Il2CppRGCTXDefinition s_rgctxValues[4] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t43FE7B12355FD0C61487669182E159B15440469B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t43FE7B12355FD0C61487669182E159B15440469B_ICollection_get_Count_m67A35400CF1A7DA9EE8BD9064E719999B7B12F68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_tB405D0A7BD918A261387E4D118DC5F45FAB2412C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3_Invoke_m8BAA4966B2775AF907C6794350BCC240138485FB },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_ProCamera2D_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_ProCamera2D_Runtime_CodeGenModule = 
{
	"ProCamera2D.Runtime.dll",
	884,
	s_methodPointers,
	5,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	4,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
