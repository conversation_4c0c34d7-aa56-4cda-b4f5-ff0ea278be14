﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif





struct Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79;
struct Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C;
struct Action_1_tB03D82616088D202ABD23F934CC2976A2ED530B4;
struct Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A;
struct Action_3_t4730167C8E7EB19F1E0034580790A915D549F6CB;
struct Dictionary_2_t2306F810ADE46E708DBCF5BFFD57ED345A61D8CA;
struct Dictionary_2_t64EF50E914B0358E99C591AF0C1660B4DF1F1317;
struct Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83;
struct HashSet_1_tEFC6605F7DE53F71946C33FD371E53C3100F2178;
struct IDictionary_2_t79D4ADB15B238AC117DF72982FEA3C42EF5AFA19;
struct IReadOnlyDictionary_2_t50D88EB60993FA9259A95B21D3156E7D87374052;
struct IReadOnlyDictionary_2_t8FD5C7F0C22A371C71196C9A42D80E0E47EAC1C8;
struct List_1_tDB72209F35D56F62A287633F9450978E90B90987;
struct List_1_t5E2EB19AA6B9CFA733A67F57AF74DCBE981C3376;
struct List_1_tFD4080EF47ACD067F2E4AB57C9E2E8E64CA953E3;
struct List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD;
struct MinimumBinaryHeap_1_tB7A85B4D870F0107AF6031545D6054927A1CB7AD;
struct TaskCompletionSource_1_tB4EF81F69CCF7C4F0D956F9B26127C0634A24A37;
struct Task_1_t1975707D1E76FF508132D6A04F85270E8DAF8A9B;
struct Task_1_t3D7638C82ED289AF156EDBAE76842D8DF4C4A9E0;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct ConfigurationEntryU5BU5D_t52C052ED69E59F867806855793A30109BBA9EEF3;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct PlayerLoopSystemU5BU5D_t07C04E53AAC3CDA17603E8BA1B41D7E1AC083C6D;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct ConsentDebugSettings_t21BCD70B1E4DB762E04807E88E78285CC51370C6;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct ExternalUserIdProperty_t8B820D8AF448EA2F129B1FFD7D4FD1FD840606E9;
struct IAdErrorClient_t1070A9DCB8BBE68B67D9ECBC19949CDF415AD2BF;
struct IAdInspectorErrorClient_t014D7BDEAEADC7BFF5FF32AB1E464B5554B0CF36;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct IAsyncStateMachine_t0680C7F905C553076B552D5A1A6E39E2F0F36AA2;
struct IConfigurationLoader_tC5D49E531BA88F2FA7772F0EF305AF524FF0AEE4;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IJsonSerializer_t5455D9405C5FC13B94B8ECE986906B51ABE30329;
struct ILoadAdErrorClient_t82CFD9A1EFB235DD188F81112DF3EAE96806A8D9;
struct ITimeProvider_t6CB8C4316E58E0ABEFDEAAF4FA47B90DE03CD2E6;
struct IUnityServices_t49EC0D24FF18A25F42A1FF0B56962B5E7AD1E9A9;
struct IUserIdentifierProvider_t37955FEADD673B2F4333F6D756F9F9BEC3486E41;
struct MaxAdContentRating_t8F4AA13668EB7FF7A5F6F369845B5280BBE0BF17;
struct MethodInfo_t;
struct NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct StreamingAssetsConfigurationLoader_tDB162F80417A64081A3F8D509A29D6A01A25D712;
struct String_t;
struct TaskScheduler_t3F0550EBEF7C41F74EC8C08FF4BED0D8CE66006E;
struct Type_t;
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977;
struct VideoOptions_t913B2174C33E206DD78043A7663F81DADF2FA042;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct IdentityTokenChanged_tE8CB0DAB5F6E640A847803F582E6CB6237742395;
struct SessionStateChanged_t1180FB66E702B635CAD9316DC661D931277B2A0C;
struct GADUConsentFormLoadCompletionHandler_tCEDA61B1731AA6292F53FF26EAEFD5960A4E3254;
struct GADUConsentFormPresentCompletionHandler_t7BE8E6E55FBFBD76CBAA12588EEBE1B796442E25;
struct GADUConsentInfoUpdateCallback_t3F5F5E587ACC5BAF163FADD1269401A40163AEDB;
struct UpdateFunction_t1C48B7EECBE47AC123A9D9D7D9D2A9EE951C56C4;
struct UpdatedEventHandler_tB0D5A5BA322FE093894992C29DCF51E7E12579C4;
struct CreationDelegate_tFBFDFC77F9F874CD9C3B966D1AD564BF5315E405;

struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;
struct PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F_marshaled_com;
struct PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F_marshaled_pinvoke;
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t77C22CB09929EB44C5402FD6400E944C42107E2E 
{
};
struct U3CModuleU3E_tA4399B639778665FAD9E820A92BC542249E0CF67 
{
};
struct U3CModuleU3E_t742906D1ECE8A5FA78682FD3E1F8204B25D019FA 
{
};
struct U3CModuleU3E_t2E436F02646AE15477D3A3C17B521F580FFA2360 
{
};
struct U3CModuleU3E_tDA285F13E9413BF3B79A99D6E310BE9AF3444EEB 
{
};
struct U3CModuleU3E_t5CFA55679A8E9D2525AFBC9C50BEC051BEA21310 
{
};
struct U3CModuleU3E_t4052AFC804778757171A467E5C89680322791B94 
{
};
struct U3CModuleU3E_t0267161C6C027BA3CDC8B3933902448EE205B45B 
{
};
struct U3CModuleU3E_tC6F3BE26E8DBB3B8108F8360B026950D37DAF754 
{
};
struct U3CModuleU3E_tD7D5A4C49FDBA2AFB002462DF64609318A53BF85 
{
};
struct U3CModuleU3E_t1A06A280C3A3120637CE5C7405E81F324A763874 
{
};
struct U3CModuleU3E_tA8E35B180310D6046FB6F4D10E1B4A172B9C0215 
{
};
struct U3CModuleU3E_tC445F3DAD73003CEBF9FF7BF1C109B3025166895 
{
};
struct U3CModuleU3E_t2C7BF608494A5C8FB8C8C4D318FB27BCF6CE322A 
{
};
struct U3CModuleU3E_t33B45E84B8820001E6F644E31DCC9BAEE6DCE23F 
{
};
struct U3CModuleU3E_t2F9091E403B25A5364AE8A6B2C249E31D405E3F4 
{
};
struct U3CModuleU3E_t4BC86AB59C63F2CC6EB51BE560C15CFCAE821BC7 
{
};
struct U3CModuleU3E_t5E8190EE43F4DF5D80E8A6651A0469A8FD445F94 
{
};
struct U3CModuleU3E_tD1F0B9705D5594A42609E77459719A6C0DB9DD53 
{
};
struct U3CModuleU3E_t6B4A7D64487421A1C7A9ACB5578F8A35510E2A0C 
{
};
struct U3CModuleU3E_tA3EED36B70F256012F5BB9BC014BD75FF7BF2C66 
{
};
struct U3CModuleU3E_tAEEA45345F4FE6CCC29A863B181BFCCC6EDBCAC7 
{
};
struct AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D  : public RuntimeObject
{
	HashSet_1_tEFC6605F7DE53F71946C33FD371E53C3100F2178* ___Keywords;
	Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83* ___Extras;
	List_1_t5E2EB19AA6B9CFA733A67F57AF74DCBE981C3376* ___MediationExtras;
};
struct AnalyticsCommon_t9A0DD5EB3A8E861A4481AA17C2105396E9C4E34C  : public RuntimeObject
{
};
struct AnalyticsSessionInfo_tDE8F7A9E13EF9723E2D975F76E916753DA61AD76  : public RuntimeObject
{
};
struct AppEvent_tDE8A307A4C93E207A0450B2C5FAB251C8FB91C56  : public RuntimeObject
{
	String_t* ___U3CNameU3Ek__BackingField;
	String_t* ___U3CDataU3Ek__BackingField;
};
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA  : public RuntimeObject
{
};
struct CloudProjectId_t14C34E1DB1EB955BA21ECB1126EACEC9818A68F0  : public RuntimeObject
{
};
struct ConfigurationCollectionHelper_t117485823F5BCB9D4E56EDFEF1652CF5C9968BFC  : public RuntimeObject
{
};
struct ConfigurationElement_tAE3EE71C256825472831FFBB7F491275DFAF089E  : public RuntimeObject
{
};
struct ConfigurationEntry_t9DC70834AC631CC28DFF4C96C859E4106EF5E75E  : public RuntimeObject
{
	String_t* ___m_Value;
	bool ___m_IsReadOnly;
};
struct ConfigurationPropertyCollection_t1DEB95D3283BB11A46B862E9D13710ED698B6C93  : public RuntimeObject
{
};
struct ConfigurationSectionGroup_tE7948C2D31B193F4BA8828947ED3094B952C7863  : public RuntimeObject
{
};
struct ConfigurationUtils_tF385B43626BC064FCBAA5093770ABE0C3D291DAC  : public RuntimeObject
{
};
struct ConsentRequestParameters_t34C1E8C04ED21B543DFE57708C303AABEA447516  : public RuntimeObject
{
	bool ___TagForUnderAgeOfConsent;
	ConsentDebugSettings_t21BCD70B1E4DB762E04807E88E78285CC51370C6* ___ConsentDebugSettings;
};
struct ContinuousEvent_t71122F6F65BF7EA8490EA664A55D5C03790CB6CF  : public RuntimeObject
{
};
struct DiagnosticsFactory_tD2C64CDF0CC356965DB1993359515DC82D715CF2  : public RuntimeObject
{
	RuntimeObject* ___U3CCommonTagsU3Ek__BackingField;
};
struct Environments_tC4B2F50A927F4B9798E0EA1415765D81DC7C0A00  : public RuntimeObject
{
	String_t* ___m_Current;
};
struct EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377  : public RuntimeObject
{
};
struct ExternalUserId_t9C3CCC17970C998BDD5330AE110097669EBA0018  : public RuntimeObject
{
};
struct ExternalUserIdProperty_t8B820D8AF448EA2F129B1FFD7D4FD1FD840606E9  : public RuntimeObject
{
	Action_1_t3CB5D1A819C3ED3F99E9E39F890F18633253949A* ___UserIdChanged;
	String_t* ___m_UserId;
};
struct Externs_t8C1E08109EC604AF48C9B439F139F4B64E537504  : public RuntimeObject
{
};
struct FormError_t925BBA051FDAC8CC3DECB9E5511864E8ED383026  : public RuntimeObject
{
	int32_t ___U3CErrorCodeU3Ek__BackingField;
	String_t* ___U3CMessageU3Ek__BackingField;
};
struct ImageConversion_tD7B6C2CDCD3E1078708B1668B9695914A502C252  : public RuntimeObject
{
};
struct InitializationOptions_t51AA79A729CADA6922543A7A47F1E87A09DBD17A  : public RuntimeObject
{
	RuntimeObject* ___U3CValuesU3Ek__BackingField;
};
struct InstallationId_tA5712B394172DB4DBA3B5F9A67D5522D37E2B873  : public RuntimeObject
{
	String_t* ___Identifier;
	RuntimeObject* ___UnityAdsIdentifierProvider;
	RuntimeObject* ___UnityAnalyticsIdentifierProvider;
};
struct JsonUtility_t731013D97E03B7EDAE6186D6D6826A53B85F7197  : public RuntimeObject
{
};
struct MaxAdContentRating_t8F4AA13668EB7FF7A5F6F369845B5280BBE0BF17  : public RuntimeObject
{
	String_t* ___U3CValueU3Ek__BackingField;
};
struct MediationExtras_t390586958F7ED4B158AD5AD18F58A86E9E7B621E  : public RuntimeObject
{
	Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83* ___U3CExtrasU3Ek__BackingField;
};
struct MetricsFactory_tFED08C34B8CB569B801796787E82F2818606FA05  : public RuntimeObject
{
	RuntimeObject* ___U3CCommonTagsU3Ek__BackingField;
};
struct MinimumBinaryHeap_tA55F58E6B1D3F482C11529BAB4C1D56F5BF745FE  : public RuntimeObject
{
};
struct NSUserDefaults_t518A0A24332EC9EC2361F2C04C06166E301FF25C  : public RuntimeObject
{
};
struct ProjectConfiguration_t653308465893F727E0FA58993E3D000D74630CC4  : public RuntimeObject
{
	RuntimeObject* ___m_ConfigValues;
	RuntimeObject* ___U3CSerializerU3Ek__BackingField;
};
struct RemoteConfigSettingsHelper_t29B2673892F8181388B45FFEEE354B3773629588  : public RuntimeObject
{
};
struct RemoteSettings_t9DFFC747AB3E7A39DF4527F245B529A407427250  : public RuntimeObject
{
};
struct ScheduledInvocationComparer_t785D4501646D05413E8F88FAECD14D9902742CC2  : public RuntimeObject
{
};
struct ServerSideVerificationOptions_tCB0CF0403C3E4DDA0E47F9594BE01D214B4673C3  : public RuntimeObject
{
	String_t* ___UserId;
	String_t* ___CustomData;
};
struct StreamingAssetsConfigurationLoader_tDB162F80417A64081A3F8D509A29D6A01A25D712  : public RuntimeObject
{
	RuntimeObject* ___m_Serializer;
};
struct StreamingAssetsUtils_tA32B07F0AAC9291ED90B0B55281F247B4A57911F  : public RuntimeObject
{
};
struct UmpClientFactory_t419C0174AA6B63B15489EF309508540C1D78CF18  : public RuntimeObject
{
};
struct UnityAdsIdentifier_t8D3A8D6DB9A1FB469E2E7F3D58B30C3D0D71DA56  : public RuntimeObject
{
};
struct UnityAnalyticsIdentifier_tAB8DD85C666D878FC108C324E10DF6528B5BDA5D  : public RuntimeObject
{
};
struct UnityServices_t4749F0FB88F542DAC1E287ACFFAB146EF9759317  : public RuntimeObject
{
};
struct UnityServicesBuilder_tA733917766742A74C8F796EEA334C8CA22F2C8DB  : public RuntimeObject
{
};
struct UnityThreadUtils_tCC1D0915CC9C90AEB5B27F2A9D791544FAC0DEBA  : public RuntimeObject
{
};
struct UnityThreadUtilsInternal_t80A0011DD0CD9318DC38466ABEEAE4AD8B07B2AC  : public RuntimeObject
{
};
struct UnityWebRequestAssetBundle_t53F46F7F69F894F1B22F3FBBD2565D7B67A5C6A8  : public RuntimeObject
{
};
struct UnityWebRequestTexture_t7C1794874F2663ED616906719C4A306A46B35C01  : public RuntimeObject
{
};
struct UtcTimeProvider_t45FE04D3C9372C9089808CBF396C87E62AA26743  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct VideoOptions_t913B2174C33E206DD78043A7663F81DADF2FA042  : public RuntimeObject
{
	bool ___ClickToExpandRequested;
	bool ___CustomControlsRequested;
	bool ___StartMuted;
};
struct __Il2CppComDelegate_tD0DD2BBA6AC8F151D32B6DFD02F6BDA339F8DC4D  : public Il2CppComObject
{
};
struct U3CInvokeInUpdateU3Ec__AnonStorey0_tF0F601D18E2E6B9DBB319D500D2C05D0BDC31C67  : public RuntimeObject
{
	UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* ___eventParam;
};
struct Nullable_1_t78F453FADB4A9F50F267A4E349019C34410D1A01 
{
	bool ___hasValue;
	bool ___value;
};
struct TaskAwaiter_1_t254638BB1FAD695D9A9542E098A189D438A000F6 
{
	Task_1_t3D7638C82ED289AF156EDBAE76842D8DF4C4A9E0* ___m_task;
};
struct AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	RuntimeObject* ___U3CAdErrorClientU3Ek__BackingField;
};
struct AdInspectorErrorClientEventArgs_tA430D6E41FFCB38B31390D9EA1860D279FA4EBFA  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	RuntimeObject* ___U3CAdErrorClientU3Ek__BackingField;
};
struct AdManagerAdRequest_t8DE61BA8D8C0D7B2A3D39C43B7A8101EEDE80BFC  : public AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D
{
	String_t* ___PublisherProvidedId;
	Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83* ___CustomTargeting;
	HashSet_1_tEFC6605F7DE53F71946C33FD371E53C3100F2178* ___CategoryExclusions;
};
struct AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF 
{
	RuntimeObject* ___m_stateMachine;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___m_defaultContextAction;
};
struct AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF_marshaled_pinvoke
{
	RuntimeObject* ___m_stateMachine;
	Il2CppMethodPointer ___m_defaultContextAction;
};
struct AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF_marshaled_com
{
	RuntimeObject* ___m_stateMachine;
	Il2CppMethodPointer ___m_defaultContextAction;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct ConfigurationCollectionAttribute_t1D7DBAAB4908B6B8F26EA1C66106A67BDE949558  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct ConfigurationElementCollection_t56E8398661A85A59616301BADF13026FB1492606  : public ConfigurationElement_tAE3EE71C256825472831FFBB7F491275DFAF089E
{
};
struct ConfigurationSection_t0BC609F0151B160A4FAB8226679B62AF22539C3E  : public ConfigurationElement_tAE3EE71C256825472831FFBB7F491275DFAF089E
{
};
struct DataContractAttribute_tD065D7D14CC8AA548815166AB8B8210D1B3C699F  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	bool ___isReference;
};
struct DataMemberAttribute_t8AE446BE9032B9BC8E7B2EDC785F5C6FA0E5BB73  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	String_t* ___name;
	int32_t ___order;
	bool ___isRequired;
	bool ___emitDefaultValue;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D 
{
	uint64_t ____dateData;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct EnumMemberAttribute_t65B5E85E642C96791DD6AE5EAD0276350954126F  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	String_t* ___value;
};
struct IgnoreDataMemberAttribute_tC1AC455123E5BF654B22396F3E5CB1C514D86777  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	RuntimeObject* ___U3CLoadAdErrorClientU3Ek__BackingField;
};
struct Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	String_t* ___Type;
	double ___Amount;
};
struct SerializableProjectConfiguration_tBAE4D3A66EC38C1869E294396DB79F127B8F58EE 
{
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___Keys;
	ConfigurationEntryU5BU5D_t52C052ED69E59F867806855793A30109BBA9EEF3* ___Values;
};
struct SerializableProjectConfiguration_tBAE4D3A66EC38C1869E294396DB79F127B8F58EE_marshaled_pinvoke
{
	char** ___Keys;
	ConfigurationEntryU5BU5D_t52C052ED69E59F867806855793A30109BBA9EEF3* ___Values;
};
struct SerializableProjectConfiguration_tBAE4D3A66EC38C1869E294396DB79F127B8F58EE_marshaled_com
{
	Il2CppChar** ___Keys;
	ConfigurationEntryU5BU5D_t52C052ED69E59F867806855793A30109BBA9EEF3* ___Values;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct AsyncTaskMethodBuilder_1_t317FE70D757AFEA76E7E60CE379D5D15652D2F0C 
{
	AsyncMethodBuilderCore_tD5ABB3A2536319A3345B32A5481E37E23DD8CEDF ___m_coreState;
	Task_1_t1975707D1E76FF508132D6A04F85270E8DAF8A9B* ___m_task;
};
struct AdChoicesPlacement_tE8327291A65D17FA4A1D5757F44193E1E95283DC 
{
	int32_t ___value__;
};
struct AdPosition_tC5663BBEC4D9F57BB911ABB0349BE51F0747D1A3 
{
	int32_t ___value__;
};
struct AdapterState_tC2EF5D755C69DB0311E40355C133DA4BE5B2E5FA 
{
	int32_t ___value__;
};
struct Allocator_t996642592271AAD9EE688F142741D512C07B5824 
{
	int32_t ___value__;
};
struct AnalyticsSessionState_t45365B3C4890CEFCFDBD5438073BE8626CFA120E 
{
	int32_t ___value__;
};
struct AppState_tBC2F72F60F51FE0CB94604F23B86874654E551A5 
{
	int32_t ___value__;
};
struct ConfigurationSaveMode_t7D0C554DA73F5D44E7DE4950E3F9004C20C71766 
{
	int32_t ___value__;
};
struct ConsentFormClient_tBEC2D325BC9255F78BF2FB9DD0EBFA8DC8A0B7E1  : public RuntimeObject
{
	intptr_t ____consentForm;
	intptr_t ____consentFormClientPtr;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____loadCompleteAction;
	Action_1_tB03D82616088D202ABD23F934CC2976A2ED530B4* ____loadFailedAction;
	Action_1_tB03D82616088D202ABD23F934CC2976A2ED530B4* ____consentFormDismissedAction;
};
struct ConsentInformationClient_tA72DF61A2E613C9DC9DE3111E8D5AC7672A23FB1  : public RuntimeObject
{
	intptr_t ____consentInformationClientPtr;
	intptr_t ____consentInformationPtr;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____consentInfoUpdateSuccessAction;
	Action_1_tB03D82616088D202ABD23F934CC2976A2ED530B4* ____consentInfoUpdateFailureAction;
};
struct DebugGeography_tE764B93413E15CC10191FEAFB27703EB137D4722 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
};
struct DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB_marshaled_com
{
	intptr_t ___m_Ptr;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct IgnoreSection_t43A7C33C0083D18639AA3CC3D75DD93FCF1C5D97  : public ConfigurationSection_t0BC609F0151B160A4FAB8226679B62AF22539C3E
{
};
struct MediaAspectRatio_tEE24DB3D35174B53152C3505E685D45CAB719FC1 
{
	int32_t ___value__;
};
struct NativeTemplateFontStyle_tCD47FC5AA8F9B31B62109C92B0397AC287A433D2 
{
	int32_t ___value__;
};
struct NativeTemplateStyle_t98BEC9523A240D4458CEEDBBF9A7B0634C62DC89  : public RuntimeObject
{
	String_t* ___TemplateId;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___MainBackgroundColor;
	NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF* ___PrimaryText;
	NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF* ___SecondaryText;
	NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF* ___TertiaryText;
	NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF* ___CallToActionText;
};
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7  : public RuntimeObject
{
	intptr_t ___m_Ptr;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___m_Corners;
};
struct NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_marshaled_com
{
	intptr_t ___m_Ptr;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* ___m_Corners;
};
struct NavMeshPathStatus_t1BCDA84284F9D364B8BB3BDF741D50D38932B205 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct Orientation_tA42A910C028412D24B526CAEFD58B00C55242CB0 
{
	int32_t ___value__;
};
struct PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F 
{
	Type_t* ___type;
	PlayerLoopSystemU5BU5D_t07C04E53AAC3CDA17603E8BA1B41D7E1AC083C6D* ___subSystemList;
	UpdateFunction_t1C48B7EECBE47AC123A9D9D7D9D2A9EE951C56C4* ___updateDelegate;
	intptr_t ___updateFunction;
	intptr_t ___loopConditionFunction;
};
struct PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F_marshaled_pinvoke
{
	Type_t* ___type;
	PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F_marshaled_pinvoke* ___subSystemList;
	Il2CppMethodPointer ___updateDelegate;
	intptr_t ___updateFunction;
	intptr_t ___loopConditionFunction;
};
struct PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F_marshaled_com
{
	Type_t* ___type;
	PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F_marshaled_com* ___subSystemList;
	Il2CppMethodPointer ___updateDelegate;
	intptr_t ___updateFunction;
	intptr_t ___loopConditionFunction;
};
struct PublisherPrivacyPersonalizationState_t1C90D3EAA372240D9EA95816905BABBEEBA1272D 
{
	int32_t ___value__;
};
struct RemoteConfigSettings_tC979947EE51355162B3241B9F80D95A8FD25FE52  : public RuntimeObject
{
	intptr_t ___m_Ptr;
	Action_1_t10DCB0C07D0D3C565CEACADC80D1152B35A45F6C* ___Updated;
};
struct RemoteConfigSettings_tC979947EE51355162B3241B9F80D95A8FD25FE52_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	Il2CppMethodPointer ___Updated;
};
struct RemoteConfigSettings_tC979947EE51355162B3241B9F80D95A8FD25FE52_marshaled_com
{
	intptr_t ___m_Ptr;
	Il2CppMethodPointer ___Updated;
};
struct ResponseInfoClientType_t8563A697FAC0F0191211D7E7871B0E769CA7FBE7 
{
	int32_t ___value__;
};
struct ScheduledInvocation_t049B2F607AC8AAA60877C47F1F9D5F8EBDBA0B93  : public RuntimeObject
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___Action;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___InvocationTime;
	int64_t ___ActionId;
};
struct ServicesInitializationState_t3285FD49152B310475A0B1254422946A268764AD 
{
	int32_t ___value__;
};
struct TagForChildDirectedTreatment_t477FA75E864BC33863535EEE486F6D05D1343C80 
{
	int32_t ___value__;
};
struct TagForUnderAgeOfConsent_t2A2F4F1D84039FADF9022AA6E92E9893E60B01ED 
{
	int32_t ___value__;
};
struct Type_tB4F3FFEF78203230302E9D3C0763C92B3C5EEE20 
{
	int32_t ___value__;
};
struct PrecisionType_t5B0FBF4C3E44BA119E289EF000BEFD5EA7EA471C 
{
	int32_t ___value__;
};
struct Tag_t65C719EEBD4BD6BC3814607DC26A76E97D24E034 
{
	int32_t ___value__;
};
struct NativeArray_1_t81F55263465517B73C455D3400CF67B4BADD85CF 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct Nullable_1_t8E5F48677B7FA223E824975C2D25CFDAD10B73AF 
{
	bool ___hasValue;
	int32_t ___value;
};
struct Nullable_1_t5C1ED9E759C25CA8D8C3AD08588837283BCD0ADD 
{
	bool ___hasValue;
	int32_t ___value;
};
struct Nullable_1_tD9EF87DE17BC8563283D8BCB64EF209BCFE74FD3 
{
	bool ___hasValue;
	int32_t ___value;
};
struct ActionScheduler_t25AE26866357EBC70F6698FCD3E0B6DBE7473154  : public RuntimeObject
{
	PlayerLoopSystem_t8AED6BF1C8A309CAA6FF71AC91DD33BDDFF7CF1F ___SchedulerLoopSystem;
	RuntimeObject* ___m_TimeProvider;
	RuntimeObject* ___m_Lock;
	MinimumBinaryHeap_1_tB7A85B4D870F0107AF6031545D6054927A1CB7AD* ___m_ScheduledActions;
	Dictionary_2_t2306F810ADE46E708DBCF5BFFD57ED345A61D8CA* ___m_IdScheduledInvocationMap;
	List_1_tFD4080EF47ACD067F2E4AB57C9E2E8E64CA953E3* ___m_ExpiredActions;
	int64_t ___m_NextId;
};
struct AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158  : public RuntimeObject
{
	int32_t ____type;
	int32_t ____orientation;
	int32_t ____width;
	int32_t ____height;
};
struct AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD  : public RuntimeObject
{
	int32_t ___U3CPrecisionU3Ek__BackingField;
	int64_t ___U3CValueU3Ek__BackingField;
	String_t* ___U3CCurrencyCodeU3Ek__BackingField;
};
struct AdapterStatus_t4DFBD853B6BC534B2B9C9379AC5FA14051FFDEE0  : public RuntimeObject
{
	int32_t ___U3CInitializationStateU3Ek__BackingField;
	String_t* ___U3CDescriptionU3Ek__BackingField;
	int32_t ___U3CLatencyU3Ek__BackingField;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ConsentDebugSettings_t21BCD70B1E4DB762E04807E88E78285CC51370C6  : public RuntimeObject
{
	int32_t ___DebugGeography;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___TestDeviceHashedIds;
};
struct DownloadHandlerAssetBundle_tCD9D8BA067912469251677D16DFCADD13CAD510C  : public DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB
{
};
struct DownloadHandlerAssetBundle_tCD9D8BA067912469251677D16DFCADD13CAD510C_marshaled_pinvoke : public DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB_marshaled_pinvoke
{
};
struct DownloadHandlerAssetBundle_tCD9D8BA067912469251677D16DFCADD13CAD510C_marshaled_com : public DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB_marshaled_com
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct NativeAdOptions_t961E770C1A3B5C13D152A9393A6ED04CD78365E7  : public RuntimeObject
{
	int32_t ___MediaAspectRatio;
	int32_t ___AdChoicesPlacement;
	VideoOptions_t913B2174C33E206DD78043A7663F81DADF2FA042* ___VideoOptions;
};
struct NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF  : public RuntimeObject
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___U3CBackgroundColorU3Ek__BackingField;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___U3CTextColorU3Ek__BackingField;
	int32_t ___U3CFontSizeU3Ek__BackingField;
	int32_t ___U3CStyleU3Ek__BackingField;
};
struct ServicesInitializationException_tBF84F1801B1054DE91A79436CA6D3FA5F0F68631  : public Exception_t
{
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct U3CGetConfigAsyncU3Ed__2_tF7D4A87AA0217216CDAE3FFB007ABC2AFA3CB152 
{
	int32_t ___U3CU3E1__state;
	AsyncTaskMethodBuilder_1_t317FE70D757AFEA76E7E60CE379D5D15652D2F0C ___U3CU3Et__builder;
	StreamingAssetsConfigurationLoader_tDB162F80417A64081A3F8D509A29D6A01A25D712* ___U3CU3E4__this;
	TaskAwaiter_1_t254638BB1FAD695D9A9542E098A189D438A000F6 ___U3CU3Eu__1;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct DownloadHandlerTexture_t45E2D719000AA1594E648810F0B57A77FA7C568C  : public DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB
{
	NativeArray_1_t81F55263465517B73C455D3400CF67B4BADD85CF ___m_NativeData;
	bool ___mNonReadable;
};
struct DownloadHandlerTexture_t45E2D719000AA1594E648810F0B57A77FA7C568C_marshaled_pinvoke : public DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB_marshaled_pinvoke
{
	NativeArray_1_t81F55263465517B73C455D3400CF67B4BADD85CF ___m_NativeData;
	int32_t ___mNonReadable;
};
struct DownloadHandlerTexture_t45E2D719000AA1594E648810F0B57A77FA7C568C_marshaled_com : public DownloadHandler_t1B56C7D3F65D97A1E4B566A14A1E783EA8AE4EBB_marshaled_com
{
	NativeArray_1_t81F55263465517B73C455D3400CF67B4BADD85CF ___m_NativeData;
	int32_t ___mNonReadable;
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB  : public RuntimeObject
{
	MaxAdContentRating_t8F4AA13668EB7FF7A5F6F369845B5280BBE0BF17* ___MaxAdContentRating;
	Nullable_1_t5C1ED9E759C25CA8D8C3AD08588837283BCD0ADD ___TagForChildDirectedTreatment;
	Nullable_1_tD9EF87DE17BC8563283D8BCB64EF209BCFE74FD3 ___TagForUnderAgeOfConsent;
	Nullable_1_t8E5F48677B7FA223E824975C2D25CFDAD10B73AF ___PublisherPrivacyPersonalizationState;
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___TestDeviceIds;
	Nullable_1_t78F453FADB4A9F50F267A4E349019C34410D1A01 ___PublisherFirstPartyIdEnabled;
};
struct UnityProjectNotLinkedException_t59C31CD6D4353D68B7205AAC8E35391D88B0ABF8  : public ServicesInitializationException_tBF84F1801B1054DE91A79436CA6D3FA5F0F68631
{
};
struct IdentityTokenChanged_tE8CB0DAB5F6E640A847803F582E6CB6237742395  : public MulticastDelegate_t
{
};
struct SessionStateChanged_t1180FB66E702B635CAD9316DC661D931277B2A0C  : public MulticastDelegate_t
{
};
struct GADUConsentFormLoadCompletionHandler_tCEDA61B1731AA6292F53FF26EAEFD5960A4E3254  : public MulticastDelegate_t
{
};
struct GADUConsentFormPresentCompletionHandler_t7BE8E6E55FBFBD76CBAA12588EEBE1B796442E25  : public MulticastDelegate_t
{
};
struct GADUConsentInfoUpdateCallback_t3F5F5E587ACC5BAF163FADD1269401A40163AEDB  : public MulticastDelegate_t
{
};
struct UpdatedEventHandler_tB0D5A5BA322FE093894992C29DCF51E7E12579C4  : public MulticastDelegate_t
{
};
struct CreationDelegate_tFBFDFC77F9F874CD9C3B966D1AD564BF5315E405  : public MulticastDelegate_t
{
};
struct GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct ObjectDisposedException_tC5FB29E8E980E2010A2F6A5B9B791089419F89EB  : public InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB
{
	String_t* ____objectName;
};
struct SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC  : public Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF
{
};
struct TilemapRenderer_t1A45FD335E86172CFBB77D657E1D6705A477A6CB  : public Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF
{
};
struct AppStateEventClient_t164A1509DF0A0207F5139DDB13D5B00EE6A085F6  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* ___AppStateChanged;
};
struct MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct ThrowStub_t9161280E38728A40D9B1A975AEE62E89C379E400  : public ObjectDisposedException_tC5FB29E8E980E2010A2F6A5B9B791089419F89EB
{
};
struct Tilemap_t18C4166D0AC702D5BFC0C411FA73C4B61D9D1751  : public GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B
{
};
struct AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D_StaticFields
{
	String_t* ___U3CVersionU3Ek__BackingField;
};
struct AnalyticsSessionInfo_tDE8F7A9E13EF9723E2D975F76E916753DA61AD76_StaticFields
{
	SessionStateChanged_t1180FB66E702B635CAD9316DC661D931277B2A0C* ___sessionStateChanged;
	IdentityTokenChanged_tE8CB0DAB5F6E640A847803F582E6CB6237742395* ___identityTokenChanged;
};
struct ConfigurationUtils_tF385B43626BC064FCBAA5093770ABE0C3D291DAC_StaticFields
{
	RuntimeObject* ___U3CConfigurationLoaderU3Ek__BackingField;
};
struct RemoteSettings_t9DFFC747AB3E7A39DF4527F245B529A407427250_StaticFields
{
	UpdatedEventHandler_tB0D5A5BA322FE093894992C29DCF51E7E12579C4* ___Updated;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___BeforeFetchFromServer;
	Action_3_t4730167C8E7EB19F1E0034580790A915D549F6CB* ___Completed;
};
struct UnityServices_t4749F0FB88F542DAC1E287ACFFAB146EF9759317_StaticFields
{
	RuntimeObject* ___U3CInstanceU3Ek__BackingField;
	TaskCompletionSource_1_tB4EF81F69CCF7C4F0D956F9B26127C0634A24A37* ___U3CInstantiationCompletionU3Ek__BackingField;
	ExternalUserIdProperty_t8B820D8AF448EA2F129B1FFD7D4FD1FD840606E9* ___ExternalUserIdProperty;
	Dictionary_2_t64EF50E914B0358E99C591AF0C1660B4DF1F1317* ___U3Cs_ServicesU3Ek__BackingField;
};
struct UnityServicesBuilder_tA733917766742A74C8F796EEA334C8CA22F2C8DB_StaticFields
{
	CreationDelegate_tFBFDFC77F9F874CD9C3B966D1AD564BF5315E405* ___U3CInstanceCreationDelegateU3Ek__BackingField;
};
struct UnityThreadUtils_tCC1D0915CC9C90AEB5B27F2A9D791544FAC0DEBA_StaticFields
{
	int32_t ___s_UnityThreadId;
	TaskScheduler_t3F0550EBEF7C41F74EC8C08FF4BED0D8CE66006E* ___U3CUnityThreadSchedulerU3Ek__BackingField;
};
struct ConsentFormClient_tBEC2D325BC9255F78BF2FB9DD0EBFA8DC8A0B7E1_StaticFields
{
	ConsentFormClient_tBEC2D325BC9255F78BF2FB9DD0EBFA8DC8A0B7E1* ____instance;
	GADUConsentFormLoadCompletionHandler_tCEDA61B1731AA6292F53FF26EAEFD5960A4E3254* ___U3CU3Ef__mgU24cache0;
	GADUConsentFormPresentCompletionHandler_t7BE8E6E55FBFBD76CBAA12588EEBE1B796442E25* ___U3CU3Ef__mgU24cache1;
	GADUConsentFormPresentCompletionHandler_t7BE8E6E55FBFBD76CBAA12588EEBE1B796442E25* ___U3CU3Ef__mgU24cache2;
	GADUConsentFormPresentCompletionHandler_t7BE8E6E55FBFBD76CBAA12588EEBE1B796442E25* ___U3CU3Ef__mgU24cache3;
};
struct ConsentInformationClient_tA72DF61A2E613C9DC9DE3111E8D5AC7672A23FB1_StaticFields
{
	ConsentInformationClient_tA72DF61A2E613C9DC9DE3111E8D5AC7672A23FB1* ____instance;
	GADUConsentInfoUpdateCallback_t3F5F5E587ACC5BAF163FADD1269401A40163AEDB* ___U3CU3Ef__mgU24cache0;
};
struct AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158_StaticFields
{
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___Banner;
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___MediumRectangle;
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___IABBanner;
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___Leaderboard;
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___SmartBanner;
	int32_t ___FullWidth;
};
struct AppStateEventClient_t164A1509DF0A0207F5139DDB13D5B00EE6A085F6_StaticFields
{
	AppStateEventClient_t164A1509DF0A0207F5139DDB13D5B00EE6A085F6* ___instance;
	Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* ___U3CU3Ef__amU24cache0;
};
struct MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A_StaticFields
{
	MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A* ___instance;
	List_1_tDB72209F35D56F62A287633F9450978E90B90987* ___adEventsQueue;
	bool ___adEventsQueueEmpty;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif



#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10800;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10800 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10801;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10801 = { sizeof(NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7), sizeof(NavMeshPath_tC77ABF4A25FC0F0A94E8A70E304E114FE690A3A7_marshaled_pinvoke), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10802;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10802 = { sizeof(U3CModuleU3E_t6B4A7D64487421A1C7A9ACB5578F8A35510E2A0C), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10803;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10803 = { sizeof(UpdatedEventHandler_tB0D5A5BA322FE093894992C29DCF51E7E12579C4), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10804;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10804 = { sizeof(RemoteSettings_t9DFFC747AB3E7A39DF4527F245B529A407427250), -1, sizeof(RemoteSettings_t9DFFC747AB3E7A39DF4527F245B529A407427250_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10805;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10805 = { sizeof(RemoteConfigSettings_tC979947EE51355162B3241B9F80D95A8FD25FE52), sizeof(RemoteConfigSettings_tC979947EE51355162B3241B9F80D95A8FD25FE52_marshaled_pinvoke), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10806;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10806 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10807;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10807 = { sizeof(RemoteConfigSettingsHelper_t29B2673892F8181388B45FFEEE354B3773629588), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10808;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10808 = { sizeof(ContinuousEvent_t71122F6F65BF7EA8490EA664A55D5C03790CB6CF), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10809;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10809 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10810;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10810 = { sizeof(SessionStateChanged_t1180FB66E702B635CAD9316DC661D931277B2A0C), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10811;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10811 = { sizeof(IdentityTokenChanged_tE8CB0DAB5F6E640A847803F582E6CB6237742395), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10812;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10812 = { sizeof(AnalyticsSessionInfo_tDE8F7A9E13EF9723E2D975F76E916753DA61AD76), -1, sizeof(AnalyticsSessionInfo_tDE8F7A9E13EF9723E2D975F76E916753DA61AD76_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10813;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10813 = { sizeof(U3CModuleU3E_tA3EED36B70F256012F5BB9BC014BD75FF7BF2C66), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10814;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10814 = { sizeof(UnityWebRequestAssetBundle_t53F46F7F69F894F1B22F3FBBD2565D7B67A5C6A8), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10815;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10815 = { sizeof(DownloadHandlerAssetBundle_tCD9D8BA067912469251677D16DFCADD13CAD510C), sizeof(DownloadHandlerAssetBundle_tCD9D8BA067912469251677D16DFCADD13CAD510C_marshaled_pinvoke), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10816;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10816 = { sizeof(U3CModuleU3E_t2E436F02646AE15477D3A3C17B521F580FFA2360), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10817;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10817 = { sizeof(GADUConsentFormLoadCompletionHandler_tCEDA61B1731AA6292F53FF26EAEFD5960A4E3254), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10818;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10818 = { sizeof(GADUConsentFormPresentCompletionHandler_t7BE8E6E55FBFBD76CBAA12588EEBE1B796442E25), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10819;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10819 = { sizeof(ConsentFormClient_tBEC2D325BC9255F78BF2FB9DD0EBFA8DC8A0B7E1), -1, sizeof(ConsentFormClient_tBEC2D325BC9255F78BF2FB9DD0EBFA8DC8A0B7E1_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10820;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10820 = { sizeof(GADUConsentInfoUpdateCallback_t3F5F5E587ACC5BAF163FADD1269401A40163AEDB), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10821;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10821 = { sizeof(ConsentInformationClient_tA72DF61A2E613C9DC9DE3111E8D5AC7672A23FB1), -1, sizeof(ConsentInformationClient_tA72DF61A2E613C9DC9DE3111E8D5AC7672A23FB1_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10822;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10822 = { sizeof(Externs_t8C1E08109EC604AF48C9B439F139F4B64E537504), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10823;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10823 = { sizeof(UmpClientFactory_t419C0174AA6B63B15489EF309508540C1D78CF18), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10824;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10824 = { sizeof(U3CModuleU3E_t77C22CB09929EB44C5402FD6400E944C42107E2E), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10825;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10825 = { sizeof(AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10826;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10826 = { sizeof(AdInspectorErrorClientEventArgs_tA430D6E41FFCB38B31390D9EA1860D279FA4EBFA), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10827;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10827 = { sizeof(AppStateEventClient_t164A1509DF0A0207F5139DDB13D5B00EE6A085F6), -1, sizeof(AppStateEventClient_t164A1509DF0A0207F5139DDB13D5B00EE6A085F6_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10828;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10828 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10829;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10829 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10830;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10830 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10831;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10831 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10832;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10832 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10833;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10833 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10834;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10834 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10835;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10835 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10836;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10836 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10837;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10837 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10838;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10838 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10839;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10839 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10840;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10840 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10841;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10841 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10842;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10842 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10843;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10843 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10844;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10844 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10845;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10845 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10846;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10846 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10847;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10847 = { sizeof(LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10848;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10848 = { sizeof(U3CInvokeInUpdateU3Ec__AnonStorey0_tF0F601D18E2E6B9DBB319D500D2C05D0BDC31C67), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10849;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10849 = { sizeof(MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A), -1, sizeof(MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10850;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10850 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10851;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10851 = { sizeof(U3CModuleU3E_tA4399B639778665FAD9E820A92BC542249E0CF67), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10852;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10852 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10853;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10853 = { sizeof(AdManagerAdRequest_t8DE61BA8D8C0D7B2A3D39C43B7A8101EEDE80BFC), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10854;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10854 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10855;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10855 = { sizeof(AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D), -1, sizeof(AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10856;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10856 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10857;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10857 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10858;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10858 = { sizeof(AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158), -1, sizeof(AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10859;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10859 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10860;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10860 = { sizeof(AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10861;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10861 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10862;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10862 = { sizeof(AdapterStatus_t4DFBD853B6BC534B2B9C9379AC5FA14051FFDEE0), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10863;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10863 = { sizeof(AppEvent_tDE8A307A4C93E207A0450B2C5FAB251C8FB91C56), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10864;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10864 = { sizeof(MaxAdContentRating_t8F4AA13668EB7FF7A5F6F369845B5280BBE0BF17), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10865;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10865 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10866;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10866 = { sizeof(NativeAdOptions_t961E770C1A3B5C13D152A9393A6ED04CD78365E7), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10867;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10867 = { sizeof(NativeTemplateStyle_t98BEC9523A240D4458CEEDBBF9A7B0634C62DC89), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10868;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10868 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10869;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10869 = { sizeof(NativeTemplateTextStyle_t8067A7E0B4D30EF21312BDD70161D375B8A3D6AF), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10870;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10870 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10871;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10871 = { sizeof(RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10872;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10872 = { sizeof(Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10873;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10873 = { sizeof(ServerSideVerificationOptions_tCB0CF0403C3E4DDA0E47F9594BE01D214B4673C3), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10874;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10874 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10875;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10875 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10876;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10876 = { sizeof(VideoOptions_t913B2174C33E206DD78043A7663F81DADF2FA042), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10877;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10877 = { sizeof(MediationExtras_t390586958F7ED4B158AD5AD18F58A86E9E7B621E), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10878;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10878 = { sizeof(U3CModuleU3E_t2F9091E403B25A5364AE8A6B2C249E31D405E3F4), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10879;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10879 = { sizeof(JsonUtility_t731013D97E03B7EDAE6186D6D6826A53B85F7197), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10880;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10880 = { sizeof(U3CModuleU3E_tAEEA45345F4FE6CCC29A863B181BFCCC6EDBCAC7), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10881;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10881 = { sizeof(DownloadHandlerTexture_t45E2D719000AA1594E648810F0B57A77FA7C568C), sizeof(DownloadHandlerTexture_t45E2D719000AA1594E648810F0B57A77FA7C568C_marshaled_pinvoke), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10882;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10882 = { sizeof(UnityWebRequestTexture_t7C1794874F2663ED616906719C4A306A46B35C01), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10883;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10883 = { sizeof(U3CModuleU3E_t33B45E84B8820001E6F644E31DCC9BAEE6DCE23F), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10884;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10884 = { sizeof(ImageConversion_tD7B6C2CDCD3E1078708B1668B9695914A502C252), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10885;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10885 = { sizeof(U3CModuleU3E_t5E8190EE43F4DF5D80E8A6651A0469A8FD445F94), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10886;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10886 = { sizeof(Tilemap_t18C4166D0AC702D5BFC0C411FA73C4B61D9D1751), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10887;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10887 = { sizeof(TilemapRenderer_t1A45FD335E86172CFBB77D657E1D6705A477A6CB), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10888;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10888 = { sizeof(U3CModuleU3E_tD1F0B9705D5594A42609E77459719A6C0DB9DD53), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10889;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10889 = { sizeof(AnalyticsCommon_t9A0DD5EB3A8E861A4481AA17C2105396E9C4E34C), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10890;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10890 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10891;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10891 = { sizeof(U3CModuleU3E_t2C7BF608494A5C8FB8C8C4D318FB27BCF6CE322A), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10892;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10892 = { sizeof(GridLayout_tAD661B1E1E57C16BE21C8C13432EA04FE1F0418B), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10893;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10893 = { sizeof(U3CModuleU3E_t4BC86AB59C63F2CC6EB51BE560C15CFCAE821BC7), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10894;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10894 = { sizeof(SpriteShapeRenderer_tE998BB73CF661079736CCC23617E597AB230A4AC), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10895;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10895 = { sizeof(U3CModuleU3E_t0267161C6C027BA3CDC8B3933902448EE205B45B), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10896;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10896 = { sizeof(CloudProjectId_t14C34E1DB1EB955BA21ECB1126EACEC9818A68F0), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10897;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10897 = { sizeof(ConfigurationCollectionHelper_t117485823F5BCB9D4E56EDFEF1652CF5C9968BFC), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10898;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10898 = { sizeof(ConfigurationEntry_t9DC70834AC631CC28DFF4C96C859E4106EF5E75E), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10899;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10899 = { sizeof(ConfigurationUtils_tF385B43626BC064FCBAA5093770ABE0C3D291DAC), -1, sizeof(ConfigurationUtils_tF385B43626BC064FCBAA5093770ABE0C3D291DAC_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10900;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10900 = { sizeof(ExternalUserId_t9C3CCC17970C998BDD5330AE110097669EBA0018), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10901;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10901 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10902;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10902 = { sizeof(ProjectConfiguration_t653308465893F727E0FA58993E3D000D74630CC4), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10903;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10903 = { sizeof(SerializableProjectConfiguration_tBAE4D3A66EC38C1869E294396DB79F127B8F58EE)+ sizeof(RuntimeObject), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10904;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10904 = { sizeof(U3CGetConfigAsyncU3Ed__2_tF7D4A87AA0217216CDAE3FFB007ABC2AFA3CB152)+ sizeof(RuntimeObject), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10905;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10905 = { sizeof(StreamingAssetsConfigurationLoader_tDB162F80417A64081A3F8D509A29D6A01A25D712), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10906;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10906 = { sizeof(StreamingAssetsUtils_tA32B07F0AAC9291ED90B0B55281F247B4A57911F), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10907;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10907 = { sizeof(U3CModuleU3E_t1A06A280C3A3120637CE5C7405E81F324A763874), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10908;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10908 = { sizeof(ActionScheduler_t25AE26866357EBC70F6698FCD3E0B6DBE7473154), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10909;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10909 = { sizeof(MinimumBinaryHeap_tA55F58E6B1D3F482C11529BAB4C1D56F5BF745FE), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10910;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10910 = { 0, 0, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10911;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10911 = { 0, 0, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10912;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10912 = { sizeof(ScheduledInvocation_t049B2F607AC8AAA60877C47F1F9D5F8EBDBA0B93), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10913;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10913 = { sizeof(ScheduledInvocationComparer_t785D4501646D05413E8F88FAECD14D9902742CC2), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10914;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10914 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10915;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10915 = { sizeof(UtcTimeProvider_t45FE04D3C9372C9089808CBF396C87E62AA26743), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10916;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10916 = { sizeof(U3CModuleU3E_t4052AFC804778757171A467E5C89680322791B94), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10917;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10917 = { sizeof(ExternalUserIdProperty_t8B820D8AF448EA2F129B1FFD7D4FD1FD840606E9), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10918;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10918 = { sizeof(InitializationOptions_t51AA79A729CADA6922543A7A47F1E87A09DBD17A), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10919;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10919 = { sizeof(ServicesInitializationException_tBF84F1801B1054DE91A79436CA6D3FA5F0F68631), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10920;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10920 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10921;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10921 = { sizeof(UnityProjectNotLinkedException_t59C31CD6D4353D68B7205AAC8E35391D88B0ABF8), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10922;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10922 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10923;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10923 = { sizeof(UnityServices_t4749F0FB88F542DAC1E287ACFFAB146EF9759317), -1, sizeof(UnityServices_t4749F0FB88F542DAC1E287ACFFAB146EF9759317_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10924;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10924 = { sizeof(CreationDelegate_tFBFDFC77F9F874CD9C3B966D1AD564BF5315E405), sizeof(Il2CppMethodPointer), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10925;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10925 = { sizeof(UnityServicesBuilder_tA733917766742A74C8F796EEA334C8CA22F2C8DB), -1, sizeof(UnityServicesBuilder_tA733917766742A74C8F796EEA334C8CA22F2C8DB_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10926;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10926 = { sizeof(UnityThreadUtils_tCC1D0915CC9C90AEB5B27F2A9D791544FAC0DEBA), -1, sizeof(UnityThreadUtils_tCC1D0915CC9C90AEB5B27F2A9D791544FAC0DEBA_StaticFields), 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10927;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10927 = { sizeof(U3CModuleU3E_t5CFA55679A8E9D2525AFBC9C50BEC051BEA21310), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10928;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10928 = { sizeof(DataContractAttribute_tD065D7D14CC8AA548815166AB8B8210D1B3C699F), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10929;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10929 = { sizeof(DataMemberAttribute_t8AE446BE9032B9BC8E7B2EDC785F5C6FA0E5BB73), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10930;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10930 = { sizeof(EnumMemberAttribute_t65B5E85E642C96791DD6AE5EAD0276350954126F), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10931;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10931 = { sizeof(IgnoreDataMemberAttribute_tC1AC455123E5BF654B22396F3E5CB1C514D86777), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10932;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10932 = { sizeof(U3CModuleU3E_t742906D1ECE8A5FA78682FD3E1F8204B25D019FA), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10933;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10933 = { sizeof(ConsentDebugSettings_t21BCD70B1E4DB762E04807E88E78285CC51370C6), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10934;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10934 = { sizeof(ConsentRequestParameters_t34C1E8C04ED21B543DFE57708C303AABEA447516), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10935;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10935 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10936;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10936 = { sizeof(FormError_t925BBA051FDAC8CC3DECB9E5511864E8ED383026), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10937;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10937 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10938;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10938 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10939;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10939 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10940;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10940 = { sizeof(U3CModuleU3E_tC6F3BE26E8DBB3B8108F8360B026950D37DAF754), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10941;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10941 = { sizeof(InstallationId_tA5712B394172DB4DBA3B5F9A67D5522D37E2B873), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10942;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10942 = { 0, -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10943;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10943 = { sizeof(NSUserDefaults_t518A0A24332EC9EC2361F2C04C06166E301FF25C), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10944;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10944 = { sizeof(UnityAdsIdentifier_t8D3A8D6DB9A1FB469E2E7F3D58B30C3D0D71DA56), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10945;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10945 = { sizeof(UnityAnalyticsIdentifier_tAB8DD85C666D878FC108C324E10DF6528B5BDA5D), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10946;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10946 = { sizeof(U3CModuleU3E_tDA285F13E9413BF3B79A99D6E310BE9AF3444EEB), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10947;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10947 = { sizeof(ConfigurationElement_tAE3EE71C256825472831FFBB7F491275DFAF089E), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10948;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10948 = { sizeof(ConfigurationSection_t0BC609F0151B160A4FAB8226679B62AF22539C3E), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10949;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10949 = { sizeof(int32_t)+ sizeof(RuntimeObject), sizeof(int32_t), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10950;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10950 = { sizeof(ConfigurationPropertyCollection_t1DEB95D3283BB11A46B862E9D13710ED698B6C93), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10951;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10951 = { sizeof(ConfigurationElementCollection_t56E8398661A85A59616301BADF13026FB1492606), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10952;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10952 = { sizeof(ConfigurationCollectionAttribute_t1D7DBAAB4908B6B8F26EA1C66106A67BDE949558), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10953;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10953 = { sizeof(ConfigurationSectionGroup_tE7948C2D31B193F4BA8828947ED3094B952C7863), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10954;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10954 = { sizeof(IgnoreSection_t43A7C33C0083D18639AA3CC3D75DD93FCF1C5D97), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10955;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10955 = { sizeof(ThrowStub_t9161280E38728A40D9B1A975AEE62E89C379E400), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10956;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10956 = { sizeof(U3CModuleU3E_tA8E35B180310D6046FB6F4D10E1B4A172B9C0215), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10957;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10957 = { sizeof(DiagnosticsFactory_tD2C64CDF0CC356965DB1993359515DC82D715CF2), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10958;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10958 = { sizeof(MetricsFactory_tFED08C34B8CB569B801796787E82F2818606FA05), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10959;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10959 = { sizeof(U3CModuleU3E_tD7D5A4C49FDBA2AFB002462DF64609318A53BF85), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10960;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10960 = { sizeof(Environments_tC4B2F50A927F4B9798E0EA1415765D81DC7C0A00), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10961;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10961 = { sizeof(U3CModuleU3E_tC445F3DAD73003CEBF9FF7BF1C109B3025166895), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10962;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10962 = { sizeof(UnityThreadUtilsInternal_t80A0011DD0CD9318DC38466ABEEAE4AD8B07B2AC), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10963;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10963 = { 0, sizeof(Il2CppIActivationFactory*), 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10964;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10964 = { sizeof(Il2CppComObject), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10965;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10965 = { sizeof(__Il2CppComDelegate_tD0DD2BBA6AC8F151D32B6DFD02F6BDA339F8DC4D), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10966;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10966 = { sizeof(Il2CppFullySharedGenericAny), -1, 0, 0 };
extern const Il2CppTypeDefinitionSizes g_typeDefinitionSize10967;
const Il2CppTypeDefinitionSizes g_typeDefinitionSize10967 = { sizeof(Il2CppFullySharedGenericStruct)+ sizeof(RuntimeObject), -1, 0, 0 };
#ifdef __clang__
#pragma clang diagnostic pop
#endif
