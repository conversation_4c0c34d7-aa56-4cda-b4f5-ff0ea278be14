﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif





struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

struct unitytls_errorstate_tC926EE4582920BE2C1DB1F3F65619B810D5AB902;
struct unitytls_key_ref_t6BD91D013DF11047C53738FEEB12CE290FDC71A2;
struct unitytls_tlsctx_tF8BBCBFE1E957B846442DED65ECB89BC5307DEAE;
struct unitytls_x509list_ref_t6C5C1CF0B720516A681CB741104A164FD8B3CF17;
struct unitytls_x509name_t8A1108C917795D8FE946B50769ACE51489C7BF5D;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct unitytls_x509_ref_t9CEB17766B4144117333AB50379B21A357FA4333 
{
	uint64_t ___handle;
};
struct unitytls_x509list_ref_t6C5C1CF0B720516A681CB741104A164FD8B3CF17 
{
	uint64_t ___handle;
};
struct ControlCommand_tB0F1C7ED893D028334F92A046E73D932D64E51D3 
{
	int32_t ___value__;
};
struct unitytls_x509verify_result_tBD0B93AE208F14C2C6CD925F0702A269BFE66FF0 
{
	uint32_t ___value__;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif

extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewAppEventCallback_m961A7D38B5CDFE03E7CB567DA13A143BF0B9C146(intptr_t ___0_bannerClient, char* ___1_name, char* ___2_info);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewClickRecordedCallback_m449A950F56417901D4841B4324157D395AFBCCC7(intptr_t ___0_adClientRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewDidDismissScreenCallback_m8F444EA6690A4B4852F16EE1698BD8239E0A3422(intptr_t ___0_bannerClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewDidFailToReceiveAdWithErrorCallback_m881B79719B00F95554EFB2847D8F412D36C8E784(intptr_t ___0_bannerClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewDidReceiveAdCallback_mC6E8AE72C73938179EA82C82C1858D3100D3BA49(intptr_t ___0_bannerClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewImpressionRecordedCallback_m2138D2ED5C3D61D04FC4218B0A1DB00ABED355B0(intptr_t ___0_adClientRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewPaidEventCallback_m50EE64914C1AE4F00E97A8F953A8A66A75C35343(intptr_t ___0_bannerClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerBannerClient_AdViewWillPresentScreenCallback_mB936401ECAB92B7C2C34764370520C749F8AEB7E(intptr_t ___0_bannerClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_AdDidDismissFullScreenContentCallback_mBCB8F5ABFE5A4B68FBC229E5F9EF7EE1114FBB6E(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_AdDidRecordClickCallback_m66338BF3C281EFC8C5E478298BD1AE7CB5B33532(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_AdDidRecordImpressionCallback_mE9E7F6EAB83157F7B98709543C7326C16524BD07(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_AdFailedToPresentFullScreenContentCallback_mA3965CF1723D40E0468EEE2858E6058B3D4AEDC1(intptr_t ___0_interstitialClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_AdWillPresentFullScreenContentCallback_mD8AD1251BC2AA490BCA12431D2109FFED6FDCCA0(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialAppEventCallback_m10A1A7DAB44D716011572F3B1D5D7BD38108308C(intptr_t ___0_interstitialClient, char* ___1_name, char* ___2_info);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialFailedToLoadCallback_mF8B2A4C5D4D2CF214AC4ACD502EE4415E274A570(intptr_t ___0_interstitialClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialLoadedCallback_mA9104FD23F75575DC4F5B79B8BD97539C68951AF(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialPaidEventCallback_mBD64A0D47B00BED4AA447D9041C3B98A9FD699A2(intptr_t ___0_interstitialClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AdDidDismissFullScreenContentCallback_m95DADA0B0645BC5DA1BF13B89BDE31CE2ACA1A97(intptr_t ___0_appOpenAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AdDidRecordClickCallback_m21590113039E74F283C820A0350B31D55978C16A(intptr_t ___0_appOpenAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AdDidRecordImpressionCallback_mB889FA5B2DF8A5EFC68719C09419A71703106667(intptr_t ___0_appOpenAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AdFailedToPresentFullScreenContentCallback_m2F2F4E90C64E5598EBDA91538950D93304FD1FFE(intptr_t ___0_appOpenAdClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AdWillPresentFullScreenContentCallback_m718EC6BEE1B5E4506530D7FEA47D712BD33051DF(intptr_t ___0_appOpenAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AppOpenAdFailedToLoadCallback_m07DE6D2C26F2A35CD0299220F74CBF154034794E(intptr_t ___0_appOpenAdClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AppOpenAdLoadedCallback_m914F892E80437C7C0DA9C88DC9A781AD62C9143C(intptr_t ___0_appOpenAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_AppOpenAdClient_AppOpenAdPaidEventCallback_mBF5DBB882D67C2F7F55CFC312D026D7FE67BC959(intptr_t ___0_appOpenAdClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewClickRecordedCallback_m410068256BE6C93EB73EF90EF663F8DC5F171FCB(intptr_t ___0_adClientRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewDidDismissScreenCallback_mE1BCD6689EAA8E50FEFB023E25F5A9142FD6D33C(intptr_t ___0_bannerClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewDidFailToReceiveAdWithErrorCallback_m7162FE4424F7EC59777F43C858F52D1E773F9165(intptr_t ___0_bannerClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewDidReceiveAdCallback_m5262538F61568E5DE2112841CAF818E4495ADA3A(intptr_t ___0_bannerClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewImpressionRecordedCallback_m070026746D88B3EF1E713AD19CC56685F4AEE44F(intptr_t ___0_adClientRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewPaidEventCallback_mF0F545BB0FF6C5DF2CB82D0A8A228EC437CF2264(intptr_t ___0_bannerClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_BannerClient_AdViewWillPresentScreenCallback_m0B7567E264EACEEF85413FD0B4493780324CDDC6(intptr_t ___0_bannerClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_ConsentFormClient_ConsentFormLoadCompletionHandler_m3161FC8CDF3BDB16015D91FA7ED7035655DE5BFD(intptr_t ___0_clientRef, intptr_t ___1_errorRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_ConsentFormClient_ConsentFormPresentCompletionHandler_m04F646ACFA0B3B5CDAC0554D1EFAC5EC039F765B(intptr_t ___0_clientRef, intptr_t ___1_errorRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_ConsentInformationClient_ConsentInfoUpdateCallback_m01A1B5982AE827C301F4BA2A0801A2A3E13577D6(intptr_t ___0_clientRef, intptr_t ___1_errorRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_CultureInfo_OnCultureInfoChangedInAppX_m407BCFC1029A4485B7B063BC2F3601968C3BE577(Il2CppChar* ___0_language);
extern "C" int32_t CDECL ReversePInvokeWrapper_DeflateStreamNative_UnmanagedRead_m321A2621068F1C9509594A4D8F405F4F12C1CEB3(intptr_t ___0_buffer, int32_t ___1_length, intptr_t ___2_data);
extern "C" int32_t CDECL ReversePInvokeWrapper_DeflateStreamNative_UnmanagedWrite_mB0AD438266A9DD2813715E8BC90BF07DC7A02F52(intptr_t ___0_buffer, int32_t ___1_length, intptr_t ___2_data);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_AdDidDismissFullScreenContentCallback_m9368B493ED3C0A1FE5B1D403962EA2C1EFA60594(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_AdDidRecordClickCallback_m8EFF6466BCE371819D47332EFEDF3CE4CADE688A(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_AdDidRecordImpressionCallback_mE8EB08B25BB6AD515814AB5B7C37A22513D4CEAF(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_AdFailedToPresentFullScreenContentCallback_m6080CC9BEA744B139B2037A71F4E0DC826F33E63(intptr_t ___0_interstitialClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_AdWillPresentFullScreenContentCallback_m9CB94524846D61C2D8379C6679FDB9AFE00A5B87(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_InterstitialFailedToLoadCallback_m3CD83AC19BB9F0DAD18D65564A644627C5002FB5(intptr_t ___0_interstitialClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_InterstitialLoadedCallback_mBD773A172B12BF8D1673A8F9127B662901632A9B(intptr_t ___0_interstitialClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_InterstitialClient_InterstitialPaidEventCallback_mDF3BCA83163459BC5B07EF0CACEBC299355DDEEC(intptr_t ___0_interstitialClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_MobileAdsClient_AdInspectorClosedCallback_m286A0DEA80E15143D7EB2C4A030E48F24F85A34F(intptr_t ___0_mobileAdsClient, intptr_t ___1_errorRef);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_MobileAdsClient_InitializationCompleteCallback_mD93EF83AC007EB4ABC8DE202F97C9CDDCA7C7830(intptr_t ___0_mobileAdsClient, intptr_t ___1_initStatus);
extern "C" int64_t CDECL ReversePInvokeWrapper_MonoBtlsBioMono_Control_mA7B1C493171314C6F02CAC5F73EB7CB94B86ED78(intptr_t ___0_instance, int32_t ___1_command, int64_t ___2_arg);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsBioMono_OnRead_m8888AD0EA2D55D9FFE183BC63D4023BC621941F9(intptr_t ___0_instance, intptr_t ___1_data, int32_t ___2_dataLength, int32_t* ___3_wantMore);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsBioMono_OnWrite_mD90183BD125599306AAF4E74E941A3983027C575(intptr_t ___0_instance, intptr_t ___1_data, int32_t ___2_dataLength);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsSsl_PrintErrorsCallback_m93ED1F9335C5974459F0915323C5527382A83959(intptr_t ___0_str, intptr_t ___1_len, intptr_t ___2_ctx);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsSslCtx_NativeSelectCallback_m5A906B70CBEC53FE7F4208810264D78D4AA26EE4(intptr_t ___0_instance, int32_t ___1_count, intptr_t ___2_sizes, intptr_t ___3_data);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsSslCtx_NativeServerNameCallback_mC76D35B6D2668F93E280D35B5560A06571810857(intptr_t ___0_instance);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsSslCtx_NativeVerifyCallback_m13C23EDCF9CDADF430C5A1664D33DC7621974916(intptr_t ___0_instance, int32_t ___1_preverify_ok, intptr_t ___2_store_ctx);
extern "C" int32_t CDECL ReversePInvokeWrapper_MonoBtlsX509LookupMono_OnGetBySubject_m85B0F4B20C12F67DE4CD9521EC58308C9A27BA24(intptr_t ___0_instance, intptr_t ___1_name_ptr, intptr_t* ___2_x509_ptr);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_AdDidRecordClickCallback_m632E829B9AB6A053F2986694906581284D0274C6(intptr_t ___0_nativeClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_AdDidRecordImpressionCallback_m5C9BA274C4BC326BB04403C09862E546FBF5BA04(intptr_t ___0_nativeClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_NativeAdDidDismissScreenCallback_mB03FBEFEE85B88DCAE6D697ED384C3914DB97420(intptr_t ___0_nativeClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_NativeAdWillPresentScreenCallback_mD46EA2137ABB06B67F659115A4A23EA79BDE5C60(intptr_t ___0_nativeClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_NativeFailedToLoadCallback_m430AE3BFFA9134C335DB34C8CAEE2585680B02C9(intptr_t ___0_nativeClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_NativeLoadedCallback_m76E3DA9C9CCEE49B4BF34BBFC2796F96CB123BF2(intptr_t ___0_nativeClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_NativeOverlayAdClient_NativePaidEventCallback_m21C6648A632B9F78515E05BE472DCA5DD4190493(intptr_t ___0_nativeClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_OSSpecificSynchronizationContext_InvocationEntry_mF93C3CF6DBEC86E377576D840CAF517CB6E4D7E3(intptr_t ___0_arg);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_AdDidDismissFullScreenContentCallback_mD40A55587745226F6496F8555470851B0DAF6D4C(intptr_t ___0_rewardedAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_AdDidRecordClickCallback_mE8AB77B03ECB290FF64D298EA3B7B0A7B215F877(intptr_t ___0_rewardedAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_AdDidRecordImpressionCallback_mC13F26EF8A563B25B2D19C82ED0C3DE1B1A26DA1(intptr_t ___0_rewardedAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_AdFailedToPresentFullScreenContentCallback_m014215CE005401E59F4248C412EC4E19A8ED0D37(intptr_t ___0_rewardedAdClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_AdWillPresentFullScreenContentCallback_mCE88BD260C0B76E861442A388BDCC8E43C385518(intptr_t ___0_rewardedAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_RewardedAdFailedToLoadCallback_m824593173C3D2043FE01BCF014F5BE24E7B567B5(intptr_t ___0_rewardedAdClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_RewardedAdLoadedCallback_m3DC362BDFCB1AD265379A9CBC76D8375CFDBB90E(intptr_t ___0_rewardedAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_RewardedAdPaidEventCallback_m9489600AB1344F172F133DF5DBF95AF426ACFDDB(intptr_t ___0_rewardedAdClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedAdClient_RewardedAdUserDidEarnRewardCallback_mC42585CD1BA6D6B4C7FCDAF2D359C44520A45ACD(intptr_t ___0_rewardedAdClient, char* ___1_rewardType, double ___2_rewardAmount);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_AdDidDismissFullScreenContentCallback_mE22BB0C8B831ED419F10CCD68C8FFD9B9C5BA86B(intptr_t ___0_rewardedInterstitialAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_AdDidRecordClickCallback_mC193623F896D781FA0BD8F7660ED077B884799DF(intptr_t ___0_rewardedInterstitialAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_AdDidRecordImpressionCallback_mA2407C34E2CE9D3D69E2CCAE0BC1B04C9CB9BC0B(intptr_t ___0_rewardedInterstitialAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_AdFailedToPresentFullScreenContentCallback_mBB4D17D0635618D261AE895D6EB9C84E2FD8857A(intptr_t ___0_rewardedInterstitialAdClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_AdWillPresentFullScreenContentCallback_m05307C2245AF2EBDF718D3AFD145ED7EE8C9AF8A(intptr_t ___0_rewardedInterstitialAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdFailedToLoadCallback_mD6D04162DA53B6D47D981FF22C30AB30EFD79386(intptr_t ___0_rewardedInterstitialAdClient, intptr_t ___1_error);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdLoadedCallback_m1C0720EF2B961EB1026C8685A7EED3A10C075E9C(intptr_t ___0_rewardedInterstitialAdClient);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdPaidEventCallback_m6C6ACAF671785A8E5003B95A8FF302756DE51AB8(intptr_t ___0_rewardedInterstitialAdClient, int32_t ___1_precision, int64_t ___2_value, char* ___3_currencyCode);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdUserDidEarnRewardCallback_m7E5CE4E4C3E8286FEBA3FC3E3126C89DFCDCCDA5(intptr_t ___0_rewardedInterstitialAdClient, char* ___1_rewardType, double ___2_rewardAmount);
extern "C" char* DEFAULT_CALL ReversePInvokeWrapper_UniWebViewInterface_ChannelFunc_mF42A990636662A6C18A871C9DDC24EE81683E363(intptr_t ___0_namePtr, intptr_t ___1_methodPtr, intptr_t ___2_parameterPtr);
extern "C" void DEFAULT_CALL ReversePInvokeWrapper_UniWebViewInterface_SendMessage_m10CFE4B67F435945F14982DD593F9D5747FCE86D(intptr_t ___0_namePtr, intptr_t ___1_methodPtr, intptr_t ___2_parameterPtr);
extern "C" void CDECL ReversePInvokeWrapper_UnityTlsContext_CertificateCallback_m8CC672A44A8CCFD2A3EB2D9B38A9A134F6EF706B(void* ___0_userData, unitytls_tlsctx_tF8BBCBFE1E957B846442DED65ECB89BC5307DEAE* ___1_ctx, uint8_t* ___2_cn, intptr_t ___3_cnLen, unitytls_x509name_t8A1108C917795D8FE946B50769ACE51489C7BF5D* ___4_caList, intptr_t ___5_caListLen, unitytls_x509list_ref_t6C5C1CF0B720516A681CB741104A164FD8B3CF17* ___6_chain, unitytls_key_ref_t6BD91D013DF11047C53738FEEB12CE290FDC71A2* ___7_key, unitytls_errorstate_tC926EE4582920BE2C1DB1F3F65619B810D5AB902* ___8_errorState);
extern "C" intptr_t CDECL ReversePInvokeWrapper_UnityTlsContext_ReadCallback_m068A7DC153B3D2C4F6922B46BC5957CF33AE3450(void* ___0_userData, uint8_t* ___1_buffer, intptr_t ___2_bufferLen, unitytls_errorstate_tC926EE4582920BE2C1DB1F3F65619B810D5AB902* ___3_errorState);
extern "C" uint32_t CDECL ReversePInvokeWrapper_UnityTlsContext_VerifyCallback_mC7AE01FAD8A336A6BF11CF8A3EFAC60CF6167E6F(void* ___0_userData, unitytls_x509list_ref_t6C5C1CF0B720516A681CB741104A164FD8B3CF17 ___1_chain, unitytls_errorstate_tC926EE4582920BE2C1DB1F3F65619B810D5AB902* ___2_errorState);
extern "C" intptr_t CDECL ReversePInvokeWrapper_UnityTlsContext_WriteCallback_m74F83CFEE7D8FB7EBD1A1021DCB8945E89382998(void* ___0_userData, uint8_t* ___1_data, intptr_t ___2_bufferLen, unitytls_errorstate_tC926EE4582920BE2C1DB1F3F65619B810D5AB902* ___3_errorState);
extern "C" uint32_t CDECL ReversePInvokeWrapper_UnityTlsProvider_x509verify_callback_mB2465D108005179B9873A37C418CD26A37194E3B(void* ___0_userData, unitytls_x509_ref_t9CEB17766B4144117333AB50379B21A357FA4333 ___1_cert, uint32_t ___2_result, unitytls_errorstate_tC926EE4582920BE2C1DB1F3F65619B810D5AB902* ___3_errorState);


IL2CPP_EXTERN_C const Il2CppMethodPointer g_ReversePInvokeWrapperPointers[];
const Il2CppMethodPointer g_ReversePInvokeWrapperPointers[89] = 
{
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewAppEventCallback_m961A7D38B5CDFE03E7CB567DA13A143BF0B9C146),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewClickRecordedCallback_m449A950F56417901D4841B4324157D395AFBCCC7),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewDidDismissScreenCallback_m8F444EA6690A4B4852F16EE1698BD8239E0A3422),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewDidFailToReceiveAdWithErrorCallback_m881B79719B00F95554EFB2847D8F412D36C8E784),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewDidReceiveAdCallback_mC6E8AE72C73938179EA82C82C1858D3100D3BA49),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewImpressionRecordedCallback_m2138D2ED5C3D61D04FC4218B0A1DB00ABED355B0),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewPaidEventCallback_m50EE64914C1AE4F00E97A8F953A8A66A75C35343),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerBannerClient_AdViewWillPresentScreenCallback_mB936401ECAB92B7C2C34764370520C749F8AEB7E),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_AdDidDismissFullScreenContentCallback_mBCB8F5ABFE5A4B68FBC229E5F9EF7EE1114FBB6E),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_AdDidRecordClickCallback_m66338BF3C281EFC8C5E478298BD1AE7CB5B33532),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_AdDidRecordImpressionCallback_mE9E7F6EAB83157F7B98709543C7326C16524BD07),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_AdFailedToPresentFullScreenContentCallback_mA3965CF1723D40E0468EEE2858E6058B3D4AEDC1),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_AdWillPresentFullScreenContentCallback_mD8AD1251BC2AA490BCA12431D2109FFED6FDCCA0),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialAppEventCallback_m10A1A7DAB44D716011572F3B1D5D7BD38108308C),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialFailedToLoadCallback_mF8B2A4C5D4D2CF214AC4ACD502EE4415E274A570),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialLoadedCallback_mA9104FD23F75575DC4F5B79B8BD97539C68951AF),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AdManagerInterstitialClient_InterstitialPaidEventCallback_mBD64A0D47B00BED4AA447D9041C3B98A9FD699A2),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AdDidDismissFullScreenContentCallback_m95DADA0B0645BC5DA1BF13B89BDE31CE2ACA1A97),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AdDidRecordClickCallback_m21590113039E74F283C820A0350B31D55978C16A),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AdDidRecordImpressionCallback_mB889FA5B2DF8A5EFC68719C09419A71703106667),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AdFailedToPresentFullScreenContentCallback_m2F2F4E90C64E5598EBDA91538950D93304FD1FFE),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AdWillPresentFullScreenContentCallback_m718EC6BEE1B5E4506530D7FEA47D712BD33051DF),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AppOpenAdFailedToLoadCallback_m07DE6D2C26F2A35CD0299220F74CBF154034794E),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AppOpenAdLoadedCallback_m914F892E80437C7C0DA9C88DC9A781AD62C9143C),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_AppOpenAdClient_AppOpenAdPaidEventCallback_mBF5DBB882D67C2F7F55CFC312D026D7FE67BC959),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewClickRecordedCallback_m410068256BE6C93EB73EF90EF663F8DC5F171FCB),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewDidDismissScreenCallback_mE1BCD6689EAA8E50FEFB023E25F5A9142FD6D33C),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewDidFailToReceiveAdWithErrorCallback_m7162FE4424F7EC59777F43C858F52D1E773F9165),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewDidReceiveAdCallback_m5262538F61568E5DE2112841CAF818E4495ADA3A),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewImpressionRecordedCallback_m070026746D88B3EF1E713AD19CC56685F4AEE44F),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewPaidEventCallback_mF0F545BB0FF6C5DF2CB82D0A8A228EC437CF2264),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_BannerClient_AdViewWillPresentScreenCallback_m0B7567E264EACEEF85413FD0B4493780324CDDC6),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_ConsentFormClient_ConsentFormLoadCompletionHandler_m3161FC8CDF3BDB16015D91FA7ED7035655DE5BFD),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_ConsentFormClient_ConsentFormPresentCompletionHandler_m04F646ACFA0B3B5CDAC0554D1EFAC5EC039F765B),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_ConsentInformationClient_ConsentInfoUpdateCallback_m01A1B5982AE827C301F4BA2A0801A2A3E13577D6),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_CultureInfo_OnCultureInfoChangedInAppX_m407BCFC1029A4485B7B063BC2F3601968C3BE577),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_DeflateStreamNative_UnmanagedRead_m321A2621068F1C9509594A4D8F405F4F12C1CEB3),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_DeflateStreamNative_UnmanagedWrite_mB0AD438266A9DD2813715E8BC90BF07DC7A02F52),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_AdDidDismissFullScreenContentCallback_m9368B493ED3C0A1FE5B1D403962EA2C1EFA60594),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_AdDidRecordClickCallback_m8EFF6466BCE371819D47332EFEDF3CE4CADE688A),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_AdDidRecordImpressionCallback_mE8EB08B25BB6AD515814AB5B7C37A22513D4CEAF),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_AdFailedToPresentFullScreenContentCallback_m6080CC9BEA744B139B2037A71F4E0DC826F33E63),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_AdWillPresentFullScreenContentCallback_m9CB94524846D61C2D8379C6679FDB9AFE00A5B87),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_InterstitialFailedToLoadCallback_m3CD83AC19BB9F0DAD18D65564A644627C5002FB5),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_InterstitialLoadedCallback_mBD773A172B12BF8D1673A8F9127B662901632A9B),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_InterstitialClient_InterstitialPaidEventCallback_mDF3BCA83163459BC5B07EF0CACEBC299355DDEEC),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MobileAdsClient_AdInspectorClosedCallback_m286A0DEA80E15143D7EB2C4A030E48F24F85A34F),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MobileAdsClient_InitializationCompleteCallback_mD93EF83AC007EB4ABC8DE202F97C9CDDCA7C7830),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsBioMono_Control_mA7B1C493171314C6F02CAC5F73EB7CB94B86ED78),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsBioMono_OnRead_m8888AD0EA2D55D9FFE183BC63D4023BC621941F9),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsBioMono_OnWrite_mD90183BD125599306AAF4E74E941A3983027C575),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsSsl_PrintErrorsCallback_m93ED1F9335C5974459F0915323C5527382A83959),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsSslCtx_NativeSelectCallback_m5A906B70CBEC53FE7F4208810264D78D4AA26EE4),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsSslCtx_NativeServerNameCallback_mC76D35B6D2668F93E280D35B5560A06571810857),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsSslCtx_NativeVerifyCallback_m13C23EDCF9CDADF430C5A1664D33DC7621974916),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_MonoBtlsX509LookupMono_OnGetBySubject_m85B0F4B20C12F67DE4CD9521EC58308C9A27BA24),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_AdDidRecordClickCallback_m632E829B9AB6A053F2986694906581284D0274C6),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_AdDidRecordImpressionCallback_m5C9BA274C4BC326BB04403C09862E546FBF5BA04),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_NativeAdDidDismissScreenCallback_mB03FBEFEE85B88DCAE6D697ED384C3914DB97420),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_NativeAdWillPresentScreenCallback_mD46EA2137ABB06B67F659115A4A23EA79BDE5C60),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_NativeFailedToLoadCallback_m430AE3BFFA9134C335DB34C8CAEE2585680B02C9),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_NativeLoadedCallback_m76E3DA9C9CCEE49B4BF34BBFC2796F96CB123BF2),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_NativeOverlayAdClient_NativePaidEventCallback_m21C6648A632B9F78515E05BE472DCA5DD4190493),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_OSSpecificSynchronizationContext_InvocationEntry_mF93C3CF6DBEC86E377576D840CAF517CB6E4D7E3),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_AdDidDismissFullScreenContentCallback_mD40A55587745226F6496F8555470851B0DAF6D4C),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_AdDidRecordClickCallback_mE8AB77B03ECB290FF64D298EA3B7B0A7B215F877),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_AdDidRecordImpressionCallback_mC13F26EF8A563B25B2D19C82ED0C3DE1B1A26DA1),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_AdFailedToPresentFullScreenContentCallback_m014215CE005401E59F4248C412EC4E19A8ED0D37),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_AdWillPresentFullScreenContentCallback_mCE88BD260C0B76E861442A388BDCC8E43C385518),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_RewardedAdFailedToLoadCallback_m824593173C3D2043FE01BCF014F5BE24E7B567B5),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_RewardedAdLoadedCallback_m3DC362BDFCB1AD265379A9CBC76D8375CFDBB90E),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_RewardedAdPaidEventCallback_m9489600AB1344F172F133DF5DBF95AF426ACFDDB),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedAdClient_RewardedAdUserDidEarnRewardCallback_mC42585CD1BA6D6B4C7FCDAF2D359C44520A45ACD),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_AdDidDismissFullScreenContentCallback_mE22BB0C8B831ED419F10CCD68C8FFD9B9C5BA86B),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_AdDidRecordClickCallback_mC193623F896D781FA0BD8F7660ED077B884799DF),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_AdDidRecordImpressionCallback_mA2407C34E2CE9D3D69E2CCAE0BC1B04C9CB9BC0B),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_AdFailedToPresentFullScreenContentCallback_mBB4D17D0635618D261AE895D6EB9C84E2FD8857A),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_AdWillPresentFullScreenContentCallback_m05307C2245AF2EBDF718D3AFD145ED7EE8C9AF8A),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdFailedToLoadCallback_mD6D04162DA53B6D47D981FF22C30AB30EFD79386),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdLoadedCallback_m1C0720EF2B961EB1026C8685A7EED3A10C075E9C),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdPaidEventCallback_m6C6ACAF671785A8E5003B95A8FF302756DE51AB8),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_RewardedInterstitialAdClient_RewardedInterstitialAdUserDidEarnRewardCallback_m7E5CE4E4C3E8286FEBA3FC3E3126C89DFCDCCDA5),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UniWebViewInterface_ChannelFunc_mF42A990636662A6C18A871C9DDC24EE81683E363),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UniWebViewInterface_SendMessage_m10CFE4B67F435945F14982DD593F9D5747FCE86D),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UnityTlsContext_CertificateCallback_m8CC672A44A8CCFD2A3EB2D9B38A9A134F6EF706B),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UnityTlsContext_ReadCallback_m068A7DC153B3D2C4F6922B46BC5957CF33AE3450),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UnityTlsContext_VerifyCallback_mC7AE01FAD8A336A6BF11CF8A3EFAC60CF6167E6F),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UnityTlsContext_WriteCallback_m74F83CFEE7D8FB7EBD1A1021DCB8945E89382998),
	reinterpret_cast<Il2CppMethodPointer>(ReversePInvokeWrapper_UnityTlsProvider_x509verify_callback_mB2465D108005179B9873A37C418CD26A37194E3B),
};
