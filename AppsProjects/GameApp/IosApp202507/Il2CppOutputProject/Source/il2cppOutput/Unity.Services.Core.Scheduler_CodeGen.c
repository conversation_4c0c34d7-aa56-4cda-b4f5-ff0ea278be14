﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ActionScheduler__ctor_m65721521873C0894B75820E4DC934F47E068495D (void);
extern void ActionScheduler__ctor_m3F34A80C3965C10463085599131EA2A7A488C0AF (void);
extern void ActionScheduler_ExecuteExpiredActions_mB683F55950DD543D90A479DF7E68539969F2B48C (void);
extern void ActionScheduler_UpdateCurrentPlayerLoopWith_m32DBF6F37F3D8776484265E95830FEFA40C6A230 (void);
extern void ActionScheduler_JoinPlayerLoopSystem_mB998001E51285B4DF2406DE197A3A5377BD62892 (void);
extern void MinimumBinaryHeap__ctor_mC63658A36EB912677E6346A2991932B83CED0B5F (void);
extern void ScheduledInvocationComparer_Compare_m288DA5B3B9D15E393A57ADABBADD311975DD9FE4 (void);
extern void ScheduledInvocationComparer__ctor_mBC023A46362D70B10A4FB65D8C0BB653C9F5F972 (void);
extern void UtcTimeProvider_get_Now_m2BBC0DB7893BDC8457768D897F441264BA779A3F (void);
extern void UtcTimeProvider__ctor_m0B5F5EE010F7BDBED40E139D11D931E90D82DFE2 (void);
static Il2CppMethodPointer s_methodPointers[29] = 
{
	ActionScheduler__ctor_m65721521873C0894B75820E4DC934F47E068495D,
	ActionScheduler__ctor_m3F34A80C3965C10463085599131EA2A7A488C0AF,
	ActionScheduler_ExecuteExpiredActions_mB683F55950DD543D90A479DF7E68539969F2B48C,
	ActionScheduler_UpdateCurrentPlayerLoopWith_m32DBF6F37F3D8776484265E95830FEFA40C6A230,
	ActionScheduler_JoinPlayerLoopSystem_mB998001E51285B4DF2406DE197A3A5377BD62892,
	MinimumBinaryHeap__ctor_mC63658A36EB912677E6346A2991932B83CED0B5F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ScheduledInvocationComparer_Compare_m288DA5B3B9D15E393A57ADABBADD311975DD9FE4,
	ScheduledInvocationComparer__ctor_mBC023A46362D70B10A4FB65D8C0BB653C9F5F972,
	NULL,
	UtcTimeProvider_get_Now_m2BBC0DB7893BDC8457768D897F441264BA779A3F,
	UtcTimeProvider__ctor_m0B5F5EE010F7BDBED40E139D11D931E90D82DFE2,
};
static const int32_t s_InvokerIndices[29] = 
{
	7120,
	5806,
	7120,
	9631,
	7120,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	2474,
	7120,
	0,
	6910,
	7120,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x02000004, { 0, 32 } },
};
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_tAE8A44D159438805EBA81C4D10A74006C6254908;
extern const uint32_t g_rgctx_TU5BU5D_t65C250DB0F0FAE0321F9134BF5AC9A8878BF4EAE;
extern const uint32_t g_rgctx_T_tCFFC03C1364E25C2328A320EF410AB74F0782511;
extern const uint32_t g_rgctx_IComparer_1_t4DCF840E7B05E61D8EFE8C3D7F2E7089E4958E30;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1__ctor_m31BBA88BB9A400B81FBBDB76F5F0D54ED7DEED3F;
extern const uint32_t g_rgctx_ICollection_1_t30A41E614327F53F72A8D285CD2E7B9967190BED;
extern const uint32_t g_rgctx_ICollection_1_get_Count_m32F2AAFF23B47E235CF46C96E8ED36B9EB179465;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_set_Count_m85D22B71113F3DFF95BEF311A6901062EFB4A116;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_get_Count_m5AD784ADCB546F17609BD8140FAF243EC6B03D6B;
extern const uint32_t g_rgctx_TU5BU5D_t65C250DB0F0FAE0321F9134BF5AC9A8878BF4EAE;
extern const uint32_t g_rgctx_IEnumerable_1_t81FDE598819AA86875C5B4DC344FBA8507B93B0F;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m58E38F1CB91CAA1E2AD8F152ACB0CE43FB2F2912;
extern const uint32_t g_rgctx_IEnumerator_1_tC7D3D5D7EAB211503E1E4D47774413FE7D3BB263;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m797A43200D52713C70D74CAA5C70EF4E3E5708A7;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_Insert_mEAC8215594BEB7D2E3D69901D0F66C10E4687F92;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_IncreaseHeapCapacityWhenFull_mDFB8DD3B67384018DD1CD42098CC409E2881E019;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_GetParentIndex_m3D366BB121EC6A40DBDAB9CF5C21F0794BDC6C8F;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_tAE8A44D159438805EBA81C4D10A74006C6254908;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_Swap_mC0D94006763E0516180AFB13BE6B142ED236837B;
extern const uint32_t g_rgctx_TU26_t00EF80003B0160A9C285F6CCA71616996342547F;
extern const uint32_t g_rgctx_IComparer_1_Compare_mB823A4DB35DCE5D8B21F77C67B2F322E86CC5383;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_IndexOf_m4CCBC5DAA409F46C17530209453DDB31C3C9EE33;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_ExtractMin_m0C940A8A3FF13C3BAEE8D7177AA8EC4D3D1928E0;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tCFFC03C1364E25C2328A320EF410AB74F0782511_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_MinHeapify_m8C0E56388BBBD98295074C8DDF5D0EE5DEBC2B73;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_DecreaseHeapCapacityWhenSpare_mD8B5D7DBB5C49DE9554D305C7A6E8CC772A09A93;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass21_0_tE1206A21F2A94612F92D334CAAC07CAC1D68CD31;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_U3CMinHeapifyU3Eg__UpdateSmallestIndexU7C21_0_m4D1D68E9EE8DEF4A1626056B7A0386D7F38F2E59;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass21_0U26_t199ADC6E0DD9E9F3A4503D7E64EFDDBEEBE217C6;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_GetLeftChildIndex_m4A9C58FB59C892E89EE62130F7C8E553824E497C;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_GetRightChildIndex_mC0B76F4E4205FD1E2AE673524EE1634561A91473;
extern const uint32_t g_rgctx_MinimumBinaryHeap_1_U3CMinHeapifyU3Eg__UpdateSmallestIfCandidateIsSmallerU7C21_1_mB2A994D93A5F66E0E262F640E917D8EFBE841D8F;
static const Il2CppRGCTXDefinition s_rgctxValues[32] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MinimumBinaryHeap_1_tAE8A44D159438805EBA81C4D10A74006C6254908 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t65C250DB0F0FAE0321F9134BF5AC9A8878BF4EAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCFFC03C1364E25C2328A320EF410AB74F0782511 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_t4DCF840E7B05E61D8EFE8C3D7F2E7089E4958E30 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1__ctor_m31BBA88BB9A400B81FBBDB76F5F0D54ED7DEED3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t30A41E614327F53F72A8D285CD2E7B9967190BED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_m32F2AAFF23B47E235CF46C96E8ED36B9EB179465 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_set_Count_m85D22B71113F3DFF95BEF311A6901062EFB4A116 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_get_Count_m5AD784ADCB546F17609BD8140FAF243EC6B03D6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t65C250DB0F0FAE0321F9134BF5AC9A8878BF4EAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t81FDE598819AA86875C5B4DC344FBA8507B93B0F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m58E38F1CB91CAA1E2AD8F152ACB0CE43FB2F2912 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tC7D3D5D7EAB211503E1E4D47774413FE7D3BB263 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m797A43200D52713C70D74CAA5C70EF4E3E5708A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_Insert_mEAC8215594BEB7D2E3D69901D0F66C10E4687F92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_IncreaseHeapCapacityWhenFull_mDFB8DD3B67384018DD1CD42098CC409E2881E019 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_GetParentIndex_m3D366BB121EC6A40DBDAB9CF5C21F0794BDC6C8F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MinimumBinaryHeap_1_tAE8A44D159438805EBA81C4D10A74006C6254908 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_Swap_mC0D94006763E0516180AFB13BE6B142ED236837B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t00EF80003B0160A9C285F6CCA71616996342547F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IComparer_1_Compare_mB823A4DB35DCE5D8B21F77C67B2F322E86CC5383 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_IndexOf_m4CCBC5DAA409F46C17530209453DDB31C3C9EE33 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_ExtractMin_m0C940A8A3FF13C3BAEE8D7177AA8EC4D3D1928E0 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tCFFC03C1364E25C2328A320EF410AB74F0782511_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_MinHeapify_m8C0E56388BBBD98295074C8DDF5D0EE5DEBC2B73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_DecreaseHeapCapacityWhenSpare_mD8B5D7DBB5C49DE9554D305C7A6E8CC772A09A93 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass21_0_tE1206A21F2A94612F92D334CAAC07CAC1D68CD31 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_U3CMinHeapifyU3Eg__UpdateSmallestIndexU7C21_0_m4D1D68E9EE8DEF4A1626056B7A0386D7F38F2E59 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass21_0U26_t199ADC6E0DD9E9F3A4503D7E64EFDDBEEBE217C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_GetLeftChildIndex_m4A9C58FB59C892E89EE62130F7C8E553824E497C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_GetRightChildIndex_mC0B76F4E4205FD1E2AE673524EE1634561A91473 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MinimumBinaryHeap_1_U3CMinHeapifyU3Eg__UpdateSmallestIfCandidateIsSmallerU7C21_1_mB2A994D93A5F66E0E262F640E917D8EFBE841D8F },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Services_Core_Scheduler_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Services_Core_Scheduler_CodeGenModule = 
{
	"Unity.Services.Core.Scheduler.dll",
	29,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	32,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
