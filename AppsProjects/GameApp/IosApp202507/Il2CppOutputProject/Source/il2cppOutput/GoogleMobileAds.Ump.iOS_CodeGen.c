﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* ConsentFormClient_ConsentFormLoadCompletionHandler_m3161FC8CDF3BDB16015D91FA7ED7035655DE5BFD_RuntimeMethod_var;
extern const RuntimeMethod* ConsentFormClient_ConsentFormPresentCompletionHandler_m04F646ACFA0B3B5CDAC0554D1EFAC5EC039F765B_RuntimeMethod_var;
extern const RuntimeMethod* ConsentInformationClient_ConsentInfoUpdateCallback_m01A1B5982AE827C301F4BA2A0801A2A3E13577D6_RuntimeMethod_var;



extern void ConsentFormClient__ctor_mC1EEA93DCAF7CE4FA1EC79FE00241D914A6B3AAE (void);
extern void ConsentFormClient_get_Instance_mA3644D237BAED202A6035C3B04C15871D26E36F1 (void);
extern void ConsentFormClient_Load_m3940F0990769CAF8E68C10C4CEB00A75EB578812 (void);
extern void ConsentFormClient_Show_m0027D3151786645885B08685CFDED4C8F549203B (void);
extern void ConsentFormClient_LoadAndShowConsentFormIfRequired_m163BC0C744D22097B175EDEC6BAFC043C9B0B795 (void);
extern void ConsentFormClient_ShowPrivacyOptionsForm_m917D5BD9A2BD58D83DB2ACB4133B312C61C653A9 (void);
extern void ConsentFormClient_ConsentFormLoadCompletionHandler_m3161FC8CDF3BDB16015D91FA7ED7035655DE5BFD (void);
extern void ConsentFormClient_ConsentFormPresentCompletionHandler_m04F646ACFA0B3B5CDAC0554D1EFAC5EC039F765B (void);
extern void ConsentFormClient_IntPtrToConsentFormClient_mB8BFD0B1074AFC85277F47ED61F7543E39D4C448 (void);
extern void ConsentFormClient_Dispose_mAD8DB765B7A9C642BAEEAAE6980D4691C762343E (void);
extern void ConsentFormClient_Finalize_m01D399787813BD345E4839C4C5CD5F0CB007D305 (void);
extern void ConsentFormClient__cctor_m68BC343BDCD3B60074410B4BE93C7BEB7413A768 (void);
extern void GADUConsentFormLoadCompletionHandler__ctor_m73C10AD0211F66C303676C205F697F25E4D00079 (void);
extern void GADUConsentFormLoadCompletionHandler_Invoke_m0ECD08D6196C89823EC5BD5D01FE9C2866750A80 (void);
extern void GADUConsentFormLoadCompletionHandler_BeginInvoke_mBCFC7FF4A05C5187A1A001D5828887BBDADB9BFC (void);
extern void GADUConsentFormLoadCompletionHandler_EndInvoke_m0DA7CFCFD6C5077706267B15467B4E6B054F0DAC (void);
extern void GADUConsentFormPresentCompletionHandler__ctor_mE8A485414387552E135CBFB0850AA971BA665F4D (void);
extern void GADUConsentFormPresentCompletionHandler_Invoke_mC69F0A2FC468CF52B09BCF4F2CE84139FB3CB258 (void);
extern void GADUConsentFormPresentCompletionHandler_BeginInvoke_mB22E9CC983737AF39D4E97169F02D170F1C23946 (void);
extern void GADUConsentFormPresentCompletionHandler_EndInvoke_m5B9DBC2B5FA37CF0AE844A83E9AB13F77395EA3F (void);
extern void ConsentInformationClient__ctor_mA4272704F07B4B3CC88339FB36187A49C94ADF82 (void);
extern void ConsentInformationClient_get_ConsentInformationPtr_m67A4ED0E10DF4DB7B9E2A1643225177905DD06C5 (void);
extern void ConsentInformationClient_set_ConsentInformationPtr_mBA0AF74DDCD02983BCFADFA8907E4144332D7823 (void);
extern void ConsentInformationClient_get_Instance_m4F33F30EFF31A02F7DCF81826BA7C4BCEE5A7A08 (void);
extern void ConsentInformationClient_Update_mCD49A6432843EE1F3EC248A386B6E3768D564183 (void);
extern void ConsentInformationClient_Reset_m33D05624221D6CB935EE36964C4BFFB99873C5D3 (void);
extern void ConsentInformationClient_GetConsentStatus_mA0ED9529AE12E1AC62492A26304BEF9F3A5DF492 (void);
extern void ConsentInformationClient_GetPrivacyOptionsRequirementStatus_mDA19820BD4C64967E245541F55513BD40F13F8B2 (void);
extern void ConsentInformationClient_CanRequestAds_m763076896786F8C9EAFEFBD2C06F86B0EC9AD958 (void);
extern void ConsentInformationClient_IsConsentFormAvailable_mD4D908AC971567F63970100C3A476D67C4FF4385 (void);
extern void ConsentInformationClient_ConsentInfoUpdateCallback_m01A1B5982AE827C301F4BA2A0801A2A3E13577D6 (void);
extern void ConsentInformationClient_IntPtrToConsentInformationClient_m7ADD9E4D86678379FB4E89C7BAB887E855609B5F (void);
extern void ConsentInformationClient_Dispose_mC49216E7F4398541A5E8D83C9B08DE9DFACE48F9 (void);
extern void ConsentInformationClient_Finalize_m34691F9551F3D7E1589E9C71FBD5F8B5ADDF1D30 (void);
extern void ConsentInformationClient__cctor_mD8CC9E5335DC768DEEE2A843907ABE64A51EC3B7 (void);
extern void GADUConsentInfoUpdateCallback__ctor_m5F3AD20929FE57E93270BCE0EFEADFAEA41812B3 (void);
extern void GADUConsentInfoUpdateCallback_Invoke_m2F8DB1303E06B75C396BA37D288E6E32BEF4E138 (void);
extern void GADUConsentInfoUpdateCallback_BeginInvoke_m5C631FC2C39183EA8FB01FFA9A61677A77134878 (void);
extern void GADUConsentInfoUpdateCallback_EndInvoke_mF0918A41C381886B02D7750CB93B160CD6853198 (void);
extern void Externs__ctor_mD45431FDBE09B3540B3F0270F5169AFBA758DE66 (void);
extern void Externs_GADUGetFormErrorCode_mCED0FCF2EFCF807A812A2698A1F928594B8B83AF (void);
extern void Externs_GADUGetFormErrorMessage_mC85893CCE27D7CB289FD724D0AE4A65CFE3732CB (void);
extern void Externs_GADUCreateRequestParameters_mA0DB2EA8850345665565D7C28D5EB06F25DCD9B5 (void);
extern void Externs_GADUSetRequestParametersTagForUnderAgeOfConsent_m21898F9CEC922FDE18A9AF55842C52E9C3755F55 (void);
extern void Externs_GADUCreateDebugSettings_mB7A04EAAA7CAEB0DB94B9054B8F69389C2A1D457 (void);
extern void Externs_GADUSetDebugSettingsDebugGeography_m724BFF876907DF87B9AA06D0BFB87AB59018772F (void);
extern void Externs_GADUSetDebugSettingsTestDeviceIdentifiers_m19F3AEECF8B6CB1F8B5931E6ABBB5C2570564847 (void);
extern void Externs_GADUSetRequestParametersDebugSettings_m710CF368AC20FCBF1FEB0EA5578136B32D51CB93 (void);
extern void Externs_GADUCreateConsentInformation_m40445E013F2C25A5030190929D07FA5204386C35 (void);
extern void Externs_GADUResetConsentInformation_m23FCECCE4CACCE15CD5B5A307F2B404BF64D6D66 (void);
extern void Externs_GADUGetConsentStatus_mD9905A66A5060741B894A9C2D7D00ECD9E36532D (void);
extern void Externs_GADUGetPrivacyOptionsRequirementStatus_m90D8398D2477241865AEC8D625B8CFFA0189595B (void);
extern void Externs_GADUUMPCanRequestAds_mA71DB43F21052850C260457704A042502C688E36 (void);
extern void Externs_GADUIsConsentFormAvailable_mF1F363401873F80C010E475B0E60685796DED593 (void);
extern void Externs_GADURequestConsentInfoUpdate_mD77603CF4CE1EB7438348E93A601A042274364F4 (void);
extern void Externs_GADUCreateConsentForm_m4EBB46674010C2BDB73AC7D2D407C483D5664EE3 (void);
extern void Externs_GADULoadConsentForm_m6852D39910D8F09C3CD3EF0CA28B85CA5F0FB878 (void);
extern void Externs_GADUPresentConsentForm_mCC8114769B778D5CC8F449AFE2925ACEFC55C487 (void);
extern void Externs_GADULoadAndPresentConsentForm_mCC529F231B582994E6A3376C403BA9040AE47607 (void);
extern void Externs_GADUPresentPrivacyOptionsForm_mEF4655C87CF478A815BEBBDFA2E3CAFF5183893F (void);
extern void Externs_GADURelease_mD645D7AE2BD9355B6F45F8E0F59FA4FA8BFF0100 (void);
extern void UmpClientFactory__ctor_m5C0430D964888036FFE3EFEEB4BFEF01975F8C01 (void);
extern void UmpClientFactory_ConsentFormClient_mADABD934A32673E760D2B45A4B246DE52D3F01A9 (void);
extern void UmpClientFactory_ConsentInformationClient_mC8BCFC29B4D19B11F8AE4E1AAE25C940CDCDAFBA (void);
static Il2CppMethodPointer s_methodPointers[64] = 
{
	ConsentFormClient__ctor_mC1EEA93DCAF7CE4FA1EC79FE00241D914A6B3AAE,
	ConsentFormClient_get_Instance_mA3644D237BAED202A6035C3B04C15871D26E36F1,
	ConsentFormClient_Load_m3940F0990769CAF8E68C10C4CEB00A75EB578812,
	ConsentFormClient_Show_m0027D3151786645885B08685CFDED4C8F549203B,
	ConsentFormClient_LoadAndShowConsentFormIfRequired_m163BC0C744D22097B175EDEC6BAFC043C9B0B795,
	ConsentFormClient_ShowPrivacyOptionsForm_m917D5BD9A2BD58D83DB2ACB4133B312C61C653A9,
	ConsentFormClient_ConsentFormLoadCompletionHandler_m3161FC8CDF3BDB16015D91FA7ED7035655DE5BFD,
	ConsentFormClient_ConsentFormPresentCompletionHandler_m04F646ACFA0B3B5CDAC0554D1EFAC5EC039F765B,
	ConsentFormClient_IntPtrToConsentFormClient_mB8BFD0B1074AFC85277F47ED61F7543E39D4C448,
	ConsentFormClient_Dispose_mAD8DB765B7A9C642BAEEAAE6980D4691C762343E,
	ConsentFormClient_Finalize_m01D399787813BD345E4839C4C5CD5F0CB007D305,
	ConsentFormClient__cctor_m68BC343BDCD3B60074410B4BE93C7BEB7413A768,
	GADUConsentFormLoadCompletionHandler__ctor_m73C10AD0211F66C303676C205F697F25E4D00079,
	GADUConsentFormLoadCompletionHandler_Invoke_m0ECD08D6196C89823EC5BD5D01FE9C2866750A80,
	GADUConsentFormLoadCompletionHandler_BeginInvoke_mBCFC7FF4A05C5187A1A001D5828887BBDADB9BFC,
	GADUConsentFormLoadCompletionHandler_EndInvoke_m0DA7CFCFD6C5077706267B15467B4E6B054F0DAC,
	GADUConsentFormPresentCompletionHandler__ctor_mE8A485414387552E135CBFB0850AA971BA665F4D,
	GADUConsentFormPresentCompletionHandler_Invoke_mC69F0A2FC468CF52B09BCF4F2CE84139FB3CB258,
	GADUConsentFormPresentCompletionHandler_BeginInvoke_mB22E9CC983737AF39D4E97169F02D170F1C23946,
	GADUConsentFormPresentCompletionHandler_EndInvoke_m5B9DBC2B5FA37CF0AE844A83E9AB13F77395EA3F,
	ConsentInformationClient__ctor_mA4272704F07B4B3CC88339FB36187A49C94ADF82,
	ConsentInformationClient_get_ConsentInformationPtr_m67A4ED0E10DF4DB7B9E2A1643225177905DD06C5,
	ConsentInformationClient_set_ConsentInformationPtr_mBA0AF74DDCD02983BCFADFA8907E4144332D7823,
	ConsentInformationClient_get_Instance_m4F33F30EFF31A02F7DCF81826BA7C4BCEE5A7A08,
	ConsentInformationClient_Update_mCD49A6432843EE1F3EC248A386B6E3768D564183,
	ConsentInformationClient_Reset_m33D05624221D6CB935EE36964C4BFFB99873C5D3,
	ConsentInformationClient_GetConsentStatus_mA0ED9529AE12E1AC62492A26304BEF9F3A5DF492,
	ConsentInformationClient_GetPrivacyOptionsRequirementStatus_mDA19820BD4C64967E245541F55513BD40F13F8B2,
	ConsentInformationClient_CanRequestAds_m763076896786F8C9EAFEFBD2C06F86B0EC9AD958,
	ConsentInformationClient_IsConsentFormAvailable_mD4D908AC971567F63970100C3A476D67C4FF4385,
	ConsentInformationClient_ConsentInfoUpdateCallback_m01A1B5982AE827C301F4BA2A0801A2A3E13577D6,
	ConsentInformationClient_IntPtrToConsentInformationClient_m7ADD9E4D86678379FB4E89C7BAB887E855609B5F,
	ConsentInformationClient_Dispose_mC49216E7F4398541A5E8D83C9B08DE9DFACE48F9,
	ConsentInformationClient_Finalize_m34691F9551F3D7E1589E9C71FBD5F8B5ADDF1D30,
	ConsentInformationClient__cctor_mD8CC9E5335DC768DEEE2A843907ABE64A51EC3B7,
	GADUConsentInfoUpdateCallback__ctor_m5F3AD20929FE57E93270BCE0EFEADFAEA41812B3,
	GADUConsentInfoUpdateCallback_Invoke_m2F8DB1303E06B75C396BA37D288E6E32BEF4E138,
	GADUConsentInfoUpdateCallback_BeginInvoke_m5C631FC2C39183EA8FB01FFA9A61677A77134878,
	GADUConsentInfoUpdateCallback_EndInvoke_mF0918A41C381886B02D7750CB93B160CD6853198,
	Externs__ctor_mD45431FDBE09B3540B3F0270F5169AFBA758DE66,
	Externs_GADUGetFormErrorCode_mCED0FCF2EFCF807A812A2698A1F928594B8B83AF,
	Externs_GADUGetFormErrorMessage_mC85893CCE27D7CB289FD724D0AE4A65CFE3732CB,
	Externs_GADUCreateRequestParameters_mA0DB2EA8850345665565D7C28D5EB06F25DCD9B5,
	Externs_GADUSetRequestParametersTagForUnderAgeOfConsent_m21898F9CEC922FDE18A9AF55842C52E9C3755F55,
	Externs_GADUCreateDebugSettings_mB7A04EAAA7CAEB0DB94B9054B8F69389C2A1D457,
	Externs_GADUSetDebugSettingsDebugGeography_m724BFF876907DF87B9AA06D0BFB87AB59018772F,
	Externs_GADUSetDebugSettingsTestDeviceIdentifiers_m19F3AEECF8B6CB1F8B5931E6ABBB5C2570564847,
	Externs_GADUSetRequestParametersDebugSettings_m710CF368AC20FCBF1FEB0EA5578136B32D51CB93,
	Externs_GADUCreateConsentInformation_m40445E013F2C25A5030190929D07FA5204386C35,
	Externs_GADUResetConsentInformation_m23FCECCE4CACCE15CD5B5A307F2B404BF64D6D66,
	Externs_GADUGetConsentStatus_mD9905A66A5060741B894A9C2D7D00ECD9E36532D,
	Externs_GADUGetPrivacyOptionsRequirementStatus_m90D8398D2477241865AEC8D625B8CFFA0189595B,
	Externs_GADUUMPCanRequestAds_mA71DB43F21052850C260457704A042502C688E36,
	Externs_GADUIsConsentFormAvailable_mF1F363401873F80C010E475B0E60685796DED593,
	Externs_GADURequestConsentInfoUpdate_mD77603CF4CE1EB7438348E93A601A042274364F4,
	Externs_GADUCreateConsentForm_m4EBB46674010C2BDB73AC7D2D407C483D5664EE3,
	Externs_GADULoadConsentForm_m6852D39910D8F09C3CD3EF0CA28B85CA5F0FB878,
	Externs_GADUPresentConsentForm_mCC8114769B778D5CC8F449AFE2925ACEFC55C487,
	Externs_GADULoadAndPresentConsentForm_mCC529F231B582994E6A3376C403BA9040AE47607,
	Externs_GADUPresentPrivacyOptionsForm_mEF4655C87CF478A815BEBBDFA2E3CAFF5183893F,
	Externs_GADURelease_mD645D7AE2BD9355B6F45F8E0F59FA4FA8BFF0100,
	UmpClientFactory__ctor_m5C0430D964888036FFE3EFEEB4BFEF01975F8C01,
	UmpClientFactory_ConsentFormClient_mADABD934A32673E760D2B45A4B246DE52D3F01A9,
	UmpClientFactory_ConsentInformationClient_mC8BCFC29B4D19B11F8AE4E1AAE25C940CDCDAFBA,
};
static const int32_t s_InvokerIndices[64] = 
{
	7120,
	10420,
	3363,
	5806,
	5806,
	5806,
	9586,
	9586,
	10053,
	7120,
	7120,
	10455,
	3361,
	3331,
	1109,
	5806,
	3361,
	3331,
	1109,
	5806,
	7120,
	6960,
	5777,
	10420,
	1909,
	7120,
	6957,
	6957,
	6887,
	6887,
	9586,
	10053,
	7120,
	7120,
	10455,
	3361,
	3331,
	1109,
	5806,
	7120,
	9940,
	10053,
	10414,
	9584,
	10414,
	9585,
	8930,
	9586,
	9972,
	10290,
	9940,
	9940,
	9833,
	9833,
	8926,
	9972,
	9587,
	9587,
	9587,
	9587,
	10290,
	7120,
	6992,
	6992,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[3] = 
{
	{ 0x06000007, 32,  (void**)&ConsentFormClient_ConsentFormLoadCompletionHandler_m3161FC8CDF3BDB16015D91FA7ED7035655DE5BFD_RuntimeMethod_var, 0 },
	{ 0x06000008, 33,  (void**)&ConsentFormClient_ConsentFormPresentCompletionHandler_m04F646ACFA0B3B5CDAC0554D1EFAC5EC039F765B_RuntimeMethod_var, 0 },
	{ 0x0600001F, 34,  (void**)&ConsentInformationClient_ConsentInfoUpdateCallback_m01A1B5982AE827C301F4BA2A0801A2A3E13577D6_RuntimeMethod_var, 0 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Ump_iOS_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Ump_iOS_CodeGenModule = 
{
	"GoogleMobileAds.Ump.iOS.dll",
	64,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	3,
	s_reversePInvokeIndices,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
