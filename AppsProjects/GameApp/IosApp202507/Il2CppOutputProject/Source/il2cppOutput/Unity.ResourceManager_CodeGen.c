﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m9E9B7C95C8B9EF234AB1705D208AE318A486E26E (void);
extern void IsUnmanagedAttribute__ctor_mB4C2A08461C05B21855918E4E33E1EEC97B9D51A (void);
extern void MonoBehaviourCallbackHooks_add_OnUpdateDelegate_m75B55B30965281C86C4A349DFBE713833E74853C (void);
extern void MonoBehaviourCallbackHooks_remove_OnUpdateDelegate_mE887431AD33057B98989EEA888B2342D18FB5DE4 (void);
extern void MonoBehaviourCallbackHooks_add_OnLateUpdateDelegate_mBC77E034EF867B93E17C722BE198B047AD17C277 (void);
extern void MonoBehaviourCallbackHooks_remove_OnLateUpdateDelegate_m0BB14D3E187A7227D840F06366E3E3B10F49B27A (void);
extern void MonoBehaviourCallbackHooks_GetGameObjectName_m5E0B21C825DC616AC283318401D06368388779FD (void);
extern void MonoBehaviourCallbackHooks_Update_m78B3E22F89771DAF1DF875FA9143E1071C29A1B2 (void);
extern void MonoBehaviourCallbackHooks_LateUpdate_m45A4B8C30A3FB1D56C4B8D8D869A6E2C020F2778 (void);
extern void MonoBehaviourCallbackHooks__ctor_mBC4522B5FA1CA9A734337F48AEDE1A66F2C75796 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mA0D7683C73544BE6BCEF283CD7F1A6A65AE932D2 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m14D67F405BB2F69B9DF9D4DA781F79B7BD947160 (void);
extern void ResourceManager_get_ExceptionHandler_m438ACA3A226A2BB1F49C225927707FC47E3FDFC8 (void);
extern void ResourceManager_set_ExceptionHandler_m852F86350D52609D6CC714E6BADF3B22FA377EC8 (void);
extern void ResourceManager_get_InternalIdTransformFunc_mF9E2EB179C238ED082683966C36F07EDC85F9087 (void);
extern void ResourceManager_set_InternalIdTransformFunc_mFCF127EC19BDB3AD31291C55CFB1242E853F3DC7 (void);
extern void ResourceManager_TransformInternalId_m2C99EDD12AF07F099F7FF88E15892F2582C17EB9 (void);
extern void ResourceManager_get_WebRequestOverride_m5B5944646CCCFEC195AF713DC15F490EDA9EDC12 (void);
extern void ResourceManager_set_WebRequestOverride_m79E2560E6B98E77E402ABDD96308DD8FA284B0EA (void);
extern void ResourceManager_get_OperationCacheCount_m158FEB6EAE44293574466B2F6688AF2E0ECFD486 (void);
extern void ResourceManager_get_InstanceOperationCount_m4D4A04EF0A6E20AFFDDC67BE03FD479874207366 (void);
extern void ResourceManager_get_DeferredCompleteCallbacksCount_m374F1A015F29BA6DCA75462E9A1C93AE9E486527 (void);
extern void ResourceManager_get_DeferredCallbackCount_mA0651DBE0BFC38678DEDBE10F0395D8253FECCD3 (void);
extern void ResourceManager_AddUpdateReceiver_mCEC1DDE2E64507B7210293AC1C5A6E5849AA29B5 (void);
extern void ResourceManager_RemoveUpdateReciever_mABD054857B61EF2D434CE0EC6E5A0C5989AFBB74 (void);
extern void ResourceManager_get_Allocator_m2F215AF545EE1A8EEDEBFBFACA32D5E0B44A74B3 (void);
extern void ResourceManager_set_Allocator_mC26F1C9C4A0495CD597D6731FCAFAE8DC6ECA616 (void);
extern void ResourceManager_get_ResourceProviders_mACBDB6BBAF95684D548E67073ED0067707D87A63 (void);
extern void ResourceManager_get_CertificateHandlerInstance_mFE0A1AC4ACD430F7FFE076B3AAF8DE302E47BDC0 (void);
extern void ResourceManager_set_CertificateHandlerInstance_m0CF67BF1839238F4EC7A4709D419AC11FF338EF4 (void);
extern void ResourceManager__ctor_m40746752A00415417500A9941BD8A55116461702 (void);
extern void ResourceManager_OnObjectAdded_m1B908412F2E8898FB93DDA7F1C476E7FE087FB35 (void);
extern void ResourceManager_OnObjectRemoved_m75B3D837CDC9FD91B3B5345A34415B5129A68038 (void);
extern void ResourceManager_RegisterForCallbacks_m805F04D79BF5BFF608E92C029378AA498A0966E6 (void);
extern void ResourceManager_ClearDiagnosticsCallback_m44780BEA8C1BABFF4FB58794B27949B5DC82CD96 (void);
extern void ResourceManager_ClearDiagnosticCallbacks_m9A2DFAA7BFD3AA6F918E7C6D9E6403C55C50ED7D (void);
extern void ResourceManager_UnregisterDiagnosticCallback_m48C1E860430066DD097E4F662402770803558A84 (void);
extern void ResourceManager_RegisterDiagnosticCallback_mC19F916DA591E4A0DB9B747A19AADFA5B8499B46 (void);
extern void ResourceManager_RegisterDiagnosticCallback_mC40ECD5723511F7C77475A417F693C84C4CC3378 (void);
extern void ResourceManager_PostDiagnosticEvent_m3D762299D2B95B1FC8AE649FDF1D79C6BF4CE67C (void);
extern void ResourceManager_GetResourceProvider_mFE9FDDC3322999023EF072BA071A2C5207993507 (void);
extern void ResourceManager_GetDefaultTypeForLocation_m43F1C1C3F03D24E0C9039A081A921998664C895C (void);
extern void ResourceManager_CalculateLocationsHash_mABD7FA03ADC150005862C7A310CE3B0098DD56DA (void);
extern void ResourceManager_ProvideResource_m601D10E98FA55391A6CF87035D16C5D4D45BD54F (void);
extern void ResourceManager_GetOperationFromCache_mF1A0849D075E13611F475A8A19FC39E20BA7DCEC (void);
extern void ResourceManager_CreateCacheKeyForLocation_m4ED1F69B2AFC87D5B6481DBEBD0963438768C6E7 (void);
extern void ResourceManager_StartOperation_mF353B4A27D8904A5BE0CA88DCECD34BFFF283428 (void);
extern void ResourceManager_OnInstanceOperationDestroy_mEAFE7F84B34E5D426D4185936321DF391E1D77D2 (void);
extern void ResourceManager_OnOperationDestroyNonCached_m7C5078CF3525865387CE15063E1CE2389465985A (void);
extern void ResourceManager_OnOperationDestroyCached_m93E5D09DE26A4D00F1F95375E98D53B2416CFD1B (void);
extern void ResourceManager_AddOperationToCache_m1399C5F2815D81712B2CCC09EA9ADF1EA13CC934 (void);
extern void ResourceManager_RemoveOperationFromCache_m1CB4CDFB4320AE9B7191BCE7F77441274F5A42FD (void);
extern void ResourceManager_IsOperationCached_m2B1EDD504388B6A568DAFC5417F719F85947A660 (void);
extern void ResourceManager_CachedOperationCount_mCADF179F6814BC055FB4D1A0CB0B588A35D71C1A (void);
extern void ResourceManager_ClearOperationCache_mEC0A3D747E2B24F7079771578640443159C96986 (void);
extern void ResourceManager_Release_m4A5F5DE044CDEA3D898AD3D665295E4B2CAC3D37 (void);
extern void ResourceManager_Acquire_mC6890CE5E48C3C5A999E630959F359F2DC6C3FE4 (void);
extern void ResourceManager_AcquireGroupOpFromCache_mD24182CB63E47A7EB10183750BA4038050934A52 (void);
extern void ResourceManager_CreateGenericGroupOperation_mD157A8B840D805E4DE84C4ED116E42807FFC54A1 (void);
extern void ResourceManager_ProvideResourceGroupCached_m4BE9B9B2D6DD75747B7978BC8EC9664FCDBD10C2 (void);
extern void ResourceManager_ProvideScene_m7443C66FADC9A66CA7E073DA080AC25BEB8FAC7F (void);
extern void ResourceManager_ProvideScene_m3442E1C8AC16048A50187756655099995CD2E8A6 (void);
extern void ResourceManager_ReleaseScene_m8AC172F7EC35C2ED4DCC308BDFCB2F9C7FB24CDB (void);
extern void ResourceManager_ProvideInstance_mF1EF383A4217EF54D26F7E414ED03AAE2CBF3D91 (void);
extern void ResourceManager_CleanupSceneInstances_m462175052395535F374CA0A1E1DE52E922504DF4 (void);
extern void ResourceManager_ExecuteDeferredCallbacks_m7BB2468360D4AAF86E437E93E9D52E134AE4AA7F (void);
extern void ResourceManager_RegisterForDeferredCallback_m05889AD5E7D950770177A9B2920B4736546B0225 (void);
extern void ResourceManager_Update_mE18FA475CB6F6B7020A53A51B0FAD323819F7B4C (void);
extern void ResourceManager_Dispose_m850F673638C3ABDBA6B5E6D7A03D5B8DDA0D67B2 (void);
extern void ResourceManager__cctor_m2C88F867A5BFC4F01082256E7775C5459C60577E (void);
extern void ResourceManager_U3C_ctorU3Eb__57_0_mBF579CFFC10E01AFD1B618936D87FFA9DE9C6DD4 (void);
extern void DiagnosticEventContext_get_OperationHandle_mC766CD6C074CD2A0E3486FD41CE0B951B25B3FA6 (void);
extern void DiagnosticEventContext_get_Type_mF65C73B3A0D1958041136121E877B4AF9ECFCEBB (void);
extern void DiagnosticEventContext_get_EventValue_m1109E4CB1BFA64FEDC13FF72B293B68DA2D0A94F (void);
extern void DiagnosticEventContext_get_Location_mEFDEFECC834FF8AA3B9542889DD449BF07A5EF5F (void);
extern void DiagnosticEventContext_get_Context_m8DC1C15D121AD87B29AE6FAF53B6797221E2224B (void);
extern void DiagnosticEventContext_get_Error_m1BCCCD62C4402CD13F6E0E071111AF0BEEA00BD3 (void);
extern void DiagnosticEventContext__ctor_m14E2DE6A37749D242E166A1CA6B94AC0F99D4C51 (void);
extern void InstanceOperation_Init_mFCAECB1B405E9722192270FFFA4DE314CCB29146 (void);
extern void InstanceOperation_GetDownloadStatus_m7EABB0A922CEC00F1001409EE5B3BFEDA21E0D5F (void);
extern void InstanceOperation_GetDependencies_m1076C4940D0C541FE934369210B799C1DA061529 (void);
extern void InstanceOperation_get_DebugName_mDC16EA8E13BCC0A14642F9330A68A3B947833C15 (void);
extern void InstanceOperation_InstanceScene_m673A930720C94D7EC8618855441A78A4D2A576D2 (void);
extern void InstanceOperation_Destroy_m48603106B240748E521EBDF9D396FA0099183648 (void);
extern void InstanceOperation_get_Progress_mD7CACE3A9173DDEF163079227400C09C6C0D1F1F (void);
extern void InstanceOperation_InvokeWaitForCompletion_m7E519B113DA5F45DF89D12AA2FFAF2A808B81FFC (void);
extern void InstanceOperation_Execute_m01F6E265405DD013116E1C90C2B2CD1F713411EE (void);
extern void InstanceOperation__ctor_m850B9D9A3CE6D26FA813A7D617C55DE1C005062E (void);
extern void WebRequestQueueOperation_get_IsDone_mF8B3459F19B874A0D0047DDEA4DCCF2FA0DFF813 (void);
extern void WebRequestQueueOperation_get_WebRequest_mB3094AE07CF5BE54F45DB4EA1F3BC2576AD26062 (void);
extern void WebRequestQueueOperation_set_WebRequest_mC166F25E13697FC277FFE136DED700E6AF1CE943 (void);
extern void WebRequestQueueOperation__ctor_mF6E7DC7A72C723056182035AAFFA7B28AF1F0007 (void);
extern void WebRequestQueueOperation_Complete_m74FC3854CF7B21E72C64FE945830A6250BBD127D (void);
extern void WebRequestQueue_SetMaxConcurrentRequests_mFC18BE6652727D9B95CDB29866D4E51C211D9BD6 (void);
extern void WebRequestQueue_QueueRequest_mB468AF5AEE67CFB938B8A623BFFE0E4A71D97644 (void);
extern void WebRequestQueue_WaitForRequestToBeActive_mC4448801875550E419C6FE8B211CCD02B2CE413A (void);
extern void WebRequestQueue_DequeueRequest_mB13E57C770245EB2F6EB2D54CC9EC00895880657 (void);
extern void WebRequestQueue_OnWebAsyncOpComplete_m63203BDC4450394C87962F4A3AF878C1C6DB7735 (void);
extern void WebRequestQueue_OnWebAsyncOpComplete_mABC4467B1BA3504890D1116198A6EE0622E16A19 (void);
extern void WebRequestQueue_BeginWebRequest_m533B9C62E91DFF7CF0A63894800D1A3A0CB18751 (void);
extern void WebRequestQueue__cctor_mDF1BE18609A3DE04B602056244523D63B06E3E67 (void);
extern void ResourceManagerException__ctor_m100F5F75AE8F119C04634B839871DECF2A9E3773 (void);
extern void ResourceManagerException__ctor_mADA995E60805D9B18593A09BDAAFEF7778D44092 (void);
extern void ResourceManagerException__ctor_mC3F8CD1DEC2E9AB49D0109669BD40D7EFAA6643C (void);
extern void ResourceManagerException__ctor_m1AEBB2A25745FEE316B7A2A7A147D5C075938210 (void);
extern void ResourceManagerException_ToString_m02FFE7C99044E1165624FD1366817B2EDD4093FF (void);
extern void UnknownResourceProviderException_get_Location_mC769291CC27D81B8573117354AB08E222DD68CF8 (void);
extern void UnknownResourceProviderException_set_Location_mC4689284960B91DB49E674BDAF259530C3F4B421 (void);
extern void UnknownResourceProviderException__ctor_mF41159A0C17377B2FA88EEB384873A9E74C0B9D4 (void);
extern void UnknownResourceProviderException__ctor_mF53A3788AA2BC62D3DD8B0480FEF37FE39D58DC7 (void);
extern void UnknownResourceProviderException__ctor_m83BA024FBC7B1044B4F661C70C5D75C5936EC598 (void);
extern void UnknownResourceProviderException__ctor_m44168BF7B02437B561517802A010C3D10C79DFFA (void);
extern void UnknownResourceProviderException__ctor_mEB7F4AAED15D3A44100003D65A4D81C4BFC432F3 (void);
extern void UnknownResourceProviderException_get_Message_m1A75337FDA1BE4FEA28CB84A8C1542034198B91A (void);
extern void UnknownResourceProviderException_ToString_m3515DCC45986154B425A190CCDC6CABA516C5C7D (void);
extern void OperationException__ctor_m86A0486864C0BBA58E2688D3FB650CAB1D616D59 (void);
extern void OperationException_ToString_m8544F18BF5A2A8B6194DBF46F672DB8C128AA34D (void);
extern void ProviderException__ctor_m298FE0E1B736CBE00C44FDC9D6C960F7A927830D (void);
extern void ProviderException_get_Location_m9E9ACD664198BD17DD1CC1E61B8201966C261981 (void);
extern void RemoteProviderException__ctor_mB262F595DD1E23F24A37B5AB6142B8180CFF70C1 (void);
extern void RemoteProviderException_get_Message_m19B282D7E74FAC7DBFECBEB7A57D1B1D9488F7CD (void);
extern void RemoteProviderException_get_WebRequestResult_mF80BDC58604D4D5C44AB968FA544CFD30C330954 (void);
extern void RemoteProviderException_ToString_m4D0CD66BDF24920556E4E8A4F418B457AC696AE4 (void);
extern void BinaryStorageBuffer_ComputeHash_m528E30FBF559FD6F302F7F415507C52E90292E14 (void);
extern void BinaryStorageBuffer_AddSerializationAdapter_m9E562666689E6AA8A3B1BE202655C8D154091445 (void);
extern void BinaryStorageBuffer_GetSerializationAdapter_m926A4AE15D4DF09AAEFA943F4F0996B1B48A7E52 (void);
extern void BinaryStorageBuffer__ctor_m7C91551045C72775B42F5AD205282DD3BD8889B0 (void);
extern void BuiltinTypesSerializer_get_Dependencies_m304075F813F5F1FDB31D5D9906D27A6CEE4FEA12 (void);
extern void BuiltinTypesSerializer_Deserialize_m13587FECB446BF4B75ED1BF275433DF945E73AF7 (void);
extern void BuiltinTypesSerializer_FindBestSeparator_m53A1EF2FF91EB254F7BC621A76BF80553FFD0E19 (void);
extern void BuiltinTypesSerializer_Serialize_mFE61CCB900707102001182E788A7B87BE1AC0227 (void);
extern void BuiltinTypesSerializer__ctor_m3CD66405A9781475B59EC177E67014D793C596ED (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m21D66D59BDCDBA8D1DEC929411CBA518F958281F (void);
extern void U3CU3Ec__DisplayClass4_0_U3CFindBestSeparatorU3Eb__0_mD72C04C02990117C3C8AE3564D0CA797D4D69C41 (void);
extern void TypeSerializer_get_Dependencies_m00EFD21CE8F4781F513F2BC387DAFD3AC97BB082 (void);
extern void TypeSerializer_Deserialize_mB4E74C5726F4C5C668242DF7FBF32B3346B086BA (void);
extern void TypeSerializer_Serialize_m96FC3B549AB21CE3E5A41AC0A70D16761E3DED76 (void);
extern void TypeSerializer__ctor_m23FFD996ED2480137E5AE3A79AC4108011C07E45 (void);
extern void Reader_Init_mACBC96ACB9DF1553E454FD5F53C164370C2D511B (void);
extern void Reader_AddSerializationAdapter_m3F3EFCBC5826EEA95610C666FBB39DB19ED7CC24 (void);
extern void Reader__ctor_mB0946D4FAE0056EBC1F6BCC574ADFDFD1FA4AE23 (void);
extern void Reader_GetBuffer_m2E63A09C64EB8D4A8C36D31B2FD8F129265C4950 (void);
extern void Reader__ctor_m89BF59E7249CF521A8F8F80BD79C93BFD8F542EF (void);
extern void Reader_ReadObjectArray_m241DA7B2226946A40D3342DC29EDDE078D910CF8 (void);
extern void Reader_ReadObjectArray_m0BF1584BE63B4DC708CB7CF482D7F482D48F7BD0 (void);
extern void Reader_ReadObject_m13F32FF600E765BE01A75D699C09DEFFEED0CA0F (void);
extern void Reader_ReadObject_m60087ED32721C8797522F51D63896A0B8D800BA2 (void);
extern void Reader_ReadString_m5FAB8E9CC8DA614FD0409114FDCF6F8972D280DB (void);
extern void Reader_ReadStringInternal_m9D382B47237FD6556BFC1A1688A45FF0F051431B (void);
extern void Reader_ReadAutoEncodedString_m4F418749F718A5AE36380D2861E68900A90F0AEC (void);
extern void Reader_ReadDynamicString_m23CBA55F75EF318044F322933E035A4A14D10EA9 (void);
extern void Writer_get_Length_mC059DA7190BE51B24FC8D35AC9EE4A8C6E2CFFD8 (void);
extern void Writer__ctor_m995376F691120630AF38CB59E47048550004D2AF (void);
extern void Writer_FindChunkWithSpace_m81B652C6FE31B0A45E2F90A87E9EA02092ECBE77 (void);
extern void Writer_WriteInternal_m928A20E0AAF2E545B89937DE02232473803E0DBB (void);
extern void Writer_ReserveInternal_m9A1779F90497775A6053B8485FCFAFF3CDC5AF73 (void);
extern void Writer_WriteInternal_mFEC1AC834A029D7D9FE87381C44967BFDB68EA64 (void);
extern void Writer_WriteObject_m9AAB78B632B61A8D5D2B0BC0C250CF70B8C8B371 (void);
extern void Writer_WriteString_m64238E7AF7A22461C56670D5CABDD9CB885A124E (void);
extern void Writer_WriteStringInternal_mB4E4841F1751962212DE70AF9C2A2283E9ACCA3E (void);
extern void Writer_SerializeToByteArray_m64133F9FEF91FC3C3602E95A4425DD25B73D8F3A (void);
extern void Writer_SerializeToStream_mD44C3D89C6FFB7D1EA031AFA6B0447726FD04D65 (void);
extern void Writer_IsUnicode_mFC41F9B7FE90C454D0148CE3B3ABBF03CB50C3D9 (void);
extern void Writer_WriteAutoEncodedString_m78B9E6426E5C73E01AB308F9765132E402A30B39 (void);
extern void Writer_WriteUnicodeString_mF197A68D41E8A308DDB0D2077B8C3DAFCD8F734B (void);
extern void Writer_ComputeStringSize_mCD7A3364EBA86185DC911EDB2383316574F9CE24 (void);
extern void Writer_RecurseDynamicStringParts_mA6C3D591057C0DD50FCCFDCDCC60043F12A65391 (void);
extern void Writer_WriteDynamicString_mC4B08A023971C752763C69B23AD7FF4F5FE3F6AF (void);
extern void Chunk__ctor_mFE9807198CD091F856E1EF6D83B09426E85D81BA (void);
extern void DelayedActionManager_GetNode_mAEF3F54F608E37822E2F8411BC021617051C6262 (void);
extern void DelayedActionManager_Clear_m175CCC934CD98C808FE583B7DDF563BA1662C768 (void);
extern void DelayedActionManager_DestroyWhenComplete_mE9C248BD5D22972468E21FD8561074E661851EE9 (void);
extern void DelayedActionManager_AddAction_m1B17561BB6B6579F169EA8056C4D9CA1160A8D29 (void);
extern void DelayedActionManager_AddActionInternal_m1EE246770285D9A30BB5BA5B7892D01738E0BF61 (void);
extern void DelayedActionManager_get_IsActive_m994AB293A783993C9952741A9A296314D36E18BA (void);
extern void DelayedActionManager_Wait_mBBFDE2F587ECE8D53F45052CAACD4041CFA5EF30 (void);
extern void DelayedActionManager_LateUpdate_m1B1943FA626B6A54B91AF815921480DAACCB8F48 (void);
extern void DelayedActionManager_InternalLateUpdate_m38B7F66D591F997005D99A5078B8233E568CA25E (void);
extern void DelayedActionManager_OnApplicationQuit_mA1A036CA8A7A9D672E5405654324C173A6BE61D4 (void);
extern void DelayedActionManager__ctor_mD942BE253CE9C25C2DA06B48AF787572D47B6C00 (void);
extern void DelegateInfo__ctor_m08FC6B330A4593BBBB9C28877297E0C157D78DF3 (void);
extern void DelegateInfo_get_InvocationTime_mA102D6CDFFEECC935EF0097705D0081C59D8B030 (void);
extern void DelegateInfo_set_InvocationTime_mF31701BE6AF283B3887E76D6DE7653325F2BF645 (void);
extern void DelegateInfo_ToString_m31AC2460408EA1D16E861E230B274BEDB0B83EAA (void);
extern void DelegateInfo_Invoke_m87A58948D2BFD8A13F922B5D7DA547EB191CA8BD (void);
extern void IdCacheKey__ctor_mA4092FB0B18300CA3B48D255D5D3F41D23EB9852 (void);
extern void IdCacheKey_Equals_m7DF07E6380476F64C0FB71EC1B3184C7C2E8B099 (void);
extern void IdCacheKey_GetHashCode_m48A78A533F677AF9FBDECC0D96B20D2A3EF63C97 (void);
extern void IdCacheKey_Equals_mD244999E0AEB59A8DEAB7843A1EF9A083ED36F21 (void);
extern void IdCacheKey_Equals_m43E8A1D18394FBC32EA7D9FD710AEE8970A9F886 (void);
extern void LocationCacheKey__ctor_m282925A8CF5CF9120CF2CC9AAEF5BEACA2CF8148 (void);
extern void LocationCacheKey_GetHashCode_mB1EBEF7D956D6ED42C5DAF9B4AA1EA02F0CE4134 (void);
extern void LocationCacheKey_Equals_m1C52245232D45B246971CDFFA407DA544867DD71 (void);
extern void LocationCacheKey_Equals_m8F8728EE1F86BE82C84DCC9F79455AE2965B37D7 (void);
extern void LocationCacheKey_Equals_m8BEF54161FE13E606502DC9D9A6B2C40503E89BF (void);
extern void DependenciesCacheKey__ctor_mF897DD52A12FB3D9A8A636072A04F00E73F9AC75 (void);
extern void DependenciesCacheKey_GetHashCode_m6420F6AEE55C5DBFBEF8BF629A58E4E50E12C574 (void);
extern void DependenciesCacheKey_Equals_m1AE039CBE8A5E1DADAE46CFBD540899B65895043 (void);
extern void DependenciesCacheKey_Equals_mB672EB7720C18FE5713D8F70DFA109D617B278C8 (void);
extern void DependenciesCacheKey_Equals_m42F09EA998F1BA6F2B524D9668DD4A9FBFCF71CF (void);
extern void AsyncOpHandlesCacheKey__ctor_mCA58B5FAB01E383F945DD6912328198DA13FA90E (void);
extern void AsyncOpHandlesCacheKey_GetHashCode_m51CCAF8584F6E54572B1D378D59BC5297BD999DB (void);
extern void AsyncOpHandlesCacheKey_Equals_mBFBDF929A1325136C4E8A1717C0B1C12EF19B670 (void);
extern void AsyncOpHandlesCacheKey_Equals_mD987EBDF7585F0899C89799363A0CFBF5FB96788 (void);
extern void AsyncOpHandlesCacheKey_Equals_mAB06D007F68B5910A36E6C97D278147D1541F3E3 (void);
extern void LocationUtils_LocationEquals_m1FD11D1B96DA6CB5C997F4FF70E5422B1782F6B0 (void);
extern void LocationUtils_DependenciesEqual_m33CF7BDD2604A392F1E10D9F492AABE6B9EEF548 (void);
extern void PlatformUtilities_PlatformUsesMultiThreading_mD9F4A5FBF83C37E67662A2424921967DCDB3E1BC (void);
extern void PlatformUtilities__ctor_m0AA459F235118E11C4950BECE8BF6CA419621FBA (void);
extern void DefaultAllocationStrategy_New_mBECB62C44432B753AB02A0FFCF50666D01E108E6 (void);
extern void DefaultAllocationStrategy_Release_m63A6725F306906C2067E7D4B94A9B88DB7418E86 (void);
extern void DefaultAllocationStrategy__ctor_m4E5FDD0B0190A871B875CCA76972BC3BE5021C56 (void);
extern void LRUCacheAllocationStrategy__ctor_mE90C189EB59AEE251D83C85F1C6422EE3F3FB541 (void);
extern void LRUCacheAllocationStrategy_GetPool_mB62AD68905391E611B6DB22C295DEF5D0A883AD5 (void);
extern void LRUCacheAllocationStrategy_ReleasePool_m3137C6983551DC95420A39C44E61ECA466A75B63 (void);
extern void LRUCacheAllocationStrategy_New_mC1CFDD601073707E84257F2769AA6D1C258D28B6 (void);
extern void LRUCacheAllocationStrategy_Release_m64D717012A7423ADF6D16BDAE6CC83C955B5C163 (void);
extern void SerializedTypeRestrictionAttribute__ctor_m1A10E6EE1B4AD72C790F7CB7210522A1C19477FF (void);
extern void SerializedType_get_AssemblyName_m42ED37D1ED570F31D87F2CF9486CC83954FAFCD8 (void);
extern void SerializedType_get_ClassName_m1597BC3A9C2E8EA40F35F8CF03B7591618AB96D9 (void);
extern void SerializedType_ToString_m286E5553A848CA6C472FCE478B65C01B3B2AA5E7 (void);
extern void SerializedType_get_Value_m8AC9DC985380FD7524D147E2C5C95664FAB9A10A (void);
extern void SerializedType_set_Value_m1AB236ECAE27E66A4B2302B4573EB71A7762F679 (void);
extern void SerializedType_get_ValueChanged_mCD14490D0BC812C462831E2BD634A60E24243D34 (void);
extern void SerializedType_set_ValueChanged_m91FD96EEB5883E2ED8B507EC3CC668109928E1E7 (void);
extern void ObjectInitializationData_get_Id_m8B020C841845122EF9123BD5E136F6A3FEC3496F (void);
extern void ObjectInitializationData_get_ObjectType_m7706EB3247D33B65F062CDEEB40792A27B8ABF69 (void);
extern void ObjectInitializationData_get_Data_mEFF201461CA817CE659A8703EE2AFEBF53824318 (void);
extern void ObjectInitializationData_ToString_m730570E98783E54D1B6A2ED0178E05A2DE394593 (void);
extern void ObjectInitializationData_GetAsyncInitHandle_m0017D1CE9481015847F724E9E22C481A2CFDF2FC (void);
extern void ResourceManagerConfig_ExtractKeyAndSubKey_m1A27EE5940B51FAB5B5C9F1E0F78080D41A43CE1 (void);
extern void ResourceManagerConfig_IsPathRemote_m3F7B514A149162329D210C849F3B5D99A77ED156 (void);
extern void ResourceManagerConfig_StripQueryParameters_mF4AB37F488D95F095D6DD266B767238E7C273EC7 (void);
extern void ResourceManagerConfig_ShouldPathUseWebRequest_m9B72A8B38C30946642D7FF983D89277596A4A35B (void);
extern void ResourceManagerConfig_PlatformCanLoadLocallyFromUrlPath_m0BF372FAE10BEF17D7593896328D1DC567CE70B9 (void);
extern void ResourceManagerConfig_CreateArrayResult_mE26798757DCC20CB22CE1DBFB13EEED145CB303A (void);
extern void ResourceManagerConfig_CreateListResult_m7E247CBEA74D2E3E6F4B5E7795CBDC314F91D83B (void);
extern void UnityWebRequestUtilities_RequestHasErrors_m44D9F211227DADD941F2E19D8273495D3F41B686 (void);
extern void UnityWebRequestUtilities_IsAssetBundleDownloaded_m424AE91A573AE10DB0ACFF7969FA6794F6C5F256 (void);
extern void UnityWebRequestUtilities__ctor_mBBC1319918D5F9CE7DC96D260D4A06B165FB9D2F (void);
extern void UnityWebRequestResult__ctor_mE837C228770475E93E476203E7EFCA9BDC807168 (void);
extern void UnityWebRequestResult_ToString_m1F2EF13FD90347BA1B8752D2D534D007BAF14390 (void);
extern void UnityWebRequestResult_get_Error_m84C4381776A6467DE9CC4DB3797661F1519FBDF5 (void);
extern void UnityWebRequestResult_set_Error_mB8AB258796A2F29BB387F34222FFA59EA3431009 (void);
extern void UnityWebRequestResult_get_ResponseCode_m9587BFE958C864F3B4744E78890AE09C1E5CF3E2 (void);
extern void UnityWebRequestResult_get_Result_mFA65ADAE33B5F1037C60A4F544F54800114B8DDA (void);
extern void UnityWebRequestResult_get_Method_m170F095C8F0E614D35EC882874DA221CF7FD372D (void);
extern void UnityWebRequestResult_get_Url_m692560D0BC25EA558272192CED8409364335F477 (void);
extern void UnityWebRequestResult_ShouldRetryDownloadError_m0801EC2326AAE1EF661B1361F7EDC2878D4EB719 (void);
extern void DownloadOnlyLocation__ctor_m5C682847E5CC673CB596207315CEC153BF116EA4 (void);
extern void AssetBundleRequestOptions_get_Hash_m5241C40F21D24CB9796C5A6B71692FCABA061F79 (void);
extern void AssetBundleRequestOptions_set_Hash_m15AA10A2ED59974D2BEC4727D2087891A3F2F383 (void);
extern void AssetBundleRequestOptions_get_Crc_m638E8363BD60FD574E69B337042C4BC8E16B3AE9 (void);
extern void AssetBundleRequestOptions_set_Crc_mB5262A0ECF589E86D7F2FF3B6A06B11EC9F322DA (void);
extern void AssetBundleRequestOptions_get_Timeout_mE116648854CC272B9D579758C71F4D59646A555F (void);
extern void AssetBundleRequestOptions_set_Timeout_m4D40D90135EFDB499654B97E189BF5F284F0B94B (void);
extern void AssetBundleRequestOptions_get_ChunkedTransfer_mE2A8C59F151B1092D4A23613239715606BD08B07 (void);
extern void AssetBundleRequestOptions_set_ChunkedTransfer_m14451B19037C6DB8A43937ACF1B04429FBD5305A (void);
extern void AssetBundleRequestOptions_get_RedirectLimit_mDDF7428FD93DBCE5FA5C953DF09C3E1F7D2BFD8C (void);
extern void AssetBundleRequestOptions_set_RedirectLimit_mDCB72FD4DBA97492FA95BD06C50208C6DEEA4D2B (void);
extern void AssetBundleRequestOptions_get_RetryCount_m13E92C2312B338C121AE8C1C9979B2E3719F385A (void);
extern void AssetBundleRequestOptions_set_RetryCount_mE25CEBBF0610F063DAB0600352CAD53023B19591 (void);
extern void AssetBundleRequestOptions_get_BundleName_mC6440526C8EA2D3AE637F9708366D49D92AEE07D (void);
extern void AssetBundleRequestOptions_set_BundleName_mFC5719990D23E342388FC8E8D40CA9F8FDF9C571 (void);
extern void AssetBundleRequestOptions_get_AssetLoadMode_m7C8EEB76C1AC675E76B7227E2B6D1B75194B2702 (void);
extern void AssetBundleRequestOptions_set_AssetLoadMode_m906864B7AC6863322225B1EF0FBFF29C50CD3D68 (void);
extern void AssetBundleRequestOptions_get_BundleSize_mBF9BE237E555C6369CF5980696A5B7F4AE12D1B1 (void);
extern void AssetBundleRequestOptions_set_BundleSize_mBEC0BFD2694FB465EF71A626BAB0DAED839D340F (void);
extern void AssetBundleRequestOptions_get_UseCrcForCachedBundle_mEA782A87DBBEEC57AD4C60376F585D719A3E8227 (void);
extern void AssetBundleRequestOptions_set_UseCrcForCachedBundle_m34DE20707D0A081FAD76F9C31E90D39647438B95 (void);
extern void AssetBundleRequestOptions_get_UseUnityWebRequestForLocalBundles_m757E1D4C7C0EA98A50A2DB1834D3BF44680BDD5A (void);
extern void AssetBundleRequestOptions_set_UseUnityWebRequestForLocalBundles_m1E382181788427AF5716B4A52A130E935F89104A (void);
extern void AssetBundleRequestOptions_get_ClearOtherCachedVersionsWhenLoaded_mB153677F48A4B47D247D161E49E913B9BDC51746 (void);
extern void AssetBundleRequestOptions_set_ClearOtherCachedVersionsWhenLoaded_m77720BB6E0F99946304DF20351DBC58A0B7F5AA0 (void);
extern void AssetBundleRequestOptions_ComputeSize_mB11B143445F4C29355A29326B23AB38D91C5ABC2 (void);
extern void AssetBundleRequestOptions__ctor_m0F5D92A41947FF6CFB4267185309F205B1C88DEE (void);
extern void AssetBundleResource_get_HasTimedOut_m2B47177ADC99FE754F7D05E86E7AA624CEF93D16 (void);
extern void AssetBundleResource_get_BytesToDownload_mAE341F337C62984133F1898982EEA4C1A816CC50 (void);
extern void AssetBundleResource_CreateWebRequest_m99093A2324F69D68ABD3D922E0A0D7F02309C245 (void);
extern void AssetBundleResource_CreateWebRequest_m79DE1A126FF993C44A29062AB674C28B871C00C9 (void);
extern void AssetBundleResource_GetAssetPreloadRequest_mEF40A5A8DE1362FF6BED69D026EF9DB165E21BCF (void);
extern void AssetBundleResource_PercentComplete_m47D08300A9374509CA6AFB646EF7B00634015935 (void);
extern void AssetBundleResource_GetDownloadStatus_m5C4B65566B058DC9736A9416C44D914B3A6814D8 (void);
extern void AssetBundleResource_GetAssetBundle_m2677FCACAC574D7C60992D0FEB7EABDFA962CF63 (void);
extern void AssetBundleResource_OnUnloadOperationComplete_mA30A75EADD9F1392EC42FFC836ADBF8A63518189 (void);
extern void AssetBundleResource_Start_mAF93CC18901AF152472DC583D170B9218DC03B44 (void);
extern void AssetBundleResource_WaitForCompletionHandler_m3FEE1A4633F3C1945FDC6BD75A159C1E5B89703D (void);
extern void AssetBundleResource_AddCallbackInvokeIfDone_m1B95B9ED5C249BB3C9C2A00F165F47D1F01A30BB (void);
extern void AssetBundleResource_GetLoadInfo_mAFC4290DC315D8402C0AB3C632F8CF81204D410D (void);
extern void AssetBundleResource_GetLoadInfo_mED6270C26AEB96BC8D81708EDDFF08000CFFB7CA (void);
extern void AssetBundleResource_BeginOperation_mCC4253DFE6CCCA1F7D8AABE819F4570AD948439D (void);
extern void AssetBundleResource_LoadLocalBundle_m6EFD51DDF5FEC1964CAADCF0C268FD6AB07C11F4 (void);
extern void AssetBundleResource_EnqueueWebRequest_m5D4DA3D4CFE08BA2464FA80F1AD3908521FC3E0D (void);
extern void AssetBundleResource_AddBeginWebRequestHandler_mE230039E25E989F0C83944D14C52A387EDD09B91 (void);
extern void AssetBundleResource_BeginWebRequestOperation_mC2F30F42B4EF0C1D24D5365B205E4EB8C9448A4A (void);
extern void AssetBundleResource_Update_m51BC83CA2F963A53669204CDE6153DC1ECE50BC1 (void);
extern void AssetBundleResource_LocalRequestOperationCompleted_m89ACDBA271F4830F27F54DCD193CE92D0B32D1B8 (void);
extern void AssetBundleResource_CompleteBundleLoad_m2D635CCC7B8438E10C04E75DDBC41B79A04F712E (void);
extern void AssetBundleResource_WebRequestOperationCompleted_m80EDEE2721B9B8493FB05643C6DA3A9A62CB4C2D (void);
extern void AssetBundleResource_Unload_m16EE6F704D14712FC1188F9D6C539EE3AF6CD120 (void);
extern void AssetBundleResource__ctor_m008E4DA8A21FBE0D1C182DDB5C1481B778B8B7F5 (void);
extern void AssetBundleResource_U3CGetAssetPreloadRequestU3Eb__28_0_m37EAFEDC337AFC75940A42D3E75F3D0C50EDD4C9 (void);
extern void AssetBundleResource_U3CAddBeginWebRequestHandlerU3Eb__41_0_m21A60C0AE1AD43CFD9401B63FA0DF8FC005A5DDD (void);
extern void AssetBundleProvider_Init_m671FCC897E9239473EA3F07825506D2F8CBD792D (void);
extern void AssetBundleProvider_get_UnloadingBundles_mE2A2813B5F21B3CFC98586F90C36A8CA7F1CD961 (void);
extern void AssetBundleProvider_set_UnloadingBundles_mDEA612E42EEDAC258115C998A94FB722884F3282 (void);
extern void AssetBundleProvider_get_UnloadingAssetBundleCount_m98CE8E2285630EC583D8F35354141E5078D4C248 (void);
extern void AssetBundleProvider_get_AssetBundleCount_m574A2DBBEBA016C60BD20C09A553AD5C060881CA (void);
extern void AssetBundleProvider_WaitForAllUnloadingBundlesToComplete_mEBC813A6870324DA71CE63FB3A49399D0BA3A7F2 (void);
extern void AssetBundleProvider_Provide_mA46E1266920D3C0B5D9D11EAF84ADC0E17E447DE (void);
extern void AssetBundleProvider_GetDefaultType_mCA86887DC5C60FB6692B4B77E3B60B3DF2B562CA (void);
extern void AssetBundleProvider_Release_m95F1EE9F98EB926F4A535EA03F3DAFD7F1B86884 (void);
extern void AssetBundleProvider_CreateCacheKeyForLocation_m51B28E2051CA587CF61E94E98144BC1898CC5205 (void);
extern void AssetBundleProvider__ctor_m53DA726879B5E33FAC8F94D17C1290B24C4937CC (void);
extern void AssetBundleProvider__cctor_m76C60D2E2D7FD0CB5AE7A6E4A885D41E5D219681 (void);
extern void U3CU3Ec__DisplayClass12_0__ctor_m78AFD6A5DA689F61372B19CF536C90B4967408BA (void);
extern void U3CU3Ec__DisplayClass12_0_U3CReleaseU3Eb__0_m5E66EB3B6B9C7FD43A7EE366661ED9AEB182BFDC (void);
extern void AtlasSpriteProvider_Provide_mBBAA733518B690CAF561FDDFD2E53F5FA5707DF2 (void);
extern void AtlasSpriteProvider_Release_mC372EBB2210E5C2313C97C386F5CF2D089BF0739 (void);
extern void AtlasSpriteProvider__ctor_mCF29705EEE5BB7AE871C09823B04C8DCBB2CCEC4 (void);
extern void BinaryDataProvider_get_IgnoreFailures_mB1618C33565A80A53488BEC0354EF97F79700F73 (void);
extern void BinaryDataProvider_set_IgnoreFailures_m0120A787D1B242D60EF07003CFD64D5E349111B8 (void);
extern void BinaryDataProvider_Convert_m7A4E44F1507D3AFCA6465882D5FF66BA8361E63C (void);
extern void BinaryDataProvider_Provide_mCD8C903D71ACE62C3CE2F710BB5384AAE03C45E0 (void);
extern void BinaryDataProvider__ctor_mB946875640ED93ABD18B9E7DBB54FFCF7A4112DF (void);
extern void InternalOp_GetPercentComplete_m481C4F23E5A9241A880C3881B53B36170F7F0815 (void);
extern void InternalOp_Start_m997221D33828DF742882C14D355F85C10F712917 (void);
extern void InternalOp_WaitForCompletionHandler_mA35787A1A74B96482E9CB77C381DAF4C6039B248 (void);
extern void InternalOp_RequestOperation_completed_m6A788C225F2B94D77D802F168BE83FB270D187EE (void);
extern void InternalOp_CompleteOperation_mFBBF22064DE286ED48936494E33C312C326577EC (void);
extern void InternalOp_ConvertBytes_m6D21DA816EF3D8BB6FCBD4FFF37831352E10D410 (void);
extern void InternalOp_SendWebRequest_m6A785CFBD698F72DFEB656082112404AC8644A40 (void);
extern void InternalOp__ctor_m4CBDDA9271CF100392A0167C0C58A9278D2EE26E (void);
extern void InternalOp_U3CSendWebRequestU3Eb__13_0_m5C2E161E02D866146D1578B565CE4AADBA8418D2 (void);
extern void BundledAssetProvider_Provide_mBE01FF42EB491FEF863873076B83891F4946C575 (void);
extern void BundledAssetProvider__ctor_mCAC8A4F760B97F22EE89F68B093CE5095A573DE3 (void);
extern void InternalOp_Start_mD6F453C005B979B3C1B2D7B4DE6562A16849503C (void);
extern void InternalOp_BeginAssetLoad_m30E6E668710155E8D51518F8D8ECFB75A30170C6 (void);
extern void InternalOp_WaitForCompletionHandler_m7BA75A8CB05D3B9BC2455C51575162FE040C8BAF (void);
extern void InternalOp_ActionComplete_m2082E408EC480CF7251DC1660845EAAE1500521C (void);
extern void InternalOp_GetArrayResult_m694DC4FAB3B9C5979801DD61B561BC477A9350B9 (void);
extern void InternalOp_GetListResult_mDAA24FC03AC31E533A7A182BA29E9FC112A25DF3 (void);
extern void InternalOp_GetAssetResult_mED436C17DAA1633F0042F0A1ED7D9CBB6C2EB2CD (void);
extern void InternalOp_GetAssetSubObjectResult_m55D32B4FC4D1DA72EF3BCC3036B13304077D469D (void);
extern void InternalOp_CompleteOperation_m861092872E86F4DCFD894EFAEF644E5E46E51EDD (void);
extern void InternalOp_ProgressCallback_m1AE6872A27642B6744CF675866712366CA9B124E (void);
extern void InternalOp__ctor_mAE3F9E21BF0389264BAF79BB0E24DDFECEF6561F (void);
extern void InternalOp_U3CStartU3Eb__7_0_m531305899E20D1C0A0B701CF73A2B2F03497D814 (void);
extern void InstantiationParameters_get_Position_mED28D6FCE87E7AEBC4D0D0D78FA4749300ADE83B (void);
extern void InstantiationParameters_get_Rotation_mDDFD08C31993BB4532D0D27B3DC801735EE93017 (void);
extern void InstantiationParameters_get_Parent_m778422BDFC47C2CCE73196FAE4A341F5308160E6 (void);
extern void InstantiationParameters_get_InstantiateInWorldPosition_m546FDC64BF6E5E7B739392EE27695D43DE65A3AD (void);
extern void InstantiationParameters_get_SetPositionRotation_m089FF9C21E2840258CE80BB9E77E35F3C2E710B3 (void);
extern void InstantiationParameters__ctor_mE2B0DEA67D18FA4C6B5A37BC07629A1364D6B107 (void);
extern void InstantiationParameters__ctor_mEFAF8D103303B2D9763B17BAB322E06931F13306 (void);
extern void InstanceProvider_ProvideInstance_m6DE99248B2973F0839880FC3FD240037A3350DF8 (void);
extern void InstanceProvider_ReleaseInstance_m52D21820C36D02F3E2C0C7C89C0511648FF66A59 (void);
extern void InstanceProvider__ctor_mF4CD1E6C8E301646D4667748D12B70556B800DB2 (void);
extern void ProvideHandle__ctor_m747E40C8B08DBF3EB4276D7B0836C0F0D5B59B0A (void);
extern void ProvideHandle_get_IsValid_m0852EF449EEE7D5E0E91FFA7902D9600554E9CE7 (void);
extern void ProvideHandle_get_InternalOp_m1C141F1D99C5BD7F5AA1E973272462966BF0FEAE (void);
extern void ProvideHandle_get_ResourceManager_m21C0C0B4C590B19E601BF83ACEA612EAF3B62262 (void);
extern void ProvideHandle_get_Type_mD0CC7F89812DD44851B9D52DA6960B1671A86559 (void);
extern void ProvideHandle_get_Location_m90F975C77A98EB20A2C3B9135D629AB0F04FFA79 (void);
extern void ProvideHandle_get_DependencyCount_mA960290D798128400EBF3411B6B005F3F215A451 (void);
extern void ProvideHandle_GetDependencies_m369FC67ECDB4E574C6BC9961896C384189EAFBAD (void);
extern void ProvideHandle_SetProgressCallback_m5D44473DCD8CEB54FB41063B2C2105220822094D (void);
extern void ProvideHandle_SetDownloadProgressCallbacks_m9FB675CA1837DD5CF495B393170257B8EE06C145 (void);
extern void ProvideHandle_SetWaitForCompletionCallback_m2B606A689DD5A940581F9C0F87C4B6B6714BE7A2 (void);
extern void SceneInstance_get_Scene_m4A9A4A43AFC047DD8FB2DF665C499A09296CBA58 (void);
extern void SceneInstance_set_Scene_mD929C70C4FEF4B9599C1A6A5C22CE787C433A06C (void);
extern void SceneInstance_Activate_m87D5B5E1C820F1666917198300E36DABFCA9EF29 (void);
extern void SceneInstance_ActivateAsync_mA5740ABD2A826DB7CCCA83C62F31AA8448DE08C6 (void);
extern void SceneInstance_GetHashCode_m0C1E8F653E55633F9897B5FC62D7D4B7B1D340F4 (void);
extern void SceneInstance_Equals_m10E8D14BA2C3057166479C12D2514056E8ACB6AD (void);
extern void SceneProviderExtensions_ReleaseScene_mA143707034C4E9860E9E80F333307D440195BCF0 (void);
extern void JsonAssetProvider_Convert_m6FA7F7A940C19DA1E361CCE24898B243EEBBD75E (void);
extern void JsonAssetProvider__ctor_mB9C7EE8BCD28AA4995F2B29CB3041582769740C2 (void);
extern void LegacyResourcesProvider_Provide_mB180B8328373F2A6B219A68631AA4B536BC09DBE (void);
extern void LegacyResourcesProvider_Release_m1E49FF2BD1D0DEEF7EA4721014BB1FF118C58DCB (void);
extern void LegacyResourcesProvider__ctor_mAEF6AE3781103993C52878AE0FF0FA88B65036B2 (void);
extern void InternalOp_Start_mBB36E8DBDE11260D2791B713DA6278B1E52A3B34 (void);
extern void InternalOp_WaitForCompletionHandler_mA0D71D7C83BF8A4500CE5BBD5B65C0D6C86A79CE (void);
extern void InternalOp_AsyncOperationCompleted_mCFE499F25DE6783BC252AEF6FFA83B2F4906C69B (void);
extern void InternalOp_PercentComplete_mFD5F139200755EFF695677464310664195431171 (void);
extern void InternalOp__ctor_m7D25C62F5CA2642016421F0A6FE4AA93F27064E1 (void);
extern void ResourceProviderBase_get_ProviderId_m32B6BC7793DD0BA3B1B8AD25A2469AE0B4CDCCB1 (void);
extern void ResourceProviderBase_Initialize_mF5BA704DAA8DBAE985D7B2AA6B84379DC08A1A62 (void);
extern void ResourceProviderBase_CanProvide_mB9058D012637D533B0214DCB5EF478E5F8CB6816 (void);
extern void ResourceProviderBase_ToString_m9E17857E9B321F341C51B01E0EA3EC09D0ACABDD (void);
extern void ResourceProviderBase_Release_mBA35FC33814192955594D9A6C36D6AF9DF54A123 (void);
extern void ResourceProviderBase_GetDefaultType_m33744F9753517EF7AAE308E7F35234742B0EA000 (void);
extern void ResourceProviderBase_InitializeAsync_mC3314BEB55F7D7F35AD13997844E919980BED1DA (void);
extern void ResourceProviderBase_UnityEngine_ResourceManagement_ResourceProviders_IResourceProvider_get_BehaviourFlags_m3ED17F0683C3298B5AC1E402259740330104F79A (void);
extern void ResourceProviderBase__ctor_mAE12700496F4A8B1363AB851FD48F2101278CBF9 (void);
extern void BaseInitAsyncOp_Init_mA692A84282503E756732EB5AED2D184E17C8896B (void);
extern void BaseInitAsyncOp_InvokeWaitForCompletion_m3EEFE285B56FC5B968B76F2A7532EBB1ECF64C32 (void);
extern void BaseInitAsyncOp_Execute_mD8E2E626B3041A9A808536A9062D19D86DB091F2 (void);
extern void BaseInitAsyncOp__ctor_m13B9AD12D8F36FC0B8DD5B72AE71117B3F8ADA1E (void);
extern void U3CU3Ec__DisplayClass10_0__ctor_mA5E2487B070975CC79B933A4F89E400047EC712B (void);
extern void U3CU3Ec__DisplayClass10_0_U3CInitializeAsyncU3Eb__0_m2152B544B8FC16CFF06127C9098353ADE47B9713 (void);
extern void ProviderLoadRequestOptions_Copy_mEA312F4D34C7FE6E0B7CE7BA4E7D68DCD5BA94A9 (void);
extern void ProviderLoadRequestOptions_get_IgnoreFailures_m38F1966139843C0C98C3598834A3BCCD9700962F (void);
extern void ProviderLoadRequestOptions_set_IgnoreFailures_m4C265B0663007A5DDE6E505E6CF15E516A0D13D1 (void);
extern void ProviderLoadRequestOptions_get_WebRequestTimeout_m81BA0608C118ECD1ED8AD0732A626E747418320B (void);
extern void ProviderLoadRequestOptions_set_WebRequestTimeout_mCC9B0B944D6B5E4599849B09C83B3A635F31B518 (void);
extern void ProviderLoadRequestOptions__ctor_m61D91E3E5DED91FDA313D772DAD3F16324F06322 (void);
extern void SceneProvider_ProvideScene_m71829B9681719B9C2F06799FAB082C7A995FFE37 (void);
extern void SceneProvider_ProvideScene_mE0A8EF44887C08C32A5180F1F0218A3FAA2DD58C (void);
extern void SceneProvider_ReleaseScene_mCF155C3784D3085A27249CC2802153A1F92611BC (void);
extern void SceneProvider_UnityEngine_ResourceManagement_ResourceProviders_ISceneProvider2_ReleaseScene_mB3B5F1A6E74DFD303103F085BA356A1C9486357D (void);
extern void SceneProvider__ctor_m1DCFAE38CC86ABEEE17922354162A857E296DC36 (void);
extern void SceneOp__ctor_m040735CBFEBFBB5766B7E13241CD23D641DAA006 (void);
extern void SceneOp_GetDownloadStatus_m2F9313B7535DD301CFF63B9B8DBE8E8B803DED6D (void);
extern void SceneOp_Init_m9A7827AAA4E02DEB793CA591D918A72C1E469B39 (void);
extern void SceneOp_Init_mFEB3786354A297874561E3BC154C2DE58C5F2C17 (void);
extern void SceneOp_InvokeWaitForCompletion_m6192F90DF7C2E5F2B986E6413341A8D5AED41B29 (void);
extern void SceneOp_GetDependencies_mF60EC68370064533BCFF6918265B5AD71CF586A0 (void);
extern void SceneOp_get_DebugName_m0681291B5BD9791094325B3EE0722917C896CA19 (void);
extern void SceneOp_Execute_m4EB7AD77E7597C59AD5C67A25587E841BE12BFD2 (void);
extern void SceneOp_InternalLoadScene_mA08E3FB4E51C417EFE1A4C5DAE4B005C0F537B20 (void);
extern void SceneOp_InternalLoad_m5D31B832DFC50152FD83FB94429A3D30E08755D8 (void);
extern void SceneOp_Destroy_m5F0765C76D7D9DA55338EBBF45B2DA79A837C1E9 (void);
extern void SceneOp_get_Progress_m46A61DF0E7EC319C9D06C209DB723EAF0E1C2456 (void);
extern void SceneOp_UnityEngine_ResourceManagement_IUpdateReceiver_Update_m506B4FE9CB742F642D38FD97D9FA0458E635A994 (void);
extern void UnloadSceneOp_Init_mC0C6886B5E43507A3D937D155153AF0A0876A312 (void);
extern void UnloadSceneOp_Execute_m7DE2D21BB6313D004CF1D6D9F1EFB01A63221E4F (void);
extern void UnloadSceneOp_InvokeWaitForCompletion_mD19AC17887FEC9B93D6FFCC72715F11AC105725A (void);
extern void UnloadSceneOp_UnloadSceneCompleted_mEACB38511795DF58A154B535AB413BDD85DD2B3C (void);
extern void UnloadSceneOp_get_Progress_mD452305AF75DD63EEA47EBAFC1876C1E17E57D8F (void);
extern void UnloadSceneOp__ctor_mEC2C9FAA9ABA49CB8374E387869FD5B9D56BFC90 (void);
extern void TextDataProvider_get_IgnoreFailures_m8BC123BCDA3C358B9EF223382A5ABA14C41D8D61 (void);
extern void TextDataProvider_set_IgnoreFailures_mFB1C076856A24422095C941AB43456FA8940D7DB (void);
extern void TextDataProvider_Convert_mC19E9961AFE2F1475B113DBA2A25BBE43EE546BC (void);
extern void TextDataProvider_Provide_m90DDE130A1AA927048310A11A8269C3E153E2F49 (void);
extern void TextDataProvider__ctor_m0F85BC00939981B4C817E850285C97FB9E26243E (void);
extern void InternalOp_GetPercentComplete_mAFF06723896D6D26284115A2F0F102C9D664CCEC (void);
extern void InternalOp_Start_mF7F18148ECFF3E5EDA8852B71F507C6948CECCA3 (void);
extern void InternalOp_WaitForCompletionHandler_mD109A0979F9D7BBC921015E9D83F2F81552C4AC2 (void);
extern void InternalOp_RequestOperation_completed_mABCF255EC8F16723D1F518952653864D3F10D6A5 (void);
extern void InternalOp_CompleteOperation_mBA10749B5FBBCEBF6FC6EDE8F74436482E0CD108 (void);
extern void InternalOp_ConvertText_m399662CFEB5F933894A69BF4DAC04E9DEE3E60B5 (void);
extern void InternalOp_SendWebRequest_mB75339F2D9CF06B6086FAD906823EEBFF11FAD22 (void);
extern void InternalOp__ctor_m5C2E4773471AE3A4E9D940D1CD799A838ED4AF7A (void);
extern void InternalOp_U3CSendWebRequestU3Eb__13_0_m7D8C14A2281B0E47BA2C1699FFE5E12A050840A9 (void);
extern void ResourceLocationBase_get_InternalId_mDD8CAC7826827E51FC21FCDB426DB6096008E992 (void);
extern void ResourceLocationBase_get_ProviderId_m0424B14B83A89FAA0EF86495E03B1EF521969C79 (void);
extern void ResourceLocationBase_get_Dependencies_m72F0BF8B5DA8D1FE659A64693B045F5F285CBDE5 (void);
extern void ResourceLocationBase_get_HasDependencies_mD189D2ABA34D0D3A2E9157C64CB379E6D2F065BF (void);
extern void ResourceLocationBase_get_Data_m8C7A683E9EB75A55EA642D9EE698A3D13C2034C9 (void);
extern void ResourceLocationBase_set_Data_m81160FA98303F532B5365B051C62BB98DC855A03 (void);
extern void ResourceLocationBase_get_PrimaryKey_mADFAF458BB2737444A019A4EDA4D851597B67D1D (void);
extern void ResourceLocationBase_set_PrimaryKey_mB1F256C71675DCEE26CB03DC0F7273BC38A952AB (void);
extern void ResourceLocationBase_get_DependencyHashCode_m9E0B40B30D210863CD93FF3B49931192022F7E86 (void);
extern void ResourceLocationBase_get_ResourceType_mBE5F33BDA7995150D37EF135584D62C5E6B1DF8B (void);
extern void ResourceLocationBase_Hash_m2C84780ECDAADFE6406BDC5BB220802A413AF16A (void);
extern void ResourceLocationBase_ToString_m90F397186213D25C1BA997AD845FA55AED6485A8 (void);
extern void ResourceLocationBase__ctor_m736BECFEE816BAEDC0FFC7E0A1C0100826A63E74 (void);
extern void ResourceLocationBase_ComputeDependencyHash_m4436815CB535B21F7DD09593479AB5EF5DBD2D2F (void);
extern void LocationWrapper__ctor_mAA6CC9A09ED34EDB9381D915BFF630E0C9C22141 (void);
extern void LocationWrapper_get_InternalId_m7ABC2B6F84C4F6C7FE2F5332CCF37EF74AB921DE (void);
extern void LocationWrapper_get_ProviderId_m810DF53B752AD42985C7BF9AD6A8FC61E2B5A926 (void);
extern void LocationWrapper_get_Dependencies_m857880D71F22D81B2959C327BACC0D4A1588E501 (void);
extern void LocationWrapper_get_DependencyHashCode_m0AD260651E814478350AD80C78E0FD36A4EF1075 (void);
extern void LocationWrapper_get_HasDependencies_mCFD06F0102DFE96A00F495219F766D7DFAE7F83C (void);
extern void LocationWrapper_get_Data_m8C0C8F501B27EB5846D9C404AB899C628BBDC88E (void);
extern void LocationWrapper_get_PrimaryKey_m7023AE6E7AA84AC50ABFCDC3BDDB9A194013BC98 (void);
extern void LocationWrapper_get_ResourceType_mB3E67E25F1DF8CCF6C378E0181A1AC354404BBA8 (void);
extern void LocationWrapper_Hash_m06AE5E2A24BAEA7EB329B79C840EDDCEB84BFCFB (void);
extern void DiagnosticEvent_get_Graph_m670206480E0149C55B547DF779321DFE8EF9436B (void);
extern void DiagnosticEvent_get_ObjectId_mF6D69C8148C581548D9CF4C21293F6A8300DAC75 (void);
extern void DiagnosticEvent_get_DisplayName_m02754B60C44C78A265F62732A3395470BEAA39CA (void);
extern void DiagnosticEvent_get_Dependencies_m5E3CA5E99242A6845C6974B9620EFEFE3777C2CD (void);
extern void DiagnosticEvent_get_Stream_mC79CA5D736C7CEBA931F272DA4F94BA8DED1BCF6 (void);
extern void DiagnosticEvent_get_Frame_m3CC82F45DB1EA65234FF98A1E739FC40D303171B (void);
extern void DiagnosticEvent_get_Value_mC412E8EC2AC4D004D2E68BCF0566AA2261008C02 (void);
extern void DiagnosticEvent__ctor_mDF62B33ABFF1F33A4B39DF5C209B8238F2D529A0 (void);
extern void DiagnosticEvent_Serialize_m6C00B0750960B7E23FB1DFE666C93DC3F5B0FF1A (void);
extern void DiagnosticEvent_Deserialize_m22BCF0716BAC26F28A164F479E6F67613B00D16A (void);
extern void DiagnosticEventCollectorSingleton_get_PlayerConnectionGuid_m12CE48F5FA9778C6B084529356CE2BA2FF19E0FC (void);
extern void DiagnosticEventCollectorSingleton_GetGameObjectName_m1361552A8D1AFD2282D01F14975908635A86D74D (void);
extern void DiagnosticEventCollectorSingleton_RegisterEventHandler_m9FCC0418DB7FCB86CDD3C233C6D59507D3A1CBC5 (void);
extern void DiagnosticEventCollectorSingleton_RegisterEventHandler_mBD7EB71E6D5A3B2D180D6B0E2163C47E7DE291BA (void);
extern void DiagnosticEventCollectorSingleton_UnregisterEventHandler_m195CD42A740153DCC2387DC36511FB4FE36E5AF2 (void);
extern void DiagnosticEventCollectorSingleton_PostEvent_m814E46059FF639B7B3E731E6F354778282DF5B2A (void);
extern void DiagnosticEventCollectorSingleton_Awake_m123CE39CFC51D74A89D0EACF7DB3FC080FD07723 (void);
extern void DiagnosticEventCollectorSingleton_Update_m5EBCBD1D3BCD145D3EEB6460DA39FCC197354BD3 (void);
extern void DiagnosticEventCollectorSingleton__ctor_m439DE5DDEF6D28BB4E48FF1A9FA7B3F100B41E03 (void);
extern void U3CU3Ec__cctor_m68A24D9C1E63B9D1C8598AA17A966FF00C9FCA63 (void);
extern void U3CU3Ec__ctor_m7E8AE3390AA2F226FD9AE66A810054796F6E3EF7 (void);
extern void U3CU3Ec_U3CRegisterEventHandlerU3Eb__8_0_mA7621F2FC763736C0E5940EFF0C40BC79177573C (void);
extern void U3CU3Ec_U3CAwakeU3Eb__11_0_mC35442B113DAF4C3C790A4D17482F6CF5888FE73 (void);
extern void DiagnosticEventCollector_get_PlayerConnectionGuid_mBA28978681F7978EDAD805C720891BB0013FC3E1 (void);
extern void DiagnosticEventCollector_FindOrCreateGlobalInstance_mF69245B78702CB0B5D38DD7F7E35DD84D1884E6F (void);
extern void DiagnosticEventCollector_RegisterEventHandler_m07723751487743072F7D163A0C94803345C3DA46 (void);
extern void DiagnosticEventCollector_UnregisterEventHandler_m6183F50FB14701F99FC2F34D16ABAFC738A63F4B (void);
extern void DiagnosticEventCollector_PostEvent_m7E9005E48B8D9756D61E13B4C6F4070B1A108E19 (void);
extern void DiagnosticEventCollector__ctor_m55F16BF9725CD02336D84D2032D17D23079D34BC (void);
extern void AsyncOperationHandle_get_Version_m1DA4995F59398AC5537A4F5A64F18E19B599FBC2 (void);
extern void AsyncOperationHandle_get_LocationName_mFBCB4F35970F65B67F34263CBBC8949DBD04954F (void);
extern void AsyncOperationHandle_set_LocationName_mA4DD8D02B510CD9974D1E5AAC4D34DD91575865C (void);
extern void AsyncOperationHandle__ctor_m68E6B69581AD8F879FE27693A96D6F36ABD3A264 (void);
extern void AsyncOperationHandle__ctor_m21CD505FAB5F5AB79ADA74F7F95AD6DEA7F145E8 (void);
extern void AsyncOperationHandle__ctor_mC618D938157A14E87066933EF6757D73A2847E22 (void);
extern void AsyncOperationHandle__ctor_m40A34479B55F7F00CB5DF17F1003A38B12D0A51A (void);
extern void AsyncOperationHandle_Acquire_mDFD8D1733D45A5F9C20B3A49EA51891BE2C1435B (void);
extern void AsyncOperationHandle_add_Completed_mD5633623ADF00C97B6A1EE712E6654452F64B2E0 (void);
extern void AsyncOperationHandle_remove_Completed_m538FFC6655C8FB0600775FFFB998D9903478DB44 (void);
extern void AsyncOperationHandle_ReleaseHandleOnCompletion_m83E5325DE63A3AFC9DB15862E0E1D14CF08A59F4 (void);
extern void AsyncOperationHandle_Equals_m496ACE1240F3D9550447E1930A1A426BF40CE186 (void);
extern void AsyncOperationHandle_get_DebugName_m8DDB7ED8B735AFC69305907ED82F695ED9F9697E (void);
extern void AsyncOperationHandle_add_Destroyed_m66F321AB085184936F570B4C3A1F9D66B1872C07 (void);
extern void AsyncOperationHandle_remove_Destroyed_m8755ED149C64A82AF2EF9710F0D9256163ED71C9 (void);
extern void AsyncOperationHandle_GetDependencies_m188E3F0A06DA1F789B431E3401D692AD14F45C39 (void);
extern void AsyncOperationHandle_GetHashCode_mA261FCCE8588545912EAD2AC611FADD3F104DF15 (void);
extern void AsyncOperationHandle_get_InternalOp_m4EF5B7F816250889F1CB8917837E3A75B249396C (void);
extern void AsyncOperationHandle_get_IsDone_m55E07BF92CC2979A9BBFD8F5B97DCCD52DA1326A (void);
extern void AsyncOperationHandle_IsValid_mC57B65EA4D8E084859EF42FD671EDA1E2ED46626 (void);
extern void AsyncOperationHandle_get_OperationException_m531FC4DC2E215075C0EA7CE135FF1D68F6049270 (void);
extern void AsyncOperationHandle_get_PercentComplete_mB5B48174489343D707D9E4A90BAFE24658D135EC (void);
extern void AsyncOperationHandle_GetDownloadStatus_m526B0F8EC8EF9A00D4F59D5BC918913CE2BED374 (void);
extern void AsyncOperationHandle_InternalGetDownloadStatus_mC00D428E94E347EE6FCD224530016D1F9433B33C (void);
extern void AsyncOperationHandle_get_ReferenceCount_m28F7ED9B712CC95D3F1E304578F94B6EEC1A5B3D (void);
extern void AsyncOperationHandle_Release_mD4ADD2AA46F56753B12E0916FA2A739A3EBB5762 (void);
extern void AsyncOperationHandle_get_Result_mC319B351EAF9A8C76AAEB948BB3BC17F94AC9746 (void);
extern void AsyncOperationHandle_get_Status_mD5FB502808777A3C88880942DB9595C614CF9B0A (void);
extern void AsyncOperationHandle_get_Task_m74053C375ECE675441967B0EA68091A7D9F84EF6 (void);
extern void AsyncOperationHandle_System_Collections_IEnumerator_get_Current_mBAF3C14B4A6BD17BBBE6663D0061A1DD42E8FE37 (void);
extern void AsyncOperationHandle_System_Collections_IEnumerator_MoveNext_m8A793FA1EEE2DAAD8B74F95FFEC60D94B9B8610E (void);
extern void AsyncOperationHandle_System_Collections_IEnumerator_Reset_m8921AB145EB1F53FD4352BE9E512505719FE6527 (void);
extern void AsyncOperationHandle_WaitForCompletion_m4F5203EAAEAA1F724EA1220006A43E89A0951784 (void);
extern void U3CU3Ec__cctor_mBADB56EC5E7D6B12C46AE6B5BEC93AB7BA55E4BE (void);
extern void U3CU3Ec__ctor_mF4359AA07619298B97298F370375E0D44C85BF77 (void);
extern void U3CU3Ec_U3CReleaseHandleOnCompletionU3Eb__16_0_mCB90700F81C08E5E333EC4F29585BAA0E5DC1B1A (void);
extern void DownloadStatus_get_Percent_m0EBC4A32546FB0EA6099462E3D6B7C9168F93985 (void);
extern void GroupOperation__ctor_mAC487B41FFD0ECF87B5042B10198D55E8460FB26 (void);
extern void GroupOperation_InvokeWaitForCompletion_m58F701EB4E389EC5FCA0E834852D3FA88530C284 (void);
extern void GroupOperation_UnityEngine_ResourceManagement_AsyncOperations_ICachable_get_Key_mBE090D49C1F51ACDEB43492606BD9BEEC3E0DC43 (void);
extern void GroupOperation_UnityEngine_ResourceManagement_AsyncOperations_ICachable_set_Key_m45EACDDD26430787AC6D4D45297312F87B6BA59D (void);
extern void GroupOperation_GetDependentOps_m9FE0CB241C9F3630AA74C2C00539CECFD817B1D4 (void);
extern void GroupOperation_GetDependencies_m4C7707C62DB7B67454BFDABC5B2C31BDEC74B395 (void);
extern void GroupOperation_ReleaseDependencies_mC61C8336FBDB7D9D6CBFF04E772DDEE94E1650B2 (void);
extern void GroupOperation_GetDownloadStatus_mCC253D27B3D655D0DE72A115047A298D32E6FBFB (void);
extern void GroupOperation_DependenciesAreUnchanged_m961C61C0E83B18B91E4C3A589DD5C98C5FA027D9 (void);
extern void GroupOperation_get_DebugName_m3B4DD1AA206F8FBE96076A01E17258594826B90B (void);
extern void GroupOperation_Execute_mDACD8D19DE8A2916CC5D12093AC39E7709C826DD (void);
extern void GroupOperation_CompleteIfDependenciesComplete_m0CBDC4B0BC6DEB0F32D2E9884F99D6EA4FBFD346 (void);
extern void GroupOperation_Destroy_m16201058C128455D5672E019015B4657ABD9614A (void);
extern void GroupOperation_get_Progress_m5CB53EFD6FAD043CE2D65F8028A2579F8A51B5AC (void);
extern void GroupOperation_Init_m8C19E45ECB5A7DB7895A83EE0F918CEF60FD932E (void);
extern void GroupOperation_Init_m63F2FE2DAA01637218A993C4008090339ECC1EC0 (void);
extern void GroupOperation_OnOperationCompleted_m9FFD2AEFFDB99EC7D040F53B0D5B62A60D6D23A7 (void);
extern void UnityWebRequestOperation__ctor_mA83D2F7455DD870CD8AF192415C0DFD29F054F62 (void);
extern void UnityWebRequestOperation_Execute_m6CDFDEA8F294580DD53D1C9D7B27872BBFA42115 (void);
extern void UnityWebRequestOperation_U3CExecuteU3Eb__2_0_mBFE21A77BDFDB2293AFCA6AD6CBE77D766921B4A (void);
static Il2CppMethodPointer s_methodPointers[892] = 
{
	EmbeddedAttribute__ctor_m9E9B7C95C8B9EF234AB1705D208AE318A486E26E,
	IsUnmanagedAttribute__ctor_mB4C2A08461C05B21855918E4E33E1EEC97B9D51A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	MonoBehaviourCallbackHooks_add_OnUpdateDelegate_m75B55B30965281C86C4A349DFBE713833E74853C,
	MonoBehaviourCallbackHooks_remove_OnUpdateDelegate_mE887431AD33057B98989EEA888B2342D18FB5DE4,
	MonoBehaviourCallbackHooks_add_OnLateUpdateDelegate_mBC77E034EF867B93E17C722BE198B047AD17C277,
	MonoBehaviourCallbackHooks_remove_OnLateUpdateDelegate_m0BB14D3E187A7227D840F06366E3E3B10F49B27A,
	MonoBehaviourCallbackHooks_GetGameObjectName_m5E0B21C825DC616AC283318401D06368388779FD,
	MonoBehaviourCallbackHooks_Update_m78B3E22F89771DAF1DF875FA9143E1071C29A1B2,
	MonoBehaviourCallbackHooks_LateUpdate_m45A4B8C30A3FB1D56C4B8D8D869A6E2C020F2778,
	MonoBehaviourCallbackHooks__ctor_mBC4522B5FA1CA9A734337F48AEDE1A66F2C75796,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mA0D7683C73544BE6BCEF283CD7F1A6A65AE932D2,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m14D67F405BB2F69B9DF9D4DA781F79B7BD947160,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ResourceManager_get_ExceptionHandler_m438ACA3A226A2BB1F49C225927707FC47E3FDFC8,
	ResourceManager_set_ExceptionHandler_m852F86350D52609D6CC714E6BADF3B22FA377EC8,
	ResourceManager_get_InternalIdTransformFunc_mF9E2EB179C238ED082683966C36F07EDC85F9087,
	ResourceManager_set_InternalIdTransformFunc_mFCF127EC19BDB3AD31291C55CFB1242E853F3DC7,
	ResourceManager_TransformInternalId_m2C99EDD12AF07F099F7FF88E15892F2582C17EB9,
	ResourceManager_get_WebRequestOverride_m5B5944646CCCFEC195AF713DC15F490EDA9EDC12,
	ResourceManager_set_WebRequestOverride_m79E2560E6B98E77E402ABDD96308DD8FA284B0EA,
	ResourceManager_get_OperationCacheCount_m158FEB6EAE44293574466B2F6688AF2E0ECFD486,
	ResourceManager_get_InstanceOperationCount_m4D4A04EF0A6E20AFFDDC67BE03FD479874207366,
	ResourceManager_get_DeferredCompleteCallbacksCount_m374F1A015F29BA6DCA75462E9A1C93AE9E486527,
	ResourceManager_get_DeferredCallbackCount_mA0651DBE0BFC38678DEDBE10F0395D8253FECCD3,
	ResourceManager_AddUpdateReceiver_mCEC1DDE2E64507B7210293AC1C5A6E5849AA29B5,
	ResourceManager_RemoveUpdateReciever_mABD054857B61EF2D434CE0EC6E5A0C5989AFBB74,
	ResourceManager_get_Allocator_m2F215AF545EE1A8EEDEBFBFACA32D5E0B44A74B3,
	ResourceManager_set_Allocator_mC26F1C9C4A0495CD597D6731FCAFAE8DC6ECA616,
	ResourceManager_get_ResourceProviders_mACBDB6BBAF95684D548E67073ED0067707D87A63,
	ResourceManager_get_CertificateHandlerInstance_mFE0A1AC4ACD430F7FFE076B3AAF8DE302E47BDC0,
	ResourceManager_set_CertificateHandlerInstance_m0CF67BF1839238F4EC7A4709D419AC11FF338EF4,
	ResourceManager__ctor_m40746752A00415417500A9941BD8A55116461702,
	ResourceManager_OnObjectAdded_m1B908412F2E8898FB93DDA7F1C476E7FE087FB35,
	ResourceManager_OnObjectRemoved_m75B3D837CDC9FD91B3B5345A34415B5129A68038,
	ResourceManager_RegisterForCallbacks_m805F04D79BF5BFF608E92C029378AA498A0966E6,
	ResourceManager_ClearDiagnosticsCallback_m44780BEA8C1BABFF4FB58794B27949B5DC82CD96,
	ResourceManager_ClearDiagnosticCallbacks_m9A2DFAA7BFD3AA6F918E7C6D9E6403C55C50ED7D,
	ResourceManager_UnregisterDiagnosticCallback_m48C1E860430066DD097E4F662402770803558A84,
	ResourceManager_RegisterDiagnosticCallback_mC19F916DA591E4A0DB9B747A19AADFA5B8499B46,
	ResourceManager_RegisterDiagnosticCallback_mC40ECD5723511F7C77475A417F693C84C4CC3378,
	ResourceManager_PostDiagnosticEvent_m3D762299D2B95B1FC8AE649FDF1D79C6BF4CE67C,
	ResourceManager_GetResourceProvider_mFE9FDDC3322999023EF072BA071A2C5207993507,
	ResourceManager_GetDefaultTypeForLocation_m43F1C1C3F03D24E0C9039A081A921998664C895C,
	ResourceManager_CalculateLocationsHash_mABD7FA03ADC150005862C7A310CE3B0098DD56DA,
	ResourceManager_ProvideResource_m601D10E98FA55391A6CF87035D16C5D4D45BD54F,
	ResourceManager_GetOperationFromCache_mF1A0849D075E13611F475A8A19FC39E20BA7DCEC,
	ResourceManager_CreateCacheKeyForLocation_m4ED1F69B2AFC87D5B6481DBEBD0963438768C6E7,
	NULL,
	NULL,
	ResourceManager_StartOperation_mF353B4A27D8904A5BE0CA88DCECD34BFFF283428,
	ResourceManager_OnInstanceOperationDestroy_mEAFE7F84B34E5D426D4185936321DF391E1D77D2,
	ResourceManager_OnOperationDestroyNonCached_m7C5078CF3525865387CE15063E1CE2389465985A,
	ResourceManager_OnOperationDestroyCached_m93E5D09DE26A4D00F1F95375E98D53B2416CFD1B,
	NULL,
	ResourceManager_AddOperationToCache_m1399C5F2815D81712B2CCC09EA9ADF1EA13CC934,
	ResourceManager_RemoveOperationFromCache_m1CB4CDFB4320AE9B7191BCE7F77441274F5A42FD,
	ResourceManager_IsOperationCached_m2B1EDD504388B6A568DAFC5417F719F85947A660,
	ResourceManager_CachedOperationCount_mCADF179F6814BC055FB4D1A0CB0B588A35D71C1A,
	ResourceManager_ClearOperationCache_mEC0A3D747E2B24F7079771578640443159C96986,
	NULL,
	NULL,
	NULL,
	ResourceManager_Release_m4A5F5DE044CDEA3D898AD3D665295E4B2CAC3D37,
	NULL,
	ResourceManager_Acquire_mC6890CE5E48C3C5A999E630959F359F2DC6C3FE4,
	ResourceManager_AcquireGroupOpFromCache_mD24182CB63E47A7EB10183750BA4038050934A52,
	NULL,
	NULL,
	ResourceManager_CreateGenericGroupOperation_mD157A8B840D805E4DE84C4ED116E42807FFC54A1,
	ResourceManager_ProvideResourceGroupCached_m4BE9B9B2D6DD75747B7978BC8EC9664FCDBD10C2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ResourceManager_ProvideScene_m7443C66FADC9A66CA7E073DA080AC25BEB8FAC7F,
	ResourceManager_ProvideScene_m3442E1C8AC16048A50187756655099995CD2E8A6,
	ResourceManager_ReleaseScene_m8AC172F7EC35C2ED4DCC308BDFCB2F9C7FB24CDB,
	ResourceManager_ProvideInstance_mF1EF383A4217EF54D26F7E414ED03AAE2CBF3D91,
	ResourceManager_CleanupSceneInstances_m462175052395535F374CA0A1E1DE52E922504DF4,
	ResourceManager_ExecuteDeferredCallbacks_m7BB2468360D4AAF86E437E93E9D52E134AE4AA7F,
	ResourceManager_RegisterForDeferredCallback_m05889AD5E7D950770177A9B2920B4736546B0225,
	ResourceManager_Update_mE18FA475CB6F6B7020A53A51B0FAD323819F7B4C,
	ResourceManager_Dispose_m850F673638C3ABDBA6B5E6D7A03D5B8DDA0D67B2,
	ResourceManager__cctor_m2C88F867A5BFC4F01082256E7775C5459C60577E,
	ResourceManager_U3C_ctorU3Eb__57_0_mBF579CFFC10E01AFD1B618936D87FFA9DE9C6DD4,
	DiagnosticEventContext_get_OperationHandle_mC766CD6C074CD2A0E3486FD41CE0B951B25B3FA6,
	DiagnosticEventContext_get_Type_mF65C73B3A0D1958041136121E877B4AF9ECFCEBB,
	DiagnosticEventContext_get_EventValue_m1109E4CB1BFA64FEDC13FF72B293B68DA2D0A94F,
	DiagnosticEventContext_get_Location_mEFDEFECC834FF8AA3B9542889DD449BF07A5EF5F,
	DiagnosticEventContext_get_Context_m8DC1C15D121AD87B29AE6FAF53B6797221E2224B,
	DiagnosticEventContext_get_Error_m1BCCCD62C4402CD13F6E0E071111AF0BEEA00BD3,
	DiagnosticEventContext__ctor_m14E2DE6A37749D242E166A1CA6B94AC0F99D4C51,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	InstanceOperation_Init_mFCAECB1B405E9722192270FFFA4DE314CCB29146,
	InstanceOperation_GetDownloadStatus_m7EABB0A922CEC00F1001409EE5B3BFEDA21E0D5F,
	InstanceOperation_GetDependencies_m1076C4940D0C541FE934369210B799C1DA061529,
	InstanceOperation_get_DebugName_mDC16EA8E13BCC0A14642F9330A68A3B947833C15,
	InstanceOperation_InstanceScene_m673A930720C94D7EC8618855441A78A4D2A576D2,
	InstanceOperation_Destroy_m48603106B240748E521EBDF9D396FA0099183648,
	InstanceOperation_get_Progress_mD7CACE3A9173DDEF163079227400C09C6C0D1F1F,
	InstanceOperation_InvokeWaitForCompletion_m7E519B113DA5F45DF89D12AA2FFAF2A808B81FFC,
	InstanceOperation_Execute_m01F6E265405DD013116E1C90C2B2CD1F713411EE,
	InstanceOperation__ctor_m850B9D9A3CE6D26FA813A7D617C55DE1C005062E,
	NULL,
	NULL,
	NULL,
	NULL,
	WebRequestQueueOperation_get_IsDone_mF8B3459F19B874A0D0047DDEA4DCCF2FA0DFF813,
	WebRequestQueueOperation_get_WebRequest_mB3094AE07CF5BE54F45DB4EA1F3BC2576AD26062,
	WebRequestQueueOperation_set_WebRequest_mC166F25E13697FC277FFE136DED700E6AF1CE943,
	WebRequestQueueOperation__ctor_mF6E7DC7A72C723056182035AAFFA7B28AF1F0007,
	WebRequestQueueOperation_Complete_m74FC3854CF7B21E72C64FE945830A6250BBD127D,
	WebRequestQueue_SetMaxConcurrentRequests_mFC18BE6652727D9B95CDB29866D4E51C211D9BD6,
	WebRequestQueue_QueueRequest_mB468AF5AEE67CFB938B8A623BFFE0E4A71D97644,
	WebRequestQueue_WaitForRequestToBeActive_mC4448801875550E419C6FE8B211CCD02B2CE413A,
	WebRequestQueue_DequeueRequest_mB13E57C770245EB2F6EB2D54CC9EC00895880657,
	WebRequestQueue_OnWebAsyncOpComplete_m63203BDC4450394C87962F4A3AF878C1C6DB7735,
	WebRequestQueue_OnWebAsyncOpComplete_mABC4467B1BA3504890D1116198A6EE0622E16A19,
	WebRequestQueue_BeginWebRequest_m533B9C62E91DFF7CF0A63894800D1A3A0CB18751,
	WebRequestQueue__cctor_mDF1BE18609A3DE04B602056244523D63B06E3E67,
	ResourceManagerException__ctor_m100F5F75AE8F119C04634B839871DECF2A9E3773,
	ResourceManagerException__ctor_mADA995E60805D9B18593A09BDAAFEF7778D44092,
	ResourceManagerException__ctor_mC3F8CD1DEC2E9AB49D0109669BD40D7EFAA6643C,
	ResourceManagerException__ctor_m1AEBB2A25745FEE316B7A2A7A147D5C075938210,
	ResourceManagerException_ToString_m02FFE7C99044E1165624FD1366817B2EDD4093FF,
	UnknownResourceProviderException_get_Location_mC769291CC27D81B8573117354AB08E222DD68CF8,
	UnknownResourceProviderException_set_Location_mC4689284960B91DB49E674BDAF259530C3F4B421,
	UnknownResourceProviderException__ctor_mF41159A0C17377B2FA88EEB384873A9E74C0B9D4,
	UnknownResourceProviderException__ctor_mF53A3788AA2BC62D3DD8B0480FEF37FE39D58DC7,
	UnknownResourceProviderException__ctor_m83BA024FBC7B1044B4F661C70C5D75C5936EC598,
	UnknownResourceProviderException__ctor_m44168BF7B02437B561517802A010C3D10C79DFFA,
	UnknownResourceProviderException__ctor_mEB7F4AAED15D3A44100003D65A4D81C4BFC432F3,
	UnknownResourceProviderException_get_Message_m1A75337FDA1BE4FEA28CB84A8C1542034198B91A,
	UnknownResourceProviderException_ToString_m3515DCC45986154B425A190CCDC6CABA516C5C7D,
	OperationException__ctor_m86A0486864C0BBA58E2688D3FB650CAB1D616D59,
	OperationException_ToString_m8544F18BF5A2A8B6194DBF46F672DB8C128AA34D,
	ProviderException__ctor_m298FE0E1B736CBE00C44FDC9D6C960F7A927830D,
	ProviderException_get_Location_m9E9ACD664198BD17DD1CC1E61B8201966C261981,
	RemoteProviderException__ctor_mB262F595DD1E23F24A37B5AB6142B8180CFF70C1,
	RemoteProviderException_get_Message_m19B282D7E74FAC7DBFECBEB7A57D1B1D9488F7CD,
	RemoteProviderException_get_WebRequestResult_mF80BDC58604D4D5C44AB968FA544CFD30C330954,
	RemoteProviderException_ToString_m4D0CD66BDF24920556E4E8A4F418B457AC696AE4,
	BinaryStorageBuffer_ComputeHash_m528E30FBF559FD6F302F7F415507C52E90292E14,
	BinaryStorageBuffer_AddSerializationAdapter_m9E562666689E6AA8A3B1BE202655C8D154091445,
	BinaryStorageBuffer_GetSerializationAdapter_m926A4AE15D4DF09AAEFA943F4F0996B1B48A7E52,
	BinaryStorageBuffer__ctor_m7C91551045C72775B42F5AD205282DD3BD8889B0,
	BuiltinTypesSerializer_get_Dependencies_m304075F813F5F1FDB31D5D9906D27A6CEE4FEA12,
	BuiltinTypesSerializer_Deserialize_m13587FECB446BF4B75ED1BF275433DF945E73AF7,
	BuiltinTypesSerializer_FindBestSeparator_m53A1EF2FF91EB254F7BC621A76BF80553FFD0E19,
	BuiltinTypesSerializer_Serialize_mFE61CCB900707102001182E788A7B87BE1AC0227,
	BuiltinTypesSerializer__ctor_m3CD66405A9781475B59EC177E67014D793C596ED,
	U3CU3Ec__DisplayClass4_0__ctor_m21D66D59BDCDBA8D1DEC929411CBA518F958281F,
	U3CU3Ec__DisplayClass4_0_U3CFindBestSeparatorU3Eb__0_mD72C04C02990117C3C8AE3564D0CA797D4D69C41,
	TypeSerializer_get_Dependencies_m00EFD21CE8F4781F513F2BC387DAFD3AC97BB082,
	TypeSerializer_Deserialize_mB4E74C5726F4C5C668242DF7FBF32B3346B086BA,
	TypeSerializer_Serialize_m96FC3B549AB21CE3E5A41AC0A70D16761E3DED76,
	TypeSerializer__ctor_m23FFD996ED2480137E5AE3A79AC4108011C07E45,
	NULL,
	NULL,
	NULL,
	Reader_Init_mACBC96ACB9DF1553E454FD5F53C164370C2D511B,
	Reader_AddSerializationAdapter_m3F3EFCBC5826EEA95610C666FBB39DB19ED7CC24,
	Reader__ctor_mB0946D4FAE0056EBC1F6BCC574ADFDFD1FA4AE23,
	Reader_GetBuffer_m2E63A09C64EB8D4A8C36D31B2FD8F129265C4950,
	Reader__ctor_m89BF59E7249CF521A8F8F80BD79C93BFD8F542EF,
	NULL,
	Reader_ReadObjectArray_m241DA7B2226946A40D3342DC29EDDE078D910CF8,
	Reader_ReadObjectArray_m0BF1584BE63B4DC708CB7CF482D7F482D48F7BD0,
	NULL,
	Reader_ReadObject_m13F32FF600E765BE01A75D699C09DEFFEED0CA0F,
	NULL,
	Reader_ReadObject_m60087ED32721C8797522F51D63896A0B8D800BA2,
	NULL,
	NULL,
	Reader_ReadString_m5FAB8E9CC8DA614FD0409114FDCF6F8972D280DB,
	Reader_ReadStringInternal_m9D382B47237FD6556BFC1A1688A45FF0F051431B,
	Reader_ReadAutoEncodedString_m4F418749F718A5AE36380D2861E68900A90F0AEC,
	Reader_ReadDynamicString_m23CBA55F75EF318044F322933E035A4A14D10EA9,
	Writer_get_Length_mC059DA7190BE51B24FC8D35AC9EE4A8C6E2CFFD8,
	Writer__ctor_m995376F691120630AF38CB59E47048550004D2AF,
	Writer_FindChunkWithSpace_m81B652C6FE31B0A45E2F90A87E9EA02092ECBE77,
	Writer_WriteInternal_m928A20E0AAF2E545B89937DE02232473803E0DBB,
	Writer_ReserveInternal_m9A1779F90497775A6053B8485FCFAFF3CDC5AF73,
	Writer_WriteInternal_mFEC1AC834A029D7D9FE87381C44967BFDB68EA64,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Writer_WriteObject_m9AAB78B632B61A8D5D2B0BC0C250CF70B8C8B371,
	Writer_WriteString_m64238E7AF7A22461C56670D5CABDD9CB885A124E,
	Writer_WriteStringInternal_mB4E4841F1751962212DE70AF9C2A2283E9ACCA3E,
	Writer_SerializeToByteArray_m64133F9FEF91FC3C3602E95A4425DD25B73D8F3A,
	Writer_SerializeToStream_mD44C3D89C6FFB7D1EA031AFA6B0447726FD04D65,
	Writer_IsUnicode_mFC41F9B7FE90C454D0148CE3B3ABBF03CB50C3D9,
	Writer_WriteAutoEncodedString_m78B9E6426E5C73E01AB308F9765132E402A30B39,
	Writer_WriteUnicodeString_mF197A68D41E8A308DDB0D2077B8C3DAFCD8F734B,
	Writer_ComputeStringSize_mCD7A3364EBA86185DC911EDB2383316574F9CE24,
	Writer_RecurseDynamicStringParts_mA6C3D591057C0DD50FCCFDCDCC60043F12A65391,
	Writer_WriteDynamicString_mC4B08A023971C752763C69B23AD7FF4F5FE3F6AF,
	Chunk__ctor_mFE9807198CD091F856E1EF6D83B09426E85D81BA,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DelayedActionManager_GetNode_mAEF3F54F608E37822E2F8411BC021617051C6262,
	DelayedActionManager_Clear_m175CCC934CD98C808FE583B7DDF563BA1662C768,
	DelayedActionManager_DestroyWhenComplete_mE9C248BD5D22972468E21FD8561074E661851EE9,
	DelayedActionManager_AddAction_m1B17561BB6B6579F169EA8056C4D9CA1160A8D29,
	DelayedActionManager_AddActionInternal_m1EE246770285D9A30BB5BA5B7892D01738E0BF61,
	DelayedActionManager_get_IsActive_m994AB293A783993C9952741A9A296314D36E18BA,
	DelayedActionManager_Wait_mBBFDE2F587ECE8D53F45052CAACD4041CFA5EF30,
	DelayedActionManager_LateUpdate_m1B1943FA626B6A54B91AF815921480DAACCB8F48,
	DelayedActionManager_InternalLateUpdate_m38B7F66D591F997005D99A5078B8233E568CA25E,
	DelayedActionManager_OnApplicationQuit_mA1A036CA8A7A9D672E5405654324C173A6BE61D4,
	DelayedActionManager__ctor_mD942BE253CE9C25C2DA06B48AF787572D47B6C00,
	DelegateInfo__ctor_m08FC6B330A4593BBBB9C28877297E0C157D78DF3,
	DelegateInfo_get_InvocationTime_mA102D6CDFFEECC935EF0097705D0081C59D8B030,
	DelegateInfo_set_InvocationTime_mF31701BE6AF283B3887E76D6DE7653325F2BF645,
	DelegateInfo_ToString_m31AC2460408EA1D16E861E230B274BEDB0B83EAA,
	DelegateInfo_Invoke_m87A58948D2BFD8A13F922B5D7DA547EB191CA8BD,
	IdCacheKey__ctor_mA4092FB0B18300CA3B48D255D5D3F41D23EB9852,
	IdCacheKey_Equals_m7DF07E6380476F64C0FB71EC1B3184C7C2E8B099,
	IdCacheKey_GetHashCode_m48A78A533F677AF9FBDECC0D96B20D2A3EF63C97,
	IdCacheKey_Equals_mD244999E0AEB59A8DEAB7843A1EF9A083ED36F21,
	IdCacheKey_Equals_m43E8A1D18394FBC32EA7D9FD710AEE8970A9F886,
	LocationCacheKey__ctor_m282925A8CF5CF9120CF2CC9AAEF5BEACA2CF8148,
	LocationCacheKey_GetHashCode_mB1EBEF7D956D6ED42C5DAF9B4AA1EA02F0CE4134,
	LocationCacheKey_Equals_m1C52245232D45B246971CDFFA407DA544867DD71,
	LocationCacheKey_Equals_m8F8728EE1F86BE82C84DCC9F79455AE2965B37D7,
	LocationCacheKey_Equals_m8BEF54161FE13E606502DC9D9A6B2C40503E89BF,
	DependenciesCacheKey__ctor_mF897DD52A12FB3D9A8A636072A04F00E73F9AC75,
	DependenciesCacheKey_GetHashCode_m6420F6AEE55C5DBFBEF8BF629A58E4E50E12C574,
	DependenciesCacheKey_Equals_m1AE039CBE8A5E1DADAE46CFBD540899B65895043,
	DependenciesCacheKey_Equals_mB672EB7720C18FE5713D8F70DFA109D617B278C8,
	DependenciesCacheKey_Equals_m42F09EA998F1BA6F2B524D9668DD4A9FBFCF71CF,
	AsyncOpHandlesCacheKey__ctor_mCA58B5FAB01E383F945DD6912328198DA13FA90E,
	AsyncOpHandlesCacheKey_GetHashCode_m51CCAF8584F6E54572B1D378D59BC5297BD999DB,
	AsyncOpHandlesCacheKey_Equals_mBFBDF929A1325136C4E8A1717C0B1C12EF19B670,
	AsyncOpHandlesCacheKey_Equals_mD987EBDF7585F0899C89799363A0CFBF5FB96788,
	AsyncOpHandlesCacheKey_Equals_mAB06D007F68B5910A36E6C97D278147D1541F3E3,
	LocationUtils_LocationEquals_m1FD11D1B96DA6CB5C997F4FF70E5422B1782F6B0,
	LocationUtils_DependenciesEqual_m33CF7BDD2604A392F1E10D9F492AABE6B9EEF548,
	PlatformUtilities_PlatformUsesMultiThreading_mD9F4A5FBF83C37E67662A2424921967DCDB3E1BC,
	PlatformUtilities__ctor_m0AA459F235118E11C4950BECE8BF6CA419621FBA,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DefaultAllocationStrategy_New_mBECB62C44432B753AB02A0FFCF50666D01E108E6,
	DefaultAllocationStrategy_Release_m63A6725F306906C2067E7D4B94A9B88DB7418E86,
	DefaultAllocationStrategy__ctor_m4E5FDD0B0190A871B875CCA76972BC3BE5021C56,
	LRUCacheAllocationStrategy__ctor_mE90C189EB59AEE251D83C85F1C6422EE3F3FB541,
	LRUCacheAllocationStrategy_GetPool_mB62AD68905391E611B6DB22C295DEF5D0A883AD5,
	LRUCacheAllocationStrategy_ReleasePool_m3137C6983551DC95420A39C44E61ECA466A75B63,
	LRUCacheAllocationStrategy_New_mC1CFDD601073707E84257F2769AA6D1C258D28B6,
	LRUCacheAllocationStrategy_Release_m64D717012A7423ADF6D16BDAE6CC83C955B5C163,
	SerializedTypeRestrictionAttribute__ctor_m1A10E6EE1B4AD72C790F7CB7210522A1C19477FF,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SerializedType_get_AssemblyName_m42ED37D1ED570F31D87F2CF9486CC83954FAFCD8,
	SerializedType_get_ClassName_m1597BC3A9C2E8EA40F35F8CF03B7591618AB96D9,
	SerializedType_ToString_m286E5553A848CA6C472FCE478B65C01B3B2AA5E7,
	SerializedType_get_Value_m8AC9DC985380FD7524D147E2C5C95664FAB9A10A,
	SerializedType_set_Value_m1AB236ECAE27E66A4B2302B4573EB71A7762F679,
	SerializedType_get_ValueChanged_mCD14490D0BC812C462831E2BD634A60E24243D34,
	SerializedType_set_ValueChanged_m91FD96EEB5883E2ED8B507EC3CC668109928E1E7,
	ObjectInitializationData_get_Id_m8B020C841845122EF9123BD5E136F6A3FEC3496F,
	ObjectInitializationData_get_ObjectType_m7706EB3247D33B65F062CDEEB40792A27B8ABF69,
	ObjectInitializationData_get_Data_mEFF201461CA817CE659A8703EE2AFEBF53824318,
	ObjectInitializationData_ToString_m730570E98783E54D1B6A2ED0178E05A2DE394593,
	NULL,
	ObjectInitializationData_GetAsyncInitHandle_m0017D1CE9481015847F724E9E22C481A2CFDF2FC,
	ResourceManagerConfig_ExtractKeyAndSubKey_m1A27EE5940B51FAB5B5C9F1E0F78080D41A43CE1,
	ResourceManagerConfig_IsPathRemote_m3F7B514A149162329D210C849F3B5D99A77ED156,
	ResourceManagerConfig_StripQueryParameters_mF4AB37F488D95F095D6DD266B767238E7C273EC7,
	ResourceManagerConfig_ShouldPathUseWebRequest_m9B72A8B38C30946642D7FF983D89277596A4A35B,
	ResourceManagerConfig_PlatformCanLoadLocallyFromUrlPath_m0BF372FAE10BEF17D7593896328D1DC567CE70B9,
	ResourceManagerConfig_CreateArrayResult_mE26798757DCC20CB22CE1DBFB13EEED145CB303A,
	NULL,
	ResourceManagerConfig_CreateListResult_m7E247CBEA74D2E3E6F4B5E7795CBDC314F91D83B,
	NULL,
	NULL,
	UnityWebRequestUtilities_RequestHasErrors_m44D9F211227DADD941F2E19D8273495D3F41B686,
	UnityWebRequestUtilities_IsAssetBundleDownloaded_m424AE91A573AE10DB0ACFF7969FA6794F6C5F256,
	UnityWebRequestUtilities__ctor_mBBC1319918D5F9CE7DC96D260D4A06B165FB9D2F,
	UnityWebRequestResult__ctor_mE837C228770475E93E476203E7EFCA9BDC807168,
	UnityWebRequestResult_ToString_m1F2EF13FD90347BA1B8752D2D534D007BAF14390,
	UnityWebRequestResult_get_Error_m84C4381776A6467DE9CC4DB3797661F1519FBDF5,
	UnityWebRequestResult_set_Error_mB8AB258796A2F29BB387F34222FFA59EA3431009,
	UnityWebRequestResult_get_ResponseCode_m9587BFE958C864F3B4744E78890AE09C1E5CF3E2,
	UnityWebRequestResult_get_Result_mFA65ADAE33B5F1037C60A4F544F54800114B8DDA,
	UnityWebRequestResult_get_Method_m170F095C8F0E614D35EC882874DA221CF7FD372D,
	UnityWebRequestResult_get_Url_m692560D0BC25EA558272192CED8409364335F477,
	UnityWebRequestResult_ShouldRetryDownloadError_m0801EC2326AAE1EF661B1361F7EDC2878D4EB719,
	DownloadOnlyLocation__ctor_m5C682847E5CC673CB596207315CEC153BF116EA4,
	NULL,
	AssetBundleRequestOptions_get_Hash_m5241C40F21D24CB9796C5A6B71692FCABA061F79,
	AssetBundleRequestOptions_set_Hash_m15AA10A2ED59974D2BEC4727D2087891A3F2F383,
	AssetBundleRequestOptions_get_Crc_m638E8363BD60FD574E69B337042C4BC8E16B3AE9,
	AssetBundleRequestOptions_set_Crc_mB5262A0ECF589E86D7F2FF3B6A06B11EC9F322DA,
	AssetBundleRequestOptions_get_Timeout_mE116648854CC272B9D579758C71F4D59646A555F,
	AssetBundleRequestOptions_set_Timeout_m4D40D90135EFDB499654B97E189BF5F284F0B94B,
	AssetBundleRequestOptions_get_ChunkedTransfer_mE2A8C59F151B1092D4A23613239715606BD08B07,
	AssetBundleRequestOptions_set_ChunkedTransfer_m14451B19037C6DB8A43937ACF1B04429FBD5305A,
	AssetBundleRequestOptions_get_RedirectLimit_mDDF7428FD93DBCE5FA5C953DF09C3E1F7D2BFD8C,
	AssetBundleRequestOptions_set_RedirectLimit_mDCB72FD4DBA97492FA95BD06C50208C6DEEA4D2B,
	AssetBundleRequestOptions_get_RetryCount_m13E92C2312B338C121AE8C1C9979B2E3719F385A,
	AssetBundleRequestOptions_set_RetryCount_mE25CEBBF0610F063DAB0600352CAD53023B19591,
	AssetBundleRequestOptions_get_BundleName_mC6440526C8EA2D3AE637F9708366D49D92AEE07D,
	AssetBundleRequestOptions_set_BundleName_mFC5719990D23E342388FC8E8D40CA9F8FDF9C571,
	AssetBundleRequestOptions_get_AssetLoadMode_m7C8EEB76C1AC675E76B7227E2B6D1B75194B2702,
	AssetBundleRequestOptions_set_AssetLoadMode_m906864B7AC6863322225B1EF0FBFF29C50CD3D68,
	AssetBundleRequestOptions_get_BundleSize_mBF9BE237E555C6369CF5980696A5B7F4AE12D1B1,
	AssetBundleRequestOptions_set_BundleSize_mBEC0BFD2694FB465EF71A626BAB0DAED839D340F,
	AssetBundleRequestOptions_get_UseCrcForCachedBundle_mEA782A87DBBEEC57AD4C60376F585D719A3E8227,
	AssetBundleRequestOptions_set_UseCrcForCachedBundle_m34DE20707D0A081FAD76F9C31E90D39647438B95,
	AssetBundleRequestOptions_get_UseUnityWebRequestForLocalBundles_m757E1D4C7C0EA98A50A2DB1834D3BF44680BDD5A,
	AssetBundleRequestOptions_set_UseUnityWebRequestForLocalBundles_m1E382181788427AF5716B4A52A130E935F89104A,
	AssetBundleRequestOptions_get_ClearOtherCachedVersionsWhenLoaded_mB153677F48A4B47D247D161E49E913B9BDC51746,
	AssetBundleRequestOptions_set_ClearOtherCachedVersionsWhenLoaded_m77720BB6E0F99946304DF20351DBC58A0B7F5AA0,
	AssetBundleRequestOptions_ComputeSize_mB11B143445F4C29355A29326B23AB38D91C5ABC2,
	AssetBundleRequestOptions__ctor_m0F5D92A41947FF6CFB4267185309F205B1C88DEE,
	AssetBundleResource_get_HasTimedOut_m2B47177ADC99FE754F7D05E86E7AA624CEF93D16,
	AssetBundleResource_get_BytesToDownload_mAE341F337C62984133F1898982EEA4C1A816CC50,
	AssetBundleResource_CreateWebRequest_m99093A2324F69D68ABD3D922E0A0D7F02309C245,
	AssetBundleResource_CreateWebRequest_m79DE1A126FF993C44A29062AB674C28B871C00C9,
	AssetBundleResource_GetAssetPreloadRequest_mEF40A5A8DE1362FF6BED69D026EF9DB165E21BCF,
	AssetBundleResource_PercentComplete_m47D08300A9374509CA6AFB646EF7B00634015935,
	AssetBundleResource_GetDownloadStatus_m5C4B65566B058DC9736A9416C44D914B3A6814D8,
	AssetBundleResource_GetAssetBundle_m2677FCACAC574D7C60992D0FEB7EABDFA962CF63,
	AssetBundleResource_OnUnloadOperationComplete_mA30A75EADD9F1392EC42FFC836ADBF8A63518189,
	AssetBundleResource_Start_mAF93CC18901AF152472DC583D170B9218DC03B44,
	AssetBundleResource_WaitForCompletionHandler_m3FEE1A4633F3C1945FDC6BD75A159C1E5B89703D,
	AssetBundleResource_AddCallbackInvokeIfDone_m1B95B9ED5C249BB3C9C2A00F165F47D1F01A30BB,
	AssetBundleResource_GetLoadInfo_mAFC4290DC315D8402C0AB3C632F8CF81204D410D,
	AssetBundleResource_GetLoadInfo_mED6270C26AEB96BC8D81708EDDFF08000CFFB7CA,
	AssetBundleResource_BeginOperation_mCC4253DFE6CCCA1F7D8AABE819F4570AD948439D,
	AssetBundleResource_LoadLocalBundle_m6EFD51DDF5FEC1964CAADCF0C268FD6AB07C11F4,
	AssetBundleResource_EnqueueWebRequest_m5D4DA3D4CFE08BA2464FA80F1AD3908521FC3E0D,
	AssetBundleResource_AddBeginWebRequestHandler_mE230039E25E989F0C83944D14C52A387EDD09B91,
	AssetBundleResource_BeginWebRequestOperation_mC2F30F42B4EF0C1D24D5365B205E4EB8C9448A4A,
	AssetBundleResource_Update_m51BC83CA2F963A53669204CDE6153DC1ECE50BC1,
	AssetBundleResource_LocalRequestOperationCompleted_m89ACDBA271F4830F27F54DCD193CE92D0B32D1B8,
	AssetBundleResource_CompleteBundleLoad_m2D635CCC7B8438E10C04E75DDBC41B79A04F712E,
	AssetBundleResource_WebRequestOperationCompleted_m80EDEE2721B9B8493FB05643C6DA3A9A62CB4C2D,
	AssetBundleResource_Unload_m16EE6F704D14712FC1188F9D6C539EE3AF6CD120,
	AssetBundleResource__ctor_m008E4DA8A21FBE0D1C182DDB5C1481B778B8B7F5,
	AssetBundleResource_U3CGetAssetPreloadRequestU3Eb__28_0_m37EAFEDC337AFC75940A42D3E75F3D0C50EDD4C9,
	AssetBundleResource_U3CAddBeginWebRequestHandlerU3Eb__41_0_m21A60C0AE1AD43CFD9401B63FA0DF8FC005A5DDD,
	AssetBundleProvider_Init_m671FCC897E9239473EA3F07825506D2F8CBD792D,
	AssetBundleProvider_get_UnloadingBundles_mE2A2813B5F21B3CFC98586F90C36A8CA7F1CD961,
	AssetBundleProvider_set_UnloadingBundles_mDEA612E42EEDAC258115C998A94FB722884F3282,
	AssetBundleProvider_get_UnloadingAssetBundleCount_m98CE8E2285630EC583D8F35354141E5078D4C248,
	AssetBundleProvider_get_AssetBundleCount_m574A2DBBEBA016C60BD20C09A553AD5C060881CA,
	AssetBundleProvider_WaitForAllUnloadingBundlesToComplete_mEBC813A6870324DA71CE63FB3A49399D0BA3A7F2,
	AssetBundleProvider_Provide_mA46E1266920D3C0B5D9D11EAF84ADC0E17E447DE,
	AssetBundleProvider_GetDefaultType_mCA86887DC5C60FB6692B4B77E3B60B3DF2B562CA,
	AssetBundleProvider_Release_m95F1EE9F98EB926F4A535EA03F3DAFD7F1B86884,
	AssetBundleProvider_CreateCacheKeyForLocation_m51B28E2051CA587CF61E94E98144BC1898CC5205,
	AssetBundleProvider__ctor_m53DA726879B5E33FAC8F94D17C1290B24C4937CC,
	AssetBundleProvider__cctor_m76C60D2E2D7FD0CB5AE7A6E4A885D41E5D219681,
	U3CU3Ec__DisplayClass12_0__ctor_m78AFD6A5DA689F61372B19CF536C90B4967408BA,
	U3CU3Ec__DisplayClass12_0_U3CReleaseU3Eb__0_m5E66EB3B6B9C7FD43A7EE366661ED9AEB182BFDC,
	AtlasSpriteProvider_Provide_mBBAA733518B690CAF561FDDFD2E53F5FA5707DF2,
	AtlasSpriteProvider_Release_mC372EBB2210E5C2313C97C386F5CF2D089BF0739,
	AtlasSpriteProvider__ctor_mCF29705EEE5BB7AE871C09823B04C8DCBB2CCEC4,
	NULL,
	NULL,
	BinaryDataProvider_get_IgnoreFailures_mB1618C33565A80A53488BEC0354EF97F79700F73,
	BinaryDataProvider_set_IgnoreFailures_m0120A787D1B242D60EF07003CFD64D5E349111B8,
	BinaryDataProvider_Convert_m7A4E44F1507D3AFCA6465882D5FF66BA8361E63C,
	BinaryDataProvider_Provide_mCD8C903D71ACE62C3CE2F710BB5384AAE03C45E0,
	BinaryDataProvider__ctor_mB946875640ED93ABD18B9E7DBB54FFCF7A4112DF,
	InternalOp_GetPercentComplete_m481C4F23E5A9241A880C3881B53B36170F7F0815,
	InternalOp_Start_m997221D33828DF742882C14D355F85C10F712917,
	InternalOp_WaitForCompletionHandler_mA35787A1A74B96482E9CB77C381DAF4C6039B248,
	InternalOp_RequestOperation_completed_m6A788C225F2B94D77D802F168BE83FB270D187EE,
	InternalOp_CompleteOperation_mFBBF22064DE286ED48936494E33C312C326577EC,
	InternalOp_ConvertBytes_m6D21DA816EF3D8BB6FCBD4FFF37831352E10D410,
	InternalOp_SendWebRequest_m6A785CFBD698F72DFEB656082112404AC8644A40,
	InternalOp__ctor_m4CBDDA9271CF100392A0167C0C58A9278D2EE26E,
	InternalOp_U3CSendWebRequestU3Eb__13_0_m5C2E161E02D866146D1578B565CE4AADBA8418D2,
	BundledAssetProvider_Provide_mBE01FF42EB491FEF863873076B83891F4946C575,
	BundledAssetProvider__ctor_mCAC8A4F760B97F22EE89F68B093CE5095A573DE3,
	NULL,
	InternalOp_Start_mD6F453C005B979B3C1B2D7B4DE6562A16849503C,
	InternalOp_BeginAssetLoad_m30E6E668710155E8D51518F8D8ECFB75A30170C6,
	InternalOp_WaitForCompletionHandler_m7BA75A8CB05D3B9BC2455C51575162FE040C8BAF,
	InternalOp_ActionComplete_m2082E408EC480CF7251DC1660845EAAE1500521C,
	InternalOp_GetArrayResult_m694DC4FAB3B9C5979801DD61B561BC477A9350B9,
	InternalOp_GetListResult_mDAA24FC03AC31E533A7A182BA29E9FC112A25DF3,
	InternalOp_GetAssetResult_mED436C17DAA1633F0042F0A1ED7D9CBB6C2EB2CD,
	InternalOp_GetAssetSubObjectResult_m55D32B4FC4D1DA72EF3BCC3036B13304077D469D,
	InternalOp_CompleteOperation_m861092872E86F4DCFD894EFAEF644E5E46E51EDD,
	InternalOp_ProgressCallback_m1AE6872A27642B6744CF675866712366CA9B124E,
	InternalOp__ctor_mAE3F9E21BF0389264BAF79BB0E24DDFECEF6561F,
	InternalOp_U3CStartU3Eb__7_0_m531305899E20D1C0A0B701CF73A2B2F03497D814,
	InstantiationParameters_get_Position_mED28D6FCE87E7AEBC4D0D0D78FA4749300ADE83B,
	InstantiationParameters_get_Rotation_mDDFD08C31993BB4532D0D27B3DC801735EE93017,
	InstantiationParameters_get_Parent_m778422BDFC47C2CCE73196FAE4A341F5308160E6,
	InstantiationParameters_get_InstantiateInWorldPosition_m546FDC64BF6E5E7B739392EE27695D43DE65A3AD,
	InstantiationParameters_get_SetPositionRotation_m089FF9C21E2840258CE80BB9E77E35F3C2E710B3,
	InstantiationParameters__ctor_mE2B0DEA67D18FA4C6B5A37BC07629A1364D6B107,
	InstantiationParameters__ctor_mEFAF8D103303B2D9763B17BAB322E06931F13306,
	NULL,
	NULL,
	NULL,
	InstanceProvider_ProvideInstance_m6DE99248B2973F0839880FC3FD240037A3350DF8,
	InstanceProvider_ReleaseInstance_m52D21820C36D02F3E2C0C7C89C0511648FF66A59,
	InstanceProvider__ctor_mF4CD1E6C8E301646D4667748D12B70556B800DB2,
	ProvideHandle__ctor_m747E40C8B08DBF3EB4276D7B0836C0F0D5B59B0A,
	ProvideHandle_get_IsValid_m0852EF449EEE7D5E0E91FFA7902D9600554E9CE7,
	ProvideHandle_get_InternalOp_m1C141F1D99C5BD7F5AA1E973272462966BF0FEAE,
	ProvideHandle_get_ResourceManager_m21C0C0B4C590B19E601BF83ACEA612EAF3B62262,
	ProvideHandle_get_Type_mD0CC7F89812DD44851B9D52DA6960B1671A86559,
	ProvideHandle_get_Location_m90F975C77A98EB20A2C3B9135D629AB0F04FFA79,
	ProvideHandle_get_DependencyCount_mA960290D798128400EBF3411B6B005F3F215A451,
	NULL,
	ProvideHandle_GetDependencies_m369FC67ECDB4E574C6BC9961896C384189EAFBAD,
	ProvideHandle_SetProgressCallback_m5D44473DCD8CEB54FB41063B2C2105220822094D,
	ProvideHandle_SetDownloadProgressCallbacks_m9FB675CA1837DD5CF495B393170257B8EE06C145,
	ProvideHandle_SetWaitForCompletionCallback_m2B606A689DD5A940581F9C0F87C4B6B6714BE7A2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SceneInstance_get_Scene_m4A9A4A43AFC047DD8FB2DF665C499A09296CBA58,
	SceneInstance_set_Scene_mD929C70C4FEF4B9599C1A6A5C22CE787C433A06C,
	SceneInstance_Activate_m87D5B5E1C820F1666917198300E36DABFCA9EF29,
	SceneInstance_ActivateAsync_mA5740ABD2A826DB7CCCA83C62F31AA8448DE08C6,
	SceneInstance_GetHashCode_m0C1E8F653E55633F9897B5FC62D7D4B7B1D340F4,
	SceneInstance_Equals_m10E8D14BA2C3057166479C12D2514056E8ACB6AD,
	NULL,
	NULL,
	NULL,
	NULL,
	SceneProviderExtensions_ReleaseScene_mA143707034C4E9860E9E80F333307D440195BCF0,
	JsonAssetProvider_Convert_m6FA7F7A940C19DA1E361CCE24898B243EEBBD75E,
	JsonAssetProvider__ctor_mB9C7EE8BCD28AA4995F2B29CB3041582769740C2,
	LegacyResourcesProvider_Provide_mB180B8328373F2A6B219A68631AA4B536BC09DBE,
	LegacyResourcesProvider_Release_m1E49FF2BD1D0DEEF7EA4721014BB1FF118C58DCB,
	LegacyResourcesProvider__ctor_mAEF6AE3781103993C52878AE0FF0FA88B65036B2,
	InternalOp_Start_mBB36E8DBDE11260D2791B713DA6278B1E52A3B34,
	InternalOp_WaitForCompletionHandler_mA0D71D7C83BF8A4500CE5BBD5B65C0D6C86A79CE,
	InternalOp_AsyncOperationCompleted_mCFE499F25DE6783BC252AEF6FFA83B2F4906C69B,
	InternalOp_PercentComplete_mFD5F139200755EFF695677464310664195431171,
	InternalOp__ctor_m7D25C62F5CA2642016421F0A6FE4AA93F27064E1,
	ResourceProviderBase_get_ProviderId_m32B6BC7793DD0BA3B1B8AD25A2469AE0B4CDCCB1,
	ResourceProviderBase_Initialize_mF5BA704DAA8DBAE985D7B2AA6B84379DC08A1A62,
	ResourceProviderBase_CanProvide_mB9058D012637D533B0214DCB5EF478E5F8CB6816,
	ResourceProviderBase_ToString_m9E17857E9B321F341C51B01E0EA3EC09D0ACABDD,
	ResourceProviderBase_Release_mBA35FC33814192955594D9A6C36D6AF9DF54A123,
	ResourceProviderBase_GetDefaultType_m33744F9753517EF7AAE308E7F35234742B0EA000,
	NULL,
	ResourceProviderBase_InitializeAsync_mC3314BEB55F7D7F35AD13997844E919980BED1DA,
	ResourceProviderBase_UnityEngine_ResourceManagement_ResourceProviders_IResourceProvider_get_BehaviourFlags_m3ED17F0683C3298B5AC1E402259740330104F79A,
	ResourceProviderBase__ctor_mAE12700496F4A8B1363AB851FD48F2101278CBF9,
	BaseInitAsyncOp_Init_mA692A84282503E756732EB5AED2D184E17C8896B,
	BaseInitAsyncOp_InvokeWaitForCompletion_m3EEFE285B56FC5B968B76F2A7532EBB1ECF64C32,
	BaseInitAsyncOp_Execute_mD8E2E626B3041A9A808536A9062D19D86DB091F2,
	BaseInitAsyncOp__ctor_m13B9AD12D8F36FC0B8DD5B72AE71117B3F8ADA1E,
	U3CU3Ec__DisplayClass10_0__ctor_mA5E2487B070975CC79B933A4F89E400047EC712B,
	U3CU3Ec__DisplayClass10_0_U3CInitializeAsyncU3Eb__0_m2152B544B8FC16CFF06127C9098353ADE47B9713,
	ProviderLoadRequestOptions_Copy_mEA312F4D34C7FE6E0B7CE7BA4E7D68DCD5BA94A9,
	ProviderLoadRequestOptions_get_IgnoreFailures_m38F1966139843C0C98C3598834A3BCCD9700962F,
	ProviderLoadRequestOptions_set_IgnoreFailures_m4C265B0663007A5DDE6E505E6CF15E516A0D13D1,
	ProviderLoadRequestOptions_get_WebRequestTimeout_m81BA0608C118ECD1ED8AD0732A626E747418320B,
	ProviderLoadRequestOptions_set_WebRequestTimeout_mCC9B0B944D6B5E4599849B09C83B3A635F31B518,
	ProviderLoadRequestOptions__ctor_m61D91E3E5DED91FDA313D772DAD3F16324F06322,
	SceneProvider_ProvideScene_m71829B9681719B9C2F06799FAB082C7A995FFE37,
	SceneProvider_ProvideScene_mE0A8EF44887C08C32A5180F1F0218A3FAA2DD58C,
	SceneProvider_ReleaseScene_mCF155C3784D3085A27249CC2802153A1F92611BC,
	SceneProvider_UnityEngine_ResourceManagement_ResourceProviders_ISceneProvider2_ReleaseScene_mB3B5F1A6E74DFD303103F085BA356A1C9486357D,
	SceneProvider__ctor_m1DCFAE38CC86ABEEE17922354162A857E296DC36,
	SceneOp__ctor_m040735CBFEBFBB5766B7E13241CD23D641DAA006,
	SceneOp_GetDownloadStatus_m2F9313B7535DD301CFF63B9B8DBE8E8B803DED6D,
	SceneOp_Init_m9A7827AAA4E02DEB793CA591D918A72C1E469B39,
	SceneOp_Init_mFEB3786354A297874561E3BC154C2DE58C5F2C17,
	SceneOp_InvokeWaitForCompletion_m6192F90DF7C2E5F2B986E6413341A8D5AED41B29,
	SceneOp_GetDependencies_mF60EC68370064533BCFF6918265B5AD71CF586A0,
	SceneOp_get_DebugName_m0681291B5BD9791094325B3EE0722917C896CA19,
	SceneOp_Execute_m4EB7AD77E7597C59AD5C67A25587E841BE12BFD2,
	SceneOp_InternalLoadScene_mA08E3FB4E51C417EFE1A4C5DAE4B005C0F537B20,
	SceneOp_InternalLoad_m5D31B832DFC50152FD83FB94429A3D30E08755D8,
	SceneOp_Destroy_m5F0765C76D7D9DA55338EBBF45B2DA79A837C1E9,
	SceneOp_get_Progress_m46A61DF0E7EC319C9D06C209DB723EAF0E1C2456,
	SceneOp_UnityEngine_ResourceManagement_IUpdateReceiver_Update_m506B4FE9CB742F642D38FD97D9FA0458E635A994,
	UnloadSceneOp_Init_mC0C6886B5E43507A3D937D155153AF0A0876A312,
	UnloadSceneOp_Execute_m7DE2D21BB6313D004CF1D6D9F1EFB01A63221E4F,
	UnloadSceneOp_InvokeWaitForCompletion_mD19AC17887FEC9B93D6FFCC72715F11AC105725A,
	UnloadSceneOp_UnloadSceneCompleted_mEACB38511795DF58A154B535AB413BDD85DD2B3C,
	UnloadSceneOp_get_Progress_mD452305AF75DD63EEA47EBAFC1876C1E17E57D8F,
	UnloadSceneOp__ctor_mEC2C9FAA9ABA49CB8374E387869FD5B9D56BFC90,
	TextDataProvider_get_IgnoreFailures_m8BC123BCDA3C358B9EF223382A5ABA14C41D8D61,
	TextDataProvider_set_IgnoreFailures_mFB1C076856A24422095C941AB43456FA8940D7DB,
	TextDataProvider_Convert_mC19E9961AFE2F1475B113DBA2A25BBE43EE546BC,
	TextDataProvider_Provide_m90DDE130A1AA927048310A11A8269C3E153E2F49,
	TextDataProvider__ctor_m0F85BC00939981B4C817E850285C97FB9E26243E,
	InternalOp_GetPercentComplete_mAFF06723896D6D26284115A2F0F102C9D664CCEC,
	InternalOp_Start_mF7F18148ECFF3E5EDA8852B71F507C6948CECCA3,
	InternalOp_WaitForCompletionHandler_mD109A0979F9D7BBC921015E9D83F2F81552C4AC2,
	InternalOp_RequestOperation_completed_mABCF255EC8F16723D1F518952653864D3F10D6A5,
	InternalOp_CompleteOperation_mBA10749B5FBBCEBF6FC6EDE8F74436482E0CD108,
	InternalOp_ConvertText_m399662CFEB5F933894A69BF4DAC04E9DEE3E60B5,
	InternalOp_SendWebRequest_mB75339F2D9CF06B6086FAD906823EEBFF11FAD22,
	InternalOp__ctor_m5C2E4773471AE3A4E9D940D1CD799A838ED4AF7A,
	InternalOp_U3CSendWebRequestU3Eb__13_0_m7D8C14A2281B0E47BA2C1699FFE5E12A050840A9,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ResourceLocationBase_get_InternalId_mDD8CAC7826827E51FC21FCDB426DB6096008E992,
	ResourceLocationBase_get_ProviderId_m0424B14B83A89FAA0EF86495E03B1EF521969C79,
	ResourceLocationBase_get_Dependencies_m72F0BF8B5DA8D1FE659A64693B045F5F285CBDE5,
	ResourceLocationBase_get_HasDependencies_mD189D2ABA34D0D3A2E9157C64CB379E6D2F065BF,
	ResourceLocationBase_get_Data_m8C7A683E9EB75A55EA642D9EE698A3D13C2034C9,
	ResourceLocationBase_set_Data_m81160FA98303F532B5365B051C62BB98DC855A03,
	ResourceLocationBase_get_PrimaryKey_mADFAF458BB2737444A019A4EDA4D851597B67D1D,
	ResourceLocationBase_set_PrimaryKey_mB1F256C71675DCEE26CB03DC0F7273BC38A952AB,
	ResourceLocationBase_get_DependencyHashCode_m9E0B40B30D210863CD93FF3B49931192022F7E86,
	ResourceLocationBase_get_ResourceType_mBE5F33BDA7995150D37EF135584D62C5E6B1DF8B,
	ResourceLocationBase_Hash_m2C84780ECDAADFE6406BDC5BB220802A413AF16A,
	ResourceLocationBase_ToString_m90F397186213D25C1BA997AD845FA55AED6485A8,
	ResourceLocationBase__ctor_m736BECFEE816BAEDC0FFC7E0A1C0100826A63E74,
	ResourceLocationBase_ComputeDependencyHash_m4436815CB535B21F7DD09593479AB5EF5DBD2D2F,
	LocationWrapper__ctor_mAA6CC9A09ED34EDB9381D915BFF630E0C9C22141,
	LocationWrapper_get_InternalId_m7ABC2B6F84C4F6C7FE2F5332CCF37EF74AB921DE,
	LocationWrapper_get_ProviderId_m810DF53B752AD42985C7BF9AD6A8FC61E2B5A926,
	LocationWrapper_get_Dependencies_m857880D71F22D81B2959C327BACC0D4A1588E501,
	LocationWrapper_get_DependencyHashCode_m0AD260651E814478350AD80C78E0FD36A4EF1075,
	LocationWrapper_get_HasDependencies_mCFD06F0102DFE96A00F495219F766D7DFAE7F83C,
	LocationWrapper_get_Data_m8C0C8F501B27EB5846D9C404AB899C628BBDC88E,
	LocationWrapper_get_PrimaryKey_m7023AE6E7AA84AC50ABFCDC3BDDB9A194013BC98,
	LocationWrapper_get_ResourceType_mB3E67E25F1DF8CCF6C378E0181A1AC354404BBA8,
	LocationWrapper_Hash_m06AE5E2A24BAEA7EB329B79C840EDDCEB84BFCFB,
	DiagnosticEvent_get_Graph_m670206480E0149C55B547DF779321DFE8EF9436B,
	DiagnosticEvent_get_ObjectId_mF6D69C8148C581548D9CF4C21293F6A8300DAC75,
	DiagnosticEvent_get_DisplayName_m02754B60C44C78A265F62732A3395470BEAA39CA,
	DiagnosticEvent_get_Dependencies_m5E3CA5E99242A6845C6974B9620EFEFE3777C2CD,
	DiagnosticEvent_get_Stream_mC79CA5D736C7CEBA931F272DA4F94BA8DED1BCF6,
	DiagnosticEvent_get_Frame_m3CC82F45DB1EA65234FF98A1E739FC40D303171B,
	DiagnosticEvent_get_Value_mC412E8EC2AC4D004D2E68BCF0566AA2261008C02,
	DiagnosticEvent__ctor_mDF62B33ABFF1F33A4B39DF5C209B8238F2D529A0,
	DiagnosticEvent_Serialize_m6C00B0750960B7E23FB1DFE666C93DC3F5B0FF1A,
	DiagnosticEvent_Deserialize_m22BCF0716BAC26F28A164F479E6F67613B00D16A,
	DiagnosticEventCollectorSingleton_get_PlayerConnectionGuid_m12CE48F5FA9778C6B084529356CE2BA2FF19E0FC,
	DiagnosticEventCollectorSingleton_GetGameObjectName_m1361552A8D1AFD2282D01F14975908635A86D74D,
	DiagnosticEventCollectorSingleton_RegisterEventHandler_m9FCC0418DB7FCB86CDD3C233C6D59507D3A1CBC5,
	DiagnosticEventCollectorSingleton_RegisterEventHandler_mBD7EB71E6D5A3B2D180D6B0E2163C47E7DE291BA,
	DiagnosticEventCollectorSingleton_UnregisterEventHandler_m195CD42A740153DCC2387DC36511FB4FE36E5AF2,
	DiagnosticEventCollectorSingleton_PostEvent_m814E46059FF639B7B3E731E6F354778282DF5B2A,
	DiagnosticEventCollectorSingleton_Awake_m123CE39CFC51D74A89D0EACF7DB3FC080FD07723,
	DiagnosticEventCollectorSingleton_Update_m5EBCBD1D3BCD145D3EEB6460DA39FCC197354BD3,
	DiagnosticEventCollectorSingleton__ctor_m439DE5DDEF6D28BB4E48FF1A9FA7B3F100B41E03,
	U3CU3Ec__cctor_m68A24D9C1E63B9D1C8598AA17A966FF00C9FCA63,
	U3CU3Ec__ctor_m7E8AE3390AA2F226FD9AE66A810054796F6E3EF7,
	U3CU3Ec_U3CRegisterEventHandlerU3Eb__8_0_mA7621F2FC763736C0E5940EFF0C40BC79177573C,
	U3CU3Ec_U3CAwakeU3Eb__11_0_mC35442B113DAF4C3C790A4D17482F6CF5888FE73,
	DiagnosticEventCollector_get_PlayerConnectionGuid_mBA28978681F7978EDAD805C720891BB0013FC3E1,
	DiagnosticEventCollector_FindOrCreateGlobalInstance_mF69245B78702CB0B5D38DD7F7E35DD84D1884E6F,
	DiagnosticEventCollector_RegisterEventHandler_m07723751487743072F7D163A0C94803345C3DA46,
	DiagnosticEventCollector_UnregisterEventHandler_m6183F50FB14701F99FC2F34D16ABAFC738A63F4B,
	DiagnosticEventCollector_PostEvent_m7E9005E48B8D9756D61E13B4C6F4070B1A108E19,
	DiagnosticEventCollector__ctor_m55F16BF9725CD02336D84D2032D17D23079D34BC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AsyncOperationHandle_get_Version_m1DA4995F59398AC5537A4F5A64F18E19B599FBC2,
	AsyncOperationHandle_get_LocationName_mFBCB4F35970F65B67F34263CBBC8949DBD04954F,
	AsyncOperationHandle_set_LocationName_mA4DD8D02B510CD9974D1E5AAC4D34DD91575865C,
	AsyncOperationHandle__ctor_m68E6B69581AD8F879FE27693A96D6F36ABD3A264,
	AsyncOperationHandle__ctor_m21CD505FAB5F5AB79ADA74F7F95AD6DEA7F145E8,
	AsyncOperationHandle__ctor_mC618D938157A14E87066933EF6757D73A2847E22,
	AsyncOperationHandle__ctor_m40A34479B55F7F00CB5DF17F1003A38B12D0A51A,
	AsyncOperationHandle_Acquire_mDFD8D1733D45A5F9C20B3A49EA51891BE2C1435B,
	AsyncOperationHandle_add_Completed_mD5633623ADF00C97B6A1EE712E6654452F64B2E0,
	AsyncOperationHandle_remove_Completed_m538FFC6655C8FB0600775FFFB998D9903478DB44,
	AsyncOperationHandle_ReleaseHandleOnCompletion_m83E5325DE63A3AFC9DB15862E0E1D14CF08A59F4,
	NULL,
	AsyncOperationHandle_Equals_m496ACE1240F3D9550447E1930A1A426BF40CE186,
	AsyncOperationHandle_get_DebugName_m8DDB7ED8B735AFC69305907ED82F695ED9F9697E,
	AsyncOperationHandle_add_Destroyed_m66F321AB085184936F570B4C3A1F9D66B1872C07,
	AsyncOperationHandle_remove_Destroyed_m8755ED149C64A82AF2EF9710F0D9256163ED71C9,
	AsyncOperationHandle_GetDependencies_m188E3F0A06DA1F789B431E3401D692AD14F45C39,
	AsyncOperationHandle_GetHashCode_mA261FCCE8588545912EAD2AC611FADD3F104DF15,
	AsyncOperationHandle_get_InternalOp_m4EF5B7F816250889F1CB8917837E3A75B249396C,
	AsyncOperationHandle_get_IsDone_m55E07BF92CC2979A9BBFD8F5B97DCCD52DA1326A,
	AsyncOperationHandle_IsValid_mC57B65EA4D8E084859EF42FD671EDA1E2ED46626,
	AsyncOperationHandle_get_OperationException_m531FC4DC2E215075C0EA7CE135FF1D68F6049270,
	AsyncOperationHandle_get_PercentComplete_mB5B48174489343D707D9E4A90BAFE24658D135EC,
	AsyncOperationHandle_GetDownloadStatus_m526B0F8EC8EF9A00D4F59D5BC918913CE2BED374,
	AsyncOperationHandle_InternalGetDownloadStatus_mC00D428E94E347EE6FCD224530016D1F9433B33C,
	AsyncOperationHandle_get_ReferenceCount_m28F7ED9B712CC95D3F1E304578F94B6EEC1A5B3D,
	AsyncOperationHandle_Release_mD4ADD2AA46F56753B12E0916FA2A739A3EBB5762,
	AsyncOperationHandle_get_Result_mC319B351EAF9A8C76AAEB948BB3BC17F94AC9746,
	AsyncOperationHandle_get_Status_mD5FB502808777A3C88880942DB9595C614CF9B0A,
	AsyncOperationHandle_get_Task_m74053C375ECE675441967B0EA68091A7D9F84EF6,
	AsyncOperationHandle_System_Collections_IEnumerator_get_Current_mBAF3C14B4A6BD17BBBE6663D0061A1DD42E8FE37,
	AsyncOperationHandle_System_Collections_IEnumerator_MoveNext_m8A793FA1EEE2DAAD8B74F95FFEC60D94B9B8610E,
	AsyncOperationHandle_System_Collections_IEnumerator_Reset_m8921AB145EB1F53FD4352BE9E512505719FE6527,
	AsyncOperationHandle_WaitForCompletion_m4F5203EAAEAA1F724EA1220006A43E89A0951784,
	U3CU3Ec__cctor_mBADB56EC5E7D6B12C46AE6B5BEC93AB7BA55E4BE,
	U3CU3Ec__ctor_mF4359AA07619298B97298F370375E0D44C85BF77,
	U3CU3Ec_U3CReleaseHandleOnCompletionU3Eb__16_0_mCB90700F81C08E5E333EC4F29585BAA0E5DC1B1A,
	DownloadStatus_get_Percent_m0EBC4A32546FB0EA6099462E3D6B7C9168F93985,
	GroupOperation__ctor_mAC487B41FFD0ECF87B5042B10198D55E8460FB26,
	GroupOperation_InvokeWaitForCompletion_m58F701EB4E389EC5FCA0E834852D3FA88530C284,
	GroupOperation_UnityEngine_ResourceManagement_AsyncOperations_ICachable_get_Key_mBE090D49C1F51ACDEB43492606BD9BEEC3E0DC43,
	GroupOperation_UnityEngine_ResourceManagement_AsyncOperations_ICachable_set_Key_m45EACDDD26430787AC6D4D45297312F87B6BA59D,
	GroupOperation_GetDependentOps_m9FE0CB241C9F3630AA74C2C00539CECFD817B1D4,
	GroupOperation_GetDependencies_m4C7707C62DB7B67454BFDABC5B2C31BDEC74B395,
	GroupOperation_ReleaseDependencies_mC61C8336FBDB7D9D6CBFF04E772DDEE94E1650B2,
	GroupOperation_GetDownloadStatus_mCC253D27B3D655D0DE72A115047A298D32E6FBFB,
	GroupOperation_DependenciesAreUnchanged_m961C61C0E83B18B91E4C3A589DD5C98C5FA027D9,
	GroupOperation_get_DebugName_m3B4DD1AA206F8FBE96076A01E17258594826B90B,
	GroupOperation_Execute_mDACD8D19DE8A2916CC5D12093AC39E7709C826DD,
	GroupOperation_CompleteIfDependenciesComplete_m0CBDC4B0BC6DEB0F32D2E9884F99D6EA4FBFD346,
	GroupOperation_Destroy_m16201058C128455D5672E019015B4657ABD9614A,
	GroupOperation_get_Progress_m5CB53EFD6FAD043CE2D65F8028A2579F8A51B5AC,
	GroupOperation_Init_m8C19E45ECB5A7DB7895A83EE0F918CEF60FD932E,
	GroupOperation_Init_m63F2FE2DAA01637218A993C4008090339ECC1EC0,
	GroupOperation_OnOperationCompleted_m9FFD2AEFFDB99EC7D040F53B0D5B62A60D6D23A7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnityWebRequestOperation__ctor_mA83D2F7455DD870CD8AF192415C0DFD29F054F62,
	UnityWebRequestOperation_Execute_m6CDFDEA8F294580DD53D1C9D7B27872BBFA42115,
	UnityWebRequestOperation_U3CExecuteU3Eb__2_0_mBFE21A77BDFDB2293AFCA6AD6CBE77D766921B4A,
};
extern void DiagnosticEventContext_get_OperationHandle_mC766CD6C074CD2A0E3486FD41CE0B951B25B3FA6_AdjustorThunk (void);
extern void DiagnosticEventContext_get_Type_mF65C73B3A0D1958041136121E877B4AF9ECFCEBB_AdjustorThunk (void);
extern void DiagnosticEventContext_get_EventValue_m1109E4CB1BFA64FEDC13FF72B293B68DA2D0A94F_AdjustorThunk (void);
extern void DiagnosticEventContext_get_Location_mEFDEFECC834FF8AA3B9542889DD449BF07A5EF5F_AdjustorThunk (void);
extern void DiagnosticEventContext_get_Context_m8DC1C15D121AD87B29AE6FAF53B6797221E2224B_AdjustorThunk (void);
extern void DiagnosticEventContext_get_Error_m1BCCCD62C4402CD13F6E0E071111AF0BEEA00BD3_AdjustorThunk (void);
extern void DiagnosticEventContext__ctor_m14E2DE6A37749D242E166A1CA6B94AC0F99D4C51_AdjustorThunk (void);
extern void DelegateInfo__ctor_m08FC6B330A4593BBBB9C28877297E0C157D78DF3_AdjustorThunk (void);
extern void DelegateInfo_get_InvocationTime_mA102D6CDFFEECC935EF0097705D0081C59D8B030_AdjustorThunk (void);
extern void DelegateInfo_set_InvocationTime_mF31701BE6AF283B3887E76D6DE7653325F2BF645_AdjustorThunk (void);
extern void DelegateInfo_ToString_m31AC2460408EA1D16E861E230B274BEDB0B83EAA_AdjustorThunk (void);
extern void DelegateInfo_Invoke_m87A58948D2BFD8A13F922B5D7DA547EB191CA8BD_AdjustorThunk (void);
extern void SerializedType_get_AssemblyName_m42ED37D1ED570F31D87F2CF9486CC83954FAFCD8_AdjustorThunk (void);
extern void SerializedType_get_ClassName_m1597BC3A9C2E8EA40F35F8CF03B7591618AB96D9_AdjustorThunk (void);
extern void SerializedType_ToString_m286E5553A848CA6C472FCE478B65C01B3B2AA5E7_AdjustorThunk (void);
extern void SerializedType_get_Value_m8AC9DC985380FD7524D147E2C5C95664FAB9A10A_AdjustorThunk (void);
extern void SerializedType_set_Value_m1AB236ECAE27E66A4B2302B4573EB71A7762F679_AdjustorThunk (void);
extern void SerializedType_get_ValueChanged_mCD14490D0BC812C462831E2BD634A60E24243D34_AdjustorThunk (void);
extern void SerializedType_set_ValueChanged_m91FD96EEB5883E2ED8B507EC3CC668109928E1E7_AdjustorThunk (void);
extern void ObjectInitializationData_get_Id_m8B020C841845122EF9123BD5E136F6A3FEC3496F_AdjustorThunk (void);
extern void ObjectInitializationData_get_ObjectType_m7706EB3247D33B65F062CDEEB40792A27B8ABF69_AdjustorThunk (void);
extern void ObjectInitializationData_get_Data_mEFF201461CA817CE659A8703EE2AFEBF53824318_AdjustorThunk (void);
extern void ObjectInitializationData_ToString_m730570E98783E54D1B6A2ED0178E05A2DE394593_AdjustorThunk (void);
extern void ObjectInitializationData_GetAsyncInitHandle_m0017D1CE9481015847F724E9E22C481A2CFDF2FC_AdjustorThunk (void);
extern void InstantiationParameters_get_Position_mED28D6FCE87E7AEBC4D0D0D78FA4749300ADE83B_AdjustorThunk (void);
extern void InstantiationParameters_get_Rotation_mDDFD08C31993BB4532D0D27B3DC801735EE93017_AdjustorThunk (void);
extern void InstantiationParameters_get_Parent_m778422BDFC47C2CCE73196FAE4A341F5308160E6_AdjustorThunk (void);
extern void InstantiationParameters_get_InstantiateInWorldPosition_m546FDC64BF6E5E7B739392EE27695D43DE65A3AD_AdjustorThunk (void);
extern void InstantiationParameters_get_SetPositionRotation_m089FF9C21E2840258CE80BB9E77E35F3C2E710B3_AdjustorThunk (void);
extern void InstantiationParameters__ctor_mE2B0DEA67D18FA4C6B5A37BC07629A1364D6B107_AdjustorThunk (void);
extern void InstantiationParameters__ctor_mEFAF8D103303B2D9763B17BAB322E06931F13306_AdjustorThunk (void);
extern void ProvideHandle__ctor_m747E40C8B08DBF3EB4276D7B0836C0F0D5B59B0A_AdjustorThunk (void);
extern void ProvideHandle_get_IsValid_m0852EF449EEE7D5E0E91FFA7902D9600554E9CE7_AdjustorThunk (void);
extern void ProvideHandle_get_InternalOp_m1C141F1D99C5BD7F5AA1E973272462966BF0FEAE_AdjustorThunk (void);
extern void ProvideHandle_get_ResourceManager_m21C0C0B4C590B19E601BF83ACEA612EAF3B62262_AdjustorThunk (void);
extern void ProvideHandle_get_Type_mD0CC7F89812DD44851B9D52DA6960B1671A86559_AdjustorThunk (void);
extern void ProvideHandle_get_Location_m90F975C77A98EB20A2C3B9135D629AB0F04FFA79_AdjustorThunk (void);
extern void ProvideHandle_get_DependencyCount_mA960290D798128400EBF3411B6B005F3F215A451_AdjustorThunk (void);
extern void ProvideHandle_GetDependencies_m369FC67ECDB4E574C6BC9961896C384189EAFBAD_AdjustorThunk (void);
extern void ProvideHandle_SetProgressCallback_m5D44473DCD8CEB54FB41063B2C2105220822094D_AdjustorThunk (void);
extern void ProvideHandle_SetDownloadProgressCallbacks_m9FB675CA1837DD5CF495B393170257B8EE06C145_AdjustorThunk (void);
extern void ProvideHandle_SetWaitForCompletionCallback_m2B606A689DD5A940581F9C0F87C4B6B6714BE7A2_AdjustorThunk (void);
extern void SceneInstance_get_Scene_m4A9A4A43AFC047DD8FB2DF665C499A09296CBA58_AdjustorThunk (void);
extern void SceneInstance_set_Scene_mD929C70C4FEF4B9599C1A6A5C22CE787C433A06C_AdjustorThunk (void);
extern void SceneInstance_Activate_m87D5B5E1C820F1666917198300E36DABFCA9EF29_AdjustorThunk (void);
extern void SceneInstance_ActivateAsync_mA5740ABD2A826DB7CCCA83C62F31AA8448DE08C6_AdjustorThunk (void);
extern void SceneInstance_GetHashCode_m0C1E8F653E55633F9897B5FC62D7D4B7B1D340F4_AdjustorThunk (void);
extern void SceneInstance_Equals_m10E8D14BA2C3057166479C12D2514056E8ACB6AD_AdjustorThunk (void);
extern void DiagnosticEvent_get_Graph_m670206480E0149C55B547DF779321DFE8EF9436B_AdjustorThunk (void);
extern void DiagnosticEvent_get_ObjectId_mF6D69C8148C581548D9CF4C21293F6A8300DAC75_AdjustorThunk (void);
extern void DiagnosticEvent_get_DisplayName_m02754B60C44C78A265F62732A3395470BEAA39CA_AdjustorThunk (void);
extern void DiagnosticEvent_get_Dependencies_m5E3CA5E99242A6845C6974B9620EFEFE3777C2CD_AdjustorThunk (void);
extern void DiagnosticEvent_get_Stream_mC79CA5D736C7CEBA931F272DA4F94BA8DED1BCF6_AdjustorThunk (void);
extern void DiagnosticEvent_get_Frame_m3CC82F45DB1EA65234FF98A1E739FC40D303171B_AdjustorThunk (void);
extern void DiagnosticEvent_get_Value_mC412E8EC2AC4D004D2E68BCF0566AA2261008C02_AdjustorThunk (void);
extern void DiagnosticEvent__ctor_mDF62B33ABFF1F33A4B39DF5C209B8238F2D529A0_AdjustorThunk (void);
extern void DiagnosticEvent_Serialize_m6C00B0750960B7E23FB1DFE666C93DC3F5B0FF1A_AdjustorThunk (void);
extern void AsyncOperationHandle_get_Version_m1DA4995F59398AC5537A4F5A64F18E19B599FBC2_AdjustorThunk (void);
extern void AsyncOperationHandle_get_LocationName_mFBCB4F35970F65B67F34263CBBC8949DBD04954F_AdjustorThunk (void);
extern void AsyncOperationHandle_set_LocationName_mA4DD8D02B510CD9974D1E5AAC4D34DD91575865C_AdjustorThunk (void);
extern void AsyncOperationHandle__ctor_m68E6B69581AD8F879FE27693A96D6F36ABD3A264_AdjustorThunk (void);
extern void AsyncOperationHandle__ctor_m21CD505FAB5F5AB79ADA74F7F95AD6DEA7F145E8_AdjustorThunk (void);
extern void AsyncOperationHandle__ctor_mC618D938157A14E87066933EF6757D73A2847E22_AdjustorThunk (void);
extern void AsyncOperationHandle__ctor_m40A34479B55F7F00CB5DF17F1003A38B12D0A51A_AdjustorThunk (void);
extern void AsyncOperationHandle_Acquire_mDFD8D1733D45A5F9C20B3A49EA51891BE2C1435B_AdjustorThunk (void);
extern void AsyncOperationHandle_add_Completed_mD5633623ADF00C97B6A1EE712E6654452F64B2E0_AdjustorThunk (void);
extern void AsyncOperationHandle_remove_Completed_m538FFC6655C8FB0600775FFFB998D9903478DB44_AdjustorThunk (void);
extern void AsyncOperationHandle_ReleaseHandleOnCompletion_m83E5325DE63A3AFC9DB15862E0E1D14CF08A59F4_AdjustorThunk (void);
extern void AsyncOperationHandle_Equals_m496ACE1240F3D9550447E1930A1A426BF40CE186_AdjustorThunk (void);
extern void AsyncOperationHandle_get_DebugName_m8DDB7ED8B735AFC69305907ED82F695ED9F9697E_AdjustorThunk (void);
extern void AsyncOperationHandle_add_Destroyed_m66F321AB085184936F570B4C3A1F9D66B1872C07_AdjustorThunk (void);
extern void AsyncOperationHandle_remove_Destroyed_m8755ED149C64A82AF2EF9710F0D9256163ED71C9_AdjustorThunk (void);
extern void AsyncOperationHandle_GetDependencies_m188E3F0A06DA1F789B431E3401D692AD14F45C39_AdjustorThunk (void);
extern void AsyncOperationHandle_GetHashCode_mA261FCCE8588545912EAD2AC611FADD3F104DF15_AdjustorThunk (void);
extern void AsyncOperationHandle_get_InternalOp_m4EF5B7F816250889F1CB8917837E3A75B249396C_AdjustorThunk (void);
extern void AsyncOperationHandle_get_IsDone_m55E07BF92CC2979A9BBFD8F5B97DCCD52DA1326A_AdjustorThunk (void);
extern void AsyncOperationHandle_IsValid_mC57B65EA4D8E084859EF42FD671EDA1E2ED46626_AdjustorThunk (void);
extern void AsyncOperationHandle_get_OperationException_m531FC4DC2E215075C0EA7CE135FF1D68F6049270_AdjustorThunk (void);
extern void AsyncOperationHandle_get_PercentComplete_mB5B48174489343D707D9E4A90BAFE24658D135EC_AdjustorThunk (void);
extern void AsyncOperationHandle_GetDownloadStatus_m526B0F8EC8EF9A00D4F59D5BC918913CE2BED374_AdjustorThunk (void);
extern void AsyncOperationHandle_InternalGetDownloadStatus_mC00D428E94E347EE6FCD224530016D1F9433B33C_AdjustorThunk (void);
extern void AsyncOperationHandle_get_ReferenceCount_m28F7ED9B712CC95D3F1E304578F94B6EEC1A5B3D_AdjustorThunk (void);
extern void AsyncOperationHandle_Release_mD4ADD2AA46F56753B12E0916FA2A739A3EBB5762_AdjustorThunk (void);
extern void AsyncOperationHandle_get_Result_mC319B351EAF9A8C76AAEB948BB3BC17F94AC9746_AdjustorThunk (void);
extern void AsyncOperationHandle_get_Status_mD5FB502808777A3C88880942DB9595C614CF9B0A_AdjustorThunk (void);
extern void AsyncOperationHandle_get_Task_m74053C375ECE675441967B0EA68091A7D9F84EF6_AdjustorThunk (void);
extern void AsyncOperationHandle_System_Collections_IEnumerator_get_Current_mBAF3C14B4A6BD17BBBE6663D0061A1DD42E8FE37_AdjustorThunk (void);
extern void AsyncOperationHandle_System_Collections_IEnumerator_MoveNext_m8A793FA1EEE2DAAD8B74F95FFEC60D94B9B8610E_AdjustorThunk (void);
extern void AsyncOperationHandle_System_Collections_IEnumerator_Reset_m8921AB145EB1F53FD4352BE9E512505719FE6527_AdjustorThunk (void);
extern void AsyncOperationHandle_WaitForCompletion_m4F5203EAAEAA1F724EA1220006A43E89A0951784_AdjustorThunk (void);
extern void DownloadStatus_get_Percent_m0EBC4A32546FB0EA6099462E3D6B7C9168F93985_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[91] = 
{
	{ 0x0600008C, DiagnosticEventContext_get_OperationHandle_mC766CD6C074CD2A0E3486FD41CE0B951B25B3FA6_AdjustorThunk },
	{ 0x0600008D, DiagnosticEventContext_get_Type_mF65C73B3A0D1958041136121E877B4AF9ECFCEBB_AdjustorThunk },
	{ 0x0600008E, DiagnosticEventContext_get_EventValue_m1109E4CB1BFA64FEDC13FF72B293B68DA2D0A94F_AdjustorThunk },
	{ 0x0600008F, DiagnosticEventContext_get_Location_mEFDEFECC834FF8AA3B9542889DD449BF07A5EF5F_AdjustorThunk },
	{ 0x06000090, DiagnosticEventContext_get_Context_m8DC1C15D121AD87B29AE6FAF53B6797221E2224B_AdjustorThunk },
	{ 0x06000091, DiagnosticEventContext_get_Error_m1BCCCD62C4402CD13F6E0E071111AF0BEEA00BD3_AdjustorThunk },
	{ 0x06000092, DiagnosticEventContext__ctor_m14E2DE6A37749D242E166A1CA6B94AC0F99D4C51_AdjustorThunk },
	{ 0x06000121, DelegateInfo__ctor_m08FC6B330A4593BBBB9C28877297E0C157D78DF3_AdjustorThunk },
	{ 0x06000122, DelegateInfo_get_InvocationTime_mA102D6CDFFEECC935EF0097705D0081C59D8B030_AdjustorThunk },
	{ 0x06000123, DelegateInfo_set_InvocationTime_mF31701BE6AF283B3887E76D6DE7653325F2BF645_AdjustorThunk },
	{ 0x06000124, DelegateInfo_ToString_m31AC2460408EA1D16E861E230B274BEDB0B83EAA_AdjustorThunk },
	{ 0x06000125, DelegateInfo_Invoke_m87A58948D2BFD8A13F922B5D7DA547EB191CA8BD_AdjustorThunk },
	{ 0x06000157, SerializedType_get_AssemblyName_m42ED37D1ED570F31D87F2CF9486CC83954FAFCD8_AdjustorThunk },
	{ 0x06000158, SerializedType_get_ClassName_m1597BC3A9C2E8EA40F35F8CF03B7591618AB96D9_AdjustorThunk },
	{ 0x06000159, SerializedType_ToString_m286E5553A848CA6C472FCE478B65C01B3B2AA5E7_AdjustorThunk },
	{ 0x0600015A, SerializedType_get_Value_m8AC9DC985380FD7524D147E2C5C95664FAB9A10A_AdjustorThunk },
	{ 0x0600015B, SerializedType_set_Value_m1AB236ECAE27E66A4B2302B4573EB71A7762F679_AdjustorThunk },
	{ 0x0600015C, SerializedType_get_ValueChanged_mCD14490D0BC812C462831E2BD634A60E24243D34_AdjustorThunk },
	{ 0x0600015D, SerializedType_set_ValueChanged_m91FD96EEB5883E2ED8B507EC3CC668109928E1E7_AdjustorThunk },
	{ 0x0600015E, ObjectInitializationData_get_Id_m8B020C841845122EF9123BD5E136F6A3FEC3496F_AdjustorThunk },
	{ 0x0600015F, ObjectInitializationData_get_ObjectType_m7706EB3247D33B65F062CDEEB40792A27B8ABF69_AdjustorThunk },
	{ 0x06000160, ObjectInitializationData_get_Data_mEFF201461CA817CE659A8703EE2AFEBF53824318_AdjustorThunk },
	{ 0x06000161, ObjectInitializationData_ToString_m730570E98783E54D1B6A2ED0178E05A2DE394593_AdjustorThunk },
	{ 0x06000163, ObjectInitializationData_GetAsyncInitHandle_m0017D1CE9481015847F724E9E22C481A2CFDF2FC_AdjustorThunk },
	{ 0x060001E1, InstantiationParameters_get_Position_mED28D6FCE87E7AEBC4D0D0D78FA4749300ADE83B_AdjustorThunk },
	{ 0x060001E2, InstantiationParameters_get_Rotation_mDDFD08C31993BB4532D0D27B3DC801735EE93017_AdjustorThunk },
	{ 0x060001E3, InstantiationParameters_get_Parent_m778422BDFC47C2CCE73196FAE4A341F5308160E6_AdjustorThunk },
	{ 0x060001E4, InstantiationParameters_get_InstantiateInWorldPosition_m546FDC64BF6E5E7B739392EE27695D43DE65A3AD_AdjustorThunk },
	{ 0x060001E5, InstantiationParameters_get_SetPositionRotation_m089FF9C21E2840258CE80BB9E77E35F3C2E710B3_AdjustorThunk },
	{ 0x060001E6, InstantiationParameters__ctor_mE2B0DEA67D18FA4C6B5A37BC07629A1364D6B107_AdjustorThunk },
	{ 0x060001E7, InstantiationParameters__ctor_mEFAF8D103303B2D9763B17BAB322E06931F13306_AdjustorThunk },
	{ 0x060001EE, ProvideHandle__ctor_m747E40C8B08DBF3EB4276D7B0836C0F0D5B59B0A_AdjustorThunk },
	{ 0x060001EF, ProvideHandle_get_IsValid_m0852EF449EEE7D5E0E91FFA7902D9600554E9CE7_AdjustorThunk },
	{ 0x060001F0, ProvideHandle_get_InternalOp_m1C141F1D99C5BD7F5AA1E973272462966BF0FEAE_AdjustorThunk },
	{ 0x060001F1, ProvideHandle_get_ResourceManager_m21C0C0B4C590B19E601BF83ACEA612EAF3B62262_AdjustorThunk },
	{ 0x060001F2, ProvideHandle_get_Type_mD0CC7F89812DD44851B9D52DA6960B1671A86559_AdjustorThunk },
	{ 0x060001F3, ProvideHandle_get_Location_m90F975C77A98EB20A2C3B9135D629AB0F04FFA79_AdjustorThunk },
	{ 0x060001F4, ProvideHandle_get_DependencyCount_mA960290D798128400EBF3411B6B005F3F215A451_AdjustorThunk },
	{ 0x060001F6, ProvideHandle_GetDependencies_m369FC67ECDB4E574C6BC9961896C384189EAFBAD_AdjustorThunk },
	{ 0x060001F7, ProvideHandle_SetProgressCallback_m5D44473DCD8CEB54FB41063B2C2105220822094D_AdjustorThunk },
	{ 0x060001F8, ProvideHandle_SetDownloadProgressCallbacks_m9FB675CA1837DD5CF495B393170257B8EE06C145_AdjustorThunk },
	{ 0x060001F9, ProvideHandle_SetWaitForCompletionCallback_m2B606A689DD5A940581F9C0F87C4B6B6714BE7A2_AdjustorThunk },
	{ 0x06000201, SceneInstance_get_Scene_m4A9A4A43AFC047DD8FB2DF665C499A09296CBA58_AdjustorThunk },
	{ 0x06000202, SceneInstance_set_Scene_mD929C70C4FEF4B9599C1A6A5C22CE787C433A06C_AdjustorThunk },
	{ 0x06000203, SceneInstance_Activate_m87D5B5E1C820F1666917198300E36DABFCA9EF29_AdjustorThunk },
	{ 0x06000204, SceneInstance_ActivateAsync_mA5740ABD2A826DB7CCCA83C62F31AA8448DE08C6_AdjustorThunk },
	{ 0x06000205, SceneInstance_GetHashCode_m0C1E8F653E55633F9897B5FC62D7D4B7B1D340F4_AdjustorThunk },
	{ 0x06000206, SceneInstance_Equals_m10E8D14BA2C3057166479C12D2514056E8ACB6AD_AdjustorThunk },
	{ 0x06000274, DiagnosticEvent_get_Graph_m670206480E0149C55B547DF779321DFE8EF9436B_AdjustorThunk },
	{ 0x06000275, DiagnosticEvent_get_ObjectId_mF6D69C8148C581548D9CF4C21293F6A8300DAC75_AdjustorThunk },
	{ 0x06000276, DiagnosticEvent_get_DisplayName_m02754B60C44C78A265F62732A3395470BEAA39CA_AdjustorThunk },
	{ 0x06000277, DiagnosticEvent_get_Dependencies_m5E3CA5E99242A6845C6974B9620EFEFE3777C2CD_AdjustorThunk },
	{ 0x06000278, DiagnosticEvent_get_Stream_mC79CA5D736C7CEBA931F272DA4F94BA8DED1BCF6_AdjustorThunk },
	{ 0x06000279, DiagnosticEvent_get_Frame_m3CC82F45DB1EA65234FF98A1E739FC40D303171B_AdjustorThunk },
	{ 0x0600027A, DiagnosticEvent_get_Value_mC412E8EC2AC4D004D2E68BCF0566AA2261008C02_AdjustorThunk },
	{ 0x0600027B, DiagnosticEvent__ctor_mDF62B33ABFF1F33A4B39DF5C209B8238F2D529A0_AdjustorThunk },
	{ 0x0600027C, DiagnosticEvent_Serialize_m6C00B0750960B7E23FB1DFE666C93DC3F5B0FF1A_AdjustorThunk },
	{ 0x0600031F, AsyncOperationHandle_get_Version_m1DA4995F59398AC5537A4F5A64F18E19B599FBC2_AdjustorThunk },
	{ 0x06000320, AsyncOperationHandle_get_LocationName_mFBCB4F35970F65B67F34263CBBC8949DBD04954F_AdjustorThunk },
	{ 0x06000321, AsyncOperationHandle_set_LocationName_mA4DD8D02B510CD9974D1E5AAC4D34DD91575865C_AdjustorThunk },
	{ 0x06000322, AsyncOperationHandle__ctor_m68E6B69581AD8F879FE27693A96D6F36ABD3A264_AdjustorThunk },
	{ 0x06000323, AsyncOperationHandle__ctor_m21CD505FAB5F5AB79ADA74F7F95AD6DEA7F145E8_AdjustorThunk },
	{ 0x06000324, AsyncOperationHandle__ctor_mC618D938157A14E87066933EF6757D73A2847E22_AdjustorThunk },
	{ 0x06000325, AsyncOperationHandle__ctor_m40A34479B55F7F00CB5DF17F1003A38B12D0A51A_AdjustorThunk },
	{ 0x06000326, AsyncOperationHandle_Acquire_mDFD8D1733D45A5F9C20B3A49EA51891BE2C1435B_AdjustorThunk },
	{ 0x06000327, AsyncOperationHandle_add_Completed_mD5633623ADF00C97B6A1EE712E6654452F64B2E0_AdjustorThunk },
	{ 0x06000328, AsyncOperationHandle_remove_Completed_m538FFC6655C8FB0600775FFFB998D9903478DB44_AdjustorThunk },
	{ 0x06000329, AsyncOperationHandle_ReleaseHandleOnCompletion_m83E5325DE63A3AFC9DB15862E0E1D14CF08A59F4_AdjustorThunk },
	{ 0x0600032B, AsyncOperationHandle_Equals_m496ACE1240F3D9550447E1930A1A426BF40CE186_AdjustorThunk },
	{ 0x0600032C, AsyncOperationHandle_get_DebugName_m8DDB7ED8B735AFC69305907ED82F695ED9F9697E_AdjustorThunk },
	{ 0x0600032D, AsyncOperationHandle_add_Destroyed_m66F321AB085184936F570B4C3A1F9D66B1872C07_AdjustorThunk },
	{ 0x0600032E, AsyncOperationHandle_remove_Destroyed_m8755ED149C64A82AF2EF9710F0D9256163ED71C9_AdjustorThunk },
	{ 0x0600032F, AsyncOperationHandle_GetDependencies_m188E3F0A06DA1F789B431E3401D692AD14F45C39_AdjustorThunk },
	{ 0x06000330, AsyncOperationHandle_GetHashCode_mA261FCCE8588545912EAD2AC611FADD3F104DF15_AdjustorThunk },
	{ 0x06000331, AsyncOperationHandle_get_InternalOp_m4EF5B7F816250889F1CB8917837E3A75B249396C_AdjustorThunk },
	{ 0x06000332, AsyncOperationHandle_get_IsDone_m55E07BF92CC2979A9BBFD8F5B97DCCD52DA1326A_AdjustorThunk },
	{ 0x06000333, AsyncOperationHandle_IsValid_mC57B65EA4D8E084859EF42FD671EDA1E2ED46626_AdjustorThunk },
	{ 0x06000334, AsyncOperationHandle_get_OperationException_m531FC4DC2E215075C0EA7CE135FF1D68F6049270_AdjustorThunk },
	{ 0x06000335, AsyncOperationHandle_get_PercentComplete_mB5B48174489343D707D9E4A90BAFE24658D135EC_AdjustorThunk },
	{ 0x06000336, AsyncOperationHandle_GetDownloadStatus_m526B0F8EC8EF9A00D4F59D5BC918913CE2BED374_AdjustorThunk },
	{ 0x06000337, AsyncOperationHandle_InternalGetDownloadStatus_mC00D428E94E347EE6FCD224530016D1F9433B33C_AdjustorThunk },
	{ 0x06000338, AsyncOperationHandle_get_ReferenceCount_m28F7ED9B712CC95D3F1E304578F94B6EEC1A5B3D_AdjustorThunk },
	{ 0x06000339, AsyncOperationHandle_Release_mD4ADD2AA46F56753B12E0916FA2A739A3EBB5762_AdjustorThunk },
	{ 0x0600033A, AsyncOperationHandle_get_Result_mC319B351EAF9A8C76AAEB948BB3BC17F94AC9746_AdjustorThunk },
	{ 0x0600033B, AsyncOperationHandle_get_Status_mD5FB502808777A3C88880942DB9595C614CF9B0A_AdjustorThunk },
	{ 0x0600033C, AsyncOperationHandle_get_Task_m74053C375ECE675441967B0EA68091A7D9F84EF6_AdjustorThunk },
	{ 0x0600033D, AsyncOperationHandle_System_Collections_IEnumerator_get_Current_mBAF3C14B4A6BD17BBBE6663D0061A1DD42E8FE37_AdjustorThunk },
	{ 0x0600033E, AsyncOperationHandle_System_Collections_IEnumerator_MoveNext_m8A793FA1EEE2DAAD8B74F95FFEC60D94B9B8610E_AdjustorThunk },
	{ 0x0600033F, AsyncOperationHandle_System_Collections_IEnumerator_Reset_m8921AB145EB1F53FD4352BE9E512505719FE6527_AdjustorThunk },
	{ 0x06000340, AsyncOperationHandle_WaitForCompletion_m4F5203EAAEAA1F724EA1220006A43E89A0951784_AdjustorThunk },
	{ 0x06000344, DownloadStatus_get_Percent_m0EBC4A32546FB0EA6099462E3D6B7C9168F93985_AdjustorThunk },
};
static const int32_t s_InvokerIndices[892] = 
{
	7120,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	5806,
	5806,
	5806,
	5806,
	6992,
	7120,
	7120,
	7120,
	10468,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10420,
	10293,
	6992,
	5806,
	5181,
	6992,
	5806,
	6957,
	6957,
	6957,
	6957,
	5806,
	5806,
	6992,
	5806,
	6992,
	6992,
	5806,
	5806,
	5806,
	5806,
	7120,
	7120,
	7120,
	5806,
	5806,
	5806,
	5970,
	2682,
	5181,
	2474,
	1417,
	2682,
	1630,
	0,
	0,
	2055,
	5806,
	5806,
	5806,
	0,
	3363,
	4261,
	4261,
	6957,
	7120,
	0,
	0,
	0,
	5693,
	0,
	5693,
	5181,
	0,
	0,
	1997,
	406,
	0,
	0,
	0,
	0,
	0,
	0,
	413,
	414,
	2017,
	1397,
	5845,
	7120,
	3349,
	5851,
	7120,
	10455,
	5806,
	6877,
	6957,
	6957,
	6992,
	6992,
	6992,
	622,
	0,
	0,
	0,
	0,
	0,
	0,
	1317,
	4558,
	5806,
	6992,
	7036,
	7120,
	7043,
	6887,
	7120,
	7120,
	0,
	0,
	0,
	0,
	6887,
	6992,
	5806,
	5806,
	5806,
	10288,
	10054,
	9623,
	10293,
	10293,
	10293,
	10293,
	10455,
	7120,
	5806,
	3363,
	3371,
	6992,
	6992,
	5806,
	5806,
	7120,
	5806,
	3363,
	3371,
	6992,
	6992,
	3363,
	6992,
	1909,
	6992,
	1328,
	6992,
	6992,
	6992,
	8896,
	8962,
	8537,
	7120,
	6992,
	1633,
	2734,
	2738,
	7120,
	7120,
	4377,
	6992,
	1633,
	2738,
	7120,
	0,
	0,
	0,
	1891,
	5806,
	1891,
	6992,
	1343,
	0,
	2694,
	1640,
	0,
	2694,
	0,
	1640,
	0,
	0,
	1649,
	1648,
	2694,
	1649,
	7106,
	3140,
	5191,
	1701,
	2743,
	1368,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	2736,
	2739,
	2738,
	6992,
	5354,
	9834,
	5354,
	5354,
	9508,
	1164,
	2739,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	5167,
	10455,
	7120,
	8972,
	1921,
	10397,
	9153,
	7120,
	5851,
	7120,
	7120,
	1921,
	7043,
	5851,
	6992,
	7120,
	3363,
	4261,
	6957,
	4261,
	4261,
	3363,
	6957,
	4261,
	4261,
	4261,
	3358,
	6957,
	4261,
	4261,
	4261,
	5806,
	6957,
	4261,
	4261,
	4261,
	9127,
	9127,
	9831,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	2681,
	3140,
	7120,
	1224,
	6992,
	5806,
	2681,
	3140,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6992,
	6992,
	6992,
	6992,
	5806,
	6887,
	5703,
	6992,
	7042,
	6992,
	6992,
	0,
	2057,
	8523,
	9834,
	10054,
	9834,
	10397,
	9381,
	0,
	9381,
	0,
	0,
	9121,
	9834,
	7120,
	5806,
	6992,
	6992,
	5806,
	6958,
	6957,
	6992,
	6992,
	6887,
	5806,
	0,
	6992,
	5806,
	7106,
	5910,
	6957,
	5774,
	6887,
	5703,
	6957,
	5774,
	6957,
	5774,
	6992,
	5806,
	6957,
	5774,
	6958,
	5775,
	6887,
	5703,
	6887,
	5703,
	6887,
	5703,
	2640,
	7120,
	6887,
	6958,
	5181,
	5181,
	6992,
	7043,
	6917,
	6992,
	5806,
	3394,
	6887,
	3363,
	8981,
	8399,
	7120,
	7120,
	5181,
	5806,
	5806,
	5851,
	5806,
	5806,
	5806,
	4139,
	7120,
	5806,
	5806,
	10455,
	10420,
	10293,
	10412,
	10412,
	10455,
	5822,
	5181,
	3363,
	1630,
	7120,
	10455,
	7120,
	5806,
	5822,
	3363,
	7120,
	0,
	0,
	6887,
	5703,
	2682,
	5822,
	7120,
	7043,
	3394,
	6887,
	5806,
	3363,
	5181,
	5806,
	7120,
	5806,
	5822,
	7120,
	0,
	5822,
	7120,
	6887,
	5806,
	5806,
	5806,
	5806,
	5806,
	7120,
	7043,
	7120,
	5806,
	7111,
	7008,
	6992,
	6887,
	6887,
	3349,
	1978,
	0,
	0,
	0,
	1612,
	3363,
	7120,
	3363,
	6887,
	6992,
	6992,
	6992,
	6992,
	6957,
	0,
	5806,
	5806,
	5806,
	5806,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7036,
	5845,
	7120,
	6992,
	6957,
	4261,
	0,
	0,
	0,
	0,
	7938,
	2682,
	7120,
	5822,
	3363,
	7120,
	5822,
	6887,
	5806,
	7043,
	7120,
	6992,
	2202,
	2202,
	6992,
	3363,
	5181,
	0,
	1395,
	6957,
	7120,
	5806,
	6887,
	7120,
	7120,
	7120,
	6887,
	6992,
	6887,
	5703,
	6957,
	5774,
	7120,
	413,
	414,
	2017,
	1406,
	7120,
	3363,
	4558,
	659,
	677,
	6887,
	5806,
	6992,
	7120,
	614,
	1616,
	7120,
	7043,
	5851,
	2772,
	7120,
	6887,
	5806,
	7043,
	7120,
	6887,
	5703,
	2682,
	5822,
	7120,
	7043,
	3394,
	6887,
	5806,
	3363,
	5181,
	5806,
	7120,
	5806,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6992,
	6992,
	6992,
	6887,
	6992,
	5806,
	6992,
	5806,
	6957,
	6992,
	4927,
	6992,
	696,
	7120,
	5806,
	6992,
	6992,
	6992,
	6957,
	6887,
	6992,
	6992,
	6992,
	4927,
	6992,
	6957,
	6992,
	6992,
	6957,
	6957,
	6957,
	225,
	6992,
	9887,
	10410,
	6992,
	8527,
	5806,
	5806,
	5732,
	7120,
	7120,
	7120,
	10455,
	7120,
	4869,
	5732,
	10410,
	10420,
	8527,
	5806,
	5732,
	7120,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6957,
	6992,
	5806,
	5806,
	3358,
	3363,
	1891,
	6877,
	5806,
	5806,
	7120,
	0,
	4151,
	6992,
	5806,
	5806,
	5806,
	6957,
	6992,
	6887,
	6887,
	6992,
	7043,
	6917,
	4558,
	6957,
	7120,
	6992,
	6957,
	6992,
	6992,
	6887,
	7120,
	6992,
	10455,
	7120,
	5693,
	7043,
	7120,
	6887,
	6992,
	5806,
	6992,
	5806,
	7120,
	4558,
	4261,
	6992,
	7120,
	7120,
	7120,
	7043,
	1870,
	3358,
	5693,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	5806,
	7120,
	5806,
};
static const Il2CppTokenRangePair s_rgctxIndices[58] = 
{
	{ 0x02000004, { 0, 26 } },
	{ 0x02000005, { 26, 30 } },
	{ 0x02000009, { 56, 41 } },
	{ 0x0200000A, { 97, 29 } },
	{ 0x0200000F, { 211, 10 } },
	{ 0x02000011, { 221, 14 } },
	{ 0x02000028, { 277, 22 } },
	{ 0x02000029, { 299, 4 } },
	{ 0x0200002A, { 303, 11 } },
	{ 0x0200003A, { 314, 12 } },
	{ 0x0200003B, { 326, 9 } },
	{ 0x0200004B, { 342, 2 } },
	{ 0x02000070, { 354, 66 } },
	{ 0x02000071, { 420, 4 } },
	{ 0x02000072, { 424, 4 } },
	{ 0x02000073, { 428, 35 } },
	{ 0x02000074, { 463, 6 } },
	{ 0x0200007C, { 471, 17 } },
	{ 0x06000064, { 126, 3 } },
	{ 0x06000065, { 129, 4 } },
	{ 0x0600006A, { 133, 2 } },
	{ 0x06000070, { 135, 3 } },
	{ 0x06000071, { 138, 3 } },
	{ 0x06000072, { 141, 8 } },
	{ 0x06000074, { 149, 3 } },
	{ 0x06000077, { 152, 4 } },
	{ 0x06000078, { 156, 4 } },
	{ 0x0600007B, { 160, 3 } },
	{ 0x0600007C, { 163, 12 } },
	{ 0x0600007D, { 175, 11 } },
	{ 0x0600007E, { 186, 7 } },
	{ 0x0600007F, { 193, 11 } },
	{ 0x06000080, { 204, 7 } },
	{ 0x060000E1, { 235, 2 } },
	{ 0x060000E4, { 237, 4 } },
	{ 0x060000E6, { 241, 2 } },
	{ 0x060000E8, { 243, 7 } },
	{ 0x060000E9, { 250, 1 } },
	{ 0x060000F4, { 251, 1 } },
	{ 0x060000F5, { 252, 4 } },
	{ 0x060000F6, { 256, 1 } },
	{ 0x060000F7, { 257, 4 } },
	{ 0x060000F8, { 261, 1 } },
	{ 0x060000F9, { 262, 1 } },
	{ 0x060000FA, { 263, 4 } },
	{ 0x060000FB, { 267, 4 } },
	{ 0x060000FC, { 271, 6 } },
	{ 0x06000162, { 335, 1 } },
	{ 0x0600016A, { 336, 2 } },
	{ 0x0600016C, { 338, 2 } },
	{ 0x0600016D, { 340, 2 } },
	{ 0x060001D4, { 344, 1 } },
	{ 0x060001E8, { 345, 5 } },
	{ 0x060001F5, { 350, 2 } },
	{ 0x060001FA, { 352, 2 } },
	{ 0x0600032A, { 469, 2 } },
	{ 0x06000371, { 488, 1 } },
	{ 0x06000373, { 489, 6 } },
};
extern const uint32_t g_rgctx_Func_2_tB657AA89B56DE58FFD262F226E686C775004BAA7;
extern const uint32_t g_rgctx_Action_1_t96DF0E7C89BAC9AFD95D29D0E369CDBAD9AAF92C;
extern const uint32_t g_rgctx_DelegateList_1_t2D4A585817F8DEDBBE3F4F365B2F2D64B657BBC4;
extern const uint32_t g_rgctx_LinkedList_1_t63405FAA9C92678A9009DCB3B74B36F5718378D6;
extern const uint32_t g_rgctx_LinkedList_1_get_Count_mC227FFB26892174F56F4891FBE705B61EEE91F03;
extern const uint32_t g_rgctx_Action_1_tB063712CA774D5ABC71ECDB08C5B39ADF5AC6BC1;
extern const uint32_t g_rgctx_Func_2_Invoke_m1792CA766E45ACC2DDE8ABE1C39AF1F0B6795463;
extern const uint32_t g_rgctx_LinkedListNode_1_t9D12EAA2087768BCD377BEC70819A20ABB95D292;
extern const uint32_t g_rgctx_LinkedList_1__ctor_mB1224BCE6BACED8D294EE9422D8B8F710A78CC24;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_mC0CDF84044EDA65DB99C3A475186A877559B80B0;
extern const uint32_t g_rgctx_LinkedList_1_get_First_m7EDAA3FCE7DAF16C27277741551A00C14482219A;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_mF0C551C504096B79FBDEDED02BB5D86E46D3E050;
extern const uint32_t g_rgctx_LinkedListNode_1_set_Value_mAAA06E418681581B1EA64FA0FD320B67D08187F3;
extern const uint32_t g_rgctx_LinkedList_1_Remove_m8091C8FF3BFED23A22E39E9643EFA722AA1CF783;
extern const uint32_t g_rgctx_Action_1_Invoke_mC676804465D944892CDCF37927234ED2FB2578BB;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mF489AD688818BA2CA07D1C52DD759628525D9123;
extern const uint32_t g_rgctx_T_tFE3B10388C5E3D61DCFFF7195A8DF4E939B6E933;
extern const uint32_t g_rgctx_Action_1_Invoke_mD2C04159DBACF435CDBD2AB307D0186FEA79A20E;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_get_CacheExists_m288E45863D22A5AA9403B60C3C47E435B273170D;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_t6F5A3D1046EB88F4580BA59E142143602AF67A6C;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_SetCacheSize_mCF4136F320E5F492DAA1AB1A51B15770FF7C0A41;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_Acquire_m27CC32F0CBCD85EB947E411593362CF24A36238D;
extern const uint32_t g_rgctx_Func_2__ctor_m1B08D557A412A0708128A5C7848D486F2C7E7390;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_Release_m786D04D14849BF559A82EE1B3B31B2769E52AD23;
extern const uint32_t g_rgctx_Action_1__ctor_m8C190A6A84103B0D6C6F79BF2D037F71D74269EB;
extern const uint32_t g_rgctx_DelegateList_1__ctor_mC30E45B26C54C08FADDA8E510D5AF3CF22002665;
extern const uint32_t g_rgctx_ListWithEvents_1_tE8B60E3978C29C35C1DC80A83A69B4A3E3DD2E09;
extern const uint32_t g_rgctx_Action_1_t3259692D2E20CF724AA3D3229E6866A93A317802;
extern const uint32_t g_rgctx_Action_1U26_t0562A736669252148200C1218239D892AFD0933A;
extern const uint32_t g_rgctx_T_tFD636C50586BA321ABBB5F7259B02A2B178232EE;
extern const uint32_t g_rgctx_Action_1_Invoke_m7598AD250C245A0808E990451516493FBE33B358;
extern const uint32_t g_rgctx_List_1_t6B9FC9BAF7D739AD163C93367C4B9F82E3A6247E;
extern const uint32_t g_rgctx_List_1_get_Item_m740982C6BF8852E872DEF525EA319620AECDC425;
extern const uint32_t g_rgctx_List_1_set_Item_m69C931DB75C1449C2210DCE44B83EDE50E12B3D8;
extern const uint32_t g_rgctx_ListWithEvents_1_InvokeRemoved_m39216321167C8C46EEA4EB2251AB7BA7806D419A;
extern const uint32_t g_rgctx_ListWithEvents_1_InvokeAdded_mBDFCDFA35C3848E20EC7D3CEFD954780CB647897;
extern const uint32_t g_rgctx_List_1_get_Count_mBC8D1A4B029D80207D5664E1F243A46C87FEE970;
extern const uint32_t g_rgctx_ICollection_1_tB0B3F6DA037BF4765B2F0682373E4BD273287CA1;
extern const uint32_t g_rgctx_ICollection_1_get_IsReadOnly_mB6015B2141C3509DBF075E4D87AE128EA1A962B1;
extern const uint32_t g_rgctx_List_1_Add_m3B08975E11ACBE777D20FE4F3E5F59B05A2C1E2A;
extern const uint32_t g_rgctx_List_1_GetEnumerator_mA5A8F8AB082AFB2406A6F2D7937B15D71B67F288;
extern const uint32_t g_rgctx_Enumerator_t2C5C3E4C5B2FE27B08B71F21A086987370E326ED;
extern const uint32_t g_rgctx_Enumerator_get_Current_m07AAFB2DF9CBA3126FA3D6A0BCD449D21C879A76;
extern const uint32_t g_rgctx_Enumerator_t2C5C3E4C5B2FE27B08B71F21A086987370E326ED;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m5785ACB49E4B9CA23D0804FF02CFD11B55DD463F;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t2C5C3E4C5B2FE27B08B71F21A086987370E326ED_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_Clear_m2B7BB5331094069DD56680558FC171B8AD75E83C;
extern const uint32_t g_rgctx_List_1_Contains_mF13920CB1DBB0267887A5D497812B6CB09AAD2AA;
extern const uint32_t g_rgctx_TU5BU5D_tD1240814399BEC0AA1806F6E8701124FD78614CF;
extern const uint32_t g_rgctx_List_1_CopyTo_m2AED55C2EBE2A7AD3925EB68DC8100D40BFCF22B;
extern const uint32_t g_rgctx_IEnumerator_1_t82BA34BAFAF756ED3003103C07F67B7E89D7AEA1;
extern const uint32_t g_rgctx_List_1_IndexOf_mCBD05D9CC899863B35A2875B9C41A17E0D7C4F33;
extern const uint32_t g_rgctx_List_1_Insert_m6620A66CA876EDE488E183E76B122A093521AE57;
extern const uint32_t g_rgctx_List_1_Remove_mA02873780B57DDA9AF61CF614E122992A556D09C;
extern const uint32_t g_rgctx_List_1_RemoveAt_m33B2A6A3A8BCE2E4A47535845AD1351F66A19614;
extern const uint32_t g_rgctx_List_1__ctor_mF7F7DCDB18521F39A98DF94DC5277D6246BA116C;
extern const uint32_t g_rgctx_ChainOperation_2_t1771C6FDEF5A45DDDC684ECB5CB22E3A259B0492;
extern const uint32_t g_rgctx_AsyncOperationBase_1__ctor_mCE9B061BE38223E9D143F95BBA597F35399ED56F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tCBD239557E1789792A562A0476C0934C9C3902EB;
extern const uint32_t g_rgctx_ChainOperation_2_OnWrappedCompleted_mE7BDEC00E466CCE8D7376F90FCF9C6A7A2242AB9;
extern const uint32_t g_rgctx_Action_1_tDFF757C8185F55D18EF448A75C0667C85656E929;
extern const uint32_t g_rgctx_Action_1__ctor_mB582F7E516BD40976AAE75E280C4CDE5CF6D4C6A;
extern const uint32_t g_rgctx_TObject_tE82607FCAA061F6E4BDDA1FD6CA94B1EDD406FA0;
extern const uint32_t g_rgctx_TObjectDependency_tF0C3A64B8C24EB2695EA6E8DE02B23E0188B30EB;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t4C9A1A9A67BF9CCBD03A6171126D9AC272AF6E0C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_DebugName_mB305FD16BAD7B79069EDADF3D6E3B42CE053776E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t4C9A1A9A67BF9CCBD03A6171126D9AC272AF6E0C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_IsValid_m3F9A32936CAA230B9AF4E4DBDBA3420C451A8226;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m9A72ADAFA326DC64D07BD6F0A16D7D78559EBE92;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Acquire_m7718EF25F30F553A7E515B72CCABC8600E2BC541;
extern const uint32_t g_rgctx_Func_2_t5ED9B8A459AF263F76A2EA4C2DFADFAE65F5A238;
extern const uint32_t g_rgctx_ChainOperation_2_RefreshDownloadStatus_mEA6BE1876BD4BFCBC09374115697932EDE9C03C6;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_IsDone_mE566894C69351CA2AD7DB8B313EA396E0A37C443;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_IsDone_m333DC14711DF7A1F576EFE3B1828D1AC8B3DC2F7;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_WaitForCompletion_mF4A524F5BCAF1882A4D96FF020BD874EF9DE69B5;
extern const uint32_t g_rgctx_TObjectDependency_tF0C3A64B8C24EB2695EA6E8DE02B23E0188B30EB;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tCBD239557E1789792A562A0476C0934C9C3902EB;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeExecute_mD4C280ABFC4A0DBD201400B82BF3EF574852B195;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t0C965C82FAFAD4ACFE5443145EFFF866FBBBEE2B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_IsValid_m15CEAE43BD0E32E28EBAE1B08E7B0FEFA13BDE02;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t0C965C82FAFAD4ACFE5443145EFFF866FBBBEE2B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_IsDone_m3D8EE79BC093705197CBD2C3A1ED9A33977FB27C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_WaitForCompletion_m3FE2F6150516CCCB192DF9DFE821DF33757D56E1;
extern const uint32_t g_rgctx_TObject_tE82607FCAA061F6E4BDDA1FD6CA94B1EDD406FA0;
extern const uint32_t g_rgctx_Func_2_Invoke_m8BB85C55B0CD830EE1182401514E19EE70E77014;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_add_Completed_mCE430428937C5D9DDA0298D8E9DE5187CDD782F8;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_Status_m5C44E5888103DA8B6A6A34E4C5139E9D7BBA9DC0;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_OperationException_m4D73E8B6B02720A570FCDAA4DABA7FCC0159D4E0;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_Result_m23971F8425A959ED4DE06DC754ECA3521FA52235;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_m043C793E65DACDFDF46C4D38297237DB21B15305;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Release_m8FD51EFCDA620F157F633342F66E71A223D6285D;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Release_mD816A1BA7E7A437154453BC450A0C33426329EFB;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_m63BE661C5F1A1B113C084EC8DD950B7D16F54DC0;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_m6C172E69BFB1A540B74F8A4572C1B2C58AC17E5F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m7FA95BA0A75B5DE9FD711B9FDFFE07F3575ACA71;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_PercentComplete_mFE8E910BA1401B8D8A41401F0F6072058BE683D3;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_PercentComplete_mE2EF24F4E870C01F957668F07260947A02E61689;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_t7A733063473B1D02B7DDDBEA2B6EE72CC21F5169;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tCCDE1A0D921C6CB2FE1536035308EA3E977BCB98;
extern const uint32_t g_rgctx_AsyncOperationBase_1__ctor_m57ADBCCFCD35309A9925E5CB7DD0B8F48D86641A;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tC7BF2B64CF0F1558E1A92497B1E3301E9AE73DDE;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_OnWrappedCompleted_m7A7B93C48FA00DB8AEA80C50A85C48816B6AADAE;
extern const uint32_t g_rgctx_Action_1_t68F88BF3C883C2A44D2C987D3632B1E24D6C90A6;
extern const uint32_t g_rgctx_Action_1__ctor_mA36C46CA57E212337A5C683B62E9970C49BFEC8C;
extern const uint32_t g_rgctx_TObject_t7CA28BCA8A6359251650157A2BA2821F70C89052;
extern const uint32_t g_rgctx_Func_2_t24AD25288E5E446B8ED9A5E17F39B872DE43D48D;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_RefreshDownloadStatus_m5862B90FEDBE3B05A6650E86CDBB3006AEF985E4;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_IsDone_mA0F5C41B481F47207440E82C3A3F4FA7602A3430;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tC7BF2B64CF0F1558E1A92497B1E3301E9AE73DDE;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeExecute_m3DD89E3DBEC668F077DD9DB7F863DD1CEF1E2DA1;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_IsValid_mD2E1DCAA0E31191CDCF17174C1D33A58803DEEFF;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tCCDE1A0D921C6CB2FE1536035308EA3E977BCB98;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_IsDone_mBC9AF4E671C8AE625CD636217FA4D171EA66162C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_WaitForCompletion_m5A9BE7BF3758659D22A9D84789D5A1967A4FB892;
extern const uint32_t g_rgctx_TObject_t7CA28BCA8A6359251650157A2BA2821F70C89052;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_Result_m914651D704375BC7649B9D15523ABC1843C0E77A;
extern const uint32_t g_rgctx_Func_2_Invoke_m73C534FC8E8AB573B23F64E0AAFD5D18EEF78F1E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_add_Completed_m0F7B95A81E60292D349ADE5932BADB92C3C522B7;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_Status_mDCD87B5AD4ABBB87448438715FEDCF9B65E33905;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_OperationException_mF0D59293332D94545DE14DC8E6083E0295B90B45;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_Result_m81F830789B0E9A079AEBD288D1C669A4E0FD31BD;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_mFA2BF4ADD7D727F9970414320C543ECE941A21A1;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Release_m561D5D387F517935AFB1681952078AE8A7BE3B12;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_mC514213F59AF9354F3A357779E9C63E38756ACF4;
extern const uint32_t g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m839190CC330711CD0064D543134497E60EFFAD54;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_PercentComplete_mFC0179902178E9F926C64625C97330C130D3F955;
extern const uint32_t g_rgctx_TObject_t0DB79463313463F84143DD41AA3896CFCB9461C7;
extern const uint32_t g_rgctx_AsyncOperationHandle_Convert_TisTObject_t0DB79463313463F84143DD41AA3896CFCB9461C7_m027E6962BB6D655C264147162FB1D3BA852115CD;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t2F60CF7B4778D8493F809CE8737C0640832DB3B4;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tF6EDF9620CED9505639E046EB7152ADD6DCED22B;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Start_mD3F7CCD9E0C40995CBDC9F578E3B55A862AA8652;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Handle_m1388212937C7C5FACD70A64D5D63EEF56B6F717B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tE5DC4D99863768FD1CA195B2F119B3EF6F8E5E17;
extern const uint32_t g_rgctx_T_t7C4E2B554CAD23D123603C790FFD75AA9C4D517D;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t7C4E2B554CAD23D123603C790FFD75AA9C4D517D_IAsyncOperation_set_OnDestroy_m5316C8EE26184140AD83413E4C69DA24034F0418;
extern const uint32_t g_rgctx_TObject_t992E6FEE1335A68F241B1A33F9D35B95C3844006;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperationInternal_TisTObject_t992E6FEE1335A68F241B1A33F9D35B95C3844006_m4E07F4FD4FFCC693179DA17301908D34FDCC13BB;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t8D77596C390A8018C25FD52336D2EA9992636C21;
extern const uint32_t g_rgctx_TObject_t2B79AED0959E5A02AE75E8B0C555F7056D0587ED;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperationInternal_TisTObject_t2B79AED0959E5A02AE75E8B0C555F7056D0587ED_m26A6635E7E11AA5E52CFC0539F88ADB0F5ADD8F5;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t4F8C1386A767DAA6C2408EA39D038B50A9929838;
extern const uint32_t g_rgctx_CompletedOperation_1_t1DBB4D614B1196561880D144F742E1587A0BAC3F;
extern const uint32_t g_rgctx_ResourceManager_CreateOperation_TisCompletedOperation_1_t1DBB4D614B1196561880D144F742E1587A0BAC3F_m0F1B10189B8E4A89BE6643A43E6F048E48594033;
extern const uint32_t g_rgctx_CompletedOperation_1_t1DBB4D614B1196561880D144F742E1587A0BAC3F;
extern const uint32_t g_rgctx_TObject_t290C6B7E779F49684D0018AD0CD1311B6FFC4995;
extern const uint32_t g_rgctx_CompletedOperation_1_Init_m60C81A41D6E566EEF431F106CE4CCA747043D5B5;
extern const uint32_t g_rgctx_ResourceManager_StartOperation_TisTObject_t290C6B7E779F49684D0018AD0CD1311B6FFC4995_m24655CAF383D0A3B038B9270A6DAEE1EEFB63BB9;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tA67472BBB0D968B6F8F072F58E35AD7689230543;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tDF7262A7F0BB1DECB842B92FA779F46DE71989D9;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t865EEC0199DEE6FCF5552F991D774B5B9C63E881;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Acquire_mA4054C8EA2052EB5FD171949B9DFC230FCCC1BA7;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t865EEC0199DEE6FCF5552F991D774B5B9C63E881;
extern const uint32_t g_rgctx_ResourceManager_ProvideResource_TisT_tAFF681A56D59520AAEF2EF0F525D7D355CC64BA5_m3C464DC8FC6E342842B851C208DA86C04B001CEA;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tD93F61116A070CE89EE8D21A552903AA52EFBC28;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m4A17DEF5D68E10740DD83C2513B6CDD30625E85A;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tD93F61116A070CE89EE8D21A552903AA52EFBC28;
extern const uint32_t g_rgctx_ResourceManager_ProvideResource_TisT_t3597071CDEAE87296CF33851BAB1E1B1C487045D_mCE91274D2D0E7F79982B71BAE1496FF04459F501;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t2CA4B2BB5EFDACF6DF41E44E2E710364EDAD9D2C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m446F5C1952026C20CED95CD3FA486A06D3B085EA;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t2CA4B2BB5EFDACF6DF41E44E2E710364EDAD9D2C;
extern const uint32_t g_rgctx_Action_1_tE330636ED01128D2EB83E50F43B25FDDF61F35DD;
extern const uint32_t g_rgctx_ResourceManager_ProvideResources_TisTObject_t94272D3461C89EA47E5977FBF0C3C2E095542FB6_mADF05A87B814C28E9A0D8C5FD67305C2034D7AC6;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t105CE8E5261F3BDC0277AC57AE095510AADDF3B8;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass100_0_1_tC318841598D48EB05586495DEEB001FF58ADBD6E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass100_0_1__ctor_mEEFBA75D585E8A278101423E6C93D943EACB2CEA;
extern const uint32_t g_rgctx_Action_1_t0498669D4E673877E2A7BC36ECCF7625A34789C2;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperation_TisIList_1_t7A6F23754486AB40BA57ACEC3E4851F8C5200528_mC16FF65274C21CFF259A76E5829E12D5D6E6F631;
extern const uint32_t g_rgctx_IList_1_t7A6F23754486AB40BA57ACEC3E4851F8C5200528;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tC9885F24B78FACC9FCAC56802D899E74AF1001AE;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass100_0_1_U3CProvideResourcesU3Eb__0_m707F7C1FDB449CF51E209424B35AFF28483AB42C;
extern const uint32_t g_rgctx_TObject_tD4A80BD04976BA5C2F60556706BE9D4DB2B99DA0;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass100_0_1_U3CProvideResourcesU3Eb__1_m278366EF89B884E43F6ED3E7D4089F46EB5B671F;
extern const uint32_t g_rgctx_Func_2_t30EE50C56B893B99FD194EC9973FDD012E1FFE49;
extern const uint32_t g_rgctx_Func_2__ctor_m867E2668B925904874456B430AEB00176075031D;
extern const uint32_t g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t7A6F23754486AB40BA57ACEC3E4851F8C5200528_m8A20CF0A8B5761C11C7E9B023F90167124B6D375;
extern const uint32_t g_rgctx_ChainOperation_2_tF0C26696B20C501EB0B9D7FDC959873622074E7E;
extern const uint32_t g_rgctx_ResourceManager_CreateOperation_TisChainOperation_2_tF0C26696B20C501EB0B9D7FDC959873622074E7E_mDBE918F0E395B7BBA465FA403785DEE0AD9E2132;
extern const uint32_t g_rgctx_ChainOperation_2_tF0C26696B20C501EB0B9D7FDC959873622074E7E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tB0807D2FD43D11CEB2AD4198ECDECEA7134CEF64;
extern const uint32_t g_rgctx_Func_2_t0E0C2D9BE53E5D6C93CE09904E64F54371FBD540;
extern const uint32_t g_rgctx_ChainOperation_2_Init_m753B7BCF358FF4FBC7D32F21A489BA0205869C74;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m23863138DD7724D3FD69532B97C6DD1A863C69A3;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tB0807D2FD43D11CEB2AD4198ECDECEA7134CEF64;
extern const uint32_t g_rgctx_ResourceManager_StartOperation_TisTObject_tF51A868E5791A3793FAD93C805C75B723E01E715_m12A03400F057025CC783D2910AA8A5BDF5B1FC1C;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tD6FF0F3EC655C2C593450B54E6C4AE591F3A655C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tF55A919845E0C432960E9C0C08FCA4FDB3F44FA9;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_t50C7D4A1B16E0123CB757009E92AD7304C6F5834;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1__ctor_m3E5374BEDF58A3976B01503C36340723FA4BB04E;
extern const uint32_t g_rgctx_Func_2_tF0C25950711DBF5848E5A5A3A2C02513311EA1F0;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_Init_m1A5450F30AFE4B446905DFF10E3948C909B0CB45;
extern const uint32_t g_rgctx_ResourceManager_StartOperation_TisTObject_t51AA3FC1624EB7EFD81B811105C6030E51EC461C_m83E5F53BA17E076E9C3B72FD72107F2963A04D2F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t2EF886BB7E4BC1C6B9CDAD15E7CB99053FF58FD8;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t0EAE9F9726BF0E4380E6CC5E7C0EEED89DFE6FFB;
extern const uint32_t g_rgctx_ChainOperation_2_t3EA92884B933F9162095667C756697F7AE51D9F0;
extern const uint32_t g_rgctx_ResourceManager_CreateOperation_TisChainOperation_2_t3EA92884B933F9162095667C756697F7AE51D9F0_m612626A76C46487ED56525C1236C76988D2352EA;
extern const uint32_t g_rgctx_ChainOperation_2_t3EA92884B933F9162095667C756697F7AE51D9F0;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tC2B48F1D49CCC7FD75F6BB39A50056E20D1E9DB7;
extern const uint32_t g_rgctx_Func_2_t7585A998799217E1E7BC07FFF623A1D39754E825;
extern const uint32_t g_rgctx_ChainOperation_2_Init_mA67CF51AA3BD5B26E1835E4E44FC9F1B4F6CCBAF;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_mF56E6A218EC2C30EFE85EFC64201971BE6DCBC1C;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tC2B48F1D49CCC7FD75F6BB39A50056E20D1E9DB7;
extern const uint32_t g_rgctx_ResourceManager_StartOperation_TisTObject_tF3D1B8D4EF4E9FC58BF76DE84F69EA5E675ADCB6_mDC4B2CEC671712413F74617D0AA970874B8DE24F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t0CD98AF621B617C4D938F2AD07EABDD95B7B7139;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tBE4D53A46F0C3DD6244C71A62A139A6C6620582E;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_t504C17717E980CA2DC68893949C869AE9464C69B;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1__ctor_mB21333B9DB3884FE545121CD4CF244988CDCE9EE;
extern const uint32_t g_rgctx_Func_2_t359D180D8C6A07D873F39C8140A19FED4C8B3F1D;
extern const uint32_t g_rgctx_ChainOperationTypelessDepedency_1_Init_mFE1D1221FF8FA865943374ACDE2974EFEEBDF0A3;
extern const uint32_t g_rgctx_ResourceManager_StartOperation_TisTObject_tE21784BBF51C6FBF5C4483AA38D33CDBE2A288BA_m161147C9F9B61F25257978E6024CC3FF9CFFFA4F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t139A193274471A55F9461A8739C8718E0B372F36;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t293A78F3727C29D78723277FC2EB1505BE9DF338;
extern const uint32_t g_rgctx_AsyncOperationBase_1__ctor_m183F657057B6F6FF6D80C905D225B83C2F475213;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t23034BD8A7348EBC352C95F8272D6C914B860DB7;
extern const uint32_t g_rgctx_TObject_t88BDF0FDCC2E0298D07A1C482380E8E359E46121;
extern const uint32_t g_rgctx_CompletedOperation_1_Init_mBDCC78B9F8C5CB7D8A45D4187E43C917492236A9;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_Result_m6B0613B1B7D4089752198AAE5B5CD51BC5FBD5EB;
extern const uint32_t g_rgctx_CompletedOperation_1_t9A7847AF845B9E400E4F57451497F1C4E4CB1455;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t23034BD8A7348EBC352C95F8272D6C914B860DB7;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeExecute_m3269ABAC6679921415BA6730C2434D6A12DE6B60;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Result_m04AD74F2E9E190BC16945049DA7C963600DF7E71;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_mB9B8FA825451F7911189A30BFDBBB36A168956AC;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass100_0_1_tF3D898C76F3063A3D77E15C27C7B618E123F9CD7;
extern const uint32_t g_rgctx_Action_1_t6B2D97B8AE6B04750ED8DABEB0D99422CA724281;
extern const uint32_t g_rgctx_TObject_t9BD547383D54EBFA633A045608B1EFDF9A8CFA04;
extern const uint32_t g_rgctx_Action_1_Invoke_mDED439A6EF174373C39805266C91FD63C1D25601;
extern const uint32_t g_rgctx_List_1_t9C46952602EC22E4A01382A04403B360E88759D3;
extern const uint32_t g_rgctx_List_1__ctor_m89F08E4AB501C662D0A14A0963AE898043A61AC9;
extern const uint32_t g_rgctx_AsyncOperationHandle_Convert_TisTObject_t9BD547383D54EBFA633A045608B1EFDF9A8CFA04_mC94990A2864A336F659D8F670D87328A75105368;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t3767F5E3DB2E13CA54373249AF07E53B4E6B53C1;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_Result_m8F278E51B2260F80BF6C662D911938154CC1F843;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t3767F5E3DB2E13CA54373249AF07E53B4E6B53C1;
extern const uint32_t g_rgctx_List_1_Add_m25029146E63368DE5FD7BD3DEB4935D9D8051978;
extern const uint32_t g_rgctx_ResourceManager_CreateCompletedOperationInternal_TisIList_1_t69E16C8E448CE85289784F65490E35B5D77FDC5D_m5BDAAB1D0865CA47635B39184FF44B4DD28C4199;
extern const uint32_t g_rgctx_IList_1_t69E16C8E448CE85289784F65490E35B5D77FDC5D;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tC027D97DA2C188250ECB547719CFEFFC833B596E;
extern const uint32_t g_rgctx_TU26_t84270B34D85358555BE6D9FE519E6E94891165DC;
extern const uint32_t g_rgctx_T_t5C0F3E77CC912EF60F10E0446BEBC8179091A551;
extern const uint32_t g_rgctx_TU5BU5D_t2D2C4B6F57F03776F72FA00EB9E15D738A427657;
extern const uint32_t g_rgctx_Reader_ReadObject_TisT_t821854D62406274345073E3DA287C24FA32A3599_mD1E1E71AE16F774466A1B2723284D5A33A449B2D;
extern const uint32_t g_rgctx_T_t821854D62406274345073E3DA287C24FA32A3599;
extern const uint32_t g_rgctx_TU5BU5D_t2D2C4B6F57F03776F72FA00EB9E15D738A427657;
extern const uint32_t g_rgctx_T_tECBAAE7384CBA557AB875DA1973DD604F24CC697;
extern const uint32_t g_rgctx_T_tECBAAE7384CBA557AB875DA1973DD604F24CC697;
extern const uint32_t g_rgctx_Reader_TryGetCachedValue_TisTU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165_m9A86761F22C0746EB96CA0279311FB9BBB1F6932;
extern const uint32_t g_rgctx_TU5BU5DU26_t659DF357F65EADB177D680E7EA9DAC9163DBDE5B;
extern const uint32_t g_rgctx_T_tBD5AE8C7901D08A42121E477B8722A04FA6FC867;
extern const uint32_t g_rgctx_TU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165;
extern const uint32_t g_rgctx_TU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165;
extern const uint32_t g_rgctx_TU2A_t83393AD558ADE2E91DD54E4853012E517E44769F;
extern const uint32_t g_rgctx_TU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165;
extern const uint32_t g_rgctx_T_tB93637C1A01BC379ECB036395CD377A25F97AFAF;
extern const uint32_t g_rgctx_T_tAD28673DFDA47E13D7EFCDBF8F3EC1BE20A2DCDF;
extern const uint32_t g_rgctx_TU26_tACD11A0F99313CB26961A8356C25BF6AC36FD4EE;
extern const uint32_t g_rgctx_T_t5C70F0C38E743DE890656C0A78E32A60FAFC8BBB;
extern const uint32_t g_rgctx_TU2A_tCF02DEB431F070C64C653B423DCD522E8BA61BE4;
extern const uint32_t g_rgctx_TU26_tACD11A0F99313CB26961A8356C25BF6AC36FD4EE;
extern const uint32_t g_rgctx_T_tDB8D1C44ADB32944EED2913050ED827CC69CF70F;
extern const uint32_t g_rgctx_TU26_t5A5418496F5D9C826ABB7A4F0E22DC92FF609436;
extern const uint32_t g_rgctx_T_tA4F5C8530B7503B3E075A70A1741D3DE52289C0B;
extern const uint32_t g_rgctx_TU2A_t42DFE7909E22953C268147ACB7CC8409BA33E20D;
extern const uint32_t g_rgctx_TU26_t5A5418496F5D9C826ABB7A4F0E22DC92FF609436;
extern const uint32_t g_rgctx_T_t6FE7FC22E2C0C40985040077400A9AC2F7747B35;
extern const uint32_t g_rgctx_T_tEF9A349F5CAC3CC260DAB237BEE7009D6C5DBB9E;
extern const uint32_t g_rgctx_TU5BU5D_tFB5954515E4E996318DD785056B07531E39CC315;
extern const uint32_t g_rgctx_T_tE75101A1EE401E1F7937BBBA8626F37D6538B3C4;
extern const uint32_t g_rgctx_TU2A_t01E2E1630DCE63EFA006A28D2C841947A26ACC33;
extern const uint32_t g_rgctx_TU5BU5D_tFB5954515E4E996318DD785056B07531E39CC315;
extern const uint32_t g_rgctx_TU5BU5D_t1967B37758833A74BB58C2467C45F213D8B21BF0;
extern const uint32_t g_rgctx_T_tF3FD38F4315E0498D98821D0182A65B47142E07B;
extern const uint32_t g_rgctx_TU2A_t6EB7D4E7B7EB57960947D80D866035D1F07401AB;
extern const uint32_t g_rgctx_TU5BU5D_t1967B37758833A74BB58C2467C45F213D8B21BF0;
extern const uint32_t g_rgctx_IEnumerable_1_tB38D197CB83EB9C43D9E73FC006AC5FC857C97C2;
extern const uint32_t g_rgctx_Enumerable_Count_TisT_tBD875C9E85BC873AE55469D0FEA83B1DD206F8B6_m917188AF7FED3EDA3C9C1A3A0945E563FC60C708;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m74F480F07AFBAF2A90F4E30689EDD7E265AB654D;
extern const uint32_t g_rgctx_IEnumerator_1_tB286B61EBF9F6DAD30D34DCF490424BDED629A3B;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_mFB1059B108D4D90302B44569C4572A7B8784F492;
extern const uint32_t g_rgctx_T_tBD875C9E85BC873AE55469D0FEA83B1DD206F8B6;
extern const uint32_t g_rgctx_LRUCache_2_tDB05E2A0DAD885C4684D001DC1E7E252401E8C21;
extern const uint32_t g_rgctx_Dictionary_2_tF728F3C4E1022E1EECFFCF2072B8A919DD21C019;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m41F26ECBDCBAD8D50FD9E33E45F0B3C3F228DEBD;
extern const uint32_t g_rgctx_LinkedList_1_tA7234D3660EB0DE19AA1F7AE3D9C63B11BA3C29D;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m4C045574531053C3BD632186B33B7DD864907D7C;
extern const uint32_t g_rgctx_TValue_t6F50F3B04B6B23A6D5670F397E89BB003AB1F2B5;
extern const uint32_t g_rgctx_TKey_tABAE23E339360A1A9A9633D4C6C3C862DF9C8974;
extern const uint32_t g_rgctx_Entry_tF69382A9A56AE0DA6BBFD465E67D06C92A137972;
extern const uint32_t g_rgctx_LinkedList_1_AddFirst_m9E37459D5EF5381A5C22EFF9C7CFB8F96E18999C;
extern const uint32_t g_rgctx_LinkedListNode_1_tF2F7AD44984C3301E08AA513B45E3809FB54697D;
extern const uint32_t g_rgctx_Dictionary_2_Add_mC07AB9CEE85A1A9B11A831094C1B3342BA71EAE7;
extern const uint32_t g_rgctx_LinkedList_1_get_Last_m5A3C228E5976C5546B1B1EECD7AF2F894097D779;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_m60A337B99AE647A8ED3B136969EE747007478180;
extern const uint32_t g_rgctx_Dictionary_2_Remove_mD72AC03C82645A5D4FCF39CBEF8A21A92ECE9069;
extern const uint32_t g_rgctx_LinkedList_1_RemoveLast_mE9B96B97AD6682CE411A458D4813F43220F90B0D;
extern const uint32_t g_rgctx_LinkedList_1_get_Count_m6E3C52329B587A295A7FE217876A65727630309C;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m79D658748206B324C6261E09E6A2583C818D337B;
extern const uint32_t g_rgctx_EntryU26_t7E16B9FBA3310AC1F3F4099911A53B1C7F7C319A;
extern const uint32_t g_rgctx_TValueU26_tF737931DBCD884C94F516439D5AD5145D3358A03;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Previous_m9B167FDAAB7399F16B265112E716C00E6E6431BE;
extern const uint32_t g_rgctx_LinkedList_1_Remove_m4EFE2B96534913C8D641135335E6D9D1BF42C9E6;
extern const uint32_t g_rgctx_LinkedList_1_AddFirst_m9740259CD113CC000D14FC215AD95826B007E5F9;
extern const uint32_t g_rgctx_Entry_t29BEACB44DD47A0502B9DB93139B3DE0E8E6381B;
extern const uint32_t g_rgctx_TValue_t619B3AF5A1A8B719CDE2B68582EC6D638BA87633;
extern const Il2CppRGCTXConstrainedData g_rgctx_TValue_t619B3AF5A1A8B719CDE2B68582EC6D638BA87633_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const Il2CppRGCTXConstrainedData g_rgctx_TValue_t619B3AF5A1A8B719CDE2B68582EC6D638BA87633_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_ComponentSingleton_1_tCC75A31DCEFE68E3BF6FEC1371DAA246B48508B4;
extern const uint32_t g_rgctx_T_t24E22872472DFEE87B691F1BD9B4E92C2F537060;
extern const uint32_t g_rgctx_ComponentSingleton_1_tCC75A31DCEFE68E3BF6FEC1371DAA246B48508B4;
extern const uint32_t g_rgctx_ComponentSingleton_1_FindInstance_m67A6F8E06521D2EB0EB2287AD35743E056397D22;
extern const uint32_t g_rgctx_ComponentSingleton_1_CreateNewSingleton_mC580FAD47A66BB73F759C08CEFD8DC51196887AB;
extern const uint32_t g_rgctx_Object_FindObjectOfType_TisT_t24E22872472DFEE87B691F1BD9B4E92C2F537060_m98C5C0EFED0DF258A19C826C1C8430D10D4EBE5D;
extern const uint32_t g_rgctx_T_t24E22872472DFEE87B691F1BD9B4E92C2F537060;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t24E22872472DFEE87B691F1BD9B4E92C2F537060_m251E03D3140935E976439034FC58A2B4CA505587;
extern const uint32_t g_rgctx_ComponentSingleton_1_GetGameObjectName_m794FDF1DA94BE1D71E0662658490CE5F96B6F3C9;
extern const uint32_t g_rgctx_ComponentSingleton_1_get_Exists_mE84A4C7EE5D5E0276613D2B0E3B58AC564992B6A;
extern const uint32_t g_rgctx_ComponentSingleton_1_get_Instance_m64AB893BBAEA16AB4EDBEA9A7BF3268274334C5D;
extern const uint32_t g_rgctx_LinkedListNodeCache_1_t3829C43319CD269B4B82E2EAD314F597CFB7FEE4;
extern const uint32_t g_rgctx_LinkedList_1_t1271D9C1FB9AFE86B4ACD2E131ED477B8D5436FD;
extern const uint32_t g_rgctx_LinkedList_1_get_First_m5C815C2FCCC67C0606B4F42B86AFD0F46A5D4D75;
extern const uint32_t g_rgctx_LinkedListNode_1_t2BA8F5BC3F2FE544487F18B9619A20CEA0C309D8;
extern const uint32_t g_rgctx_LinkedList_1_RemoveFirst_mC60F2FC5C62165FFD3233FE2E5862029EF6762F0;
extern const uint32_t g_rgctx_T_t45AC0849CDCBA23EFF219A328DDC39024D6C8E29;
extern const uint32_t g_rgctx_LinkedListNode_1_set_Value_mB3E3EF662FE828B838A43ABAFC85531A445164C3;
extern const uint32_t g_rgctx_LinkedListNode_1__ctor_m4B6756CBDAB9C4D57CC3780C11A59FDE4B441575;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m4F19967336D00152E2A15B1077C4AA8E8FC64A4F;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_m3516BB60DEB48100F0F822593744B8D8083B3683;
extern const uint32_t g_rgctx_LinkedList_1_get_Count_m50DE8908F6E0EC311777B8C263486933232D962E;
extern const uint32_t g_rgctx_LinkedList_1_RemoveLast_m1D15814C1623585852BD1546F245BAB2D32F08A9;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_t063B2F67429F037AAB88F8A9FB4AC1DFC6DF4C17;
extern const uint32_t g_rgctx_LinkedListNodeCache_1_tBC87877C4C116D2ED92BBF862E7DF0D18F09038A;
extern const uint32_t g_rgctx_GlobalLinkedListNodeCache_1_t063B2F67429F037AAB88F8A9FB4AC1DFC6DF4C17;
extern const uint32_t g_rgctx_LinkedListNodeCache_1__ctor_mBB2EC56C707F4E7B8AB5BC88374A0DAE82903A9A;
extern const uint32_t g_rgctx_LinkedListNodeCache_1_set_CachedNodeCount_mADB2AFC8832CF63D012A4FC75B4246889385EBC8;
extern const uint32_t g_rgctx_T_tF4EC86031C24522A4A3A942153EE355BD556D1FE;
extern const uint32_t g_rgctx_LinkedListNodeCache_1_Acquire_mCCEF06B4306099613A41EF43A381D0731446A8B0;
extern const uint32_t g_rgctx_LinkedListNode_1_t1D8497E0EDCC13635FB482C848536EC7E1AFDF67;
extern const uint32_t g_rgctx_LinkedListNodeCache_1_Release_m208DE838E7CD8E535679CF25A564091C1A093660;
extern const uint32_t g_rgctx_TObject_tC1105AECAFFF7F16C1513FC5BDAA46B7C3927AA9;
extern const uint32_t g_rgctx_TObject_tF17C5B95CAEE5536ED9BC475D6C8FB67D18769F1;
extern const uint32_t g_rgctx_TObject_tF17C5B95CAEE5536ED9BC475D6C8FB67D18769F1;
extern const uint32_t g_rgctx_TObject_tE32DA324E8F0BF9F11637592FB25C961ADCA1AC2;
extern const uint32_t g_rgctx_TObject_tE32DA324E8F0BF9F11637592FB25C961ADCA1AC2;
extern const uint32_t g_rgctx_T1_t8A86DFD91DE6DC41EDB1B634CCF3260786E2F941;
extern const uint32_t g_rgctx_T2_t20CA7FF7A31CA44D9763139F1CFAC9C87A8867B5;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTAdapter_t9EAC58637DA93C8720A9F745723789BEDA618AF7_m5336F2E07013F9B0AA97E086139D002053DA1FB3;
extern const uint32_t g_rgctx_TAdapter_t9EAC58637DA93C8720A9F745723789BEDA618AF7;
extern const uint32_t g_rgctx_T_tEBE114FEA3901CFC5955E4C778788A5D2DBBC0F8;
extern const uint32_t g_rgctx_TObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC;
extern const uint32_t g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_m1B1FE976661F3DF510CA73C858D0F34DB85CB330;
extern const uint32_t g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_mC0AAD6B292DE174CFC89660D36C51D9183508E1B;
extern const uint32_t g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_mD010590E760F0EAB5ADACE0C78ABF662A5430AA4;
extern const uint32_t g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_mE91CD03D52054E8985DF9A0D2E8CDED35BE9A01D;
extern const uint32_t g_rgctx_IGenericProviderOperation_GetDependency_TisTDepObject_t032A91F8C8D44534919E4B01824496D679F1F771_m6AEAA4D9E5F3FCD5B33E8F5E5A22A43C4A773636;
extern const uint32_t g_rgctx_TDepObject_t032A91F8C8D44534919E4B01824496D679F1F771;
extern const uint32_t g_rgctx_T_t3FB64C62813E68C5C368C363B6043E571C340C2B;
extern const uint32_t g_rgctx_IGenericProviderOperation_ProviderCompleted_TisT_t3FB64C62813E68C5C368C363B6043E571C340C2B_mCEE75A0DD9E797F528ECBC64564A86CBE6E0747A;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t629AF4B8DDB948B06613D567AA2F9BCFD05D013D;
extern const uint32_t g_rgctx_TObject_tE3CBD04F86F44F9E70B44A1B40F8A61A8A461688;
extern const uint32_t g_rgctx_DelegateList_1_t1C8830934893CF4BBD7A1E5B14A120D782DC09F2;
extern const uint32_t g_rgctx_DelegateList_1_get_Count_mCF97165B0F17059CB0D857EE2BF54967416B4395;
extern const uint32_t g_rgctx_AsyncOperationBase_1_UpdateCallback_m6E0B0854DFACEEFAA75C4731177D068E89A36BB1;
extern const uint32_t g_rgctx_AsyncOperationBase_1_U3C_ctorU3Eb__38_0_mFCFCB7D5119779A0430BC2C8F61DB0138F378EC9;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeWaitForCompletion_m58BD3906851849863D7D6B2A1564744F294C1EC0;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Handle_mF6373E086D7485243E131ED841B36210F6363D57;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tFA133FA5812CE09EC9E01A8048B387432E421B29;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m9982C7C5CA9CF5A371C1152B146EEB60113AE28F;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tFA133FA5812CE09EC9E01A8048B387432E421B29;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Destroy_m7B054374D7F2D8ADD67B6500AEBFFF6EAF7F7A86;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_Result_mE0E816F081243CB676EBAA23F08E9E2E072E1332;
extern const uint32_t g_rgctx_TaskCompletionSource_1_t8A9FC4717ABE8019988A2761A03B6AAFB8C1307F;
extern const uint32_t g_rgctx_TaskCompletionSource_1__ctor_mACE7013F8FC4271C43DCEEF07C7AE61556C17B86;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_IsDone_m0D3BA85C8CB8528DD8729FAE298D2EE7F2FC7183;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_CompletedEventHasListeners_m55C160EF9C54B23CE0E6DF8124B49D2E3FE565C1;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Result_m088FB231E90661764204034B83B84F3653E42E37;
extern const uint32_t g_rgctx_TaskCompletionSource_1_SetResult_mBFEAAE091FF23FDC59864F3CDD255CC75213782F;
extern const uint32_t g_rgctx_TaskCompletionSource_1_get_Task_m1FD966E58B5D9A9EFEBD44E1F3E1ABB980E80A01;
extern const uint32_t g_rgctx_Task_1_tB35927EAA854A33A147D7C01BA32B3BBDD625069;
extern const uint32_t g_rgctx_DelegateList_1_CreateWithGlobalCache_m6F835979BD13643C4188A71B8823CF74875549AA;
extern const uint32_t g_rgctx_DelegateList_1_t1C8830934893CF4BBD7A1E5B14A120D782DC09F2;
extern const uint32_t g_rgctx_Action_1_t2650D2B87DD9C0824F2C1207E157E1C61827230D;
extern const uint32_t g_rgctx_DelegateList_1_Add_m5C75550561CEE7BC35942F6ECE927A018BC81CA2;
extern const uint32_t g_rgctx_AsyncOperationBase_1_RegisterForDeferredCallbackEvent_m8CC02AE9F658679AB712ECAEC175B46F00E4AE26;
extern const uint32_t g_rgctx_DelegateList_1_Remove_mCBBE67A8A9B9F55C8D6F20800EF4012A10F18B47;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass60_0_tDE6DAF31E7584FC244F4F2EF9E6DCD491EC945FA;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass60_0__ctor_mB8B843B8B4270F6A571613F27DEE685D962DD71E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass60_0_U3Cadd_CompletedTypelessU3Eb__0_m905EC1CEFF4C9B92280220734A268BAB93A91C61;
extern const uint32_t g_rgctx_Action_1__ctor_m69C6E9BEADFD89115B4CB69F697C8D46B176FE69;
extern const uint32_t g_rgctx_AsyncOperationBase_1_add_Completed_m497CCFD6F8A136EEF8A58741085D5B8E965938DA;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass61_0_t65DB736AC9C09279637FDF5100C67EA0A5829DB0;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass61_0__ctor_m04A01F75A92565EB5B0D82CB1BC54B78886C3DF2;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass61_0_U3Cremove_CompletedTypelessU3Eb__0_m020C4C490E90F2C6D413EFC07F00BC65296B8789;
extern const uint32_t g_rgctx_AsyncOperationBase_1_remove_Completed_m884D0D3E3015BB5C73A6112026AE65CCFC65A025;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Status_m3E1E71EF030F30725F733D568E6A6BE435CE583B;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Progress_m6C55CECF99969A371DF8BD9B1F8A80B1ECCE1714;
extern const uint32_t g_rgctx_DelegateList_1_Invoke_mA382F7E6131F788765D61D44BF3ED517BBC48A79;
extern const uint32_t g_rgctx_DelegateList_1_Clear_m71093C86A3B951191D71E398290F882D06564CDA;
extern const uint32_t g_rgctx_TaskCompletionSource_1_TrySetResult_m5B17C3C72B05F5EFB3F5B79F9D276FE602641F5F;
extern const uint32_t g_rgctx_AsyncOperationHandle_1__ctor_m0C91AC51BC63BA5518717F6FAE63A160A5C7E2B4;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_m129FC83A9973FAE652E5ACD8E25178E76BB3103D;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_mA83618A5686BB850B13FD99E5E79638FE72CF278;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_DebugName_m9412F620CF9717A47A2ABE1F555AE7D9F244617A;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_OperationException_m97E4EAD3094F4DFE5FB14E8F91336EB608D4AFFD;
extern const uint32_t g_rgctx_AsyncOperationBase_1_ReleaseDependencies_mE28188AFCF9859C99C929738475A51EEA7A1508F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeCompletionEvent_m11C513489E21935B007F71A1F632110D0633F68E;
extern const uint32_t g_rgctx_AsyncOperationBase_1_DecrementReferenceCount_m8ABDCAD40218A5A1CB3F05662D5F2F4F89776D0E;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_IsRunning_m9C1E401DC012241FD3295F2C6A3BF9D90E91E80C;
extern const uint32_t g_rgctx_AsyncOperationBase_1_IncrementReferenceCount_mA58F707615ED0E5400DC52244CE84DB9945C26F2;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeExecute_mD27188CEFB526E09D0E3D3DDAC6591215619D907;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Execute_m6DD865D3670DA55EB4662BE0FC141F74920597C1;
extern const uint32_t g_rgctx_AsyncOperationBase_1_add_CompletedTypeless_m8BFD647FD99EB98C190B38E6AD2A7091F7C22B35;
extern const uint32_t g_rgctx_AsyncOperationBase_1_remove_CompletedTypeless_mC182602345A0280838E7813CE35E3BBA6EABBB1E;
extern const uint32_t g_rgctx_AsyncOperationBase_1_add_Destroyed_mBE75C8208827A738C78889746B1424ACF22048DE;
extern const uint32_t g_rgctx_AsyncOperationBase_1_remove_Destroyed_m0948FF3B960CBFF021BAF3BE2202F93F4F244CF8;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Version_mD559AA0B05492553A72D70BC1533A3A2C18449C4;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_ReferenceCount_mF2CD140CCF7F544DAEF14474ECACF4F4A09857A8;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_PercentComplete_mF77322F5AFECC1ED2934FE4CA8310AE32B52AB09;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_OperationException_m6479D75258CB8ADB3E49F5E63594D9C001F0038A;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_OnDestroy_m7E3F08EE27E387FF12EF948F3A4497FE583BE81B;
extern const uint32_t g_rgctx_TObject_tE3CBD04F86F44F9E70B44A1B40F8A61A8A461688;
extern const uint32_t g_rgctx_AsyncOperationBase_1_GetDependencies_m16601590EC6445923D764917F2F0CB51F6EC3159;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Start_mA24D040D77E011B55BB836BB7CB38AE974CA6CFC;
extern const uint32_t g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m4B1392072E614D979D87928088DDDB6AB317FA58;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass60_0_tB9477614D3B3737FE292F8B443BE27C2C36DB96E;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t48FEF83E169922192CA234535556633127410A87;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_m70808B822DFB251CFF3A0437CA3854A00A5E07F1;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t48FEF83E169922192CA234535556633127410A87;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass61_0_tE6F3D5088FF982BF6F68C8A8A30DE1654D5D42AA;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t751F6204C708E675406B4412829F1D5EC4687875;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_op_Implicit_mCF4FF0A129A0A00C57CFAFD4A0B11BF14C9A24A5;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t751F6204C708E675406B4412829F1D5EC4687875;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t5E74A3B08DA3FD6088E9C8F6F01E82D679710CEB;
extern const uint32_t g_rgctx_AsyncOperationBase_1_tB3D75DB0BD350C2D25F09A960996B944DE407B6B;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Version_m80B61B95508A94793D7463BDA60E74E4EF63B209;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_m5CE73A688B8E1D9437F62D1E68374310DECC11C7;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t5E74A3B08DA3FD6088E9C8F6F01E82D679710CEB;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_InternalOp_m9135A6B80C43976000F8803932CD314B96321EC4;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_IsDone_m6C1B49341697499A92324FF30D5EFDCCABD7D80F;
extern const uint32_t g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m41413B242A6BE6C8C72B2F217C3013ECED937DCA;
extern const uint32_t g_rgctx_AsyncOperationBase_1_IncrementReferenceCount_mF28B3AAD3BD09ADA7E415F4EA28765CA11286618;
extern const uint32_t g_rgctx_Action_1_tBECC6DD59730920BB05A3E9151A4F551A20943AC;
extern const uint32_t g_rgctx_AsyncOperationBase_1_add_Completed_mB1BEB91E5CF67359A584AF9017F9F2F29D4DAD08;
extern const uint32_t g_rgctx_AsyncOperationBase_1_remove_Completed_m18C383682251473CC86863FF723136F7DEE81951;
extern const uint32_t g_rgctx_U3CU3Ec_t93C54F810FADE1AC130F3CD2AD5A07AC53ACCDF6;
extern const uint32_t g_rgctx_U3CU3Ec_t93C54F810FADE1AC130F3CD2AD5A07AC53ACCDF6;
extern const uint32_t g_rgctx_U3CU3Ec_U3CReleaseHandleOnCompletionU3Eb__20_0_m6379A1D59227871E77D294A958DB06E0A3E8C2E4;
extern const uint32_t g_rgctx_Action_1__ctor_mF972C6B81392C79680988CF2374372365B655A53;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_add_Completed_m7EF99D1354427147040F82F2805F3EAA1E33BA77;
extern const uint32_t g_rgctx_AsyncOperationBase_1_add_CompletedTypeless_m4CF3EF1D654996B8F98EAE9A0333DBAF1F5461A0;
extern const uint32_t g_rgctx_AsyncOperationBase_1_remove_CompletedTypeless_m34DDFD98FAA60D64F7276917BC02C6201381F141;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_IsValid_mE7125A43455F56F6660A6C3261BB8AEA79FE5428;
extern const uint32_t g_rgctx_AsyncOperationBase_1_GetDependencies_m3E3500CD8110D7E874130FAC220F5C19A002A0A6;
extern const uint32_t g_rgctx_AsyncOperationBase_1_add_Destroyed_m4EAB48EDF29A056CBA9671FA8EC759B4509F10F7;
extern const uint32_t g_rgctx_AsyncOperationBase_1_remove_Destroyed_mF63CB4FD8D17B634DE33C21E4643C47A14281318;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_IsDone_mF45D0129C6406F51D5DED2A84632B80F521FBEF7;
extern const uint32_t g_rgctx_AsyncOperationBase_1_WaitForCompletion_m763F60F24D08D14ADCCEF5B9B44FA2EC873047AF;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_get_Result_mB257D6A345E903F29145618379187DD8746FF5DD;
extern const uint32_t g_rgctx_TObject_t4E421E5A4CFD4C8D595C00A0DA8CA3CDED2ED175;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_OperationException_m09F1F2EDB7767D9EF00D256926584301A6203193;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_PercentComplete_m54DEDC07DE00F2C58426053E269E7B5A4FA14342;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_ReferenceCount_m9613584F99597F8176A9D35DDAEE8AEC0D767303;
extern const uint32_t g_rgctx_AsyncOperationBase_1_DecrementReferenceCount_mE0B0AC17068E804F8A54AD5223F7315E61D21A1C;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Result_m9C646ED37EA0F6A2BA498629D4EC6867BE404744;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Status_m348651738F30F82B1E2121D9D59140B6FF893BC0;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Task_mA992693B1C2B3BA73C685A18A3F8990F55ACCB10;
extern const uint32_t g_rgctx_Task_1_t1644483AEC968E3A2A2AAB1AF13AEBCB6FB15D38;
extern const uint32_t g_rgctx_U3CU3Ec_t8C7AEB7E19D398D60C4BA3B2479EC839396D2947;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_m331CAAE8D47AC27642F566D4D789B524A197F14A;
extern const uint32_t g_rgctx_U3CU3Ec_t8C7AEB7E19D398D60C4BA3B2479EC839396D2947;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t3E98084D393DF28814B2A1D62C3F54459050B12B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_Release_mEB5DF206799EBF339D16D1616B434EE5148AD31A;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_t3E98084D393DF28814B2A1D62C3F54459050B12B;
extern const uint32_t g_rgctx_AsyncOperationHandle_1_tCAD63F4245F9AA03D4348901356B0A3828461624;
extern const uint32_t g_rgctx_AsyncOperationHandle_1__ctor_m7464BD1B25B26FFDBF538003A60EB9CD82EBEE20;
extern const uint32_t g_rgctx_ProviderOperation_1_t4D236987283AE317B692FA2C2BA1AA1C6A26A294;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_IsDone_mE4C97BB432FAE177C6204B50D62B1CF769B15D64;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t2144181075E702B37FA261A834EC963A8991F1BC;
extern const uint32_t g_rgctx_AsyncOperationBase_1_InvokeExecute_mEA53A28013995EDFEEB78CAF15924896B8729C26;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Status_mE36B14EAEAB6436B65184E9D8E474DCAF7EC1C4F;
extern const uint32_t g_rgctx_AsyncOperationBase_1__ctor_mF8C8FC6B3101AA07A664B56D3190391BEB8E2A16;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t2144181075E702B37FA261A834EC963A8991F1BC;
extern const uint32_t g_rgctx_TObject_tDC9E3BF6AF8A685EECA3DA99B40EE4249DCF8D89;
extern const uint32_t g_rgctx_AsyncOperationBase_1_ShortenPath_mFF05F08B7623220C8891FE0D9EA5FD64DB55A125;
extern const uint32_t g_rgctx_TObject_tDC9E3BF6AF8A685EECA3DA99B40EE4249DCF8D89;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_Result_mDC7E0EB1D1A0F69FE362D5E191D87B4C8934BC2B;
extern const uint32_t g_rgctx_AsyncOperationBase_1_get_Result_m9469C982380BE7451EA3E090914DC104F6C3A565;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_m456A0CF9E48C7017A48BBE4D680964D505D0DEE9;
extern const uint32_t g_rgctx_AsyncOperationBase_1_Complete_mE2C565D22E043F0F06187F1639F7F094D799A9A0;
extern const uint32_t g_rgctx_ProviderOperation_1_ProviderCompleted_TisTObject_tDC9E3BF6AF8A685EECA3DA99B40EE4249DCF8D89_m76A148F8CBCEB51576497170806FCCFAEB4E2C2B;
extern const uint32_t g_rgctx_ProviderOperation_1_WaitForCompletionHandler_m536242851C10D069797A83CB23983A68A933D08C;
extern const uint32_t g_rgctx_ProviderOperation_1_SetWaitForCompletionCallback_m4E498166C7ACCFB0F7F06108CBC6064AE72F5500;
extern const uint32_t g_rgctx_TDepObject_t0FB6D81E5FCCF9757C7829864BA1A690B6D8DD60;
extern const uint32_t g_rgctx_ProviderOperation_1_tA925151037A31C1266269AD012793F2692406CD0;
extern const uint32_t g_rgctx_T_t908D0CB71CCF4187CF10D8450C6ECF3D096095D6;
extern const uint32_t g_rgctx_AsyncOperationBase_1_t271FC0CC2D450FBBC34E84B8EE513139BEE7EA62;
extern const uint32_t g_rgctx_AsyncOperationBase_1_set_Result_mAC5383430D3EB7EE72534B1017C8DC3853217FD5;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t908D0CB71CCF4187CF10D8450C6ECF3D096095D6_Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3;
extern const uint32_t g_rgctx_T_t908D0CB71CCF4187CF10D8450C6ECF3D096095D6;
static const Il2CppRGCTXDefinition s_rgctxValues[495] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tB657AA89B56DE58FFD262F226E686C775004BAA7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t96DF0E7C89BAC9AFD95D29D0E369CDBAD9AAF92C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DelegateList_1_t2D4A585817F8DEDBBE3F4F365B2F2D64B657BBC4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t63405FAA9C92678A9009DCB3B74B36F5718378D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_Count_mC227FFB26892174F56F4891FBE705B61EEE91F03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tB063712CA774D5ABC71ECDB08C5B39ADF5AC6BC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m1792CA766E45ACC2DDE8ABE1C39AF1F0B6795463 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t9D12EAA2087768BCD377BEC70819A20ABB95D292 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_mB1224BCE6BACED8D294EE9422D8B8F710A78CC24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_mC0CDF84044EDA65DB99C3A475186A877559B80B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_m7EDAA3FCE7DAF16C27277741551A00C14482219A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_mF0C551C504096B79FBDEDED02BB5D86E46D3E050 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_set_Value_mAAA06E418681581B1EA64FA0FD320B67D08187F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_m8091C8FF3BFED23A22E39E9643EFA722AA1CF783 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mC676804465D944892CDCF37927234ED2FB2578BB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mF489AD688818BA2CA07D1C52DD759628525D9123 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFE3B10388C5E3D61DCFFF7195A8DF4E939B6E933 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mD2C04159DBACF435CDBD2AB307D0186FEA79A20E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_get_CacheExists_m288E45863D22A5AA9403B60C3C47E435B273170D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_t6F5A3D1046EB88F4580BA59E142143602AF67A6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_SetCacheSize_mCF4136F320E5F492DAA1AB1A51B15770FF7C0A41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_Acquire_m27CC32F0CBCD85EB947E411593362CF24A36238D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m1B08D557A412A0708128A5C7848D486F2C7E7390 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_Release_m786D04D14849BF559A82EE1B3B31B2769E52AD23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m8C190A6A84103B0D6C6F79BF2D037F71D74269EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1__ctor_mC30E45B26C54C08FADDA8E510D5AF3CF22002665 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ListWithEvents_1_tE8B60E3978C29C35C1DC80A83A69B4A3E3DD2E09 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t3259692D2E20CF724AA3D3229E6866A93A317802 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1U26_t0562A736669252148200C1218239D892AFD0933A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFD636C50586BA321ABBB5F7259B02A2B178232EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m7598AD250C245A0808E990451516493FBE33B358 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t6B9FC9BAF7D739AD163C93367C4B9F82E3A6247E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m740982C6BF8852E872DEF525EA319620AECDC425 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m69C931DB75C1449C2210DCE44B83EDE50E12B3D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ListWithEvents_1_InvokeRemoved_m39216321167C8C46EEA4EB2251AB7BA7806D419A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ListWithEvents_1_InvokeAdded_mBDFCDFA35C3848E20EC7D3CEFD954780CB647897 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mBC8D1A4B029D80207D5664E1F243A46C87FEE970 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tB0B3F6DA037BF4765B2F0682373E4BD273287CA1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_IsReadOnly_mB6015B2141C3509DBF075E4D87AE128EA1A962B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m3B08975E11ACBE777D20FE4F3E5F59B05A2C1E2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_mA5A8F8AB082AFB2406A6F2D7937B15D71B67F288 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t2C5C3E4C5B2FE27B08B71F21A086987370E326ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m07AAFB2DF9CBA3126FA3D6A0BCD449D21C879A76 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t2C5C3E4C5B2FE27B08B71F21A086987370E326ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m5785ACB49E4B9CA23D0804FF02CFD11B55DD463F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t2C5C3E4C5B2FE27B08B71F21A086987370E326ED_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m2B7BB5331094069DD56680558FC171B8AD75E83C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Contains_mF13920CB1DBB0267887A5D497812B6CB09AAD2AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD1240814399BEC0AA1806F6E8701124FD78614CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_CopyTo_m2AED55C2EBE2A7AD3925EB68DC8100D40BFCF22B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t82BA34BAFAF756ED3003103C07F67B7E89D7AEA1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_IndexOf_mCBD05D9CC899863B35A2875B9C41A17E0D7C4F33 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Insert_m6620A66CA876EDE488E183E76B122A093521AE57 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Remove_mA02873780B57DDA9AF61CF614E122992A556D09C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_m33B2A6A3A8BCE2E4A47535845AD1351F66A19614 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mF7F7DCDB18521F39A98DF94DC5277D6246BA116C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChainOperation_2_t1771C6FDEF5A45DDDC684ECB5CB22E3A259B0492 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1__ctor_mCE9B061BE38223E9D143F95BBA597F35399ED56F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tCBD239557E1789792A562A0476C0934C9C3902EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperation_2_OnWrappedCompleted_mE7BDEC00E466CCE8D7376F90FCF9C6A7A2242AB9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tDFF757C8185F55D18EF448A75C0667C85656E929 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mB582F7E516BD40976AAE75E280C4CDE5CF6D4C6A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_tE82607FCAA061F6E4BDDA1FD6CA94B1EDD406FA0 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObjectDependency_tF0C3A64B8C24EB2695EA6E8DE02B23E0188B30EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t4C9A1A9A67BF9CCBD03A6171126D9AC272AF6E0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_DebugName_mB305FD16BAD7B79069EDADF3D6E3B42CE053776E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t4C9A1A9A67BF9CCBD03A6171126D9AC272AF6E0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_IsValid_m3F9A32936CAA230B9AF4E4DBDBA3420C451A8226 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m9A72ADAFA326DC64D07BD6F0A16D7D78559EBE92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Acquire_m7718EF25F30F553A7E515B72CCABC8600E2BC541 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t5ED9B8A459AF263F76A2EA4C2DFADFAE65F5A238 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperation_2_RefreshDownloadStatus_mEA6BE1876BD4BFCBC09374115697932EDE9C03C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_IsDone_mE566894C69351CA2AD7DB8B313EA396E0A37C443 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_IsDone_m333DC14711DF7A1F576EFE3B1828D1AC8B3DC2F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_WaitForCompletion_mF4A524F5BCAF1882A4D96FF020BD874EF9DE69B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObjectDependency_tF0C3A64B8C24EB2695EA6E8DE02B23E0188B30EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tCBD239557E1789792A562A0476C0934C9C3902EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeExecute_mD4C280ABFC4A0DBD201400B82BF3EF574852B195 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t0C965C82FAFAD4ACFE5443145EFFF866FBBBEE2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_IsValid_m15CEAE43BD0E32E28EBAE1B08E7B0FEFA13BDE02 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t0C965C82FAFAD4ACFE5443145EFFF866FBBBEE2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_IsDone_m3D8EE79BC093705197CBD2C3A1ED9A33977FB27C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_WaitForCompletion_m3FE2F6150516CCCB192DF9DFE821DF33757D56E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tE82607FCAA061F6E4BDDA1FD6CA94B1EDD406FA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m8BB85C55B0CD830EE1182401514E19EE70E77014 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_add_Completed_mCE430428937C5D9DDA0298D8E9DE5187CDD782F8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_Status_m5C44E5888103DA8B6A6A34E4C5139E9D7BBA9DC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_OperationException_m4D73E8B6B02720A570FCDAA4DABA7FCC0159D4E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_Result_m23971F8425A959ED4DE06DC754ECA3521FA52235 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_m043C793E65DACDFDF46C4D38297237DB21B15305 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Release_m8FD51EFCDA620F157F633342F66E71A223D6285D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Release_mD816A1BA7E7A437154453BC450A0C33426329EFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_m63BE661C5F1A1B113C084EC8DD950B7D16F54DC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_m6C172E69BFB1A540B74F8A4572C1B2C58AC17E5F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m7FA95BA0A75B5DE9FD711B9FDFFE07F3575ACA71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_PercentComplete_mFE8E910BA1401B8D8A41401F0F6072058BE683D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_PercentComplete_mE2EF24F4E870C01F957668F07260947A02E61689 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_t7A733063473B1D02B7DDDBEA2B6EE72CC21F5169 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tCCDE1A0D921C6CB2FE1536035308EA3E977BCB98 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1__ctor_m57ADBCCFCD35309A9925E5CB7DD0B8F48D86641A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tC7BF2B64CF0F1558E1A92497B1E3301E9AE73DDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_OnWrappedCompleted_m7A7B93C48FA00DB8AEA80C50A85C48816B6AADAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t68F88BF3C883C2A44D2C987D3632B1E24D6C90A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mA36C46CA57E212337A5C683B62E9970C49BFEC8C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_t7CA28BCA8A6359251650157A2BA2821F70C89052 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t24AD25288E5E446B8ED9A5E17F39B872DE43D48D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_RefreshDownloadStatus_m5862B90FEDBE3B05A6650E86CDBB3006AEF985E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_IsDone_mA0F5C41B481F47207440E82C3A3F4FA7602A3430 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tC7BF2B64CF0F1558E1A92497B1E3301E9AE73DDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeExecute_m3DD89E3DBEC668F077DD9DB7F863DD1CEF1E2DA1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_IsValid_mD2E1DCAA0E31191CDCF17174C1D33A58803DEEFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tCCDE1A0D921C6CB2FE1536035308EA3E977BCB98 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_IsDone_mBC9AF4E671C8AE625CD636217FA4D171EA66162C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_WaitForCompletion_m5A9BE7BF3758659D22A9D84789D5A1967A4FB892 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t7CA28BCA8A6359251650157A2BA2821F70C89052 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_Result_m914651D704375BC7649B9D15523ABC1843C0E77A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m73C534FC8E8AB573B23F64E0AAFD5D18EEF78F1E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_add_Completed_m0F7B95A81E60292D349ADE5932BADB92C3C522B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_Status_mDCD87B5AD4ABBB87448438715FEDCF9B65E33905 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_OperationException_mF0D59293332D94545DE14DC8E6083E0295B90B45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_Result_m81F830789B0E9A079AEBD288D1C669A4E0FD31BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_mFA2BF4ADD7D727F9970414320C543ECE941A21A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Release_m561D5D387F517935AFB1681952078AE8A7BE3B12 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_mC514213F59AF9354F3A357779E9C63E38756ACF4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m839190CC330711CD0064D543134497E60EFFAD54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_PercentComplete_mFC0179902178E9F926C64625C97330C130D3F955 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_t0DB79463313463F84143DD41AA3896CFCB9461C7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_Convert_TisTObject_t0DB79463313463F84143DD41AA3896CFCB9461C7_m027E6962BB6D655C264147162FB1D3BA852115CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t2F60CF7B4778D8493F809CE8737C0640832DB3B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tF6EDF9620CED9505639E046EB7152ADD6DCED22B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Start_mD3F7CCD9E0C40995CBDC9F578E3B55A862AA8652 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Handle_m1388212937C7C5FACD70A64D5D63EEF56B6F717B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tE5DC4D99863768FD1CA195B2F119B3EF6F8E5E17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7C4E2B554CAD23D123603C790FFD75AA9C4D517D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t7C4E2B554CAD23D123603C790FFD75AA9C4D517D_IAsyncOperation_set_OnDestroy_m5316C8EE26184140AD83413E4C69DA24034F0418 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t992E6FEE1335A68F241B1A33F9D35B95C3844006 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperationInternal_TisTObject_t992E6FEE1335A68F241B1A33F9D35B95C3844006_m4E07F4FD4FFCC693179DA17301908D34FDCC13BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t8D77596C390A8018C25FD52336D2EA9992636C21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t2B79AED0959E5A02AE75E8B0C555F7056D0587ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperationInternal_TisTObject_t2B79AED0959E5A02AE75E8B0C555F7056D0587ED_m26A6635E7E11AA5E52CFC0539F88ADB0F5ADD8F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t4F8C1386A767DAA6C2408EA39D038B50A9929838 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_CompletedOperation_1_t1DBB4D614B1196561880D144F742E1587A0BAC3F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateOperation_TisCompletedOperation_1_t1DBB4D614B1196561880D144F742E1587A0BAC3F_m0F1B10189B8E4A89BE6643A43E6F048E48594033 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CompletedOperation_1_t1DBB4D614B1196561880D144F742E1587A0BAC3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t290C6B7E779F49684D0018AD0CD1311B6FFC4995 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CompletedOperation_1_Init_m60C81A41D6E566EEF431F106CE4CCA747043D5B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_StartOperation_TisTObject_t290C6B7E779F49684D0018AD0CD1311B6FFC4995_m24655CAF383D0A3B038B9270A6DAEE1EEFB63BB9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tA67472BBB0D968B6F8F072F58E35AD7689230543 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tDF7262A7F0BB1DECB842B92FA779F46DE71989D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t865EEC0199DEE6FCF5552F991D774B5B9C63E881 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Acquire_mA4054C8EA2052EB5FD171949B9DFC230FCCC1BA7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t865EEC0199DEE6FCF5552F991D774B5B9C63E881 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_ProvideResource_TisT_tAFF681A56D59520AAEF2EF0F525D7D355CC64BA5_m3C464DC8FC6E342842B851C208DA86C04B001CEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tD93F61116A070CE89EE8D21A552903AA52EFBC28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m4A17DEF5D68E10740DD83C2513B6CDD30625E85A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tD93F61116A070CE89EE8D21A552903AA52EFBC28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_ProvideResource_TisT_t3597071CDEAE87296CF33851BAB1E1B1C487045D_mCE91274D2D0E7F79982B71BAE1496FF04459F501 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t2CA4B2BB5EFDACF6DF41E44E2E710364EDAD9D2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m446F5C1952026C20CED95CD3FA486A06D3B085EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t2CA4B2BB5EFDACF6DF41E44E2E710364EDAD9D2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tE330636ED01128D2EB83E50F43B25FDDF61F35DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_ProvideResources_TisTObject_t94272D3461C89EA47E5977FBF0C3C2E095542FB6_mADF05A87B814C28E9A0D8C5FD67305C2034D7AC6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t105CE8E5261F3BDC0277AC57AE095510AADDF3B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass100_0_1_tC318841598D48EB05586495DEEB001FF58ADBD6E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass100_0_1__ctor_mEEFBA75D585E8A278101423E6C93D943EACB2CEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t0498669D4E673877E2A7BC36ECCF7625A34789C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperation_TisIList_1_t7A6F23754486AB40BA57ACEC3E4851F8C5200528_mC16FF65274C21CFF259A76E5829E12D5D6E6F631 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t7A6F23754486AB40BA57ACEC3E4851F8C5200528 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tC9885F24B78FACC9FCAC56802D899E74AF1001AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass100_0_1_U3CProvideResourcesU3Eb__0_m707F7C1FDB449CF51E209424B35AFF28483AB42C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_tD4A80BD04976BA5C2F60556706BE9D4DB2B99DA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass100_0_1_U3CProvideResourcesU3Eb__1_m278366EF89B884E43F6ED3E7D4089F46EB5B671F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t30EE50C56B893B99FD194EC9973FDD012E1FFE49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m867E2668B925904874456B430AEB00176075031D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateChainOperation_TisIList_1_t7A6F23754486AB40BA57ACEC3E4851F8C5200528_m8A20CF0A8B5761C11C7E9B023F90167124B6D375 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ChainOperation_2_tF0C26696B20C501EB0B9D7FDC959873622074E7E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateOperation_TisChainOperation_2_tF0C26696B20C501EB0B9D7FDC959873622074E7E_mDBE918F0E395B7BBA465FA403785DEE0AD9E2132 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChainOperation_2_tF0C26696B20C501EB0B9D7FDC959873622074E7E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tB0807D2FD43D11CEB2AD4198ECDECEA7134CEF64 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t0E0C2D9BE53E5D6C93CE09904E64F54371FBD540 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperation_2_Init_m753B7BCF358FF4FBC7D32F21A489BA0205869C74 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m23863138DD7724D3FD69532B97C6DD1A863C69A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tB0807D2FD43D11CEB2AD4198ECDECEA7134CEF64 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_StartOperation_TisTObject_tF51A868E5791A3793FAD93C805C75B723E01E715_m12A03400F057025CC783D2910AA8A5BDF5B1FC1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tD6FF0F3EC655C2C593450B54E6C4AE591F3A655C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tF55A919845E0C432960E9C0C08FCA4FDB3F44FA9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_t50C7D4A1B16E0123CB757009E92AD7304C6F5834 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1__ctor_m3E5374BEDF58A3976B01503C36340723FA4BB04E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tF0C25950711DBF5848E5A5A3A2C02513311EA1F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_Init_m1A5450F30AFE4B446905DFF10E3948C909B0CB45 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_StartOperation_TisTObject_t51AA3FC1624EB7EFD81B811105C6030E51EC461C_m83E5F53BA17E076E9C3B72FD72107F2963A04D2F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t2EF886BB7E4BC1C6B9CDAD15E7CB99053FF58FD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t0EAE9F9726BF0E4380E6CC5E7C0EEED89DFE6FFB },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ChainOperation_2_t3EA92884B933F9162095667C756697F7AE51D9F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateOperation_TisChainOperation_2_t3EA92884B933F9162095667C756697F7AE51D9F0_m612626A76C46487ED56525C1236C76988D2352EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChainOperation_2_t3EA92884B933F9162095667C756697F7AE51D9F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tC2B48F1D49CCC7FD75F6BB39A50056E20D1E9DB7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t7585A998799217E1E7BC07FFF623A1D39754E825 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperation_2_Init_mA67CF51AA3BD5B26E1835E4E44FC9F1B4F6CCBAF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_mF56E6A218EC2C30EFE85EFC64201971BE6DCBC1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tC2B48F1D49CCC7FD75F6BB39A50056E20D1E9DB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_StartOperation_TisTObject_tF3D1B8D4EF4E9FC58BF76DE84F69EA5E675ADCB6_mDC4B2CEC671712413F74617D0AA970874B8DE24F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t0CD98AF621B617C4D938F2AD07EABDD95B7B7139 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tBE4D53A46F0C3DD6244C71A62A139A6C6620582E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_t504C17717E980CA2DC68893949C869AE9464C69B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1__ctor_mB21333B9DB3884FE545121CD4CF244988CDCE9EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t359D180D8C6A07D873F39C8140A19FED4C8B3F1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChainOperationTypelessDepedency_1_Init_mFE1D1221FF8FA865943374ACDE2974EFEEBDF0A3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_StartOperation_TisTObject_tE21784BBF51C6FBF5C4483AA38D33CDBE2A288BA_m161147C9F9B61F25257978E6024CC3FF9CFFFA4F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t139A193274471A55F9461A8739C8718E0B372F36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t293A78F3727C29D78723277FC2EB1505BE9DF338 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1__ctor_m183F657057B6F6FF6D80C905D225B83C2F475213 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t23034BD8A7348EBC352C95F8272D6C914B860DB7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t88BDF0FDCC2E0298D07A1C482380E8E359E46121 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CompletedOperation_1_Init_mBDCC78B9F8C5CB7D8A45D4187E43C917492236A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_Result_m6B0613B1B7D4089752198AAE5B5CD51BC5FBD5EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CompletedOperation_1_t9A7847AF845B9E400E4F57451497F1C4E4CB1455 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t23034BD8A7348EBC352C95F8272D6C914B860DB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeExecute_m3269ABAC6679921415BA6730C2434D6A12DE6B60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Result_m04AD74F2E9E190BC16945049DA7C963600DF7E71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_mB9B8FA825451F7911189A30BFDBBB36A168956AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass100_0_1_tF3D898C76F3063A3D77E15C27C7B618E123F9CD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t6B2D97B8AE6B04750ED8DABEB0D99422CA724281 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t9BD547383D54EBFA633A045608B1EFDF9A8CFA04 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mDED439A6EF174373C39805266C91FD63C1D25601 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t9C46952602EC22E4A01382A04403B360E88759D3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m89F08E4AB501C662D0A14A0963AE898043A61AC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_Convert_TisTObject_t9BD547383D54EBFA633A045608B1EFDF9A8CFA04_mC94990A2864A336F659D8F670D87328A75105368 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t3767F5E3DB2E13CA54373249AF07E53B4E6B53C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_Result_m8F278E51B2260F80BF6C662D911938154CC1F843 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t3767F5E3DB2E13CA54373249AF07E53B4E6B53C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m25029146E63368DE5FD7BD3DEB4935D9D8051978 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ResourceManager_CreateCompletedOperationInternal_TisIList_1_t69E16C8E448CE85289784F65490E35B5D77FDC5D_m5BDAAB1D0865CA47635B39184FF44B4DD28C4199 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_t69E16C8E448CE85289784F65490E35B5D77FDC5D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tC027D97DA2C188250ECB547719CFEFFC833B596E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t84270B34D85358555BE6D9FE519E6E94891165DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5C0F3E77CC912EF60F10E0446BEBC8179091A551 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2D2C4B6F57F03776F72FA00EB9E15D738A427657 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Reader_ReadObject_TisT_t821854D62406274345073E3DA287C24FA32A3599_mD1E1E71AE16F774466A1B2723284D5A33A449B2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t821854D62406274345073E3DA287C24FA32A3599 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2D2C4B6F57F03776F72FA00EB9E15D738A427657 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tECBAAE7384CBA557AB875DA1973DD604F24CC697 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tECBAAE7384CBA557AB875DA1973DD604F24CC697 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Reader_TryGetCachedValue_TisTU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165_m9A86761F22C0746EB96CA0279311FB9BBB1F6932 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t659DF357F65EADB177D680E7EA9DAC9163DBDE5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBD5AE8C7901D08A42121E477B8722A04FA6FC867 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t83393AD558ADE2E91DD54E4853012E517E44769F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE296BD1081E7871C5C095D9F0A5E91F693670165 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB93637C1A01BC379ECB036395CD377A25F97AFAF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tAD28673DFDA47E13D7EFCDBF8F3EC1BE20A2DCDF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tACD11A0F99313CB26961A8356C25BF6AC36FD4EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5C70F0C38E743DE890656C0A78E32A60FAFC8BBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tCF02DEB431F070C64C653B423DCD522E8BA61BE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tACD11A0F99313CB26961A8356C25BF6AC36FD4EE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDB8D1C44ADB32944EED2913050ED827CC69CF70F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5A5418496F5D9C826ABB7A4F0E22DC92FF609436 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA4F5C8530B7503B3E075A70A1741D3DE52289C0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t42DFE7909E22953C268147ACB7CC8409BA33E20D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5A5418496F5D9C826ABB7A4F0E22DC92FF609436 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6FE7FC22E2C0C40985040077400A9AC2F7747B35 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEF9A349F5CAC3CC260DAB237BEE7009D6C5DBB9E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tFB5954515E4E996318DD785056B07531E39CC315 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE75101A1EE401E1F7937BBBA8626F37D6538B3C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t01E2E1630DCE63EFA006A28D2C841947A26ACC33 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tFB5954515E4E996318DD785056B07531E39CC315 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t1967B37758833A74BB58C2467C45F213D8B21BF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF3FD38F4315E0498D98821D0182A65B47142E07B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t6EB7D4E7B7EB57960947D80D866035D1F07401AB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t1967B37758833A74BB58C2467C45F213D8B21BF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_tB38D197CB83EB9C43D9E73FC006AC5FC857C97C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Count_TisT_tBD875C9E85BC873AE55469D0FEA83B1DD206F8B6_m917188AF7FED3EDA3C9C1A3A0945E563FC60C708 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m74F480F07AFBAF2A90F4E30689EDD7E265AB654D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB286B61EBF9F6DAD30D34DCF490424BDED629A3B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_mFB1059B108D4D90302B44569C4572A7B8784F492 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBD875C9E85BC873AE55469D0FEA83B1DD206F8B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LRUCache_2_tDB05E2A0DAD885C4684D001DC1E7E252401E8C21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tF728F3C4E1022E1EECFFCF2072B8A919DD21C019 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m41F26ECBDCBAD8D50FD9E33E45F0B3C3F228DEBD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_tA7234D3660EB0DE19AA1F7AE3D9C63B11BA3C29D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m4C045574531053C3BD632186B33B7DD864907D7C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t6F50F3B04B6B23A6D5670F397E89BB003AB1F2B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_tABAE23E339360A1A9A9633D4C6C3C862DF9C8974 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Entry_tF69382A9A56AE0DA6BBFD465E67D06C92A137972 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddFirst_m9E37459D5EF5381A5C22EFF9C7CFB8F96E18999C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_tF2F7AD44984C3301E08AA513B45E3809FB54697D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_mC07AB9CEE85A1A9B11A831094C1B3342BA71EAE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_Last_m5A3C228E5976C5546B1B1EECD7AF2F894097D779 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_m60A337B99AE647A8ED3B136969EE747007478180 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_mD72AC03C82645A5D4FCF39CBEF8A21A92ECE9069 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_RemoveLast_mE9B96B97AD6682CE411A458D4813F43220F90B0D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_Count_m6E3C52329B587A295A7FE217876A65727630309C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m79D658748206B324C6261E09E6A2583C818D337B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EntryU26_t7E16B9FBA3310AC1F3F4099911A53B1C7F7C319A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_tF737931DBCD884C94F516439D5AD5145D3358A03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Previous_m9B167FDAAB7399F16B265112E716C00E6E6431BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_m4EFE2B96534913C8D641135335E6D9D1BF42C9E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddFirst_m9740259CD113CC000D14FC215AD95826B007E5F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Entry_t29BEACB44DD47A0502B9DB93139B3DE0E8E6381B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t619B3AF5A1A8B719CDE2B68582EC6D638BA87633 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TValue_t619B3AF5A1A8B719CDE2B68582EC6D638BA87633_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TValue_t619B3AF5A1A8B719CDE2B68582EC6D638BA87633_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ComponentSingleton_1_tCC75A31DCEFE68E3BF6FEC1371DAA246B48508B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t24E22872472DFEE87B691F1BD9B4E92C2F537060 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ComponentSingleton_1_tCC75A31DCEFE68E3BF6FEC1371DAA246B48508B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ComponentSingleton_1_FindInstance_m67A6F8E06521D2EB0EB2287AD35743E056397D22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ComponentSingleton_1_CreateNewSingleton_mC580FAD47A66BB73F759C08CEFD8DC51196887AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectOfType_TisT_t24E22872472DFEE87B691F1BD9B4E92C2F537060_m98C5C0EFED0DF258A19C826C1C8430D10D4EBE5D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t24E22872472DFEE87B691F1BD9B4E92C2F537060 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t24E22872472DFEE87B691F1BD9B4E92C2F537060_m251E03D3140935E976439034FC58A2B4CA505587 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ComponentSingleton_1_GetGameObjectName_m794FDF1DA94BE1D71E0662658490CE5F96B6F3C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ComponentSingleton_1_get_Exists_mE84A4C7EE5D5E0276613D2B0E3B58AC564992B6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ComponentSingleton_1_get_Instance_m64AB893BBAEA16AB4EDBEA9A7BF3268274334C5D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNodeCache_1_t3829C43319CD269B4B82E2EAD314F597CFB7FEE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t1271D9C1FB9AFE86B4ACD2E131ED477B8D5436FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_m5C815C2FCCC67C0606B4F42B86AFD0F46A5D4D75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t2BA8F5BC3F2FE544487F18B9619A20CEA0C309D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_RemoveFirst_mC60F2FC5C62165FFD3233FE2E5862029EF6762F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t45AC0849CDCBA23EFF219A328DDC39024D6C8E29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_set_Value_mB3E3EF662FE828B838A43ABAFC85531A445164C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1__ctor_m4B6756CBDAB9C4D57CC3780C11A59FDE4B441575 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m4F19967336D00152E2A15B1077C4AA8E8FC64A4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_m3516BB60DEB48100F0F822593744B8D8083B3683 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_Count_m50DE8908F6E0EC311777B8C263486933232D962E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_RemoveLast_m1D15814C1623585852BD1546F245BAB2D32F08A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_t063B2F67429F037AAB88F8A9FB4AC1DFC6DF4C17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNodeCache_1_tBC87877C4C116D2ED92BBF862E7DF0D18F09038A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GlobalLinkedListNodeCache_1_t063B2F67429F037AAB88F8A9FB4AC1DFC6DF4C17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNodeCache_1__ctor_mBB2EC56C707F4E7B8AB5BC88374A0DAE82903A9A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNodeCache_1_set_CachedNodeCount_mADB2AFC8832CF63D012A4FC75B4246889385EBC8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF4EC86031C24522A4A3A942153EE355BD556D1FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNodeCache_1_Acquire_mCCEF06B4306099613A41EF43A381D0731446A8B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t1D8497E0EDCC13635FB482C848536EC7E1AFDF67 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNodeCache_1_Release_m208DE838E7CD8E535679CF25A564091C1A093660 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tC1105AECAFFF7F16C1513FC5BDAA46B7C3927AA9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_tF17C5B95CAEE5536ED9BC475D6C8FB67D18769F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tF17C5B95CAEE5536ED9BC475D6C8FB67D18769F1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_tE32DA324E8F0BF9F11637592FB25C961ADCA1AC2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tE32DA324E8F0BF9F11637592FB25C961ADCA1AC2 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T1_t8A86DFD91DE6DC41EDB1B634CCF3260786E2F941 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T2_t20CA7FF7A31CA44D9763139F1CFAC9C87A8867B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTAdapter_t9EAC58637DA93C8720A9F745723789BEDA618AF7_m5336F2E07013F9B0AA97E086139D002053DA1FB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAdapter_t9EAC58637DA93C8720A9F745723789BEDA618AF7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEBE114FEA3901CFC5955E4C778788A5D2DBBC0F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_m1B1FE976661F3DF510CA73C858D0F34DB85CB330 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_mC0AAD6B292DE174CFC89660D36C51D9183508E1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_mD010590E760F0EAB5ADACE0C78ABF662A5430AA4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_Instantiate_TisTObject_t0877FA72FDBA70B5A73CB049D85AABC30B71DCCC_mE91CD03D52054E8985DF9A0D2E8CDED35BE9A01D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IGenericProviderOperation_GetDependency_TisTDepObject_t032A91F8C8D44534919E4B01824496D679F1F771_m6AEAA4D9E5F3FCD5B33E8F5E5A22A43C4A773636 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDepObject_t032A91F8C8D44534919E4B01824496D679F1F771 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3FB64C62813E68C5C368C363B6043E571C340C2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IGenericProviderOperation_ProviderCompleted_TisT_t3FB64C62813E68C5C368C363B6043E571C340C2B_mCEE75A0DD9E797F528ECBC64564A86CBE6E0747A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t629AF4B8DDB948B06613D567AA2F9BCFD05D013D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tE3CBD04F86F44F9E70B44A1B40F8A61A8A461688 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DelegateList_1_t1C8830934893CF4BBD7A1E5B14A120D782DC09F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1_get_Count_mCF97165B0F17059CB0D857EE2BF54967416B4395 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_UpdateCallback_m6E0B0854DFACEEFAA75C4731177D068E89A36BB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_U3C_ctorU3Eb__38_0_mFCFCB7D5119779A0430BC2C8F61DB0138F378EC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeWaitForCompletion_m58BD3906851849863D7D6B2A1564744F294C1EC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Handle_mF6373E086D7485243E131ED841B36210F6363D57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tFA133FA5812CE09EC9E01A8048B387432E421B29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m9982C7C5CA9CF5A371C1152B146EEB60113AE28F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tFA133FA5812CE09EC9E01A8048B387432E421B29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Destroy_m7B054374D7F2D8ADD67B6500AEBFFF6EAF7F7A86 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_Result_mE0E816F081243CB676EBAA23F08E9E2E072E1332 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskCompletionSource_1_t8A9FC4717ABE8019988A2761A03B6AAFB8C1307F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1__ctor_mACE7013F8FC4271C43DCEEF07C7AE61556C17B86 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_IsDone_m0D3BA85C8CB8528DD8729FAE298D2EE7F2FC7183 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_CompletedEventHasListeners_m55C160EF9C54B23CE0E6DF8124B49D2E3FE565C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Result_m088FB231E90661764204034B83B84F3653E42E37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_SetResult_mBFEAAE091FF23FDC59864F3CDD255CC75213782F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_get_Task_m1FD966E58B5D9A9EFEBD44E1F3E1ABB980E80A01 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Task_1_tB35927EAA854A33A147D7C01BA32B3BBDD625069 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1_CreateWithGlobalCache_m6F835979BD13643C4188A71B8823CF74875549AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DelegateList_1_t1C8830934893CF4BBD7A1E5B14A120D782DC09F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t2650D2B87DD9C0824F2C1207E157E1C61827230D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1_Add_m5C75550561CEE7BC35942F6ECE927A018BC81CA2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_RegisterForDeferredCallbackEvent_m8CC02AE9F658679AB712ECAEC175B46F00E4AE26 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1_Remove_mCBBE67A8A9B9F55C8D6F20800EF4012A10F18B47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass60_0_tDE6DAF31E7584FC244F4F2EF9E6DCD491EC945FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass60_0__ctor_mB8B843B8B4270F6A571613F27DEE685D962DD71E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass60_0_U3Cadd_CompletedTypelessU3Eb__0_m905EC1CEFF4C9B92280220734A268BAB93A91C61 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m69C6E9BEADFD89115B4CB69F697C8D46B176FE69 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_add_Completed_m497CCFD6F8A136EEF8A58741085D5B8E965938DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass61_0_t65DB736AC9C09279637FDF5100C67EA0A5829DB0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass61_0__ctor_m04A01F75A92565EB5B0D82CB1BC54B78886C3DF2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass61_0_U3Cremove_CompletedTypelessU3Eb__0_m020C4C490E90F2C6D413EFC07F00BC65296B8789 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_remove_Completed_m884D0D3E3015BB5C73A6112026AE65CCFC65A025 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Status_m3E1E71EF030F30725F733D568E6A6BE435CE583B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Progress_m6C55CECF99969A371DF8BD9B1F8A80B1ECCE1714 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1_Invoke_mA382F7E6131F788765D61D44BF3ED517BBC48A79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateList_1_Clear_m71093C86A3B951191D71E398290F882D06564CDA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_TrySetResult_m5B17C3C72B05F5EFB3F5B79F9D276FE602641F5F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1__ctor_m0C91AC51BC63BA5518717F6FAE63A160A5C7E2B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_m129FC83A9973FAE652E5ACD8E25178E76BB3103D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_mA83618A5686BB850B13FD99E5E79638FE72CF278 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_DebugName_m9412F620CF9717A47A2ABE1F555AE7D9F244617A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_OperationException_m97E4EAD3094F4DFE5FB14E8F91336EB608D4AFFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_ReleaseDependencies_mE28188AFCF9859C99C929738475A51EEA7A1508F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeCompletionEvent_m11C513489E21935B007F71A1F632110D0633F68E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_DecrementReferenceCount_m8ABDCAD40218A5A1CB3F05662D5F2F4F89776D0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_IsRunning_m9C1E401DC012241FD3295F2C6A3BF9D90E91E80C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_IncrementReferenceCount_mA58F707615ED0E5400DC52244CE84DB9945C26F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeExecute_mD27188CEFB526E09D0E3D3DDAC6591215619D907 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Execute_m6DD865D3670DA55EB4662BE0FC141F74920597C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_add_CompletedTypeless_m8BFD647FD99EB98C190B38E6AD2A7091F7C22B35 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_remove_CompletedTypeless_mC182602345A0280838E7813CE35E3BBA6EABBB1E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_add_Destroyed_mBE75C8208827A738C78889746B1424ACF22048DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_remove_Destroyed_m0948FF3B960CBFF021BAF3BE2202F93F4F244CF8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Version_mD559AA0B05492553A72D70BC1533A3A2C18449C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_ReferenceCount_mF2CD140CCF7F544DAEF14474ECACF4F4A09857A8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_PercentComplete_mF77322F5AFECC1ED2934FE4CA8310AE32B52AB09 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_OperationException_m6479D75258CB8ADB3E49F5E63594D9C001F0038A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_OnDestroy_m7E3F08EE27E387FF12EF948F3A4497FE583BE81B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_tE3CBD04F86F44F9E70B44A1B40F8A61A8A461688 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_GetDependencies_m16601590EC6445923D764917F2F0CB51F6EC3159 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Start_mA24D040D77E011B55BB836BB7CB38AE974CA6CFC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m4B1392072E614D979D87928088DDDB6AB317FA58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass60_0_tB9477614D3B3737FE292F8B443BE27C2C36DB96E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t48FEF83E169922192CA234535556633127410A87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_m70808B822DFB251CFF3A0437CA3854A00A5E07F1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t48FEF83E169922192CA234535556633127410A87 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass61_0_tE6F3D5088FF982BF6F68C8A8A30DE1654D5D42AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t751F6204C708E675406B4412829F1D5EC4687875 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_op_Implicit_mCF4FF0A129A0A00C57CFAFD4A0B11BF14C9A24A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t751F6204C708E675406B4412829F1D5EC4687875 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t5E74A3B08DA3FD6088E9C8F6F01E82D679710CEB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_tB3D75DB0BD350C2D25F09A960996B944DE407B6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Version_m80B61B95508A94793D7463BDA60E74E4EF63B209 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_InternalGetDownloadStatus_m5CE73A688B8E1D9437F62D1E68374310DECC11C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t5E74A3B08DA3FD6088E9C8F6F01E82D679710CEB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_InternalOp_m9135A6B80C43976000F8803932CD314B96321EC4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_IsDone_m6C1B49341697499A92324FF30D5EFDCCABD7D80F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_GetDownloadStatus_m41413B242A6BE6C8C72B2F217C3013ECED937DCA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_IncrementReferenceCount_mF28B3AAD3BD09ADA7E415F4EA28765CA11286618 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tBECC6DD59730920BB05A3E9151A4F551A20943AC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_add_Completed_mB1BEB91E5CF67359A584AF9017F9F2F29D4DAD08 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_remove_Completed_m18C383682251473CC86863FF723136F7DEE81951 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t93C54F810FADE1AC130F3CD2AD5A07AC53ACCDF6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t93C54F810FADE1AC130F3CD2AD5A07AC53ACCDF6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3CReleaseHandleOnCompletionU3Eb__20_0_m6379A1D59227871E77D294A958DB06E0A3E8C2E4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mF972C6B81392C79680988CF2374372365B655A53 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_add_Completed_m7EF99D1354427147040F82F2805F3EAA1E33BA77 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_add_CompletedTypeless_m4CF3EF1D654996B8F98EAE9A0333DBAF1F5461A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_remove_CompletedTypeless_m34DDFD98FAA60D64F7276917BC02C6201381F141 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_IsValid_mE7125A43455F56F6660A6C3261BB8AEA79FE5428 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_GetDependencies_m3E3500CD8110D7E874130FAC220F5C19A002A0A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_add_Destroyed_m4EAB48EDF29A056CBA9671FA8EC759B4509F10F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_remove_Destroyed_mF63CB4FD8D17B634DE33C21E4643C47A14281318 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_IsDone_mF45D0129C6406F51D5DED2A84632B80F521FBEF7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_WaitForCompletion_m763F60F24D08D14ADCCEF5B9B44FA2EC873047AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_get_Result_mB257D6A345E903F29145618379187DD8746FF5DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_t4E421E5A4CFD4C8D595C00A0DA8CA3CDED2ED175 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_OperationException_m09F1F2EDB7767D9EF00D256926584301A6203193 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_PercentComplete_m54DEDC07DE00F2C58426053E269E7B5A4FA14342 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_ReferenceCount_m9613584F99597F8176A9D35DDAEE8AEC0D767303 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_DecrementReferenceCount_mE0B0AC17068E804F8A54AD5223F7315E61D21A1C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Result_m9C646ED37EA0F6A2BA498629D4EC6867BE404744 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Status_m348651738F30F82B1E2121D9D59140B6FF893BC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Task_mA992693B1C2B3BA73C685A18A3F8990F55ACCB10 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Task_1_t1644483AEC968E3A2A2AAB1AF13AEBCB6FB15D38 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t8C7AEB7E19D398D60C4BA3B2479EC839396D2947 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_m331CAAE8D47AC27642F566D4D789B524A197F14A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t8C7AEB7E19D398D60C4BA3B2479EC839396D2947 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t3E98084D393DF28814B2A1D62C3F54459050B12B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1_Release_mEB5DF206799EBF339D16D1616B434EE5148AD31A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_t3E98084D393DF28814B2A1D62C3F54459050B12B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationHandle_1_tCAD63F4245F9AA03D4348901356B0A3828461624 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationHandle_1__ctor_m7464BD1B25B26FFDBF538003A60EB9CD82EBEE20 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ProviderOperation_1_t4D236987283AE317B692FA2C2BA1AA1C6A26A294 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_IsDone_mE4C97BB432FAE177C6204B50D62B1CF769B15D64 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t2144181075E702B37FA261A834EC963A8991F1BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_InvokeExecute_mEA53A28013995EDFEEB78CAF15924896B8729C26 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Status_mE36B14EAEAB6436B65184E9D8E474DCAF7EC1C4F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1__ctor_mF8C8FC6B3101AA07A664B56D3190391BEB8E2A16 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t2144181075E702B37FA261A834EC963A8991F1BC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TObject_tDC9E3BF6AF8A685EECA3DA99B40EE4249DCF8D89 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_ShortenPath_mFF05F08B7623220C8891FE0D9EA5FD64DB55A125 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tDC9E3BF6AF8A685EECA3DA99B40EE4249DCF8D89 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_Result_mDC7E0EB1D1A0F69FE362D5E191D87B4C8934BC2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_get_Result_m9469C982380BE7451EA3E090914DC104F6C3A565 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_m456A0CF9E48C7017A48BBE4D680964D505D0DEE9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_Complete_mE2C565D22E043F0F06187F1639F7F094D799A9A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ProviderOperation_1_ProviderCompleted_TisTObject_tDC9E3BF6AF8A685EECA3DA99B40EE4249DCF8D89_m76A148F8CBCEB51576497170806FCCFAEB4E2C2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ProviderOperation_1_WaitForCompletionHandler_m536242851C10D069797A83CB23983A68A933D08C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ProviderOperation_1_SetWaitForCompletionCallback_m4E498166C7ACCFB0F7F06108CBC6064AE72F5500 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDepObject_t0FB6D81E5FCCF9757C7829864BA1A690B6D8DD60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ProviderOperation_1_tA925151037A31C1266269AD012793F2692406CD0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t908D0CB71CCF4187CF10D8450C6ECF3D096095D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AsyncOperationBase_1_t271FC0CC2D450FBBC34E84B8EE513139BEE7EA62 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AsyncOperationBase_1_set_Result_mAC5383430D3EB7EE72534B1017C8DC3853217FD5 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t908D0CB71CCF4187CF10D8450C6ECF3D096095D6_Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t908D0CB71CCF4187CF10D8450C6ECF3D096095D6 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_ResourceManager_CodeGenModule;
const Il2CppCodeGenModule g_Unity_ResourceManager_CodeGenModule = 
{
	"Unity.ResourceManager.dll",
	892,
	s_methodPointers,
	91,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	58,
	s_rgctxIndices,
	495,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
