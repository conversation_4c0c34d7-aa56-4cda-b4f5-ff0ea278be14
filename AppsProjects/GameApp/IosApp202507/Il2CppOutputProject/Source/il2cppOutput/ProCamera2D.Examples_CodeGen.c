﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void RoomsEventsExample_StartedRoomTransition_mB4B5DF0B9F5DD9A8C2B04182DAC6EC2F41E96059 (void);
extern void RoomsEventsExample_FinishedRoomTransition_mD7442168C08B2B1E8C47074D2E88E617C4A025AC (void);
extern void RoomsEventsExample__ctor_mF1F0EB6594E59B82B766065AD81CDD0F286AC5C6 (void);
extern void PatrolWaypoint__ctor_m4346B8DEF361999BB13B6D7CEAAA2CA98B095604 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mED291E3179D552DAE229D554963CA65348EB7B68 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m2D993A6E821DDD613FD105724211AE0DF3CFCEEB (void);
extern void DollyZoomExample_OnGUI_mA72711315474DA4120F4B724CCFA2D7D1DFA8093 (void);
extern void DollyZoomExample__ctor_m78D040E25B0EF690CB3E04EEBE0585A8A6462E82 (void);
extern void ShakeExample_OnGUI_mA8F1CA91AA26DBAD6541D10FF8FFAE61812C6FCB (void);
extern void ShakeExample__ctor_mFF5F4C02B8CEC2BBB13CF12A0514ADF20E6437C9 (void);
extern void Bullet_Awake_m90EFE3F944600E559BAC59C1052C037E099F89BE (void);
extern void Bullet_OnEnable_m90C0777990E296671E2E6681FAB13276E4339622 (void);
extern void Bullet_Update_m8D3E684DC99ED194BE13E5EA49BFC94803B91F2F (void);
extern void Bullet_Collide_m6E81F876F22642A9655243F40B79445F404F7378 (void);
extern void Bullet_Disable_mD396EDE0986A2FADE0AE307FD5180825D2DAFD6E (void);
extern void Bullet__ctor_mD35FE001CE7989D4DABAAF677152980B5FCDF425 (void);
extern void Door_get_IsOpen_m3E6B5405808EA7EDFE85AFBD191419C7F1E98ABD (void);
extern void Door_Awake_m41F8DB1897D3C52BE7B0A45A6E4BEF3D172DFE78 (void);
extern void Door_OpenDoor_m2ED0EC299966E5A5DB499414EF8CCD5D2FB5286E (void);
extern void Door_CloseDoor_mACA48C2055FF840E1323FD838A26AEAC3A76AD5F (void);
extern void Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91 (void);
extern void Door_MoveRoutine_m6F58F24265CBB412BAB13B2AFC2F185077E0E274 (void);
extern void Door__ctor_mD0A0A61EA2319044DCC266542E718EF863EF6CC6 (void);
extern void U3CMoveRoutineU3Ed__14__ctor_m607707C035642A19E1DB9DC86F282FA23C0B54C3 (void);
extern void U3CMoveRoutineU3Ed__14_System_IDisposable_Dispose_m043EA3A98F1E54AF3AC73B27BEC4DA9095319A00 (void);
extern void U3CMoveRoutineU3Ed__14_MoveNext_m39F20AEE4BB6211FCBFFE4A7B450B028FAFFABF8 (void);
extern void U3CMoveRoutineU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBCC5B169B9498C34846F6CBEB7564EDB6493A66B (void);
extern void U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m83038F31C79F09ADC8371A69D36E3CD9EA9FBDBC (void);
extern void U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_get_Current_m8B9300B5A036631DE6796995FCC857C6209C34C6 (void);
extern void DoorKey_OnTriggerEnter_mFF97DB2F123A569B03CAEBA82681D29D6D084BC2 (void);
extern void DoorKey__ctor_m06CBB092E887F34E310CD07241548378C4C82A84 (void);
extern void EnemyAttack_Awake_m11C309606A16AFDFB551F13AF341DE48F6131A0F (void);
extern void EnemyAttack_Attack_mC8175509964CC1FDECB2489E43ABB9942BE4E041 (void);
extern void EnemyAttack_StopAttack_m06D74602CE6B55BCBE6D1E61794AEC0D1D1A9F31 (void);
extern void EnemyAttack_LookAtTarget_mF7E1575BAE9820A7EDEF1E877FFE48C6B7B34817 (void);
extern void EnemyAttack_FollowTarget_m93D435D7A86AAC69D7F588E7B8CC45A3B34324B4 (void);
extern void EnemyAttack_Fire_m67DACC353A9273C61DFBBBB66B8AEC6148A1800D (void);
extern void EnemyAttack_RandomOnUnitCircle2_m**************************************** (void);
extern void EnemyAttack__ctor_mCD4691EDE8565527169EFDDA781859825CDA8199 (void);
extern void U3CFireU3Ed__14__ctor_mF974A778358856524656CBD0DE30B0DF026A72FB (void);
extern void U3CFireU3Ed__14_System_IDisposable_Dispose_mE3801C079CD2919B97406B77DAAE1C0052250CE0 (void);
extern void U3CFireU3Ed__14_MoveNext_mA6F8D319F38027A3D275693D2F44DFCE16FFEE7A (void);
extern void U3CFireU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEF3CD6F581D7BA5C693DBE52A7D1AF9D2E1890F9 (void);
extern void U3CFireU3Ed__14_System_Collections_IEnumerator_Reset_mAAE10DF1D0AD7E34390EA24E0B24C2284739132C (void);
extern void U3CFireU3Ed__14_System_Collections_IEnumerator_get_Current_m527DE4601E490AE577C1491EBD29F4A99D6D902A (void);
extern void U3CFollowTargetU3Ed__13__ctor_m0EA79959FB6E07AEF9E7A9FA804EF113BA68D2A4 (void);
extern void U3CFollowTargetU3Ed__13_System_IDisposable_Dispose_m0E36F547E951B227EF000EF15726A0F0B62B78AA (void);
extern void U3CFollowTargetU3Ed__13_MoveNext_m0666111C44C021A7DA1C2B902DFCF99D4559706B (void);
extern void U3CFollowTargetU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA992CE3AB889CBC006D539BCB6C9B4409BA66CE4 (void);
extern void U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_Reset_m298311DFA1E697CC6529B47667B96F65D1C91BFF (void);
extern void U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_get_Current_m2FAEE37FEA5CB5340328103FEE4B2D63C339FC7D (void);
extern void U3CLookAtTargetU3Ed__12__ctor_mE673E3FF67DBAE553F5C2C89180EB7BB25C97E04 (void);
extern void U3CLookAtTargetU3Ed__12_System_IDisposable_Dispose_m8C2F6EBF4D50429EA0CBFF9E7BDC6EE6DFBE2A90 (void);
extern void U3CLookAtTargetU3Ed__12_MoveNext_m135683B9F0C5D17F3B9A2EB79E6F72B9DB65D5EC (void);
extern void U3CLookAtTargetU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0F14DFD66B82EAA6C2D3E231A176F9574DA74156 (void);
extern void U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_Reset_m07A15C1319B75C4CCC440FCC4C0B203C523BF341 (void);
extern void U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_get_Current_m36B6379AF74D5A3B846A9667E6A8F991D52F2BA0 (void);
extern void EnemyFSM_Awake_m0514E35DDA932033623F72BF29E519BE06AB125F (void);
extern void EnemyFSM_Start_m7CBEBC35A2703910AD84DBB006FADB1DBCF9A19B (void);
extern void EnemyFSM_OnDestroy_m48A5BE63191B9091C47AAF1B02D92F9A2802C556 (void);
extern void EnemyFSM_Hit_mC7B0EFEDCB7593E3ED9D9346A26675ABEF718F19 (void);
extern void EnemyFSM_HitAnim_m2E5E2A327FA865C218278AED5EBE2642611898DC (void);
extern void EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161 (void);
extern void EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57 (void);
extern void EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8 (void);
extern void EnemyFSM_DropLoot_m430F27F92C046E4BC274B64F0A2DE062CD1E631A (void);
extern void EnemyFSM_Die_m71FBAD0CCFF374720C3A00DD8B2A9BEC447ADB5D (void);
extern void EnemyFSM__ctor_m3E733A6E1B13A41DCAEC2C6195B4CB6665DB67CE (void);
extern void U3CHitAnimU3Ed__13__ctor_m44EAC43C99235EE91342314684E606005E438330 (void);
extern void U3CHitAnimU3Ed__13_System_IDisposable_Dispose_m8B0AAE54D2EB6F782CE68D60CAA62ED5848EB0FC (void);
extern void U3CHitAnimU3Ed__13_MoveNext_mB6CFBCC1AED03B4D5ECCBD32A9657817153B5D65 (void);
extern void U3CHitAnimU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB2E175D2290FEF4EBB8508BBD07E09857285BF6F (void);
extern void U3CHitAnimU3Ed__13_System_Collections_IEnumerator_Reset_mC293E5D1F71063700FC867EC86C325635844BC88 (void);
extern void U3CHitAnimU3Ed__13_System_Collections_IEnumerator_get_Current_m681FDD9D07ACD1C96BBBD43A8279632591303FED (void);
extern void EnemyPatrol_Awake_mDB0A16ADCF61BF6978DB3A7F721D5A9EE04CF2B6 (void);
extern void EnemyPatrol_Update_m70910553F4F7228F3625371F1448846507CD8E84 (void);
extern void EnemyPatrol_StartPatrol_mD0558D3C453D136B64FE856BAB40518D47240A68 (void);
extern void EnemyPatrol_PausePatrol_m63C754BC41C88234EA100ED242875DFC8F145A7F (void);
extern void EnemyPatrol_ResumePatrol_mA6030EE1A672F3A8BAE00100973483705845A3C3 (void);
extern void EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96 (void);
extern void EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4 (void);
extern void EnemyPatrol__ctor_m623FEFA07A17946CE2E68DD6A9A5FEB0CFFE4A38 (void);
extern void EnemySight_Awake_mF30310C2F76237E2F257E52FC922043D3B3A3A64 (void);
extern void EnemySight_Start_mEFBF736E90D4325211846E30933BFA1948FD2482 (void);
extern void EnemySight__ctor_m992A9356FD2E86D59753BBDEE9ADC134E4B76E88 (void);
extern void U3CStartU3Ed__10__ctor_m05C20E28E3D83795CEF6B8F439B82FC39BC25D83 (void);
extern void U3CStartU3Ed__10_System_IDisposable_Dispose_mC938CE609305244877A6357A56CC271AE57A9137 (void);
extern void U3CStartU3Ed__10_MoveNext_mEB568DFE8985DBF6D401574AC5BA20AB1EAA9440 (void);
extern void U3CStartU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m059C8C17FB2C78D3F39C0D5F51B8FC9C1A90BFBC (void);
extern void U3CStartU3Ed__10_System_Collections_IEnumerator_Reset_mDFE86914DF5B0391F9DE7A36C764BBFEEAAEE343 (void);
extern void U3CStartU3Ed__10_System_Collections_IEnumerator_get_Current_mD9E5DAB3B5419BC16047DCF96A05022875686660 (void);
extern void EnemyWander_Awake_m3E1BC61CC3C36C7879B4D0C4F6C57F927C2F7E2C (void);
extern void EnemyWander_StartWandering_m2FFB17766D4C97045A8E29AB89C9276A5C7C35F5 (void);
extern void EnemyWander_StopWandering_mACE0DF86A60D0743E3B5E09CF35B917B9F96BAAA (void);
extern void EnemyWander_CheckAgentPosition_m7BFEDDFC1A658844F1B307FD28EECFA19B43CD71 (void);
extern void EnemyWander_GoToWaypoint_mC46CF5C24F723424F8878E3A417A6DBE78AAAD15 (void);
extern void EnemyWander__ctor_mD8B13D46CF1A8D22BA85FE5C061BCA7D5AF6DA56 (void);
extern void U3CCheckAgentPositionU3Ed__10__ctor_mD1E04080E9A832ECEE6A50A4D142844CE11B33C9 (void);
extern void U3CCheckAgentPositionU3Ed__10_System_IDisposable_Dispose_m5079AFD4C832B5D40E85A1757DB8D4505198CA43 (void);
extern void U3CCheckAgentPositionU3Ed__10_MoveNext_m1FBDE9F9BBDBC9983EF86367633488A9B44AFA4B (void);
extern void U3CCheckAgentPositionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0CDF703AA428A431258E05DE289FC7A4AD8E923 (void);
extern void U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_Reset_m055746713979B4B4C1F920905D84B3C0D4F55C9B (void);
extern void U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_get_Current_mD02DE6AC35BC9D6F84B83BAB74CB2BD9E4B342CD (void);
extern void GameOver_Awake_mE8746FFD083A7B470F2CB93113A0A8605162DD17 (void);
extern void GameOver_ShowScreen_m91A0DCC3CA95E7AC8C9895D6B7DF9E0BF7B480D4 (void);
extern void GameOver_PlayAgain_m5130E860511E1D3A53FDE8DEC65340AD43BE0656 (void);
extern void GameOver__ctor_m1FBF6EDFA0F5310848A79816D6A01AF5A6D6CA1D (void);
extern void Goal_OnTriggerEnter_m397A4D3BEE326F3C16B4581EFADF7FD0E27119A8 (void);
extern void Goal__ctor_mF0029EB41C7DB255447EF0321DF92A006F5A9D4F (void);
extern void ObjectMove_Awake_m3A0F42D78BE8F9DA31716FB3F072CDCB790D82FC (void);
extern void ObjectMove_LateUpdate_m2C898044689A3751D189011C66220447100641BE (void);
extern void ObjectMove__ctor_m8DF486D96BAB3438CAA7FC6CC50F46EE0D0FB1A0 (void);
extern void ObjectRotate_Awake_mEF1E2172BD8C35E5B2A94B6B7E6578FC368A709B (void);
extern void ObjectRotate_LateUpdate_m83C084CEAC89133F7431DD76D960F14404FBF61A (void);
extern void ObjectRotate__ctor_m3B44A3B585DF7089576EA3FFCF29DD25C8E640CB (void);
extern void PlayerFire_Awake_m948F53E4F2DCCB95C92DF1BF1D3F3533E5112B54 (void);
extern void PlayerFire_Update_m561CAFB0D567A65E4A85A252CBC735D191F9B2E7 (void);
extern void PlayerFire_Fire_mC9C434664104BFAF935929AA4133DC30D6117C20 (void);
extern void PlayerFire__ctor_m471056A5E1EB8DAE8A1083B097032619350B2974 (void);
extern void U3CFireU3Ed__8__ctor_m1D08ACF93FC08FAEEE86A325184A3B83189EAD93 (void);
extern void U3CFireU3Ed__8_System_IDisposable_Dispose_mC0B77D9A4BA0100468D66FD603456DD5AA21BE41 (void);
extern void U3CFireU3Ed__8_MoveNext_m53B9D3C88B05B1D2A80AE6708387D2413E3E90CC (void);
extern void U3CFireU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m31DAE23A4BB45B6A91D9CDFF9BC1CD4BF35EFC52 (void);
extern void U3CFireU3Ed__8_System_Collections_IEnumerator_Reset_mA16B82A9DF02A5BBB3009AA868FFB6175BDBDBED (void);
extern void U3CFireU3Ed__8_System_Collections_IEnumerator_get_Current_m9D956562E85667ABD8C2D44CC91C10F5F1AEBF1C (void);
extern void PlayerHealth_Awake_m4EF1CCD86D25B865858E6C9E1255073FFA897BBF (void);
extern void PlayerHealth_Hit_m2E72191B49697C1ABFA9050F7202D5F3D5B02543 (void);
extern void PlayerHealth_HitAnim_m502FE49C7A0F44B646B2D62B886D220235D48EE1 (void);
extern void PlayerHealth__ctor_m8B821C450A0FFF4BFEB2823CE37F8A895456C8DA (void);
extern void U3CHitAnimU3Ed__5__ctor_m7E9DD4E9433C185C734A9D36477091BCA69D2192 (void);
extern void U3CHitAnimU3Ed__5_System_IDisposable_Dispose_m3009F9D3448A7899D6D03FAFB05F2D3A919B5C55 (void);
extern void U3CHitAnimU3Ed__5_MoveNext_m4B4F74EB9BF2AA3323CD7F6542118207E1D11634 (void);
extern void U3CHitAnimU3Ed__5_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m888E818BD1863568CBF8D43974C209464612D7D3 (void);
extern void U3CHitAnimU3Ed__5_System_Collections_IEnumerator_Reset_m21C0332FD0DD74A1C22BCD2F196B9EAE65CB047A (void);
extern void U3CHitAnimU3Ed__5_System_Collections_IEnumerator_get_Current_m60C2346A67FF5DEF4E21787E195108E09F03445B (void);
extern void PlayerInput_Start_mD0291A721D4AD067A4814D9033E030A602BCC180 (void);
extern void PlayerInput_Update_m62F19B56CFB86CA0ACF41F97793395F56D855AC5 (void);
extern void PlayerInput_IncrementTowards_mD355CF9B9D9CDBC87C7C1DF45A2C7D3BE306C349 (void);
extern void PlayerInput__ctor_m6B9027968AA8D70AFEEAFB6F03F8D79BC420286B (void);
extern void PlayerInput_U3CStartU3Eb__8_0_m76653BA2097A5C65BA097230660F8D321A4076EC (void);
extern void PlayerInput_U3CStartU3Eb__8_1_mCAAC9ACD09B0DB47A5E94AA1F4B1E0698584CF65 (void);
extern void Pool_get_nextThing_mBB707503DABD3A940FA5D9E841E12C7CA3C3A0F1 (void);
extern void Pool_set_nextThing_m85C1B38EC0E3F26779600AAB56ABAA3C592033CA (void);
extern void Pool__ctor_mEDE0372E7C92CB10BEF27EBCB54F74BD76987D7B (void);
extern void PoolMember_OnDisable_m97FBA0D7B7BA4C015BB8A5041EB572CFD3EF45FA (void);
extern void PoolMember__ctor_m7070035AA0BE5D19A5C2B7E664F8BF888306AD98 (void);
extern void RotateTowardsMouse_Awake_mA64A3A4C9057CA30CD3F50B4ADC5B58A82F0EF82 (void);
extern void RotateTowardsMouse_Update_mE51D6F4E0E8CB2B9536825426096CB06431B8B18 (void);
extern void RotateTowardsMouse__ctor_mC2822F31F15D5DB1DDF42AC2F64EE5B3CB838933 (void);
extern void SwitchCameraProjection_Awake_m4B57A739BA2FF19BD5E2285F39E959DE44E374C1 (void);
extern void SwitchCameraProjection_OnGUI_mF03DD99C8C0D379BC2A9EA253DE827F1672966E7 (void);
extern void SwitchCameraProjection_Switch_m0A36CA85AC6C211D8CED6B7FFF760C07BEF90953 (void);
extern void SwitchCameraProjection__ctor_m4621DC1A9C8DCBE5CE8DA4B6A6D630A07CD89A97 (void);
extern void PlayerInput_Start_mA030D60FD01D12C3C5B3D9A9613EBBD659DFA4BE (void);
extern void PlayerInput_Update_m3519692162C77C08ECC1EDB53A68D94A8D890A6A (void);
extern void PlayerInput_IncrementTowards_mD28E8347BA822363232A5157709A19E5E15EE43A (void);
extern void PlayerInput__ctor_m7B90EFECE5655E3E732BBA4F86708565AEBB0385 (void);
extern void PlayerInputBot_Start_mB9848527D69090273F7F40462DC2DA8B66DE9178 (void);
extern void PlayerInputBot_RandomInputJump_m088DD669939E0E38E25CBAA1FFBB40CD797B9D85 (void);
extern void PlayerInputBot_RandomInputSpeed_mBD5BE6DB4021C7F69A581CB6B02087AA5ABCF2D6 (void);
extern void PlayerInputBot_Update_m31C836C012EC83E4E61D6EF9165A4FC717A62949 (void);
extern void PlayerInputBot_IncrementTowards_m03CD9814F330F30FE7D763E8B679BD7F52777629 (void);
extern void PlayerInputBot__ctor_m9C0D7B64314D75EECB98C462DD5C31F9A60FD6B5 (void);
extern void U3CRandomInputJumpU3Ed__13__ctor_mB8DCD6A6EDB7F831EF765A45D6678A392D5EFFA5 (void);
extern void U3CRandomInputJumpU3Ed__13_System_IDisposable_Dispose_m7C5B6E60F836226EA8CA408D093C668A84A217C9 (void);
extern void U3CRandomInputJumpU3Ed__13_MoveNext_mCF805A089DA0A13695DF8FC54B77ED1A610EF7D5 (void);
extern void U3CRandomInputJumpU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBB657D69CEB99A139B9E89184AF916896EF94808 (void);
extern void U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_Reset_m49E9633C06623C867A599BEC40859603D93CBE8C (void);
extern void U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_get_Current_mE9B95957B4F2D96650FF229E4567F3F16119BA1D (void);
extern void U3CRandomInputSpeedU3Ed__14__ctor_mF9403523C795C886A933D9B8DF002059891CA68A (void);
extern void U3CRandomInputSpeedU3Ed__14_System_IDisposable_Dispose_m9BC5DBA756ABEE6955B06C1FB02BD54F4EA2100B (void);
extern void U3CRandomInputSpeedU3Ed__14_MoveNext_mB2221D545B780984750471F989349D01D73B0079 (void);
extern void U3CRandomInputSpeedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43BA88FB08DFAD375D69D7F9558FC26AAC9181AC (void);
extern void U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_Reset_m5C882BB510DBCD5495FF38F35446C2A45EAA96C1 (void);
extern void U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_get_Current_m4AF1BF5A3C32967740C7385D352302258BE058D7 (void);
extern void ToggleCinematics_OnGUI_m2051D42958ED9A0B8B9A98DD8926502A4789CBF2 (void);
extern void ToggleCinematics__ctor_mA06EF3C0F3591B5E13DE3534B911448054F41C4C (void);
extern void ToggleTransitionsFX_OnGUI_mC99F88436FC1AFE5B11791C517C2B16927EA427B (void);
extern void ToggleTransitionsFX__ctor_m741E30759E3313807DAE5011C96215E44B82964A (void);
static Il2CppMethodPointer s_methodPointers[179] = 
{
	RoomsEventsExample_StartedRoomTransition_mB4B5DF0B9F5DD9A8C2B04182DAC6EC2F41E96059,
	RoomsEventsExample_FinishedRoomTransition_mD7442168C08B2B1E8C47074D2E88E617C4A025AC,
	RoomsEventsExample__ctor_mF1F0EB6594E59B82B766065AD81CDD0F286AC5C6,
	PatrolWaypoint__ctor_m4346B8DEF361999BB13B6D7CEAAA2CA98B095604,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mED291E3179D552DAE229D554963CA65348EB7B68,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m2D993A6E821DDD613FD105724211AE0DF3CFCEEB,
	DollyZoomExample_OnGUI_mA72711315474DA4120F4B724CCFA2D7D1DFA8093,
	DollyZoomExample__ctor_m78D040E25B0EF690CB3E04EEBE0585A8A6462E82,
	ShakeExample_OnGUI_mA8F1CA91AA26DBAD6541D10FF8FFAE61812C6FCB,
	ShakeExample__ctor_mFF5F4C02B8CEC2BBB13CF12A0514ADF20E6437C9,
	Bullet_Awake_m90EFE3F944600E559BAC59C1052C037E099F89BE,
	Bullet_OnEnable_m90C0777990E296671E2E6681FAB13276E4339622,
	Bullet_Update_m8D3E684DC99ED194BE13E5EA49BFC94803B91F2F,
	Bullet_Collide_m6E81F876F22642A9655243F40B79445F404F7378,
	Bullet_Disable_mD396EDE0986A2FADE0AE307FD5180825D2DAFD6E,
	Bullet__ctor_mD35FE001CE7989D4DABAAF677152980B5FCDF425,
	Door_get_IsOpen_m3E6B5405808EA7EDFE85AFBD191419C7F1E98ABD,
	Door_Awake_m41F8DB1897D3C52BE7B0A45A6E4BEF3D172DFE78,
	Door_OpenDoor_m2ED0EC299966E5A5DB499414EF8CCD5D2FB5286E,
	Door_CloseDoor_mACA48C2055FF840E1323FD838A26AEAC3A76AD5F,
	Door_Move_mA521B0AA3F93E2DFD7C32A3C1C9290E208BEFC91,
	Door_MoveRoutine_m6F58F24265CBB412BAB13B2AFC2F185077E0E274,
	Door__ctor_mD0A0A61EA2319044DCC266542E718EF863EF6CC6,
	U3CMoveRoutineU3Ed__14__ctor_m607707C035642A19E1DB9DC86F282FA23C0B54C3,
	U3CMoveRoutineU3Ed__14_System_IDisposable_Dispose_m043EA3A98F1E54AF3AC73B27BEC4DA9095319A00,
	U3CMoveRoutineU3Ed__14_MoveNext_m39F20AEE4BB6211FCBFFE4A7B450B028FAFFABF8,
	U3CMoveRoutineU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBCC5B169B9498C34846F6CBEB7564EDB6493A66B,
	U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_Reset_m83038F31C79F09ADC8371A69D36E3CD9EA9FBDBC,
	U3CMoveRoutineU3Ed__14_System_Collections_IEnumerator_get_Current_m8B9300B5A036631DE6796995FCC857C6209C34C6,
	DoorKey_OnTriggerEnter_mFF97DB2F123A569B03CAEBA82681D29D6D084BC2,
	DoorKey__ctor_m06CBB092E887F34E310CD07241548378C4C82A84,
	EnemyAttack_Awake_m11C309606A16AFDFB551F13AF341DE48F6131A0F,
	EnemyAttack_Attack_mC8175509964CC1FDECB2489E43ABB9942BE4E041,
	EnemyAttack_StopAttack_m06D74602CE6B55BCBE6D1E61794AEC0D1D1A9F31,
	EnemyAttack_LookAtTarget_mF7E1575BAE9820A7EDEF1E877FFE48C6B7B34817,
	EnemyAttack_FollowTarget_m93D435D7A86AAC69D7F588E7B8CC45A3B34324B4,
	EnemyAttack_Fire_m67DACC353A9273C61DFBBBB66B8AEC6148A1800D,
	EnemyAttack_RandomOnUnitCircle2_m****************************************,
	EnemyAttack__ctor_mCD4691EDE8565527169EFDDA781859825CDA8199,
	U3CFireU3Ed__14__ctor_mF974A778358856524656CBD0DE30B0DF026A72FB,
	U3CFireU3Ed__14_System_IDisposable_Dispose_mE3801C079CD2919B97406B77DAAE1C0052250CE0,
	U3CFireU3Ed__14_MoveNext_mA6F8D319F38027A3D275693D2F44DFCE16FFEE7A,
	U3CFireU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mEF3CD6F581D7BA5C693DBE52A7D1AF9D2E1890F9,
	U3CFireU3Ed__14_System_Collections_IEnumerator_Reset_mAAE10DF1D0AD7E34390EA24E0B24C2284739132C,
	U3CFireU3Ed__14_System_Collections_IEnumerator_get_Current_m527DE4601E490AE577C1491EBD29F4A99D6D902A,
	U3CFollowTargetU3Ed__13__ctor_m0EA79959FB6E07AEF9E7A9FA804EF113BA68D2A4,
	U3CFollowTargetU3Ed__13_System_IDisposable_Dispose_m0E36F547E951B227EF000EF15726A0F0B62B78AA,
	U3CFollowTargetU3Ed__13_MoveNext_m0666111C44C021A7DA1C2B902DFCF99D4559706B,
	U3CFollowTargetU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA992CE3AB889CBC006D539BCB6C9B4409BA66CE4,
	U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_Reset_m298311DFA1E697CC6529B47667B96F65D1C91BFF,
	U3CFollowTargetU3Ed__13_System_Collections_IEnumerator_get_Current_m2FAEE37FEA5CB5340328103FEE4B2D63C339FC7D,
	U3CLookAtTargetU3Ed__12__ctor_mE673E3FF67DBAE553F5C2C89180EB7BB25C97E04,
	U3CLookAtTargetU3Ed__12_System_IDisposable_Dispose_m8C2F6EBF4D50429EA0CBFF9E7BDC6EE6DFBE2A90,
	U3CLookAtTargetU3Ed__12_MoveNext_m135683B9F0C5D17F3B9A2EB79E6F72B9DB65D5EC,
	U3CLookAtTargetU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0F14DFD66B82EAA6C2D3E231A176F9574DA74156,
	U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_Reset_m07A15C1319B75C4CCC440FCC4C0B203C523BF341,
	U3CLookAtTargetU3Ed__12_System_Collections_IEnumerator_get_Current_m36B6379AF74D5A3B846A9667E6A8F991D52F2BA0,
	EnemyFSM_Awake_m0514E35DDA932033623F72BF29E519BE06AB125F,
	EnemyFSM_Start_m7CBEBC35A2703910AD84DBB006FADB1DBCF9A19B,
	EnemyFSM_OnDestroy_m48A5BE63191B9091C47AAF1B02D92F9A2802C556,
	EnemyFSM_Hit_mC7B0EFEDCB7593E3ED9D9346A26675ABEF718F19,
	EnemyFSM_HitAnim_m2E5E2A327FA865C218278AED5EBE2642611898DC,
	EnemyFSM_OnPlayerInSight_m8DA16747B5D504B1E7FB65BEA5D59732413DB161,
	EnemyFSM_OnPlayerOutOfSight_m81AFAC4A51850B80086DE1A26B83C92F0A1B9A57,
	EnemyFSM_Colorize_m971A4A3350A020E7183282F1B9EA7C5ACCCED2A8,
	EnemyFSM_DropLoot_m430F27F92C046E4BC274B64F0A2DE062CD1E631A,
	EnemyFSM_Die_m71FBAD0CCFF374720C3A00DD8B2A9BEC447ADB5D,
	EnemyFSM__ctor_m3E733A6E1B13A41DCAEC2C6195B4CB6665DB67CE,
	U3CHitAnimU3Ed__13__ctor_m44EAC43C99235EE91342314684E606005E438330,
	U3CHitAnimU3Ed__13_System_IDisposable_Dispose_m8B0AAE54D2EB6F782CE68D60CAA62ED5848EB0FC,
	U3CHitAnimU3Ed__13_MoveNext_mB6CFBCC1AED03B4D5ECCBD32A9657817153B5D65,
	U3CHitAnimU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB2E175D2290FEF4EBB8508BBD07E09857285BF6F,
	U3CHitAnimU3Ed__13_System_Collections_IEnumerator_Reset_mC293E5D1F71063700FC867EC86C325635844BC88,
	U3CHitAnimU3Ed__13_System_Collections_IEnumerator_get_Current_m681FDD9D07ACD1C96BBBD43A8279632591303FED,
	EnemyPatrol_Awake_mDB0A16ADCF61BF6978DB3A7F721D5A9EE04CF2B6,
	EnemyPatrol_Update_m70910553F4F7228F3625371F1448846507CD8E84,
	EnemyPatrol_StartPatrol_mD0558D3C453D136B64FE856BAB40518D47240A68,
	EnemyPatrol_PausePatrol_m63C754BC41C88234EA100ED242875DFC8F145A7F,
	EnemyPatrol_ResumePatrol_mA6030EE1A672F3A8BAE00100973483705845A3C3,
	EnemyPatrol_GoToNextWaypoint_mE3588F3D5E24D745E6A3D638310010F33A66EA96,
	EnemyPatrol_GoToWaypoint_m2CBF871C09C8F49C1214798731D7C08F035868D4,
	EnemyPatrol__ctor_m623FEFA07A17946CE2E68DD6A9A5FEB0CFFE4A38,
	EnemySight_Awake_mF30310C2F76237E2F257E52FC922043D3B3A3A64,
	EnemySight_Start_mEFBF736E90D4325211846E30933BFA1948FD2482,
	EnemySight__ctor_m992A9356FD2E86D59753BBDEE9ADC134E4B76E88,
	U3CStartU3Ed__10__ctor_m05C20E28E3D83795CEF6B8F439B82FC39BC25D83,
	U3CStartU3Ed__10_System_IDisposable_Dispose_mC938CE609305244877A6357A56CC271AE57A9137,
	U3CStartU3Ed__10_MoveNext_mEB568DFE8985DBF6D401574AC5BA20AB1EAA9440,
	U3CStartU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m059C8C17FB2C78D3F39C0D5F51B8FC9C1A90BFBC,
	U3CStartU3Ed__10_System_Collections_IEnumerator_Reset_mDFE86914DF5B0391F9DE7A36C764BBFEEAAEE343,
	U3CStartU3Ed__10_System_Collections_IEnumerator_get_Current_mD9E5DAB3B5419BC16047DCF96A05022875686660,
	EnemyWander_Awake_m3E1BC61CC3C36C7879B4D0C4F6C57F927C2F7E2C,
	EnemyWander_StartWandering_m2FFB17766D4C97045A8E29AB89C9276A5C7C35F5,
	EnemyWander_StopWandering_mACE0DF86A60D0743E3B5E09CF35B917B9F96BAAA,
	EnemyWander_CheckAgentPosition_m7BFEDDFC1A658844F1B307FD28EECFA19B43CD71,
	EnemyWander_GoToWaypoint_mC46CF5C24F723424F8878E3A417A6DBE78AAAD15,
	EnemyWander__ctor_mD8B13D46CF1A8D22BA85FE5C061BCA7D5AF6DA56,
	U3CCheckAgentPositionU3Ed__10__ctor_mD1E04080E9A832ECEE6A50A4D142844CE11B33C9,
	U3CCheckAgentPositionU3Ed__10_System_IDisposable_Dispose_m5079AFD4C832B5D40E85A1757DB8D4505198CA43,
	U3CCheckAgentPositionU3Ed__10_MoveNext_m1FBDE9F9BBDBC9983EF86367633488A9B44AFA4B,
	U3CCheckAgentPositionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0CDF703AA428A431258E05DE289FC7A4AD8E923,
	U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_Reset_m055746713979B4B4C1F920905D84B3C0D4F55C9B,
	U3CCheckAgentPositionU3Ed__10_System_Collections_IEnumerator_get_Current_mD02DE6AC35BC9D6F84B83BAB74CB2BD9E4B342CD,
	GameOver_Awake_mE8746FFD083A7B470F2CB93113A0A8605162DD17,
	GameOver_ShowScreen_m91A0DCC3CA95E7AC8C9895D6B7DF9E0BF7B480D4,
	GameOver_PlayAgain_m5130E860511E1D3A53FDE8DEC65340AD43BE0656,
	GameOver__ctor_m1FBF6EDFA0F5310848A79816D6A01AF5A6D6CA1D,
	Goal_OnTriggerEnter_m397A4D3BEE326F3C16B4581EFADF7FD0E27119A8,
	Goal__ctor_mF0029EB41C7DB255447EF0321DF92A006F5A9D4F,
	ObjectMove_Awake_m3A0F42D78BE8F9DA31716FB3F072CDCB790D82FC,
	ObjectMove_LateUpdate_m2C898044689A3751D189011C66220447100641BE,
	ObjectMove__ctor_m8DF486D96BAB3438CAA7FC6CC50F46EE0D0FB1A0,
	ObjectRotate_Awake_mEF1E2172BD8C35E5B2A94B6B7E6578FC368A709B,
	ObjectRotate_LateUpdate_m83C084CEAC89133F7431DD76D960F14404FBF61A,
	ObjectRotate__ctor_m3B44A3B585DF7089576EA3FFCF29DD25C8E640CB,
	PlayerFire_Awake_m948F53E4F2DCCB95C92DF1BF1D3F3533E5112B54,
	PlayerFire_Update_m561CAFB0D567A65E4A85A252CBC735D191F9B2E7,
	PlayerFire_Fire_mC9C434664104BFAF935929AA4133DC30D6117C20,
	PlayerFire__ctor_m471056A5E1EB8DAE8A1083B097032619350B2974,
	U3CFireU3Ed__8__ctor_m1D08ACF93FC08FAEEE86A325184A3B83189EAD93,
	U3CFireU3Ed__8_System_IDisposable_Dispose_mC0B77D9A4BA0100468D66FD603456DD5AA21BE41,
	U3CFireU3Ed__8_MoveNext_m53B9D3C88B05B1D2A80AE6708387D2413E3E90CC,
	U3CFireU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m31DAE23A4BB45B6A91D9CDFF9BC1CD4BF35EFC52,
	U3CFireU3Ed__8_System_Collections_IEnumerator_Reset_mA16B82A9DF02A5BBB3009AA868FFB6175BDBDBED,
	U3CFireU3Ed__8_System_Collections_IEnumerator_get_Current_m9D956562E85667ABD8C2D44CC91C10F5F1AEBF1C,
	PlayerHealth_Awake_m4EF1CCD86D25B865858E6C9E1255073FFA897BBF,
	PlayerHealth_Hit_m2E72191B49697C1ABFA9050F7202D5F3D5B02543,
	PlayerHealth_HitAnim_m502FE49C7A0F44B646B2D62B886D220235D48EE1,
	PlayerHealth__ctor_m8B821C450A0FFF4BFEB2823CE37F8A895456C8DA,
	U3CHitAnimU3Ed__5__ctor_m7E9DD4E9433C185C734A9D36477091BCA69D2192,
	U3CHitAnimU3Ed__5_System_IDisposable_Dispose_m3009F9D3448A7899D6D03FAFB05F2D3A919B5C55,
	U3CHitAnimU3Ed__5_MoveNext_m4B4F74EB9BF2AA3323CD7F6542118207E1D11634,
	U3CHitAnimU3Ed__5_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m888E818BD1863568CBF8D43974C209464612D7D3,
	U3CHitAnimU3Ed__5_System_Collections_IEnumerator_Reset_m21C0332FD0DD74A1C22BCD2F196B9EAE65CB047A,
	U3CHitAnimU3Ed__5_System_Collections_IEnumerator_get_Current_m60C2346A67FF5DEF4E21787E195108E09F03445B,
	PlayerInput_Start_mD0291A721D4AD067A4814D9033E030A602BCC180,
	PlayerInput_Update_m62F19B56CFB86CA0ACF41F97793395F56D855AC5,
	PlayerInput_IncrementTowards_mD355CF9B9D9CDBC87C7C1DF45A2C7D3BE306C349,
	PlayerInput__ctor_m6B9027968AA8D70AFEEAFB6F03F8D79BC420286B,
	PlayerInput_U3CStartU3Eb__8_0_m76653BA2097A5C65BA097230660F8D321A4076EC,
	PlayerInput_U3CStartU3Eb__8_1_mCAAC9ACD09B0DB47A5E94AA1F4B1E0698584CF65,
	Pool_get_nextThing_mBB707503DABD3A940FA5D9E841E12C7CA3C3A0F1,
	Pool_set_nextThing_m85C1B38EC0E3F26779600AAB56ABAA3C592033CA,
	Pool__ctor_mEDE0372E7C92CB10BEF27EBCB54F74BD76987D7B,
	PoolMember_OnDisable_m97FBA0D7B7BA4C015BB8A5041EB572CFD3EF45FA,
	PoolMember__ctor_m7070035AA0BE5D19A5C2B7E664F8BF888306AD98,
	RotateTowardsMouse_Awake_mA64A3A4C9057CA30CD3F50B4ADC5B58A82F0EF82,
	RotateTowardsMouse_Update_mE51D6F4E0E8CB2B9536825426096CB06431B8B18,
	RotateTowardsMouse__ctor_mC2822F31F15D5DB1DDF42AC2F64EE5B3CB838933,
	SwitchCameraProjection_Awake_m4B57A739BA2FF19BD5E2285F39E959DE44E374C1,
	SwitchCameraProjection_OnGUI_mF03DD99C8C0D379BC2A9EA253DE827F1672966E7,
	SwitchCameraProjection_Switch_m0A36CA85AC6C211D8CED6B7FFF760C07BEF90953,
	SwitchCameraProjection__ctor_m4621DC1A9C8DCBE5CE8DA4B6A6D630A07CD89A97,
	PlayerInput_Start_mA030D60FD01D12C3C5B3D9A9613EBBD659DFA4BE,
	PlayerInput_Update_m3519692162C77C08ECC1EDB53A68D94A8D890A6A,
	PlayerInput_IncrementTowards_mD28E8347BA822363232A5157709A19E5E15EE43A,
	PlayerInput__ctor_m7B90EFECE5655E3E732BBA4F86708565AEBB0385,
	PlayerInputBot_Start_mB9848527D69090273F7F40462DC2DA8B66DE9178,
	PlayerInputBot_RandomInputJump_m088DD669939E0E38E25CBAA1FFBB40CD797B9D85,
	PlayerInputBot_RandomInputSpeed_mBD5BE6DB4021C7F69A581CB6B02087AA5ABCF2D6,
	PlayerInputBot_Update_m31C836C012EC83E4E61D6EF9165A4FC717A62949,
	PlayerInputBot_IncrementTowards_m03CD9814F330F30FE7D763E8B679BD7F52777629,
	PlayerInputBot__ctor_m9C0D7B64314D75EECB98C462DD5C31F9A60FD6B5,
	U3CRandomInputJumpU3Ed__13__ctor_mB8DCD6A6EDB7F831EF765A45D6678A392D5EFFA5,
	U3CRandomInputJumpU3Ed__13_System_IDisposable_Dispose_m7C5B6E60F836226EA8CA408D093C668A84A217C9,
	U3CRandomInputJumpU3Ed__13_MoveNext_mCF805A089DA0A13695DF8FC54B77ED1A610EF7D5,
	U3CRandomInputJumpU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBB657D69CEB99A139B9E89184AF916896EF94808,
	U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_Reset_m49E9633C06623C867A599BEC40859603D93CBE8C,
	U3CRandomInputJumpU3Ed__13_System_Collections_IEnumerator_get_Current_mE9B95957B4F2D96650FF229E4567F3F16119BA1D,
	U3CRandomInputSpeedU3Ed__14__ctor_mF9403523C795C886A933D9B8DF002059891CA68A,
	U3CRandomInputSpeedU3Ed__14_System_IDisposable_Dispose_m9BC5DBA756ABEE6955B06C1FB02BD54F4EA2100B,
	U3CRandomInputSpeedU3Ed__14_MoveNext_mB2221D545B780984750471F989349D01D73B0079,
	U3CRandomInputSpeedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m43BA88FB08DFAD375D69D7F9558FC26AAC9181AC,
	U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_Reset_m5C882BB510DBCD5495FF38F35446C2A45EAA96C1,
	U3CRandomInputSpeedU3Ed__14_System_Collections_IEnumerator_get_Current_m4AF1BF5A3C32967740C7385D352302258BE058D7,
	ToggleCinematics_OnGUI_m2051D42958ED9A0B8B9A98DD8926502A4789CBF2,
	ToggleCinematics__ctor_mA06EF3C0F3591B5E13DE3534B911448054F41C4C,
	ToggleTransitionsFX_OnGUI_mC99F88436FC1AFE5B11791C517C2B16927EA427B,
	ToggleTransitionsFX__ctor_m741E30759E3313807DAE5011C96215E44B82964A,
};
static const int32_t s_InvokerIndices[179] = 
{
	3113,
	3113,
	7120,
	7120,
	10464,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	6887,
	7120,
	5851,
	7120,
	1981,
	1654,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5806,
	7120,
	7120,
	5806,
	7120,
	6992,
	6992,
	6992,
	10263,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	5774,
	6992,
	5806,
	7120,
	5706,
	7120,
	7120,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	5774,
	7120,
	7120,
	6992,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	6992,
	7120,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	7120,
	5806,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	6992,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	5774,
	6992,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	1685,
	7120,
	7120,
	7120,
	6992,
	5806,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	7120,
	1685,
	7120,
	7120,
	6992,
	6992,
	7120,
	1685,
	7120,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	5774,
	7120,
	6887,
	6992,
	7120,
	6992,
	7120,
	7120,
	7120,
	7120,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_ProCamera2D_Examples_CodeGenModule;
const Il2CppCodeGenModule g_ProCamera2D_Examples_CodeGenModule = 
{
	"ProCamera2D.Examples.dll",
	179,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
