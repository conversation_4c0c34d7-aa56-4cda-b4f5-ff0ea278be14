﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ParticleSystem_Emit_m27ED2FE38DEE11C9C98F7D30392BB5B39B6A16ED (void);
extern void ParticleSystem_Emit_m329091E7F55C972CE85A100CA624192AD8442688 (void);
extern void ParticleSystem_get_isPlaying_mC5170DA3C904670B88200C8DA1E0F8FC1BC7C42B (void);
extern void ParticleSystem_Play_m4A59E0A2C7CA49EF75287A067305ABB314A16E62 (void);
extern void ParticleSystem_Play_mD943E601BFE16CB9BB5D1F5E6AED5C36F5F11EF5 (void);
extern void ParticleSystem_Stop_mB5761CB85083F593FFEC3D27931CACF1855A6326 (void);
extern void ParticleSystem_Stop_m6CA855033D5CE2D3AF7927B6709BC65DBCD632DF (void);
extern void ParticleSystem_Clear_m26F18A1A58006417A0F66688E3124CFD1E880E11 (void);
extern void ParticleSystem_Clear_mE026AF9610248EB560530CD292FEED0F7571F732 (void);
extern void ParticleSystem_IsAlive_m4C919DCFF8EC256F68415875DF421DC53065AC7B (void);
extern void ParticleSystem_Emit_m3E9E6359087607E84E0D4D35844D80D9452AD72D (void);
extern void ParticleSystem_Emit_Internal_m54D6D9A78E8634846C9DB6445C0E0A0885E8A20E (void);
extern void ParticleSystem_Emit_m5AD1A3F02A19B61E3B0CC738FD498B52D19B65AA (void);
extern void ParticleSystem_EmitOld_Internal_mD22E235F6AB32455147A7DAF814AC8B4949C89D3 (void);
extern void ParticleSystem_get_main_mD86DFCD96150E2CE760CD2F37052BB3BCA33C189 (void);
extern void ParticleSystem_get_emission_mD9402CE632A3607DA0B0486F9F58F7FB2B44CF08 (void);
extern void ParticleSystem_get_shape_mD7F072CC18587858138AA7B3A882995493AA7C80 (void);
extern void ParticleSystem_get_colorOverLifetime_mD8C72661EFE9BB063126752E744544EE2FF5814C (void);
extern void ParticleSystem_get_sizeOverLifetime_m9142CFF6D940A4EB07D07495CC09C2C0AA38549F (void);
extern void ParticleSystem__ctor_mABC4A409D6EC077A89AD3AEF259CE48D32EC47EF (void);
extern void ParticleSystem_Emit_Injected_mC5638D07E58C6EDB8C0CD3717A66A61CBA30C3A6 (void);
extern void MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A (void);
extern void MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77 (void);
extern void MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8 (void);
extern void MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE (void);
extern void MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514 (void);
extern void MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75 (void);
extern void MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B (void);
extern void MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A (void);
extern void MainModule_set_simulationSpace_m23D533E66925AABF0C336894FBF2FF03FF3891BC (void);
extern void MainModule_set_duration_Injected_m704DD7FC97FA887A55B3FA4CC4ACF1156302F04A (void);
extern void MainModule_get_loop_Injected_mB2E11A7A29523C771FC0A9D2A4C061B25716E3F9 (void);
extern void MainModule_set_loop_Injected_mF673B0C6B2194F3C5FD0DCAB438350685E5CE815 (void);
extern void MainModule_set_startLifetime_Injected_m6EB92974DED01D362E15FC6708FEDEDF71BE850E (void);
extern void MainModule_set_startSpeed_Injected_m88DF340149F7C459065FCC37E3A1BCF12003354B (void);
extern void MainModule_set_startSize_Injected_m1D975526AB75F6536C653FB3BBE6D57631CF9F79 (void);
extern void MainModule_set_startColor_Injected_mF946A6488A011017014FD84C86FAC959F2909812 (void);
extern void MainModule_set_simulationSpace_Injected_m7710E894FA3BF3F226A89C24DC461672201445BA (void);
extern void EmissionModule__ctor_m6AE98CC2103BECB52B7551D1304E733AE8BD70B1 (void);
extern void EmissionModule_set_enabled_mC82B6915ED485AB8DB54DFA6599C9C973BB5D867 (void);
extern void EmissionModule_set_rateOverTime_m71BF3C0A80EA572CD87EFF5944E8FA680F51DC20 (void);
extern void EmissionModule_SetBursts_mD309779B6F8852E7F415A08F340798EA7FA13F1E (void);
extern void EmissionModule_SetBursts_m08B9E686EAF2691981D8DEC7791BF77818150B31 (void);
extern void EmissionModule_SetBurst_mE73709190EBF66BFA9064A18B6D5B27D8A2585E8 (void);
extern void EmissionModule_set_burstCount_m25190308EDE6BDEE704494ACAA98D155C609751F (void);
extern void EmissionModule_set_enabled_Injected_m78ACA5DBC6846ADDCFFA4748293EB9AA053DCEC6 (void);
extern void EmissionModule_set_rateOverTime_Injected_mFD0E86FBF84842984C096ABB6F41E9B53783D201 (void);
extern void EmissionModule_SetBurst_Injected_m7E3F8B2580F45973F4FBF71486C27EF7AE0F6296 (void);
extern void EmissionModule_set_burstCount_Injected_mAC77A262254B652E39C073F2C0C3980C395D1826 (void);
extern void ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD (void);
extern void ShapeModule_set_enabled_m6BA02351FEED67A82664135B922DDA66D71DF399 (void);
extern void ShapeModule_set_shapeType_m4B5D1EA91037AD7065FE1D23652D919FED4D6D7F (void);
extern void ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C (void);
extern void ShapeModule_set_enabled_Injected_m1EE1CA0CCE2E7B3FDA6AD07E7D45A7A4B236C23C (void);
extern void ShapeModule_set_shapeType_Injected_mD337F7CDD597F13EC744DFA482A95558282166F4 (void);
extern void ShapeModule_set_radius_Injected_m5C12B0E7933E1044FED367AFC72712AD04974B82 (void);
extern void Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911 (void);
extern void Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E (void);
extern void Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B (void);
extern void Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018 (void);
extern void Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77 (void);
extern void Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538 (void);
extern void Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2 (void);
extern void Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F (void);
extern void Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62 (void);
extern void Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8 (void);
extern void Burst__ctor_m5F971EB74D6A40967CFF4E8590758F899D5AC261 (void);
extern void MinMaxCurve__ctor_m1D3846251475D7BBC7B128CCD7DFF40B16AAEF9E (void);
extern void MinMaxCurve__ctor_m02A81CDCC1009C0D466A4A59ED57CD3371A89E7B (void);
extern void MinMaxCurve_op_Implicit_m133028E91CF2F823F5E20F6B19A3332A02404086 (void);
extern void MinMaxGradient__ctor_m982C2A8AD071EA714E5583F90727C306B126F062 (void);
extern void MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858 (void);
extern void MinMaxGradient_op_Implicit_mAA6154D6644E72BBA49EB34DC957538EE929DE02 (void);
extern void MinMaxGradient_op_Implicit_m8568E0D8DD06940C6A710801B3FC41BB5307298B (void);
extern void ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3 (void);
extern void ColorOverLifetimeModule_set_enabled_m356D3ABCE0A5B979105F546C2EAA54702297059C (void);
extern void ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B (void);
extern void ColorOverLifetimeModule_set_enabled_Injected_m411FF32BB2718487AA6374F488FD779E7D04CD62 (void);
extern void ColorOverLifetimeModule_set_color_Injected_mC9CD2A5A5155CEB2DEFAE335517A371F4E9E08C0 (void);
extern void SizeOverLifetimeModule__ctor_m6225AAFA0CF0011BBBE04AA04F1EB156FA234E30 (void);
extern void SizeOverLifetimeModule_set_enabled_mC45334E7AC31CCBE1CFA531D28D29E1BD38D5D6F (void);
extern void SizeOverLifetimeModule_set_size_m1D5987F4FB9E948DEAA5E3D8FB21D1AFEE15EBE5 (void);
extern void SizeOverLifetimeModule_set_enabled_Injected_m973D8103027AE81D32CE18066701D1090E9F079D (void);
extern void SizeOverLifetimeModule_set_size_Injected_mD98D272FA25AF425E0BB990A4DDC7AB7910413EA (void);
extern void ParticleSystemRenderer_GetMeshes_m3CA9AA8947C7F0468F6C0B7F1344D747EA43D440 (void);
static Il2CppMethodPointer s_methodPointers[85] = 
{
	ParticleSystem_Emit_m27ED2FE38DEE11C9C98F7D30392BB5B39B6A16ED,
	ParticleSystem_Emit_m329091E7F55C972CE85A100CA624192AD8442688,
	ParticleSystem_get_isPlaying_mC5170DA3C904670B88200C8DA1E0F8FC1BC7C42B,
	ParticleSystem_Play_m4A59E0A2C7CA49EF75287A067305ABB314A16E62,
	ParticleSystem_Play_mD943E601BFE16CB9BB5D1F5E6AED5C36F5F11EF5,
	ParticleSystem_Stop_mB5761CB85083F593FFEC3D27931CACF1855A6326,
	ParticleSystem_Stop_m6CA855033D5CE2D3AF7927B6709BC65DBCD632DF,
	ParticleSystem_Clear_m26F18A1A58006417A0F66688E3124CFD1E880E11,
	ParticleSystem_Clear_mE026AF9610248EB560530CD292FEED0F7571F732,
	ParticleSystem_IsAlive_m4C919DCFF8EC256F68415875DF421DC53065AC7B,
	ParticleSystem_Emit_m3E9E6359087607E84E0D4D35844D80D9452AD72D,
	ParticleSystem_Emit_Internal_m54D6D9A78E8634846C9DB6445C0E0A0885E8A20E,
	ParticleSystem_Emit_m5AD1A3F02A19B61E3B0CC738FD498B52D19B65AA,
	ParticleSystem_EmitOld_Internal_mD22E235F6AB32455147A7DAF814AC8B4949C89D3,
	ParticleSystem_get_main_mD86DFCD96150E2CE760CD2F37052BB3BCA33C189,
	ParticleSystem_get_emission_mD9402CE632A3607DA0B0486F9F58F7FB2B44CF08,
	ParticleSystem_get_shape_mD7F072CC18587858138AA7B3A882995493AA7C80,
	ParticleSystem_get_colorOverLifetime_mD8C72661EFE9BB063126752E744544EE2FF5814C,
	ParticleSystem_get_sizeOverLifetime_m9142CFF6D940A4EB07D07495CC09C2C0AA38549F,
	ParticleSystem__ctor_mABC4A409D6EC077A89AD3AEF259CE48D32EC47EF,
	ParticleSystem_Emit_Injected_mC5638D07E58C6EDB8C0CD3717A66A61CBA30C3A6,
	MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A,
	MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77,
	MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8,
	MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE,
	MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514,
	MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75,
	MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B,
	MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A,
	MainModule_set_simulationSpace_m23D533E66925AABF0C336894FBF2FF03FF3891BC,
	MainModule_set_duration_Injected_m704DD7FC97FA887A55B3FA4CC4ACF1156302F04A,
	MainModule_get_loop_Injected_mB2E11A7A29523C771FC0A9D2A4C061B25716E3F9,
	MainModule_set_loop_Injected_mF673B0C6B2194F3C5FD0DCAB438350685E5CE815,
	MainModule_set_startLifetime_Injected_m6EB92974DED01D362E15FC6708FEDEDF71BE850E,
	MainModule_set_startSpeed_Injected_m88DF340149F7C459065FCC37E3A1BCF12003354B,
	MainModule_set_startSize_Injected_m1D975526AB75F6536C653FB3BBE6D57631CF9F79,
	MainModule_set_startColor_Injected_mF946A6488A011017014FD84C86FAC959F2909812,
	MainModule_set_simulationSpace_Injected_m7710E894FA3BF3F226A89C24DC461672201445BA,
	EmissionModule__ctor_m6AE98CC2103BECB52B7551D1304E733AE8BD70B1,
	EmissionModule_set_enabled_mC82B6915ED485AB8DB54DFA6599C9C973BB5D867,
	EmissionModule_set_rateOverTime_m71BF3C0A80EA572CD87EFF5944E8FA680F51DC20,
	EmissionModule_SetBursts_mD309779B6F8852E7F415A08F340798EA7FA13F1E,
	EmissionModule_SetBursts_m08B9E686EAF2691981D8DEC7791BF77818150B31,
	EmissionModule_SetBurst_mE73709190EBF66BFA9064A18B6D5B27D8A2585E8,
	EmissionModule_set_burstCount_m25190308EDE6BDEE704494ACAA98D155C609751F,
	EmissionModule_set_enabled_Injected_m78ACA5DBC6846ADDCFFA4748293EB9AA053DCEC6,
	EmissionModule_set_rateOverTime_Injected_mFD0E86FBF84842984C096ABB6F41E9B53783D201,
	EmissionModule_SetBurst_Injected_m7E3F8B2580F45973F4FBF71486C27EF7AE0F6296,
	EmissionModule_set_burstCount_Injected_mAC77A262254B652E39C073F2C0C3980C395D1826,
	ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD,
	ShapeModule_set_enabled_m6BA02351FEED67A82664135B922DDA66D71DF399,
	ShapeModule_set_shapeType_m4B5D1EA91037AD7065FE1D23652D919FED4D6D7F,
	ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C,
	ShapeModule_set_enabled_Injected_m1EE1CA0CCE2E7B3FDA6AD07E7D45A7A4B236C23C,
	ShapeModule_set_shapeType_Injected_mD337F7CDD597F13EC744DFA482A95558282166F4,
	ShapeModule_set_radius_Injected_m5C12B0E7933E1044FED367AFC72712AD04974B82,
	Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911,
	Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E,
	Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B,
	Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018,
	Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77,
	Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538,
	Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2,
	Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F,
	Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62,
	Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8,
	Burst__ctor_m5F971EB74D6A40967CFF4E8590758F899D5AC261,
	MinMaxCurve__ctor_m1D3846251475D7BBC7B128CCD7DFF40B16AAEF9E,
	MinMaxCurve__ctor_m02A81CDCC1009C0D466A4A59ED57CD3371A89E7B,
	MinMaxCurve_op_Implicit_m133028E91CF2F823F5E20F6B19A3332A02404086,
	MinMaxGradient__ctor_m982C2A8AD071EA714E5583F90727C306B126F062,
	MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858,
	MinMaxGradient_op_Implicit_mAA6154D6644E72BBA49EB34DC957538EE929DE02,
	MinMaxGradient_op_Implicit_m8568E0D8DD06940C6A710801B3FC41BB5307298B,
	ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3,
	ColorOverLifetimeModule_set_enabled_m356D3ABCE0A5B979105F546C2EAA54702297059C,
	ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B,
	ColorOverLifetimeModule_set_enabled_Injected_m411FF32BB2718487AA6374F488FD779E7D04CD62,
	ColorOverLifetimeModule_set_color_Injected_mC9CD2A5A5155CEB2DEFAE335517A371F4E9E08C0,
	SizeOverLifetimeModule__ctor_m6225AAFA0CF0011BBBE04AA04F1EB156FA234E30,
	SizeOverLifetimeModule_set_enabled_mC45334E7AC31CCBE1CFA531D28D29E1BD38D5D6F,
	SizeOverLifetimeModule_set_size_m1D5987F4FB9E948DEAA5E3D8FB21D1AFEE15EBE5,
	SizeOverLifetimeModule_set_enabled_Injected_m973D8103027AE81D32CE18066701D1090E9F079D,
	SizeOverLifetimeModule_set_size_Injected_mD98D272FA25AF425E0BB990A4DDC7AB7910413EA,
	ParticleSystemRenderer_GetMeshes_m3CA9AA8947C7F0468F6C0B7F1344D747EA43D440,
};
extern void MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A_AdjustorThunk (void);
extern void MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77_AdjustorThunk (void);
extern void MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8_AdjustorThunk (void);
extern void MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE_AdjustorThunk (void);
extern void MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514_AdjustorThunk (void);
extern void MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75_AdjustorThunk (void);
extern void MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B_AdjustorThunk (void);
extern void MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A_AdjustorThunk (void);
extern void MainModule_set_simulationSpace_m23D533E66925AABF0C336894FBF2FF03FF3891BC_AdjustorThunk (void);
extern void EmissionModule__ctor_m6AE98CC2103BECB52B7551D1304E733AE8BD70B1_AdjustorThunk (void);
extern void EmissionModule_set_enabled_mC82B6915ED485AB8DB54DFA6599C9C973BB5D867_AdjustorThunk (void);
extern void EmissionModule_set_rateOverTime_m71BF3C0A80EA572CD87EFF5944E8FA680F51DC20_AdjustorThunk (void);
extern void EmissionModule_SetBursts_mD309779B6F8852E7F415A08F340798EA7FA13F1E_AdjustorThunk (void);
extern void EmissionModule_SetBursts_m08B9E686EAF2691981D8DEC7791BF77818150B31_AdjustorThunk (void);
extern void EmissionModule_SetBurst_mE73709190EBF66BFA9064A18B6D5B27D8A2585E8_AdjustorThunk (void);
extern void EmissionModule_set_burstCount_m25190308EDE6BDEE704494ACAA98D155C609751F_AdjustorThunk (void);
extern void ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD_AdjustorThunk (void);
extern void ShapeModule_set_enabled_m6BA02351FEED67A82664135B922DDA66D71DF399_AdjustorThunk (void);
extern void ShapeModule_set_shapeType_m4B5D1EA91037AD7065FE1D23652D919FED4D6D7F_AdjustorThunk (void);
extern void ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C_AdjustorThunk (void);
extern void Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911_AdjustorThunk (void);
extern void Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E_AdjustorThunk (void);
extern void Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B_AdjustorThunk (void);
extern void Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018_AdjustorThunk (void);
extern void Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77_AdjustorThunk (void);
extern void Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538_AdjustorThunk (void);
extern void Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2_AdjustorThunk (void);
extern void Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F_AdjustorThunk (void);
extern void Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62_AdjustorThunk (void);
extern void Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8_AdjustorThunk (void);
extern void Burst__ctor_m5F971EB74D6A40967CFF4E8590758F899D5AC261_AdjustorThunk (void);
extern void MinMaxCurve__ctor_m1D3846251475D7BBC7B128CCD7DFF40B16AAEF9E_AdjustorThunk (void);
extern void MinMaxCurve__ctor_m02A81CDCC1009C0D466A4A59ED57CD3371A89E7B_AdjustorThunk (void);
extern void MinMaxGradient__ctor_m982C2A8AD071EA714E5583F90727C306B126F062_AdjustorThunk (void);
extern void MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858_AdjustorThunk (void);
extern void ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3_AdjustorThunk (void);
extern void ColorOverLifetimeModule_set_enabled_m356D3ABCE0A5B979105F546C2EAA54702297059C_AdjustorThunk (void);
extern void ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B_AdjustorThunk (void);
extern void SizeOverLifetimeModule__ctor_m6225AAFA0CF0011BBBE04AA04F1EB156FA234E30_AdjustorThunk (void);
extern void SizeOverLifetimeModule_set_enabled_mC45334E7AC31CCBE1CFA531D28D29E1BD38D5D6F_AdjustorThunk (void);
extern void SizeOverLifetimeModule_set_size_m1D5987F4FB9E948DEAA5E3D8FB21D1AFEE15EBE5_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[41] = 
{
	{ 0x06000016, MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A_AdjustorThunk },
	{ 0x06000017, MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77_AdjustorThunk },
	{ 0x06000018, MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8_AdjustorThunk },
	{ 0x06000019, MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE_AdjustorThunk },
	{ 0x0600001A, MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514_AdjustorThunk },
	{ 0x0600001B, MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75_AdjustorThunk },
	{ 0x0600001C, MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B_AdjustorThunk },
	{ 0x0600001D, MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A_AdjustorThunk },
	{ 0x0600001E, MainModule_set_simulationSpace_m23D533E66925AABF0C336894FBF2FF03FF3891BC_AdjustorThunk },
	{ 0x06000027, EmissionModule__ctor_m6AE98CC2103BECB52B7551D1304E733AE8BD70B1_AdjustorThunk },
	{ 0x06000028, EmissionModule_set_enabled_mC82B6915ED485AB8DB54DFA6599C9C973BB5D867_AdjustorThunk },
	{ 0x06000029, EmissionModule_set_rateOverTime_m71BF3C0A80EA572CD87EFF5944E8FA680F51DC20_AdjustorThunk },
	{ 0x0600002A, EmissionModule_SetBursts_mD309779B6F8852E7F415A08F340798EA7FA13F1E_AdjustorThunk },
	{ 0x0600002B, EmissionModule_SetBursts_m08B9E686EAF2691981D8DEC7791BF77818150B31_AdjustorThunk },
	{ 0x0600002C, EmissionModule_SetBurst_mE73709190EBF66BFA9064A18B6D5B27D8A2585E8_AdjustorThunk },
	{ 0x0600002D, EmissionModule_set_burstCount_m25190308EDE6BDEE704494ACAA98D155C609751F_AdjustorThunk },
	{ 0x06000032, ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD_AdjustorThunk },
	{ 0x06000033, ShapeModule_set_enabled_m6BA02351FEED67A82664135B922DDA66D71DF399_AdjustorThunk },
	{ 0x06000034, ShapeModule_set_shapeType_m4B5D1EA91037AD7065FE1D23652D919FED4D6D7F_AdjustorThunk },
	{ 0x06000035, ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C_AdjustorThunk },
	{ 0x06000039, Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911_AdjustorThunk },
	{ 0x0600003A, Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E_AdjustorThunk },
	{ 0x0600003B, Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B_AdjustorThunk },
	{ 0x0600003C, Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018_AdjustorThunk },
	{ 0x0600003D, Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77_AdjustorThunk },
	{ 0x0600003E, Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538_AdjustorThunk },
	{ 0x0600003F, Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2_AdjustorThunk },
	{ 0x06000040, Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F_AdjustorThunk },
	{ 0x06000041, Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62_AdjustorThunk },
	{ 0x06000042, Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8_AdjustorThunk },
	{ 0x06000043, Burst__ctor_m5F971EB74D6A40967CFF4E8590758F899D5AC261_AdjustorThunk },
	{ 0x06000044, MinMaxCurve__ctor_m1D3846251475D7BBC7B128CCD7DFF40B16AAEF9E_AdjustorThunk },
	{ 0x06000045, MinMaxCurve__ctor_m02A81CDCC1009C0D466A4A59ED57CD3371A89E7B_AdjustorThunk },
	{ 0x06000047, MinMaxGradient__ctor_m982C2A8AD071EA714E5583F90727C306B126F062_AdjustorThunk },
	{ 0x06000048, MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858_AdjustorThunk },
	{ 0x0600004B, ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3_AdjustorThunk },
	{ 0x0600004C, ColorOverLifetimeModule_set_enabled_m356D3ABCE0A5B979105F546C2EAA54702297059C_AdjustorThunk },
	{ 0x0600004D, ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B_AdjustorThunk },
	{ 0x06000050, SizeOverLifetimeModule__ctor_m6225AAFA0CF0011BBBE04AA04F1EB156FA234E30_AdjustorThunk },
	{ 0x06000051, SizeOverLifetimeModule_set_enabled_mC45334E7AC31CCBE1CFA531D28D29E1BD38D5D6F_AdjustorThunk },
	{ 0x06000052, SizeOverLifetimeModule_set_size_m1D5987F4FB9E948DEAA5E3D8FB21D1AFEE15EBE5_AdjustorThunk },
};
static const int32_t s_InvokerIndices[85] = 
{
	720,
	5963,
	6887,
	5703,
	7120,
	2810,
	5703,
	5703,
	7120,
	4167,
	5774,
	5774,
	3459,
	5689,
	7165,
	7164,
	7166,
	7163,
	7167,
	7120,
	2797,
	5806,
	5851,
	6887,
	5703,
	5961,
	5961,
	5961,
	5962,
	5774,
	9558,
	9822,
	9554,
	9553,
	9553,
	9553,
	9553,
	9555,
	5806,
	5703,
	5961,
	5806,
	3358,
	3259,
	5774,
	9554,
	9553,
	8863,
	9555,
	5806,
	5703,
	5774,
	5851,
	9554,
	9555,
	9558,
	5851,
	5915,
	5915,
	5851,
	5851,
	5708,
	5910,
	5851,
	5915,
	5915,
	3407,
	5851,
	3410,
	10315,
	5706,
	5806,
	10316,
	10317,
	5806,
	5703,
	5962,
	9554,
	9553,
	5806,
	5703,
	5961,
	9554,
	9553,
	4927,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule = 
{
	"UnityEngine.ParticleSystemModule.dll",
	85,
	s_methodPointers,
	41,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
