// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00000000008063A1000160D3 /* libiPhone-lib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D8A1C72A0E8063A1000160D3 /* libiPhone-lib.a */; };
		00B1680459E35383A06DC548 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 94CD3AD5F74D4265461E0C6D /* CoreTelephony.framework */; };
		01214F5E521901A252D833EC /* GDTMobUPluginUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 06307F0DDC88536113EE103A /* GDTMobUPluginUtil.m */; };
		030293BF8AB3F381DBE983B4 /* GDTVideoConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 4C378673D5DF403FDFC49E5D /* GDTVideoConfig.h */; };
		034B0A486E1AFEADBD2E0B9A /* GDTSplashAd.h in Headers */ = {isa = PBXBuildFile; fileRef = BAAB40540DA41C1B454DDA91 /* GDTSplashAd.h */; };
		0F2C9017C0FC279BB5D58EF7 /* GDTUnifiedNativeAd.h in Headers */ = {isa = PBXBuildFile; fileRef = 310F5D7A4EECCB4E3739971F /* GDTUnifiedNativeAd.h */; };
		108959862548F94A325ECC2D /* unity-plugin-library.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C684380BEA113884C86ACE5A /* unity-plugin-library.a */; };
		149790414243CABDCD39AE98 /* GDTVideoAdReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 726240544A4D431FB28D10E4 /* GDTVideoAdReporter.h */; };
		15EDD835E166901FC93365AF /* CustomAppController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 1BAA14B679EE49250AFB248E /* CustomAppController.mm */; };
		16619FEE818CCF364865C30A /* Reachability.h in Headers */ = {isa = PBXBuildFile; fileRef = A78015B5DCE025229C145EB5 /* Reachability.h */; };
		1D2669CE14FED93CF852BF28 /* UnityAds.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 23772533C9AE112A1A30D5AA /* UnityAds.framework */; };
		1D45A5CCCCC4E76191AAF1D0 /* libil2cpp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = EE54DC7E56FA015DDC9659BD /* libil2cpp.a */; };
		28818E3FDD0A6E7A3535D165 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 65F37FDAB09705DF055776EF /* AdSupport.framework */; };
		2989A2C045F8EDF22D1ADE69 /* GDTUnifiedNativeAdDataObject.h in Headers */ = {isa = PBXBuildFile; fileRef = FF37CE76CE459AC4C81D7B58 /* GDTUnifiedNativeAdDataObject.h */; };
		29C3C722F6354D4EE671E608 /* GDTPrivacyConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 6B4143260DD352AF6D26E4EA /* GDTPrivacyConfiguration.h */; };
		2A05EDAC7CD6A3B684B25E37 /* GDTAdTestSetting.h in Headers */ = {isa = PBXBuildFile; fileRef = DFED97C0BA877B9D186DA8C0 /* GDTAdTestSetting.h */; };
		2AFDC3C9885BA5C4A06F07A7 /* GDTUnifiedInterstitialAdNetworkConnectorProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = E7CE71A354C4D7AD8FC71667 /* GDTUnifiedInterstitialAdNetworkConnectorProtocol.h */; };
		3035FDE6435EA7A15BFC07E1 /* UnityAnalyticsWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = F9A594EF02BB90976F5CC883 /* UnityAnalyticsWrapper.m */; };
		30A38D0D3F66E28D9A919A5E /* NativeDialogsPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 73200F4F052CF846BE8AEF82 /* NativeDialogsPlugin.m */; };
		31801B2E1CF728C094FFDB4D /* UnityAdsLoadListener.h in Headers */ = {isa = PBXBuildFile; fileRef = C7004552E7186E16DD3B3A69 /* UnityAdsLoadListener.h */; };
		34E040FE918BD0A6C8C0E3D6 /* UnityAdsShowListener.h in Headers */ = {isa = PBXBuildFile; fileRef = 94F7DED7298B1330B361D3F2 /* UnityAdsShowListener.h */; };
		37FF245A4DB8B002307C4DA0 /* UnityAdsShowListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 53F0CCEB205A1010D1F75B8A /* UnityAdsShowListener.mm */; };
		3917572A929C5A7612468C8F /* GDTMobBing.m in Sources */ = {isa = PBXBuildFile; fileRef = B8217FFE13E391CF878EB017 /* GDTMobBing.m */; };
		39D5AF21BCD5D72365F18E4B /* GDTSplashAdNetworkConnectorProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C8160F6348A42D9576710E3 /* GDTSplashAdNetworkConnectorProtocol.h */; };
		3C7226497EFF0130435E7042 /* GDTServerSideVerificationOptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2500BFE7D99AC01CA8516A76 /* GDTServerSideVerificationOptions.h */; };
		3D720B7978C6DFDF3C981C34 /* GDTMobInterstitialManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2619CE6330647084F758F15C /* GDTMobInterstitialManager.h */; };
		415674EFD2EC187355BC05B1 /* LaunchScreen-iPhoneLandscape.png in Resources */ = {isa = PBXBuildFile; fileRef = 1E24B93E9D27D99850693BAF /* LaunchScreen-iPhoneLandscape.png */; };
		4240696C7C0EC9B9869B2FA2 /* IAppsTeamIOSUntilBinding.m in Sources */ = {isa = PBXBuildFile; fileRef = BC1E972BAE1F1493505BFCCD /* IAppsTeamIOSUntilBinding.m */; };
		42FD121083A4C70F0876F159 /* GDTBaseAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 0999F02616A40C59D4204935 /* GDTBaseAdNetworkAdapterProtocol.h */; };
		43B93FC0CB598C780D8C28E7 /* GDTMobUPluginUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = C824AEFE49BF546C3D3A94CF /* GDTMobUPluginUtil.h */; };
		475B26F38A3A652C6174529D /* IUnityInterface.h in Headers */ = {isa = PBXBuildFile; fileRef = 679ACDCF6D14CA6FE6F81479 /* IUnityInterface.h */; };
		4B22388811C76AA169252B4B /* ISN_InApp.mm in Sources */ = {isa = PBXBuildFile; fileRef = 91E821D27E6EC7332DE9064A /* ISN_InApp.mm */; };
		4E641CD1A18DDAAB6BDAD632 /* GADTMediumTemplateView.xib in Resources */ = {isa = PBXBuildFile; fileRef = B9A318561A46439EDE55DD9D /* GADTMediumTemplateView.xib */; };
		4F6219C6868658EB1B91886B /* GDTUnifiedNativeAdView.h in Headers */ = {isa = PBXBuildFile; fileRef = 72831D4D6869FD6E7154268C /* GDTUnifiedNativeAdView.h */; };
		52F7727355892063750D8CD0 /* NativeDialogsPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = D7A4EDC694A07687D3E0DA67 /* NativeDialogsPlugin.h */; };
		5623C57617FDCB0800090B9E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		5623C57717FDCB0800090B9E /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 830B5C100E5ED4C100C7819F /* UIKit.framework */; };
		5623C57D17FDCB0900090B9E = {isa = PBXBuildFile; fileRef = 5623C57B17FDCB0900090B9E /* InfoPlist.strings */; };
		5623C57F17FDCB0900090B9E /* Unity_iPhone_Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 5623C57E17FDCB0900090B9E /* Unity_iPhone_Tests.m */; };
		56C56C9817D6015200616839 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 56C56C9717D6015100616839 /* Images.xcassets */; };
		574A8D1BFDF4103B13A6538A /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21ECD27EDF8EB21168769097 /* GameController.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		577641CFC02BF84EC13208C1 /* GADUAdNetworkExtras.h in Headers */ = {isa = PBXBuildFile; fileRef = AF0BD4189D7A5B98C73791B8 /* GADUAdNetworkExtras.h */; };
		59014FD6537F4A3C1D64FA5B /* MBProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = 4A0DA52C8484E22887233B08 /* MBProgressHUD.h */; };
		595C5BC77ED98A0781E72599 /* GDTRewardVideoAdNetworkConnectorProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = BA0580A085177C256385613D /* GDTRewardVideoAdNetworkConnectorProtocol.h */; };
		5D8E0920DEE1C188F000CB6E /* GDTSplashAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 4BA967BA7CC936378ADB4F4D /* GDTSplashAdNetworkAdapterProtocol.h */; };
		61EE4AFDB2A4DFF0292A2EEA /* UnityAdsInitializationListener.h in Headers */ = {isa = PBXBuildFile; fileRef = EF1F31341EF3BB1E55473A8D /* UnityAdsInitializationListener.h */; };
		63ACF7178776C648B96974EB /* baselib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7CD8D832DF5E49F3229DE515 /* baselib.a */; };
		6429802090B2874D35AAF9EA /* GDTUnifiedBannerAdNetworkConnectorProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = E97C06AF3FC01BD0933580D7 /* GDTUnifiedBannerAdNetworkConnectorProtocol.h */; };
		69373607DCBB9849DF0D23F5 /* GDTSDKConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = A0EAFA894ED09EBB217A485C /* GDTSDKConfig.h */; };
		69F884C2CB9A942E00E8930F /* ISN_NativeCore.h in Headers */ = {isa = PBXBuildFile; fileRef = 7FE7BDC9191C134A46FF908E /* ISN_NativeCore.h */; };
		6E97D27850343C6B1E39F1BE /* unity_services_locale.mm in Sources */ = {isa = PBXBuildFile; fileRef = FD6E23C902477A19B0E251BF /* unity_services_locale.mm */; };
		6EEEC83C54E5CF29828775FA /* GDTNativeExpressAdViewAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 94AD788D56925343C4E55E99 /* GDTNativeExpressAdViewAdapterProtocol.h */; };
		6FD04192B675A7C7EF4EC94F /* UnityEarlyTransactionObserver.mm in Sources */ = {isa = PBXBuildFile; fileRef = 5E7DE52DB56AC9BD83B13EEB /* UnityEarlyTransactionObserver.mm */; };
		763ED5D885EABF761B2FE262 /* LaunchScreen-iPad.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A6444ABDD2799CC877EBAF7E /* LaunchScreen-iPad.storyboard */; };
		7661C37E9B58E3F45481F9EF /* RSSecrets.h in Headers */ = {isa = PBXBuildFile; fileRef = 348EAB3637E0B7146935FAEE /* RSSecrets.h */; };
		7859A8900D73BD9B8296F668 /* GDTAdParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 567AABCBAFF94733CABF9ED7 /* GDTAdParams.h */; };
		7AB6FF91721F38E447B8D517 /* GDTSDKDefines.h in Headers */ = {isa = PBXBuildFile; fileRef = BC2B619F8DB8F40FD73613B7 /* GDTSDKDefines.h */; };
		7BAE7832F743002BBDA5C0C1 /* VolumeIOSPlugin.mm in Sources */ = {isa = PBXBuildFile; fileRef = AB3BD440282B7832E28896A1 /* VolumeIOSPlugin.mm */; };
		7F4E05AB2717219200A2CBE4 /* libGameAssembly.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */; };
		826E40C67FAA509B29A0EFA3 /* IUnityGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = 30347B5D7991A325732A4F10 /* IUnityGraphics.h */; };
		82DB471255F4AE4DDB0B70B3 /* GDTUnifiedInterstitialAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FF625858089E21BDF0720A0 /* GDTUnifiedInterstitialAdNetworkAdapterProtocol.h */; };
		848B693C6E00A1FED85E262F /* IAppsTeamIOSUnitl.h in Headers */ = {isa = PBXBuildFile; fileRef = 16814697D32894BA4CF74524 /* IAppsTeamIOSUnitl.h */; };
		862C245020AEC7AC006FB4AD /* UnityWebRequest.mm in Sources */ = {isa = PBXBuildFile; fileRef = 862C244F20AEC7AC006FB4AD /* UnityWebRequest.mm */; };
		87E9527F8F67B766BCA27955 /* Pods_UnityFramework.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 766DE0A8CD087FAEDF341E93 /* Pods_UnityFramework.framework */; };
		87FDF0A3B0DACAD8C27B6FAA /* libUniWebView.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B7DF838F39D5637124C42C27 /* libUniWebView.a */; };
		8870BB19A7F961025BF117D7 /* GDTUnifiedInterstitialAd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2A03AC66624754249B56C9B3 /* GDTUnifiedInterstitialAd.h */; };
		8A20382D213D4B3C005E6C56 /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8A20382C213D4B3C005E6C56 /* AVKit.framework */; };
		8A215703245064AA00E582EB /* NoGraphicsHelper.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A2BC6DE245061EE00C7C97D /* NoGraphicsHelper.mm */; };
		8A3831B5246956AA00CD74FD /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8A3831B4246956AA00CD74FD /* Metal.framework */; };
		8BEDFA3929C88F86007F26D7 /* UnityViewControllerBase+visionOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 8BEDFA3729C88F86007F26D7 /* UnityViewControllerBase+visionOS.h */; };
		8BEDFA3A29C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8BEDFA3829C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm */; };
		8D4B7957F6DCEA85F99DF883 /* ISN_NativeCore.mm in Sources */ = {isa = PBXBuildFile; fileRef = 4B12CB0E9215CEBA025CD056 /* ISN_NativeCore.mm */; };
		90358174B2325C48A84899EA /* GDTLogoView.h in Headers */ = {isa = PBXBuildFile; fileRef = 615F1EC7832D2B8CB2005C27 /* GDTLogoView.h */; };
		91C1EB5F348833EAD02A7E9A /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F2E4BD281D8D2761198E3AA0 /* PrivacyInfo.xcprivacy */; };
		9703160D4A0AEEA22D60F088 /* GDTUnifiedNativeAdNetworkConnectorProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 5CD37B9613BB23D856E08103 /* GDTUnifiedNativeAdNetworkConnectorProtocol.h */; };
		98370FC6F17417DE352D8849 /* GDTNativeExpressAd.h in Headers */ = {isa = PBXBuildFile; fileRef = 5A3A145D9786ACFE6D088E06 /* GDTNativeExpressAd.h */; };
		987F3D7761A3B7A884300CED /* Pods_Unity_iPhone.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E1495550DA3888ACBD5B8B0 /* Pods_Unity_iPhone.framework */; };
		99AF3D66DF97143A5F374744 /* GDTNativeExpressAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 83BDD9D20D2D7764D399F567 /* GDTNativeExpressAdNetworkAdapterProtocol.h */; };
		9D0A618B21BFE7F30094DC33 /* libiconv.2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 9D0A618A21BFE7F30094DC33 /* libiconv.2.tbd */; };
		9D25ABA1213FB47800354C27 /* UnityFramework.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D25AB9F213FB47800354C27 /* UnityFramework.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9D25ABA5213FB47800354C27 /* UnityFramework.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 9D25AB9D213FB47800354C27 /* UnityFramework.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		9D25ABAD213FB6B700354C27 /* AppDelegateListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AF7755F17997D1300341121 /* AppDelegateListener.mm */; };
		9D25ABAE213FB6BA00354C27 /* LifeCycleListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A292A9817992CE100409BA4 /* LifeCycleListener.mm */; };
		9D25ABAF213FB6BE00354C27 /* RenderPluginDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A5C1491174E662D0006EB36 /* RenderPluginDelegate.mm */; };
		9D25ABB0213FB6C400354C27 /* UnityViewControllerListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = AAFE69D119F187C200638316 /* UnityViewControllerListener.mm */; };
		9D25ABB1213FB6D600354C27 /* UnityView+Keyboard.mm in Sources */ = {isa = PBXBuildFile; fileRef = 85E5623620F4F4D1001DFEF6 /* UnityView+Keyboard.mm */; };
		9D25ABB2213FB6E300354C27 /* ActivityIndicator.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A9FCB121617295F00C05364 /* ActivityIndicator.mm */; };
		9D25ABB3213FB6E300354C27 /* Keyboard.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A142DC51636943E00DD87CA /* Keyboard.mm */; };
		9D25ABB4213FB6E300354C27 /* OrientationSupport.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AC71EC319E7FBA90027502F /* OrientationSupport.mm */; };
		9D25ABB6213FB6E300354C27 /* StoreReview.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E090A331F27884B0077B28D /* StoreReview.m */; };
		9D25ABB7213FB6E300354C27 /* UnityAppController+ViewHandling.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A4815C017A287D2003FBFD5 /* UnityAppController+ViewHandling.mm */; };
		9D25ABB8213FB6E300354C27 /* UnityView.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A851BA616FB2F6D00E911DB /* UnityView.mm */; };
		9D25ABB9213FB6E300354C27 /* UnityView+iOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A7939FF1ED43EE100B44EF1 /* UnityView+iOS.mm */; };
		9D25ABBA213FB6E300354C27 /* UnityView+tvOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A793A011ED43EE100B44EF1 /* UnityView+tvOS.mm */; };
		9D25ABBB213FB6E300354C27 /* UnityViewControllerBase.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A7939FC1ED2F53200B44EF1 /* UnityViewControllerBase.mm */; };
		9D25ABBC213FB6E300354C27 /* UnityViewControllerBase+iOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A793A031ED43EE100B44EF1 /* UnityViewControllerBase+iOS.mm */; };
		9D25ABBD213FB6E300354C27 /* UnityViewControllerBase+tvOS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A793A051ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.mm */; };
		9D25ABBE213FB6F800354C27 /* OnDemandResources.mm in Sources */ = {isa = PBXBuildFile; fileRef = FC0B20A11B7A4F0B00FDFC55 /* OnDemandResources.mm */; };
		9D25ABBF213FB6F800354C27 /* AVCapture.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AC74A9419B47FEF00019D38 /* AVCapture.mm */; };
		9D25ABC0213FB6F800354C27 /* CameraCapture.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8ADCE38A19C87177006F04F6 /* CameraCapture.mm */; };
		9D25ABC1213FB6F800354C27 /* CMVideoSampling.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A2AA93416E0978D001FB470 /* CMVideoSampling.mm */; };
		9D25ABC2213FB6F800354C27 /* CVTextureCache.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A367F5A16A6D36F0012ED11 /* CVTextureCache.mm */; };
		9D25ABC3213FB6F800354C27 /* DeviceSettings.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8ACB801B177081D4005D0019 /* DeviceSettings.mm */; };
		9D25ABC4213FB6F800354C27 /* DisplayManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A5E0B9016849D1800CBB6FE /* DisplayManager.mm */; };
		9D25ABC6213FB6F800354C27 /* Filesystem.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A25E6D118D767E20006A227 /* Filesystem.mm */; };
		9D25ABC8213FB6F800354C27 /* InternalProfiler.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 8A6720A319EEB905006C92E0 /* InternalProfiler.cpp */; };
		9D25ABC9213FB6F800354C27 /* MetalHelper.mm in Sources */ = {isa = PBXBuildFile; fileRef = 1859EA9A19214E7B0022C3D3 /* MetalHelper.mm */; };
		9D25ABCA213FB6F800354C27 /* FullScreenVideoPlayer.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A16150B1A8E4362006FA788 /* FullScreenVideoPlayer.mm */; };
		9D25ABCB213FB6F800354C27 /* UnityReplayKit.mm in Sources */ = {isa = PBXBuildFile; fileRef = 84DC28F51C5137FE00BC67D7 /* UnityReplayKit.mm */; };
		9D25ABCC213FB6F800354C27 /* UnityReplayKit_Scripting.mm in Sources */ = {isa = PBXBuildFile; fileRef = 848031E01C5160D700FCEAB7 /* UnityReplayKit_Scripting.mm */; };
		9D25ABCD213FB6F800354C27 /* VideoPlayer.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AB3CB3D16D390BB00697AD5 /* VideoPlayer.mm */; };
		9D25ABE0213FB76500354C27 /* CrashReporter.mm in Sources */ = {isa = PBXBuildFile; fileRef = FC85CCB916C3ED8000BAF7C7 /* CrashReporter.mm */; };
		9D25ABE1213FB76500354C27 /* iPhone_Sensors.mm in Sources */ = {isa = PBXBuildFile; fileRef = 56DBF99C15E3CDC9007A4A8D /* iPhone_Sensors.mm */; };
		9D25ABE3213FB76500354C27 /* UnityAppController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A851BA916FB3AD000E911DB /* UnityAppController.mm */; };
		9D25ABE4213FB76500354C27 /* UnityAppController+Rendering.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8AA5D80117ABE9AF007B9910 /* UnityAppController+Rendering.mm */; };
		9D25ABE5213FB76500354C27 /* UnityAppController+UnityInterface.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A8D90D91A274A7800456C4E /* UnityAppController+UnityInterface.mm */; };
		9D25ABE6213FB7C100354C27 /* RegisterFeatures.cpp in Sources */ = {isa = PBXBuildFile; fileRef = AAC3E38B1A68945900F6174A /* RegisterFeatures.cpp */; };
		9D25ABE9213FB7CC00354C27 /* Il2CppOptions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 03F528621B447098000F4FB8 /* Il2CppOptions.cpp */; };
		9D690CCF21BFD341005026B1 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5BAD78601F2B5A59006103DE /* Security.framework */; };
		9D690CD021BFD349005026B1 /* MediaToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 960391211D6CE46E003BF157 /* MediaToolbox.framework */; };
		9D690CD221BFD36C005026B1 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AA5D99861AFAD3C800B27605 /* CoreText.framework */; };
		9D690CD321BFD376005026B1 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8358D1B70ED1CC3700E3A684 /* AudioToolbox.framework */; };
		9D690CD421BFD37E005026B1 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F36C11013C5C673007FBDD9 /* AVFoundation.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		9D690CD521BFD388005026B1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56FD43950ED4745200FE3770 /* CFNetwork.framework */; };
		9D690CD621BFD391005026B1 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56B7959A1442E0F20026B3DD /* CoreGraphics.framework */; };
		9D690CD721BFD39D005026B1 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F36C10E13C5C673007FBDD9 /* CoreMedia.framework */; };
		9D690CD821BFD3A5005026B1 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56B795C11442E1100026B3DD /* CoreMotion.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		9D690CD921BFD3AC005026B1 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7F36C10F13C5C673007FBDD9 /* CoreVideo.framework */; };
		9D690CDA21BFD3B5005026B1 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		9D690CDB21BFD3BF005026B1 /* OpenAL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 83B2574B0E63022200468741 /* OpenAL.framework */; };
		9D690CDD21BFD3D0005026B1 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 83B2570A0E62FF8A00468741 /* QuartzCore.framework */; };
		9D690CDE21BFD3D9005026B1 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 56BCBA380FCF049A0030C3B2 /* SystemConfiguration.framework */; };
		9D690CDF21BFD3E3005026B1 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 830B5C100E5ED4C100C7819F /* UIKit.framework */; };
		9D9DE4EA221D84E60049D9A1 /* Data in Resources */ = {isa = PBXBuildFile; fileRef = AA31BF961B55660D0013FB1B /* Data */; };
		9DA3B0442174CB96001678C7 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = 9DA3B0432174CB96001678C7 /* main.mm */; };
		9DC67E8521CBBEBB005F9FA1 /* UnityAppController.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A851BA816FB3AD000E911DB /* UnityAppController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8621CBBEC7005F9FA1 /* UndefinePlatforms.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D16CD8021C938B300DD46C0 /* UndefinePlatforms.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8721CBBEC7005F9FA1 /* RedefinePlatforms.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D16CD8121C938BB00DD46C0 /* RedefinePlatforms.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8821CBBED5005F9FA1 /* RenderPluginDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A5C1490174E662D0006EB36 /* RenderPluginDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DC67E8921CBBEDF005F9FA1 /* LifeCycleListener.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A292A9717992CE100409BA4 /* LifeCycleListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9DFA7F9D21410F2E00C2880E /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = D82DCFBB0E8000A5005D6AD8 /* main.mm */; };
		9E277B346B541C2D072C5489 /* IUnityGraphicsMetal.h in Headers */ = {isa = PBXBuildFile; fileRef = 894C1B251937BE1BC2B4D36C /* IUnityGraphicsMetal.h */; };
		9FCC42758212E45AD1BBDF9A /* Dummy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3E1B402ABB1754FC384CBC29 /* Dummy.swift */; };
		A16D22C39C8B2731B13498AF /* GDTMobManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = A9860406B2D3274C13FA2227 /* GDTMobManager.mm */; };
		A207889197A446B92EBEE772 /* UnityPurchasing.m in Sources */ = {isa = PBXBuildFile; fileRef = 3B5211DE4BADE84A27EFC97A /* UnityPurchasing.m */; };
		A2801FBBBD09C973F2D56DCC /* GADTSmallTemplateView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 95579700ADBEB4B7D5EBFB8A /* GADTSmallTemplateView.xib */; };
		A57E0644BB053CF3D7CEAEE4 /* GDTMediaView.h in Headers */ = {isa = PBXBuildFile; fileRef = 8224070CACC22FB63E254585 /* GDTMediaView.h */; };
		A63BDA6A5B55E52118FF67F3 /* GDTNativeExpressAdView.h in Headers */ = {isa = PBXBuildFile; fileRef = 9BF21BC9FDBD95DE7F910C71 /* GDTNativeExpressAdView.h */; };
		A65F336D356D94E457582832 /* RSSecrets.m in Sources */ = {isa = PBXBuildFile; fileRef = 715BCED89CFA351F61DC42B9 /* RSSecrets.m */; };
		A7208CB0DBEF6C66034C47FE /* MBProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = A5B4D102DE21CCAA54308C32 /* MBProgressHUD.m */; };
		AF2786F2943EBE0A756F21CC /* NSUserDefaults.mm in Sources */ = {isa = PBXBuildFile; fileRef = 5D2B6D2D12B065A216EB6E1F /* NSUserDefaults.mm */; };
		B20A79306CC31F7F33C97C34 /* libGDTMobSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5C57731C0A13EC17F5128874 /* libGDTMobSDK.a */; };
		B60CD3BA6EEA3D94C738BE63 /* GDTAdProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 161CEB4CFA7E0007CC483626 /* GDTAdProtocol.h */; };
		BD28D362F6E9BB0C5AB19AB3 /* GDTMobManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 3A1E61D20C1CA4561B648BDD /* GDTMobManager.h */; };
		BE525877766DDC18D1FD9C99 /* GDTServerBiddingResult.h in Headers */ = {isa = PBXBuildFile; fileRef = FD168AB2453FC7718218F844 /* GDTServerBiddingResult.h */; };
		BEA37D3A3BD93FA7472287D0 /* IAppsTeamIOSUnitl.mm in Sources */ = {isa = PBXBuildFile; fileRef = DA514D93A8CEF6AF3DEF3121 /* IAppsTeamIOSUnitl.mm */; };
		BEE3E21D322649C83B0DD4DC /* LaunchScreen-iPhone.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 5A3A1B4C3FB1FC90023B942E /* LaunchScreen-iPhone.storyboard */; };
		C1FB2DD88FDDFC9C8790090F /* GDTUnifiedBannerView.h in Headers */ = {isa = PBXBuildFile; fileRef = FE1C49F2D96DA6C9DFE1AB2B /* GDTUnifiedBannerView.h */; };
		C3967C465EF23939E44D6B54 /* UnityPurchasing.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C95D7846077093786C47710 /* UnityPurchasing.h */; };
		C430A98EA348F937212F7C5F /* UnityAdsUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 99334BA41D46C76F13A39E7C /* UnityAdsUtilities.m */; };
		C4BB858072D48F175D4F9386 /* UnityEarlyTransactionObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = 47169E11833C56E7D860700C /* UnityEarlyTransactionObserver.h */; };
		C7587F9DCADED12FAD5AA7B7 /* UnityAdsInitializationListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 23EAFE12569624894DA33D7B /* UnityAdsInitializationListener.mm */; };
		C8944D4B3B13BCD6C3C8C91D /* GDTUnifiedNativeAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 4397B2765ACE4841ADABF5AF /* GDTUnifiedNativeAdNetworkAdapterProtocol.h */; };
		CD08ECCB65CEC6CD4D672F55 /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 889E696927F9303A40F5E3F4 /* Reachability.m */; };
		CF2C091D00127205326668D3 /* GDTRewardVideoAd.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D8D2D85D4E6EE2B82F9C0D1 /* GDTRewardVideoAd.h */; };
		D0289961826D5AE404C69D0D /* UnityAdsUnityWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DE28CCD862A989C5889A8E5 /* UnityAdsUnityWrapper.m */; };
		D085C06443DB1B1B5C6637B6 /* LaunchScreen-iPad.png in Resources */ = {isa = PBXBuildFile; fileRef = D556450C3DBEECE838996A3D /* LaunchScreen-iPad.png */; };
		D3A2C08A2A0F557085B06193 /* GDTMobInterstitialManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B36C010DDFE4675872BFA156 /* GDTMobInterstitialManager.mm */; };
		D475686CAFD27A0DA6418053 /* GDTMobInterstitial.h in Headers */ = {isa = PBXBuildFile; fileRef = 02CBAEF2E6358F55B77CF190 /* GDTMobInterstitial.h */; };
		D88E9DC890A98004B2FC7F06 /* GDTNativeExpressAdNetworkConnectorProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = FF68204794520066FE5268D8 /* GDTNativeExpressAdNetworkConnectorProtocol.h */; };
		DAAF19EECF4756E705B67680 /* GDTRewardVideoAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 51C9F166688B5AF41217532F /* GDTRewardVideoAdNetworkAdapterProtocol.h */; };
		DE6F4DDF93991EC95AE6D98A /* UnityAdvertisement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65A78184B609DFC82D3D4939 /* UnityAdvertisement.swift */; };
		E635643506C918F335FC373C /* UnityAdsLoadListener.mm in Sources */ = {isa = PBXBuildFile; fileRef = 78DCC5B0D7B57EB22AB67E50 /* UnityAdsLoadListener.mm */; };
		E6BF764B13DDB30BE1080A09 /* GDTLoadAdParams.h in Headers */ = {isa = PBXBuildFile; fileRef = C6120AAF314BB32048EF255D /* GDTLoadAdParams.h */; };
		E77D0542B32B17BD05428C14 /* UnityAdsUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 7CB7FE9F812CDE7E67229FC1 /* UnityAdsUtilities.h */; };
		E94E990D810869BE1CEEAF3D /* GDTMobUTypes.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E192277BBADB145533A1FCC /* GDTMobUTypes.h */; };
		EF5C426DB71727665936F6DE /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C801E7F3CBAB00070AC52BC6 /* StoreKit.framework */; };
		F5C03E9DD3BF40D8EC0F4404 /* LaunchScreen-iPhonePortrait.png in Resources */ = {isa = PBXBuildFile; fileRef = 681AA63C19788ACA381FB8C1 /* LaunchScreen-iPhonePortrait.png */; };
		F981FE9AF0502B4560EDD3A4 /* UnityAdsNativeObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 35326249479B106D3D50D6EB /* UnityAdsNativeObject.m */; };
		FE7304CADFC302976BEBCAF2 /* GDTUnifiedBannerAdNetworkAdapterProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 709BAA32BB8A406717625013 /* GDTUnifiedBannerAdNetworkAdapterProtocol.h */; };
		FEE05A90163B36DF45DECF77 /* UnityBannerUnityWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = AF3F17F76B73432AF11E9DA4 /* UnityBannerUnityWrapper.m */; };
		FEF4673A8DD14865BB898CF1 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C801E7F3CBAB00070AC52BC6 /* StoreKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		5623C58117FDCB0900090B9E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 29B97313FDCFA39411CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1D6058900D05DD3D006BFB54;
			remoteInfo = "Unity-iPhone";
		};
		7F401A4C272AC77C005AF450 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 29B97313FDCFA39411CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7F4E059F2717216D00A2CBE4;
			remoteInfo = GameAssembly;
		};
		9D25ABA2213FB47800354C27 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 29B97313FDCFA39411CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9D25AB9C213FB47800354C27;
			remoteInfo = UnityFramework;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7F4E059E2717216D00A2CBE4 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83D0C1FD0E6C8D7700EBCE5D /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25ABAB213FB47800354C27 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				9D25ABA5213FB47800354C27 /* UnityFramework.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0037EF7247D13977D5A576B2 /* Unity.Services.Core.Telemetry.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Telemetry.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry.cpp; sourceTree = SOURCE_ROOT; };
		004B7ED377C245FA07EA4CA7 /* Unity.TextMeshPro_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.TextMeshPro_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0084A6F53FDC53D8C88D04BC /* mscorlib__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp; sourceTree = SOURCE_ROOT; };
		01542E4A7351BF537D5F9A2D /* UnityEngine.TextCoreTextEngineModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp; sourceTree = SOURCE_ROOT; };
		0261C17A7919F2AE5BFC8D0A /* Assembly-CSharp__52.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__52.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__52.cpp"; sourceTree = SOURCE_ROOT; };
		02CBAEF2E6358F55B77CF190 /* GDTMobInterstitial.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTMobInterstitial.h; path = Libraries/Plugins/iOS/GdtMob/GDTMobInterstitial.h; sourceTree = SOURCE_ROOT; };
		037725F928F56B860897F0C1 /* FairyGUI_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = FairyGUI_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI_CodeGen.c; sourceTree = SOURCE_ROOT; };
		03A87A26A63177A70A5AC67E /* Assembly-CSharp__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__15.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__15.cpp"; sourceTree = SOURCE_ROOT; };
		03F528621B447098000F4FB8 /* Il2CppOptions.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Il2CppOptions.cpp; sourceTree = "<group>"; };
		043632D66EB0B7E5F3272285 /* System.Xml__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp; sourceTree = SOURCE_ROOT; };
		0464E0DCE4A0FC27BFF43BB4 /* System.Xml.Linq.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml.Linq.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp; sourceTree = SOURCE_ROOT; };
		058D9CAFDB588D59044A2B25 /* System__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__4.cpp; sourceTree = SOURCE_ROOT; };
		0625BA89AA0F71807DF4285A /* Assembly-CSharp__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__2.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__2.cpp"; sourceTree = SOURCE_ROOT; };
		06307F0DDC88536113EE103A /* GDTMobUPluginUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GDTMobUPluginUtil.m; path = Libraries/Plugins/iOS/GdtMob/GDTMobUPluginUtil.m; sourceTree = SOURCE_ROOT; };
		06D2F6EE203E616EFFD31304 /* GenericMethods__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__11.cpp; sourceTree = SOURCE_ROOT; };
		07CA36577698941D4EE4BC70 /* Assembly-CSharp__34.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__34.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__34.cpp"; sourceTree = SOURCE_ROOT; };
		07F77C1BA780C0E1C591423E /* Unity.TextMeshPro__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp; sourceTree = SOURCE_ROOT; };
		086221B24C5434B49FF5CF1B /* ProCamera2D.Examples_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = ProCamera2D.Examples_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Examples_CodeGen.c; sourceTree = SOURCE_ROOT; };
		08D218E34CD3080971C43706 /* System.Xml__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp; sourceTree = SOURCE_ROOT; };
		08D9ECBDADDA4DD82C86134D /* Assembly-CSharp__42.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__42.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__42.cpp"; sourceTree = SOURCE_ROOT; };
		0999F02616A40C59D4204935 /* GDTBaseAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTBaseAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTBaseAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		0A99F41A6E79CC9C6BE2D738 /* Assembly-CSharp__24.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__24.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__24.cpp"; sourceTree = SOURCE_ROOT; };
		0B698999CFCA23787EA976B2 /* Assembly-CSharp__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__8.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__8.cpp"; sourceTree = SOURCE_ROOT; };
		0B7A8CEE2ED092ADC2009F10 /* Generics__61.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__61.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__61.cpp; sourceTree = SOURCE_ROOT; };
		0BAFCF00AED9C44606DB1581 /* GoogleMobileAds.Ump.iOS_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = GoogleMobileAds.Ump.iOS_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump.iOS_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0D31AAF3C6C0CC11D285C738 /* UnityEngine.CoreModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp; sourceTree = SOURCE_ROOT; };
		0D74EFEA392C0E65155F7A96 /* UnityEngine.PropertiesModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PropertiesModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		0E3F3917340B2949C8B8E209 /* Il2CppMetadataUsage.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppMetadataUsage.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c; sourceTree = SOURCE_ROOT; };
		0E687189B4541BF06DADF853 /* Generics__24.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__24.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__24.cpp; sourceTree = SOURCE_ROOT; };
		0E9D8AC8B3C7AE96482477D4 /* Generics__59.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__59.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__59.cpp; sourceTree = SOURCE_ROOT; };
		0F1F382C4530E826AE63D9D6 /* UnityEngine.UIElementsModule__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp; sourceTree = SOURCE_ROOT; };
		0F795415320EF5804B496AD2 /* Il2CppInvokerTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppInvokerTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp; sourceTree = SOURCE_ROOT; };
		0FA54142F83E19F3610B1991 /* Unity.TextMeshPro__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp; sourceTree = SOURCE_ROOT; };
		0FD8E595BA80129EA8DD43F4 /* Generics__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__16.cpp; sourceTree = SOURCE_ROOT; };
		0FF38D860993A4461BE3D1D6 /* UnityEngine.ImageConversionModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ImageConversionModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule.cpp; sourceTree = SOURCE_ROOT; };
		1128E095D2864E4B88CBF20F /* Generics__58.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__58.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__58.cpp; sourceTree = SOURCE_ROOT; };
		11856605578FD8270FB7624A /* Generics__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__10.cpp; sourceTree = SOURCE_ROOT; };
		14057A4CB8A9CA9F599A65F9 /* FairyGUI__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__4.cpp; sourceTree = SOURCE_ROOT; };
		144481ABCCC0803449B7F050 /* GoogleMobileAds.Ump_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = GoogleMobileAds.Ump_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump_CodeGen.c; sourceTree = SOURCE_ROOT; };
		1474DA4D7B44DB6E68618883 /* Generics__72.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__72.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__72.cpp; sourceTree = SOURCE_ROOT; };
		1492999617EA37FB6D4727C7 /* Assembly-CSharp__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__16.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__16.cpp"; sourceTree = SOURCE_ROOT; };
		1562360321C1A01A12D225BC /* UnityEngine.SharedInternalsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SharedInternalsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp; sourceTree = SOURCE_ROOT; };
		15A19AFEA90DC0B5F336605F /* UnityEngine.UIElementsModule__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp; sourceTree = SOURCE_ROOT; };
		15D71EBFA9562B150B2E69B7 /* Pods-Unity-iPhone.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Unity-iPhone.release.xcconfig"; path = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.release.xcconfig"; sourceTree = "<group>"; };
		161CEB4CFA7E0007CC483626 /* GDTAdProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTAdProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTAdProtocol.h; sourceTree = SOURCE_ROOT; };
		162968812633BCE3D7FB740D /* Unity.Services.Core.Telemetry_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Telemetry_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry_CodeGen.c; sourceTree = SOURCE_ROOT; };
		16814697D32894BA4CF74524 /* IAppsTeamIOSUnitl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IAppsTeamIOSUnitl.h; path = Libraries/Plugins/iOS/IApps/IAppsTeamIOSUnitl.h; sourceTree = SOURCE_ROOT; };
		16A72414295A2B23C5F331A7 /* Il2CppCCalculateTypeValues1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateTypeValues1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp; sourceTree = SOURCE_ROOT; };
		16E144C6C0B3E8367F911BA8 /* System.Xml_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Xml_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c; sourceTree = SOURCE_ROOT; };
		1701016BB5DA32953B06ADB5 /* UnityEngine_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c; sourceTree = SOURCE_ROOT; };
		17A88A24B72C4E31367418A7 /* Assembly-CSharp__19.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__19.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__19.cpp"; sourceTree = SOURCE_ROOT; };
		1859EA9A19214E7B0022C3D3 /* MetalHelper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = MetalHelper.mm; sourceTree = "<group>"; };
		188198725C11AA1A9BB3A8BF /* Assembly-CSharp__47.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__47.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__47.cpp"; sourceTree = SOURCE_ROOT; };
		18C2DA2269DDF3862E09DE6E /* Generics__36.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__36.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__36.cpp; sourceTree = SOURCE_ROOT; };
		1972A87589EFBAF4986775C2 /* Generics__65.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__65.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__65.cpp; sourceTree = SOURCE_ROOT; };
		19CC4FC0E5C5AFF6FF9E64FA /* System__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__11.cpp; sourceTree = SOURCE_ROOT; };
		1B1F61655F05BA3F7ED2CC6A /* DOTweenPro_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = DOTweenPro_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/DOTweenPro_CodeGen.c; sourceTree = SOURCE_ROOT; };
		1B8A50CD128C437C997726BB /* HOTween__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = HOTween__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/HOTween__1.cpp; sourceTree = SOURCE_ROOT; };
		1B92F201E0C11B4944DF0F0B /* System.Runtime.Serialization.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Runtime.Serialization.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp; sourceTree = SOURCE_ROOT; };
		1BAA14B679EE49250AFB248E /* CustomAppController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = CustomAppController.mm; path = Libraries/Plugins/iOS/IApps/CustomAppController.mm; sourceTree = SOURCE_ROOT; };
		1C3F27E9009F948E3EC03458 /* System.Xml__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp; sourceTree = SOURCE_ROOT; };
		1D30AB110D05D00D00671497 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		1D4F5F3EC41EFCB9F59CC481 /* Unity.Services.Core.Internal_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Internal_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal_CodeGen.c; sourceTree = SOURCE_ROOT; };
		1D6058910D05DD3D006BFB54 /* EduApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = EduApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1DC31C363AA49E88C15788BB /* mscorlib__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp; sourceTree = SOURCE_ROOT; };
		1DD696E89C6A943954768877 /* Il2CppGenericInstDefinitions.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericInstDefinitions.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c; sourceTree = SOURCE_ROOT; };
		1E24B93E9D27D99850693BAF /* LaunchScreen-iPhoneLandscape.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "LaunchScreen-iPhoneLandscape.png"; sourceTree = SOURCE_ROOT; };
		1EF6732A52CD49B602825AEB /* Il2CppCCalculateTypeValues.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateTypeValues.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp; sourceTree = SOURCE_ROOT; };
		1F951416510E376441DD5ABD /* Assembly-CSharp__18.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__18.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__18.cpp"; sourceTree = SOURCE_ROOT; };
		215B46B0AE4EE3D4B0F67064 /* Unity.Services.Analytics_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Analytics_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics_CodeGen.c; sourceTree = SOURCE_ROOT; };
		218A5F5892B6E72316C9FFDA /* UnityEngine.PropertiesModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.PropertiesModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp; sourceTree = SOURCE_ROOT; };
		21ECD27EDF8EB21168769097 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = System/Library/Frameworks/GameController.framework; sourceTree = SDKROOT; };
		223EBC6A186ED13AA8BAEDBA /* Assembly-CSharp__23.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__23.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__23.cpp"; sourceTree = SOURCE_ROOT; };
		2262E7A75098C5BC7A54BE96 /* Generics__54.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__54.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__54.cpp; sourceTree = SOURCE_ROOT; };
		2274C9C05B1B4B06D30C0211 /* System.Data__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__3.cpp; sourceTree = SOURCE_ROOT; };
		23225D541B6C1620CBF548E2 /* UnityEngine.TextCoreTextEngineModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp; sourceTree = SOURCE_ROOT; };
		23772533C9AE112A1A30D5AA /* UnityAds.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UnityAds.framework; path = Frameworks/com.unity.ads/Plugins/iOS/UnityAds.framework; sourceTree = SOURCE_ROOT; };
		2380B52F90A1138DCA07F2BF /* GenericMethods__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__5.cpp; sourceTree = SOURCE_ROOT; };
		238B504DA5717798C8149303 /* Il2CppRgctxTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppRgctxTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c; sourceTree = SOURCE_ROOT; };
		23EAFE12569624894DA33D7B /* UnityAdsInitializationListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = UnityAdsInitializationListener.mm; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsInitializationListener.mm; sourceTree = SOURCE_ROOT; };
		23F4A86D1E5E4A13C05B0120 /* GenericMethods__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__14.cpp; sourceTree = SOURCE_ROOT; };
		2479439FAEB0099AB1925F36 /* GenericMethods__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__8.cpp; sourceTree = SOURCE_ROOT; };
		2500BFE7D99AC01CA8516A76 /* GDTServerSideVerificationOptions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTServerSideVerificationOptions.h; path = Libraries/Plugins/iOS/GdtMob/GDTServerSideVerificationOptions.h; sourceTree = SOURCE_ROOT; };
		25711C69564281E1F27185F1 /* Mono.Security.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Mono.Security.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp; sourceTree = SOURCE_ROOT; };
		25EDC3486A64365C2398DAB5 /* Assembly-CSharp__44.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__44.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__44.cpp"; sourceTree = SOURCE_ROOT; };
		2619CE6330647084F758F15C /* GDTMobInterstitialManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTMobInterstitialManager.h; path = Libraries/Plugins/iOS/GdtMob/GDTMobInterstitialManager.h; sourceTree = SOURCE_ROOT; };
		26CA78F1FFFB56AD4EBA84D6 /* Generics__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__15.cpp; sourceTree = SOURCE_ROOT; };
		272CC69ABD46E8615FA1E8A5 /* FairyGUI__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__8.cpp; sourceTree = SOURCE_ROOT; };
		2884252ACE3370EA428CB631 /* Unity.Services.Core.Device.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Device.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device.cpp; sourceTree = SOURCE_ROOT; };
		28C3B8EF48C0EB6F619F326F /* System.Core.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp; sourceTree = SOURCE_ROOT; };
		28F47737911BB66213AD93BA /* Generics__68.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__68.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__68.cpp; sourceTree = SOURCE_ROOT; };
		29218B0B79CD49C8515B939E /* System.Xml__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__10.cpp; sourceTree = SOURCE_ROOT; };
		29DB862887FD80F9544C0F27 /* UnityEngine.CoreModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.CoreModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		2A03AC66624754249B56C9B3 /* GDTUnifiedInterstitialAd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedInterstitialAd.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedInterstitialAd.h; sourceTree = SOURCE_ROOT; };
		2A4E1F27CBC17CFBC9D90381 /* UnityEngine.UIElementsModule__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp; sourceTree = SOURCE_ROOT; };
		2A64BC0FB139C78B893FDCAA /* UnityEngine.UnityAnalyticsCommonModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityAnalyticsCommonModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule.cpp; sourceTree = SOURCE_ROOT; };
		2AAD40450F6ACA5039B33CD7 /* Unity.Services.Core.Device_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Device_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device_CodeGen.c; sourceTree = SOURCE_ROOT; };
		2B422D1DE14C2A8D3F21065A /* UnityEngine.UIElementsModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp; sourceTree = SOURCE_ROOT; };
		2BCFF7AAB07F012FD2E34C5B /* mscorlib__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp; sourceTree = SOURCE_ROOT; };
		2BEDAA3AFB931C1E1C406501 /* mscorlib__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp; sourceTree = SOURCE_ROOT; };
		2C95D7846077093786C47710 /* UnityPurchasing.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UnityPurchasing.h; path = Libraries/com.unity.purchasing/Plugins/UnityPurchasing/iOS/UnityPurchasing.h; sourceTree = SOURCE_ROOT; };
		2D55E3A876DECECA9E2A124C /* System.Configuration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Configuration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp; sourceTree = SOURCE_ROOT; };
		2D5AF8F8E52C508883AD72E2 /* System.Xml__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__7.cpp; sourceTree = SOURCE_ROOT; };
		2DC31A2C6BF0EFF3721FCBBB /* Generics__56.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__56.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__56.cpp; sourceTree = SOURCE_ROOT; };
		2E1495550DA3888ACBD5B8B0 /* Pods_Unity_iPhone.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Unity_iPhone.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2E38E8560CE5D41091E04C4B /* System.Numerics_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Numerics_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Numerics_CodeGen.c; sourceTree = SOURCE_ROOT; };
		2E8E2D02765123AF23FC2A10 /* System.Numerics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Numerics.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp; sourceTree = SOURCE_ROOT; };
		2E9FEEBC77FE9D53FD71E76A /* UnityEngine.UI__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp; sourceTree = SOURCE_ROOT; };
		30347B5D7991A325732A4F10 /* IUnityGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IUnityGraphics.h; path = Classes/Unity/IUnityGraphics.h; sourceTree = SOURCE_ROOT; };
		310F5D7A4EECCB4E3739971F /* GDTUnifiedNativeAd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedNativeAd.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedNativeAd.h; sourceTree = SOURCE_ROOT; };
		317B44F3876EC140C859BDEB /* Generics__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__11.cpp; sourceTree = SOURCE_ROOT; };
		31B87AF94D0FCE4F2238BC4D /* Generics__21.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__21.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__21.cpp; sourceTree = SOURCE_ROOT; };
		322D52AD7129B47862C9AEE0 /* GoogleMobileAds_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = GoogleMobileAds_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds_CodeGen.c; sourceTree = SOURCE_ROOT; };
		325555CE8DEBEC25FFFE051F /* Generics__48.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__48.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__48.cpp; sourceTree = SOURCE_ROOT; };
		334180F2099CA4932596C6A5 /* Unity.Services.Core.Internal.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Internal.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal.cpp; sourceTree = SOURCE_ROOT; };
		3369FBBB1202A2D16B16FA7E /* mscorlib__21.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__21.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp; sourceTree = SOURCE_ROOT; };
		348EAB3637E0B7146935FAEE /* RSSecrets.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = RSSecrets.h; path = Libraries/Plugins/iOS/IApps/RSSecrets.h; sourceTree = SOURCE_ROOT; };
		34B2FF818AC0E4621CBC5379 /* UnityEngine.UIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp; sourceTree = SOURCE_ROOT; };
		35326249479B106D3D50D6EB /* UnityAdsNativeObject.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UnityAdsNativeObject.m; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsNativeObject.m; sourceTree = SOURCE_ROOT; };
		35446C8F021B4551882708E5 /* System__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__1.cpp; sourceTree = SOURCE_ROOT; };
		354C830C3FA3287CFC1D3844 /* UnityEngine.SpriteShapeModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.SpriteShapeModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp; sourceTree = SOURCE_ROOT; };
		358C4B63F8E8BD45D3DC57E3 /* Generics__25.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__25.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__25.cpp; sourceTree = SOURCE_ROOT; };
		359EA6DE6F0ADB889FC61B63 /* Assembly-CSharp_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "Assembly-CSharp_CodeGen.c"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"; sourceTree = SOURCE_ROOT; };
		35BA4E1F3D3749F3A77D35B2 /* UnityEngine.UIElementsModule__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp; sourceTree = SOURCE_ROOT; };
		361242239BA4C93111DF15F3 /* FairyGUI__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__1.cpp; sourceTree = SOURCE_ROOT; };
		368713E01A8699AB23E17224 /* Unity.Services.Analytics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Analytics.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics.cpp; sourceTree = SOURCE_ROOT; };
		36BDE40EFAF15832D01C4E85 /* Generics__49.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__49.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__49.cpp; sourceTree = SOURCE_ROOT; };
		37AC7AC7977FAFF44E7DAA61 /* UnityEngine.TilemapModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TilemapModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		37EEDE6C9967DEE1D5EF5912 /* Generics__18.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__18.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__18.cpp; sourceTree = SOURCE_ROOT; };
		38C36B7E75EBED75318772C6 /* Unity.ResourceManager_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.ResourceManager_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.ResourceManager_CodeGen.c; sourceTree = SOURCE_ROOT; };
		39A4F548F86A869BD5CA86C0 /* Assembly-CSharp__26.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__26.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__26.cpp"; sourceTree = SOURCE_ROOT; };
		3A1BDF828EAFC31251B10CAE /* ProCamera2D.Runtime_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = ProCamera2D.Runtime_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime_CodeGen.c; sourceTree = SOURCE_ROOT; };
		3A1E61D20C1CA4561B648BDD /* GDTMobManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTMobManager.h; path = Libraries/Plugins/iOS/GdtMob/GDTMobManager.h; sourceTree = SOURCE_ROOT; };
		3B5211DE4BADE84A27EFC97A /* UnityPurchasing.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UnityPurchasing.m; path = Libraries/com.unity.purchasing/Plugins/UnityPurchasing/iOS/UnityPurchasing.m; sourceTree = SOURCE_ROOT; };
		3B557CF9358F36A58A8F38FC /* UnityEngine.PhysicsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.PhysicsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		3BF0F548641EA67234EF4822 /* Assembly-CSharp__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__3.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__3.cpp"; sourceTree = SOURCE_ROOT; };
		3C54469E02AE026A630B4244 /* Assembly-CSharp__25.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__25.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__25.cpp"; sourceTree = SOURCE_ROOT; };
		3CC546E182B89988089AC598 /* UnityEngine.UIElementsModule__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp; sourceTree = SOURCE_ROOT; };
		3E1B402ABB1754FC384CBC29 /* Dummy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Dummy.swift; sourceTree = SOURCE_ROOT; };
		3E6AB690991E5D5C419B99F5 /* Newtonsoft.Json__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp; sourceTree = SOURCE_ROOT; };
		3EAD2E0234EACBB68F7569A5 /* Assembly-CSharp__36.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__36.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__36.cpp"; sourceTree = SOURCE_ROOT; };
		40C608E7DF3C3442EDC4FE0B /* DOTween_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = DOTween_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/DOTween_CodeGen.c; sourceTree = SOURCE_ROOT; };
		4114445645D738D6E9CC35B8 /* Assembly-CSharp__17.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__17.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__17.cpp"; sourceTree = SOURCE_ROOT; };
		4117F67ACFF8CBA17CC65251 /* System.Xml__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__15.cpp; sourceTree = SOURCE_ROOT; };
		414991A97FF59CD6675FD9EB /* Newtonsoft.Json__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp; sourceTree = SOURCE_ROOT; };
		41772366CC01AABF3A141A21 /* System.Xml__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp; sourceTree = SOURCE_ROOT; };
		418A53639A9FDF6906C6BFBC /* UnityEngine.CoreModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp; sourceTree = SOURCE_ROOT; };
		41D7183E8E0676D21577C84A /* DOTween__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = DOTween__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/DOTween__1.cpp; sourceTree = SOURCE_ROOT; };
		42E075EB26412806102DDFF7 /* Unity.Services.Core.Threading.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Threading.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading.cpp; sourceTree = SOURCE_ROOT; };
		43289C428F36A64040F01B14 /* mscorlib__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp; sourceTree = SOURCE_ROOT; };
		4397B2765ACE4841ADABF5AF /* GDTUnifiedNativeAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedNativeAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedNativeAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		439D39F1FC43F7B0A6C04FFF /* Generics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics.cpp; sourceTree = SOURCE_ROOT; };
		43B4BE7C58A2AA60D8FEAD26 /* Pods-UnityFramework.releaseforprofiling.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UnityFramework.releaseforprofiling.xcconfig"; path = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.releaseforprofiling.xcconfig"; sourceTree = "<group>"; };
		43DCC1F9F099008314351765 /* Assembly-CSharp__49.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__49.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__49.cpp"; sourceTree = SOURCE_ROOT; };
		444A6E27F397365C367A0E17 /* Assembly-CSharp__21.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__21.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__21.cpp"; sourceTree = SOURCE_ROOT; };
		45600B8C0ADE759ED6CA9C0D /* Unity.Addressables__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Addressables__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Addressables__1.cpp; sourceTree = SOURCE_ROOT; };
		45EFF0285CC84ED02CB30A69 /* UnityEngine.UIElementsModule__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp; sourceTree = SOURCE_ROOT; };
		465EB73EB7CAF8A291ADD317 /* Assembly-CSharp__27.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__27.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__27.cpp"; sourceTree = SOURCE_ROOT; };
		470F4DBFC6CF22AFB4920379 /* mscorlib_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = mscorlib_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c; sourceTree = SOURCE_ROOT; };
		47169E11833C56E7D860700C /* UnityEarlyTransactionObserver.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UnityEarlyTransactionObserver.h; path = Libraries/com.unity.purchasing/Plugins/UnityPurchasing/iOS/UnityEarlyTransactionObserver.h; sourceTree = SOURCE_ROOT; };
		4731C5266AFA863C29647599 /* System__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__8.cpp; sourceTree = SOURCE_ROOT; };
		489B743989EEF3955BDA2C20 /* Generics__23.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__23.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__23.cpp; sourceTree = SOURCE_ROOT; };
		4A0DA52C8484E22887233B08 /* MBProgressHUD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = MBProgressHUD.h; path = Libraries/Plugins/iOS/MBProgressHUD.h; sourceTree = SOURCE_ROOT; };
		4A8BF4F505104D1B8AE63E79 /* Assembly-CSharp__48.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__48.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__48.cpp"; sourceTree = SOURCE_ROOT; };
		4AE6620E3ECDEE30CBB8DF48 /* UnityEngine.UnityWebRequestTextureModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestTextureModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule.cpp; sourceTree = SOURCE_ROOT; };
		4B12CB0E9215CEBA025CD056 /* ISN_NativeCore.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = ISN_NativeCore.mm; path = Libraries/Plugins/iOS/ISN_NativeCore.mm; sourceTree = SOURCE_ROOT; };
		4BA967BA7CC936378ADB4F4D /* GDTSplashAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTSplashAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTSplashAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		4BC1C33C24833176E207546D /* System.Data.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data.cpp; sourceTree = SOURCE_ROOT; };
		4C378673D5DF403FDFC49E5D /* GDTVideoConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTVideoConfig.h; path = Libraries/Plugins/iOS/GdtMob/GDTVideoConfig.h; sourceTree = SOURCE_ROOT; };
		4CF8EE25016BB90ABFD83417 /* System.Core__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__3.cpp; sourceTree = SOURCE_ROOT; };
		4E090A331F27884B0077B28D /* StoreReview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StoreReview.m; sourceTree = "<group>"; };
		4E33F765E8E51E7DFF5D52A6 /* UnityEngine.TextCoreFontEngineModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreFontEngineModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp; sourceTree = SOURCE_ROOT; };
		4ED0B579112CB1E12AEF01F5 /* mscorlib.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp; sourceTree = SOURCE_ROOT; };
		4EEAF035005D81C6A49A7C45 /* UnityEngine.TextCoreTextEngineModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp; sourceTree = SOURCE_ROOT; };
		4F97D266790DB44CD01959EC /* Assembly-CSharp__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__4.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__4.cpp"; sourceTree = SOURCE_ROOT; };
		504F10354AA6664ACC10AE55 /* Assembly-CSharp__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__7.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__7.cpp"; sourceTree = SOURCE_ROOT; };
		51C9F166688B5AF41217532F /* GDTRewardVideoAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTRewardVideoAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTRewardVideoAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		525CDBA21FAADF221B8950A5 /* Generics__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp; sourceTree = SOURCE_ROOT; };
		53584FD960B4B411A167C0F4 /* Pods-Unity-iPhone.releaseforrunning.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Unity-iPhone.releaseforrunning.xcconfig"; path = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.releaseforrunning.xcconfig"; sourceTree = "<group>"; };
		5390DEEE9EABDA6BF3A9E027 /* Unity.Services.Core.Threading_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Threading_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading_CodeGen.c; sourceTree = SOURCE_ROOT; };
		53D50B1E0F71DFEBC9CABD14 /* UnityEngine.UIElementsModule__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp; sourceTree = SOURCE_ROOT; };
		53F0CCEB205A1010D1F75B8A /* UnityAdsShowListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = UnityAdsShowListener.mm; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsShowListener.mm; sourceTree = SOURCE_ROOT; };
		544BB825B8624DFAB4CA9C2C /* UnityEngine.JSONSerializeModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.JSONSerializeModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		54EE3B312E220CA8996C49C7 /* Mono.Security__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Mono.Security__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp; sourceTree = SOURCE_ROOT; };
		56115212599D5E43FF019CAB /* System__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__5.cpp; sourceTree = SOURCE_ROOT; };
		561FE8E0DA0AF097A24473A9 /* System__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__10.cpp; sourceTree = SOURCE_ROOT; };
		5623C57317FDCB0800090B9E /* Unity-iPhone Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Unity-iPhone Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		5623C57A17FDCB0900090B9E /* Unity-iPhone Tests-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Unity-iPhone Tests-Info.plist"; sourceTree = "<group>"; };
		5623C57C17FDCB0900090B9E /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		5623C57E17FDCB0900090B9E /* Unity_iPhone_Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Unity_iPhone_Tests.m; sourceTree = "<group>"; };
		5623C58017FDCB0900090B9E /* Unity-iPhone Tests-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Unity-iPhone Tests-Prefix.pch"; sourceTree = "<group>"; };
		567AABCBAFF94733CABF9ED7 /* GDTAdParams.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTAdParams.h; path = Libraries/Plugins/iOS/GdtMob/GDTAdParams.h; sourceTree = SOURCE_ROOT; };
		5692F3DC0FA9D8E500EBA2F1 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		56B7959A1442E0F20026B3DD /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		56B795C11442E1100026B3DD /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		56BCBA380FCF049A0030C3B2 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		56C56C9717D6015100616839 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = "Unity-iPhone/Images.xcassets"; sourceTree = "<group>"; };
		56DBF99C15E3CDC9007A4A8D /* iPhone_Sensors.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = iPhone_Sensors.mm; sourceTree = "<group>"; };
		56DBF99E15E3CE85007A4A8D /* iPhone_Sensors.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = iPhone_Sensors.h; sourceTree = "<group>"; };
		56FD43950ED4745200FE3770 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		58C462906CFE1012884EAB16 /* System.Core__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__1.cpp; sourceTree = SOURCE_ROOT; };
		58D0A5E12D87A4B766719265 /* mscorlib__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp; sourceTree = SOURCE_ROOT; };
		58D3A7800BA0F8BFFDC7DDAA /* UnityEngine.GridModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GridModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		59DBD7F6973027B5CE2179D9 /* GoogleMobileAds.Core.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GoogleMobileAds.Core.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Core.cpp; sourceTree = SOURCE_ROOT; };
		5A3A145D9786ACFE6D088E06 /* GDTNativeExpressAd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTNativeExpressAd.h; path = Libraries/Plugins/iOS/GdtMob/GDTNativeExpressAd.h; sourceTree = SOURCE_ROOT; };
		5A3A1B4C3FB1FC90023B942E /* LaunchScreen-iPhone.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "LaunchScreen-iPhone.storyboard"; sourceTree = SOURCE_ROOT; };
		5B2F54EB41B243A7C1793B5C /* UnityEngine.TextCoreTextEngineModule__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextCoreTextEngineModule__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp; sourceTree = SOURCE_ROOT; };
		5B53B64A88569D3F8EB8A9F1 /* Assembly-CSharp-firstpass__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass__1.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__1.cpp"; sourceTree = SOURCE_ROOT; };
		5BAD78601F2B5A59006103DE /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		5C3AA7AEC8BCC431E5FEEC04 /* Assembly-CSharp__37.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__37.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__37.cpp"; sourceTree = SOURCE_ROOT; };
		5C57731C0A13EC17F5128874 /* libGDTMobSDK.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libGDTMobSDK.a; path = Libraries/Plugins/iOS/GdtMob/libGDTMobSDK.a; sourceTree = SOURCE_ROOT; };
		5CD37B9613BB23D856E08103 /* GDTUnifiedNativeAdNetworkConnectorProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedNativeAdNetworkConnectorProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedNativeAdNetworkConnectorProtocol.h; sourceTree = SOURCE_ROOT; };
		5CE747857641C3A4C4FD32A4 /* System.Xml__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__13.cpp; sourceTree = SOURCE_ROOT; };
		5D2B6D2D12B065A216EB6E1F /* NSUserDefaults.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = NSUserDefaults.mm; path = Libraries/com.unity.services.core/Runtime/Device/UserIdProviders/NSUserDefaults.mm; sourceTree = SOURCE_ROOT; };
		5D2CB0C1599CC73B675915AB /* Assembly-CSharp__43.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__43.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__43.cpp"; sourceTree = SOURCE_ROOT; };
		5E6EECA05C59F494EE5D3F45 /* System.Runtime.Serialization_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Runtime.Serialization_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization_CodeGen.c; sourceTree = SOURCE_ROOT; };
		5E7DE52DB56AC9BD83B13EEB /* UnityEarlyTransactionObserver.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = UnityEarlyTransactionObserver.mm; path = Libraries/com.unity.purchasing/Plugins/UnityPurchasing/iOS/UnityEarlyTransactionObserver.mm; sourceTree = SOURCE_ROOT; };
		5E93FF130E65841D72EE889D /* Assembly-CSharp__46.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__46.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__46.cpp"; sourceTree = SOURCE_ROOT; };
		5EA521C38706003E7FF399CD /* Il2CppMetadataRegistration.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppMetadataRegistration.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataRegistration.c; sourceTree = SOURCE_ROOT; };
		5FF625858089E21BDF0720A0 /* GDTUnifiedInterstitialAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedInterstitialAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedInterstitialAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		6124A6655AB8A1E235C3FDCC /* FairyGUI__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__2.cpp; sourceTree = SOURCE_ROOT; };
		6141015D135F7A3CB8A19391 /* Pods-Unity-iPhone.releaseforprofiling.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Unity-iPhone.releaseforprofiling.xcconfig"; path = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.releaseforprofiling.xcconfig"; sourceTree = "<group>"; };
		615F1EC7832D2B8CB2005C27 /* GDTLogoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTLogoView.h; path = Libraries/Plugins/iOS/GdtMob/GDTLogoView.h; sourceTree = SOURCE_ROOT; };
		61644B28313FCE72AA0F7731 /* Il2CppReversePInvokeWrapperTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppReversePInvokeWrapperTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp; sourceTree = SOURCE_ROOT; };
		61703DF54F612431C9B2CEC6 /* UnityEngine.IMGUIModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.IMGUIModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp; sourceTree = SOURCE_ROOT; };
		6189F3F814EA67BAEB8664B9 /* Generics__57.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__57.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__57.cpp; sourceTree = SOURCE_ROOT; };
		624F1BD53712BEA8AB14E15D /* FairyGUI__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__5.cpp; sourceTree = SOURCE_ROOT; };
		625C59B7F72E20CA1DAD37D1 /* Assembly-CSharp__29.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__29.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__29.cpp"; sourceTree = SOURCE_ROOT; };
		6268E9B44CD8602C2A9D5536 /* GenericMethods__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__4.cpp; sourceTree = SOURCE_ROOT; };
		62B31F6AE6313940631A3B13 /* Il2CppInteropDataTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppInteropDataTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp; sourceTree = SOURCE_ROOT; };
		62CBF79C3A505CA169C0A2F8 /* UnityEngine.InputLegacyModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.InputLegacyModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		64C9D91441AE69C1D77D155E /* Assembly-CSharp-firstpass__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass__4.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__4.cpp"; sourceTree = SOURCE_ROOT; };
		64E2DA6CA86A91002CE87FE3 /* UnityEngine.UIElementsModule__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp; sourceTree = SOURCE_ROOT; };
		64E52B1EC636E618B7604298 /* GenericMethods__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__13.cpp; sourceTree = SOURCE_ROOT; };
		650E98E487E282D0BD28CD94 /* Generics__42.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__42.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__42.cpp; sourceTree = SOURCE_ROOT; };
		65A609DC99CBAA15044D5D88 /* mscorlib__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp; sourceTree = SOURCE_ROOT; };
		65A78184B609DFC82D3D4939 /* UnityAdvertisement.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = UnityAdvertisement.swift; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdvertisement.swift; sourceTree = SOURCE_ROOT; };
		65F37FDAB09705DF055776EF /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		6679B66625DA79D57B50501A /* UnityClassRegistration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityClassRegistration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityClassRegistration.cpp; sourceTree = SOURCE_ROOT; };
		672987B2BECEFF858A090C4A /* UnityEngine.UI__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp; sourceTree = SOURCE_ROOT; };
		6762A34FEF91061AC451841F /* UnityEngine.UI_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UI_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c; sourceTree = SOURCE_ROOT; };
		677B3A5BE7B2CB7BD477BB0A /* UnityEngine.InputLegacyModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.InputLegacyModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp; sourceTree = SOURCE_ROOT; };
		679ACDCF6D14CA6FE6F81479 /* IUnityInterface.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IUnityInterface.h; path = Classes/Unity/IUnityInterface.h; sourceTree = SOURCE_ROOT; };
		67B80E918530D8754535F3F9 /* Il2CppCodeRegistration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCodeRegistration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp; sourceTree = SOURCE_ROOT; };
		681AA63C19788ACA381FB8C1 /* LaunchScreen-iPhonePortrait.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "LaunchScreen-iPhonePortrait.png"; sourceTree = SOURCE_ROOT; };
		6943C5DACCA99586C14B905B /* Generics__19.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__19.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__19.cpp; sourceTree = SOURCE_ROOT; };
		69BE7C8970379AE244085D79 /* mscorlib__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp; sourceTree = SOURCE_ROOT; };
		69CD4252BE3616B9901D3253 /* Unity.Services.Core.Configuration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Configuration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration.cpp; sourceTree = SOURCE_ROOT; };
		69F1F8EAF4DAB02EAB96CB93 /* Generics__43.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__43.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__43.cpp; sourceTree = SOURCE_ROOT; };
		6ACBE790647C380298DD6CF0 /* UnityEngine.UIElementsModule__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp; sourceTree = SOURCE_ROOT; };
		6AF6E713D4C420DE4590CB06 /* Assembly-CSharp__51.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__51.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__51.cpp"; sourceTree = SOURCE_ROOT; };
		6B4143260DD352AF6D26E4EA /* GDTPrivacyConfiguration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTPrivacyConfiguration.h; path = Libraries/Plugins/iOS/GdtMob/GDTPrivacyConfiguration.h; sourceTree = SOURCE_ROOT; };
		6C27159912D87628ECCE5205 /* Generics__34.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__34.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__34.cpp; sourceTree = SOURCE_ROOT; };
		6D100D31A5258D5757D1271A /* UnityEngine.UIElementsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp; sourceTree = SOURCE_ROOT; };
		6F88E695F4B9054ACDB02B6F /* Generics__35.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__35.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__35.cpp; sourceTree = SOURCE_ROOT; };
		6F9722D0330536FF815E94C5 /* System.Xml__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp; sourceTree = SOURCE_ROOT; };
		709BAA32BB8A406717625013 /* GDTUnifiedBannerAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedBannerAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedBannerAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		70FE2BDFB277A35F0A3B88EE /* Assembly-CSharp-firstpass__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass__2.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__2.cpp"; sourceTree = SOURCE_ROOT; };
		712E424EC1D2E248B742B6A0 /* Unity.Services.Core.Scheduler.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Scheduler.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler.cpp; sourceTree = SOURCE_ROOT; };
		71520C37DB84A0B4A8E0F46F /* System.Xml.Linq_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Xml.Linq_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c; sourceTree = SOURCE_ROOT; };
		715BA45FFD028756DA8BD144 /* Generics__17.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__17.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__17.cpp; sourceTree = SOURCE_ROOT; };
		715BCED89CFA351F61DC42B9 /* RSSecrets.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = RSSecrets.m; path = Libraries/Plugins/iOS/IApps/RSSecrets.m; sourceTree = SOURCE_ROOT; };
		726240544A4D431FB28D10E4 /* GDTVideoAdReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTVideoAdReporter.h; path = Libraries/Plugins/iOS/GdtMob/GDTVideoAdReporter.h; sourceTree = SOURCE_ROOT; };
		727353E0A17F3CD26C387C4B /* UnityEngine.UIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		72831D4D6869FD6E7154268C /* GDTUnifiedNativeAdView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedNativeAdView.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedNativeAdView.h; sourceTree = SOURCE_ROOT; };
		73200F4F052CF846BE8AEF82 /* NativeDialogsPlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = NativeDialogsPlugin.m; path = Libraries/Plugins/iOS/NativeDialogsPlugin.m; sourceTree = SOURCE_ROOT; };
		732F023F742A46D0095260A9 /* Assembly-CSharp-firstpass_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "Assembly-CSharp-firstpass_CodeGen.c"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c"; sourceTree = SOURCE_ROOT; };
		733D2142097013827A6FD7A8 /* Il2CppGenericClassTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericClassTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c; sourceTree = SOURCE_ROOT; };
		73E1EB45A942E11178F3915F /* UnityEngine.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp; sourceTree = SOURCE_ROOT; };
		74388B1F695F09EEC24A1072 /* ProCamera2D.Runtime__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ProCamera2D.Runtime__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime__1.cpp; sourceTree = SOURCE_ROOT; };
		749E2B722F0B2B49039D8675 /* Generics__44.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__44.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__44.cpp; sourceTree = SOURCE_ROOT; };
		750BF4D08D860C9A70ACE432 /* System.Xml__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__12.cpp; sourceTree = SOURCE_ROOT; };
		75954FC5A3663951E6786128 /* Generics__50.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__50.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__50.cpp; sourceTree = SOURCE_ROOT; };
		766DE0A8CD087FAEDF341E93 /* Pods_UnityFramework.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_UnityFramework.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		76A06ED55BC26ABEC74D9B5E /* UnityEngine.UnityWebRequestTextureModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestTextureModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		76E90196E5C6B8BA16A9EB4C /* System_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c; sourceTree = SOURCE_ROOT; };
		7771951A12B3A7284778E4C6 /* System__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__7.cpp; sourceTree = SOURCE_ROOT; };
		777CD2AF69C4EDDDC8094D5F /* GenericMethods__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__12.cpp; sourceTree = SOURCE_ROOT; };
		780EF42D0F810F6942D61886 /* Assembly-CSharp__50.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__50.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__50.cpp"; sourceTree = SOURCE_ROOT; };
		782F20A8C011459AB9E0A41E /* System__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__6.cpp; sourceTree = SOURCE_ROOT; };
		783E22A3A60914DCD509C3A0 /* Pods-UnityFramework.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UnityFramework.debug.xcconfig"; path = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.debug.xcconfig"; sourceTree = "<group>"; };
		78DCC5B0D7B57EB22AB67E50 /* UnityAdsLoadListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = UnityAdsLoadListener.mm; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsLoadListener.mm; sourceTree = SOURCE_ROOT; };
		7931F0EB3287754DA068276D /* System.Xml__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__11.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__11.cpp; sourceTree = SOURCE_ROOT; };
		79D15C82C81C8A9D8853F133 /* GoogleMobileAds.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GoogleMobileAds.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.cpp; sourceTree = SOURCE_ROOT; };
		7A0126D9A77C3A2BC78EAC19 /* UnityEngine.PhysicsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.PhysicsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp; sourceTree = SOURCE_ROOT; };
		7C2E380E3D2F92B20313DD1D /* Il2CppGenericMethodTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericMethodTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodTable.c; sourceTree = SOURCE_ROOT; };
		7C3D9FDE49610AB93793E874 /* Generics__31.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__31.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__31.cpp; sourceTree = SOURCE_ROOT; };
		7C8160F6348A42D9576710E3 /* GDTSplashAdNetworkConnectorProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTSplashAdNetworkConnectorProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTSplashAdNetworkConnectorProtocol.h; sourceTree = SOURCE_ROOT; };
		7CB7FE9F812CDE7E67229FC1 /* UnityAdsUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UnityAdsUtilities.h; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsUtilities.h; sourceTree = SOURCE_ROOT; };
		7CD8D832DF5E49F3229DE515 /* baselib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = baselib.a; path = Libraries/baselib.a; sourceTree = SOURCE_ROOT; };
		7DB84657C7BE9BD9279A5A0D /* Generics__64.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__64.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__64.cpp; sourceTree = SOURCE_ROOT; };
		7DE28CCD862A989C5889A8E5 /* UnityAdsUnityWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UnityAdsUnityWrapper.m; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsUnityWrapper.m; sourceTree = SOURCE_ROOT; };
		7EEF5FB367689FB1ECEBB6A7 /* System.Xml__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp; sourceTree = SOURCE_ROOT; };
		7F36C10E13C5C673007FBDD9 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		7F36C10F13C5C673007FBDD9 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		7F36C11013C5C673007FBDD9 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libGameAssembly.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7FA39E9CC391DDDACEDDE1E9 /* Assembly-CSharp__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__9.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__9.cpp"; sourceTree = SOURCE_ROOT; };
		7FE7BDC9191C134A46FF908E /* ISN_NativeCore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ISN_NativeCore.h; path = Libraries/Plugins/iOS/ISN_NativeCore.h; sourceTree = SOURCE_ROOT; };
		80D2FA80EB689285AB336DF4 /* Unity.Services.Core.Registration_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Registration_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration_CodeGen.c; sourceTree = SOURCE_ROOT; };
		816FDCA20CB4A3E8F33CC76A /* Generics__60.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__60.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__60.cpp; sourceTree = SOURCE_ROOT; };
		817B6BBCBF9F078D00727259 /* System.Data_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Data_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Data_CodeGen.c; sourceTree = SOURCE_ROOT; };
		81F3C5309AEB771634823147 /* Generics__69.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__69.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__69.cpp; sourceTree = SOURCE_ROOT; };
		8224070CACC22FB63E254585 /* GDTMediaView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTMediaView.h; path = Libraries/Plugins/iOS/GdtMob/GDTMediaView.h; sourceTree = SOURCE_ROOT; };
		825CA0BD6C0EBAA9DC8AA9D4 /* Pods-UnityFramework.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UnityFramework.release.xcconfig"; path = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.release.xcconfig"; sourceTree = "<group>"; };
		830B5C100E5ED4C100C7819F /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		8358D1B70ED1CC3700E3A684 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		83B2570A0E62FF8A00468741 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		83B2574B0E63022200468741 /* OpenAL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenAL.framework; path = System/Library/Frameworks/OpenAL.framework; sourceTree = SDKROOT; };
		83BDD9D20D2D7764D399F567 /* GDTNativeExpressAdNetworkAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTNativeExpressAdNetworkAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTNativeExpressAdNetworkAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		848031E01C5160D700FCEAB7 /* UnityReplayKit_Scripting.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityReplayKit_Scripting.mm; sourceTree = "<group>"; };
		8482C9568E6A49FD58B18615 /* System__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__9.cpp; sourceTree = SOURCE_ROOT; };
		848BF6A7383F7523CFD9F1F2 /* Generics__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__13.cpp; sourceTree = SOURCE_ROOT; };
		84AB00D51327D54081DFA7C2 /* Generics__63.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__63.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__63.cpp; sourceTree = SOURCE_ROOT; };
		84C6A84D75C32015E4995C33 /* System.Xml__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__16.cpp; sourceTree = SOURCE_ROOT; };
		84DC28F51C5137FE00BC67D7 /* UnityReplayKit.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityReplayKit.mm; sourceTree = "<group>"; };
		84DC28F71C51383500BC67D7 /* UnityReplayKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityReplayKit.h; sourceTree = "<group>"; };
		857AE3AA6D7217204F38E177 /* UnityEngine.GridModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.GridModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp; sourceTree = SOURCE_ROOT; };
		85E5623620F4F4D1001DFEF6 /* UnityView+Keyboard.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityView+Keyboard.mm"; sourceTree = "<group>"; };
		862C244F20AEC7AC006FB4AD /* UnityWebRequest.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityWebRequest.mm; sourceTree = "<group>"; };
		86D61A6AC37C64EC49B5261F /* System.Core__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__2.cpp; sourceTree = SOURCE_ROOT; };
		87605856C2B447518B98EAF3 /* UnityICallRegistration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityICallRegistration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityICallRegistration.cpp; sourceTree = SOURCE_ROOT; };
		889E696927F9303A40F5E3F4 /* Reachability.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = Reachability.m; path = Libraries/Plugins/iOS/IApps/Reachability.m; sourceTree = SOURCE_ROOT; };
		88AA051394FF8002BF048FA1 /* Generics__28.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__28.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__28.cpp; sourceTree = SOURCE_ROOT; };
		894C1B251937BE1BC2B4D36C /* IUnityGraphicsMetal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IUnityGraphicsMetal.h; path = Classes/Unity/IUnityGraphicsMetal.h; sourceTree = SOURCE_ROOT; };
		89EF3E4A2B0003F14AAD225D /* UnityEngine.UI__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp; sourceTree = SOURCE_ROOT; };
		89FC170F9154433D73BADA70 /* Pods-UnityFramework.releaseforrunning.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UnityFramework.releaseforrunning.xcconfig"; path = "Target Support Files/Pods-UnityFramework/Pods-UnityFramework.releaseforrunning.xcconfig"; sourceTree = "<group>"; };
		8A142DC41636943E00DD87CA /* Keyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Keyboard.h; sourceTree = "<group>"; };
		8A142DC51636943E00DD87CA /* Keyboard.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = Keyboard.mm; sourceTree = "<group>"; };
		8A16150B1A8E4362006FA788 /* FullScreenVideoPlayer.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = FullScreenVideoPlayer.mm; sourceTree = "<group>"; };
		8A20382C213D4B3C005E6C56 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		8A21AED21622F59300AF8007 /* UnityViewControllerBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityViewControllerBase.h; sourceTree = "<group>"; };
		8A25E6D118D767E20006A227 /* Filesystem.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = Filesystem.mm; sourceTree = "<group>"; };
		8A292A9717992CE100409BA4 /* LifeCycleListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LifeCycleListener.h; sourceTree = "<group>"; };
		8A292A9817992CE100409BA4 /* LifeCycleListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = LifeCycleListener.mm; sourceTree = "<group>"; };
		8A2AA93316E0978D001FB470 /* CMVideoSampling.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CMVideoSampling.h; sourceTree = "<group>"; };
		8A2AA93416E0978D001FB470 /* CMVideoSampling.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CMVideoSampling.mm; sourceTree = "<group>"; };
		8A2BC6DE245061EE00C7C97D /* NoGraphicsHelper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = NoGraphicsHelper.mm; sourceTree = "<group>"; };
		8A367F5916A6D36F0012ED11 /* CVTextureCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CVTextureCache.h; sourceTree = "<group>"; };
		8A367F5A16A6D36F0012ED11 /* CVTextureCache.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CVTextureCache.mm; sourceTree = "<group>"; };
		8A3831B4246956AA00CD74FD /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		8A4815BF17A287D2003FBFD5 /* UnityAppController+ViewHandling.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UnityAppController+ViewHandling.h"; sourceTree = "<group>"; };
		8A4815C017A287D2003FBFD5 /* UnityAppController+ViewHandling.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityAppController+ViewHandling.mm"; sourceTree = "<group>"; };
		8A5C1490174E662D0006EB36 /* RenderPluginDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RenderPluginDelegate.h; sourceTree = "<group>"; };
		8A5C1491174E662D0006EB36 /* RenderPluginDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = RenderPluginDelegate.mm; sourceTree = "<group>"; };
		8A5E0B8F16849D1800CBB6FE /* DisplayManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DisplayManager.h; sourceTree = "<group>"; };
		8A5E0B9016849D1800CBB6FE /* DisplayManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = DisplayManager.mm; sourceTree = "<group>"; };
		8A6137121A10B57700059EDF /* ObjCRuntime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ObjCRuntime.h; sourceTree = "<group>"; };
		8A6720A319EEB905006C92E0 /* InternalProfiler.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = InternalProfiler.cpp; sourceTree = "<group>"; };
		8A6720A419EEB905006C92E0 /* InternalProfiler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InternalProfiler.h; sourceTree = "<group>"; };
		8A6720A619EFAF25006C92E0 /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Prefix.pch; sourceTree = "<group>"; };
		8A7939FC1ED2F53200B44EF1 /* UnityViewControllerBase.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityViewControllerBase.mm; sourceTree = "<group>"; };
		8A7939FE1ED43EE100B44EF1 /* UnityView+iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityView+iOS.h"; sourceTree = "<group>"; };
		8A7939FF1ED43EE100B44EF1 /* UnityView+iOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityView+iOS.mm"; sourceTree = "<group>"; };
		8A793A001ED43EE100B44EF1 /* UnityView+tvOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityView+tvOS.h"; sourceTree = "<group>"; };
		8A793A011ED43EE100B44EF1 /* UnityView+tvOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityView+tvOS.mm"; sourceTree = "<group>"; };
		8A793A021ED43EE100B44EF1 /* UnityViewControllerBase+iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityViewControllerBase+iOS.h"; sourceTree = "<group>"; };
		8A793A031ED43EE100B44EF1 /* UnityViewControllerBase+iOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityViewControllerBase+iOS.mm"; sourceTree = "<group>"; };
		8A793A041ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityViewControllerBase+tvOS.h"; sourceTree = "<group>"; };
		8A793A051ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityViewControllerBase+tvOS.mm"; sourceTree = "<group>"; };
		8A851BA516FB2F6D00E911DB /* UnityView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityView.h; sourceTree = "<group>"; };
		8A851BA616FB2F6D00E911DB /* UnityView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityView.mm; sourceTree = "<group>"; };
		8A851BA816FB3AD000E911DB /* UnityAppController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityAppController.h; sourceTree = "<group>"; };
		8A851BA916FB3AD000E911DB /* UnityAppController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityAppController.mm; sourceTree = "<group>"; };
		8A851BAB16FC875E00E911DB /* UnityInterface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityInterface.h; sourceTree = "<group>"; };
		8A8D90D81A274A7800456C4E /* UnityAppController+UnityInterface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityAppController+UnityInterface.h"; sourceTree = "<group>"; };
		8A8D90D91A274A7800456C4E /* UnityAppController+UnityInterface.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityAppController+UnityInterface.mm"; sourceTree = "<group>"; };
		8A90541019EE8843003D1039 /* UnityForwardDecls.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityForwardDecls.h; sourceTree = "<group>"; };
		8A9FCB111617295F00C05364 /* ActivityIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ActivityIndicator.h; sourceTree = "<group>"; };
		8A9FCB121617295F00C05364 /* ActivityIndicator.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ActivityIndicator.mm; sourceTree = "<group>"; };
		8AA108C01948732900D0538B /* UnityRendering.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityRendering.h; sourceTree = "<group>"; };
		8AA5D80017ABE9AF007B9910 /* UnityAppController+Rendering.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityAppController+Rendering.h"; sourceTree = "<group>"; };
		8AA5D80117ABE9AF007B9910 /* UnityAppController+Rendering.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityAppController+Rendering.mm"; sourceTree = "<group>"; };
		8AA6ADDB17818CFD00A1C5F1 /* UnityTrampolineConfigure.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityTrampolineConfigure.h; sourceTree = "<group>"; };
		8AB3CB3C16D390BA00697AD5 /* VideoPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VideoPlayer.h; sourceTree = "<group>"; };
		8AB3CB3D16D390BB00697AD5 /* VideoPlayer.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = VideoPlayer.mm; sourceTree = "<group>"; };
		8ABDBCE019CAFCF700A842FF /* AVCapture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AVCapture.h; sourceTree = "<group>"; };
		8AC71EC219E7FBA90027502F /* OrientationSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OrientationSupport.h; sourceTree = "<group>"; };
		8AC71EC319E7FBA90027502F /* OrientationSupport.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = OrientationSupport.mm; sourceTree = "<group>"; };
		8AC74A9419B47FEF00019D38 /* AVCapture.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AVCapture.mm; sourceTree = "<group>"; };
		8ACB801B177081D4005D0019 /* DeviceSettings.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = DeviceSettings.mm; sourceTree = "<group>"; };
		8ACB801D177081F7005D0019 /* Preprocessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Preprocessor.h; sourceTree = "<group>"; };
		8ADAC812E52229002239D46B /* UnityEngine.UnityWebRequestWWWModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestWWWModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule.cpp; sourceTree = SOURCE_ROOT; };
		8ADCE38919C87177006F04F6 /* CameraCapture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CameraCapture.h; sourceTree = "<group>"; };
		8ADCE38A19C87177006F04F6 /* CameraCapture.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CameraCapture.mm; sourceTree = "<group>"; };
		8AECDC781950835600CB29E8 /* UnityMetalSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityMetalSupport.h; sourceTree = "<group>"; };
		8AF7755E17997D1300341121 /* AppDelegateListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegateListener.h; sourceTree = "<group>"; };
		8AF7755F17997D1300341121 /* AppDelegateListener.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = AppDelegateListener.mm; sourceTree = "<group>"; };
		8BEDFA3729C88F86007F26D7 /* UnityViewControllerBase+visionOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UnityViewControllerBase+visionOS.h"; sourceTree = "<group>"; };
		8BEDFA3829C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UnityViewControllerBase+visionOS.mm"; sourceTree = "<group>"; };
		8BF3CC7245E4CCD0D7BC3513 /* Generics__29.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__29.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__29.cpp; sourceTree = SOURCE_ROOT; };
		8CE5D574D9D536B5CB300F91 /* Unity.Services.Core.Scheduler_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Scheduler_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler_CodeGen.c; sourceTree = SOURCE_ROOT; };
		8D1107310486CEB800E47090 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8D13621DBE69B85E504D57D6 /* Generics__66.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__66.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__66.cpp; sourceTree = SOURCE_ROOT; };
		8DECD8BC6547FA2FC7D9E876 /* GoogleMobileAds.iOS.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GoogleMobileAds.iOS.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.iOS.cpp; sourceTree = SOURCE_ROOT; };
		8DF7D168881C3EF89850AD0A /* Generics__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp; sourceTree = SOURCE_ROOT; };
		8E6728ADD56BA33B896627DA /* Generics__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp; sourceTree = SOURCE_ROOT; };
		8EE47B422BDF2BE7441AA757 /* Newtonsoft.Json.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp; sourceTree = SOURCE_ROOT; };
		8F13D042492DBEB7CF96DC23 /* Generics__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__9.cpp; sourceTree = SOURCE_ROOT; };
		8F8539D174DC5F6F066347FF /* Generics__39.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__39.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__39.cpp; sourceTree = SOURCE_ROOT; };
		8FE3E92069BB36F8854D7277 /* __Generated_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = __Generated_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c; sourceTree = SOURCE_ROOT; };
		90114C270D8D46748478032F /* UnityEngine.CoreModule__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.CoreModule__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp; sourceTree = SOURCE_ROOT; };
		90E69A22DD43A16E030D8110 /* UnityEngine.UnityAnalyticsCommonModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityAnalyticsCommonModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9144340CC541DD77AADA21F3 /* System__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__2.cpp; sourceTree = SOURCE_ROOT; };
		91B3C31E164148A3C3C440BA /* Assembly-CSharp__31.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__31.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__31.cpp"; sourceTree = SOURCE_ROOT; };
		91E821D27E6EC7332DE9064A /* ISN_InApp.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = ISN_InApp.mm; path = Libraries/Plugins/iOS/ISN_InApp.mm; sourceTree = SOURCE_ROOT; };
		92E17166D3BDB3EA24239E77 /* mscorlib__20.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__20.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp; sourceTree = SOURCE_ROOT; };
		93CB0776020C99B75942BABB /* Assembly-CSharp__28.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__28.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__28.cpp"; sourceTree = SOURCE_ROOT; };
		93DCE62BE3E166440E8359CE /* Unity.Addressables_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Addressables_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Addressables_CodeGen.c; sourceTree = SOURCE_ROOT; };
		93E6825D15CDF948BB0DCB44 /* Il2CppCCalculateTypeValues2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateTypeValues2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues2.cpp; sourceTree = SOURCE_ROOT; };
		94171DB83A0E4A9A61CFBC08 /* Generics__46.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__46.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__46.cpp; sourceTree = SOURCE_ROOT; };
		9432641D8FAD3395C648341C /* UnityEngine.Physics2DModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.Physics2DModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9497B750CCFB7578BDEF70E5 /* GenericMethods__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__2.cpp; sourceTree = SOURCE_ROOT; };
		94AD788D56925343C4E55E99 /* GDTNativeExpressAdViewAdapterProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTNativeExpressAdViewAdapterProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTNativeExpressAdViewAdapterProtocol.h; sourceTree = SOURCE_ROOT; };
		94CD3AD5F74D4265461E0C6D /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		94D7711027E56C025D8682E4 /* GoogleMobileAds.Ump.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GoogleMobileAds.Ump.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump.cpp; sourceTree = SOURCE_ROOT; };
		94F7DED7298B1330B361D3F2 /* UnityAdsShowListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UnityAdsShowListener.h; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsShowListener.h; sourceTree = SOURCE_ROOT; };
		95156BB6430AD1E82A3BB488 /* ProCamera2D.Examples.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ProCamera2D.Examples.cpp; path = Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Examples.cpp; sourceTree = SOURCE_ROOT; };
		95579700ADBEB4B7D5EBFB8A /* GADTSmallTemplateView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = GADTSmallTemplateView.xib; path = Libraries/Plugins/iOS/NativeTemplates/GADTSmallTemplateView.xib; sourceTree = SOURCE_ROOT; };
		960391211D6CE46E003BF157 /* MediaToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaToolbox.framework; path = System/Library/Frameworks/MediaToolbox.framework; sourceTree = SDKROOT; };
		961D2F6C098B83FD6726598D /* Generics__41.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__41.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__41.cpp; sourceTree = SOURCE_ROOT; };
		9664DF812E72471EB20F5443 /* Unity.TextMeshPro__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp; sourceTree = SOURCE_ROOT; };
		979CE552E8632B2F59D7A75B /* UnityEngine.AssetBundleModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AssetBundleModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		97EFDE40B546C8DD238C8231 /* Il2CppCCalculateFieldValues3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues3.cpp; sourceTree = SOURCE_ROOT; };
		980A11C75E6594F47A4D0C4E /* System.Xml__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__8.cpp; sourceTree = SOURCE_ROOT; };
		98A7FB6B217C3D47128FE608 /* GenericMethods__16.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__16.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__16.cpp; sourceTree = SOURCE_ROOT; };
		99204864EEA9F9D90FC4D0E9 /* Generics__45.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__45.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__45.cpp; sourceTree = SOURCE_ROOT; };
		992385BAB68D22DD4E5C0DB3 /* DOTweenPro.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = DOTweenPro.cpp; path = Il2CppOutputProject/Source/il2cppOutput/DOTweenPro.cpp; sourceTree = SOURCE_ROOT; };
		99334BA41D46C76F13A39E7C /* UnityAdsUtilities.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UnityAdsUtilities.m; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsUtilities.m; sourceTree = SOURCE_ROOT; };
		996AF65B21117545805EDE7A /* UnityEngine.UnityWebRequestModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp; sourceTree = SOURCE_ROOT; };
		996B8B35A5892AE2820D8033 /* Il2CppTypeDefinitions.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppTypeDefinitions.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c; sourceTree = SOURCE_ROOT; };
		99818D2EBD0B58F62CB9889B /* FairyGUI.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI.cpp; sourceTree = SOURCE_ROOT; };
		999431141A9E39D05E5FE97B /* UniWebView-CSharp.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "UniWebView-CSharp.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp.cpp"; sourceTree = SOURCE_ROOT; };
		9A4AB585B67FED485B96F428 /* GenericMethods__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__7.cpp; sourceTree = SOURCE_ROOT; };
		9AC5D532B0DA5D61DDBE0A59 /* Generics__51.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__51.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__51.cpp; sourceTree = SOURCE_ROOT; };
		9AEC99D2D5AD154137C30A61 /* Pods-Unity-iPhone.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Unity-iPhone.debug.xcconfig"; path = "Target Support Files/Pods-Unity-iPhone/Pods-Unity-iPhone.debug.xcconfig"; sourceTree = "<group>"; };
		9BF21BC9FDBD95DE7F910C71 /* GDTNativeExpressAdView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTNativeExpressAdView.h; path = Libraries/Plugins/iOS/GdtMob/GDTNativeExpressAdView.h; sourceTree = SOURCE_ROOT; };
		9C5F4A52C023FBEBCB9B9C51 /* Assembly-CSharp__11.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__11.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__11.cpp"; sourceTree = SOURCE_ROOT; };
		9C6571465C64216B59151280 /* UnityEngine.JSONSerializeModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.JSONSerializeModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp; sourceTree = SOURCE_ROOT; };
		9CC7631EEE51DCCD5E2C1ABA /* UnityEngine.UIElementsModule__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__15.cpp; sourceTree = SOURCE_ROOT; };
		9CE38A0EB9BA95FB48A7537E /* UnityEngine.TextRenderingModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextRenderingModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9D0A618A21BFE7F30094DC33 /* libiconv.2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.2.tbd; path = usr/lib/libiconv.2.tbd; sourceTree = SDKROOT; };
		9D0A7564828238ED0C6F4AFD /* UnityEngine.AIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule.cpp; sourceTree = SOURCE_ROOT; };
		9D16CD8021C938B300DD46C0 /* UndefinePlatforms.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UndefinePlatforms.h; sourceTree = "<group>"; };
		9D16CD8121C938BB00DD46C0 /* RedefinePlatforms.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RedefinePlatforms.h; sourceTree = "<group>"; };
		9D25AB9D213FB47800354C27 /* UnityFramework.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UnityFramework.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9D25AB9F213FB47800354C27 /* UnityFramework.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityFramework.h; sourceTree = "<group>"; };
		9D25ABA0213FB47800354C27 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9D3B639B6E4EB4B913EDABF2 /* Unity.TextMeshPro__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp; sourceTree = SOURCE_ROOT; };
		9D8D2D85D4E6EE2B82F9C0D1 /* GDTRewardVideoAd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTRewardVideoAd.h; path = Libraries/Plugins/iOS/GdtMob/GDTRewardVideoAd.h; sourceTree = SOURCE_ROOT; };
		9DA3B0432174CB96001678C7 /* main.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		9E192277BBADB145533A1FCC /* GDTMobUTypes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTMobUTypes.h; path = Libraries/Plugins/iOS/GdtMob/GDTMobUTypes.h; sourceTree = SOURCE_ROOT; };
		9E90AE8AEA3FE3DC0C6E0BED /* UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		9EA3E57A19EDE7497E36F3DF /* UnityEngine.GameCenterModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.GameCenterModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A0EAFA894ED09EBB217A485C /* GDTSDKConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTSDKConfig.h; path = Libraries/Plugins/iOS/GdtMob/GDTSDKConfig.h; sourceTree = SOURCE_ROOT; };
		A1B3624D0AECCFE9A3417712 /* System.Drawing.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Drawing.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Drawing.cpp; sourceTree = SOURCE_ROOT; };
		A239769F708DCF300ED81D67 /* Assembly-CSharp__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__14.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__14.cpp"; sourceTree = SOURCE_ROOT; };
		A2562FA24FCA1F0C707324C8 /* UnityEngine.UnityWebRequestWWWModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestWWWModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A34734E12320BFE93D77614B /* Assembly-CSharp__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__5.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__5.cpp"; sourceTree = SOURCE_ROOT; };
		A4591E5013E0219646DFFBD4 /* UnityEngine.GameCenterModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.GameCenterModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp; sourceTree = SOURCE_ROOT; };
		A504EDDC3C17119152F24F7F /* GenericMethods__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__3.cpp; sourceTree = SOURCE_ROOT; };
		A5B4D102DE21CCAA54308C32 /* MBProgressHUD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = MBProgressHUD.m; path = Libraries/Plugins/iOS/MBProgressHUD.m; sourceTree = SOURCE_ROOT; };
		A6444ABDD2799CC877EBAF7E /* LaunchScreen-iPad.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "LaunchScreen-iPad.storyboard"; sourceTree = SOURCE_ROOT; };
		A736D24CED71BDA3F309CC8E /* Il2CppCCalculateFieldValues2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp; sourceTree = SOURCE_ROOT; };
		A78015B5DCE025229C145EB5 /* Reachability.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Reachability.h; path = Libraries/Plugins/iOS/IApps/Reachability.h; sourceTree = SOURCE_ROOT; };
		A7CC23DF498D4EFEEB0AF888 /* UnityEngine.AnimationModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AnimationModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A849FEA549C242344B5B7C14 /* Assembly-CSharp__45.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__45.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__45.cpp"; sourceTree = SOURCE_ROOT; };
		A882C70BF6B0539484D63840 /* Assembly-CSharp__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__13.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__13.cpp"; sourceTree = SOURCE_ROOT; };
		A8EB050C496A3CF9B48DB001 /* Generics__53.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__53.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__53.cpp; sourceTree = SOURCE_ROOT; };
		A918C8B44A33797E60C31B4D /* UnityEngine.ImageConversionModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ImageConversionModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		A9860406B2D3274C13FA2227 /* GDTMobManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = GDTMobManager.mm; path = Libraries/Plugins/iOS/GdtMob/GDTMobManager.mm; sourceTree = SOURCE_ROOT; };
		AA31BF961B55660D0013FB1B /* Data */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Data; sourceTree = "<group>"; };
		AA5D99861AFAD3C800B27605 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		AAC3E38B1A68945900F6174A /* RegisterFeatures.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = RegisterFeatures.cpp; sourceTree = "<group>"; };
		AAC3E38C1A68945900F6174A /* RegisterFeatures.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RegisterFeatures.h; sourceTree = "<group>"; };
		AAC85F52F8C010404109B18C /* Mono.Security_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Mono.Security_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c; sourceTree = SOURCE_ROOT; };
		AAE63453E56E81C5A6FED0FD /* GenericMethods__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__6.cpp; sourceTree = SOURCE_ROOT; };
		AAFE69D019F187C200638316 /* UnityViewControllerListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnityViewControllerListener.h; sourceTree = "<group>"; };
		AAFE69D119F187C200638316 /* UnityViewControllerListener.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = UnityViewControllerListener.mm; sourceTree = "<group>"; };
		AB2BF8F7BAFBE6886580905D /* mscorlib__22.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__22.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp; sourceTree = SOURCE_ROOT; };
		AB3BD440282B7832E28896A1 /* VolumeIOSPlugin.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = VolumeIOSPlugin.mm; path = Libraries/com.unity.services.analytics/Runtime/Plugins/iOS/VolumeIOSPlugin.mm; sourceTree = SOURCE_ROOT; };
		ABF66CEE0A3ACFFBDA78140E /* UnityEngine.UIElementsModule__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp; sourceTree = SOURCE_ROOT; };
		ACE173F91BF3866BEA121D66 /* UnityEngine.SharedInternalsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SharedInternalsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		ADED06C4E8F915803998EFA2 /* GoogleMobileAds.iOS_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = GoogleMobileAds.iOS_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.iOS_CodeGen.c; sourceTree = SOURCE_ROOT; };
		AE25855F1DB729A61745C8CB /* Il2CppGenericAdjustorThunkTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericAdjustorThunkTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c; sourceTree = SOURCE_ROOT; };
		AED95C519544DEF0103B9C95 /* __Generated.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = __Generated.cpp; path = Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp; sourceTree = SOURCE_ROOT; };
		AF0BD4189D7A5B98C73791B8 /* GADUAdNetworkExtras.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GADUAdNetworkExtras.h; path = Libraries/Plugins/iOS/GADUAdNetworkExtras.h; sourceTree = SOURCE_ROOT; };
		AF0D68325B7E9D496A51734C /* UnityEngine.UnityWebRequestAssetBundleModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityWebRequestAssetBundleModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule.cpp; sourceTree = SOURCE_ROOT; };
		AF34E8A8B0B8A4140B1BA06F /* Generics__67.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__67.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__67.cpp; sourceTree = SOURCE_ROOT; };
		AF3F17F76B73432AF11E9DA4 /* UnityBannerUnityWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UnityBannerUnityWrapper.m; path = Libraries/com.unity.ads/Plugins/iOS/UnityBannerUnityWrapper.m; sourceTree = SOURCE_ROOT; };
		B13B5CAA12B4643FE811ED64 /* UnityEngine.UnityWebRequestModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityWebRequestModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B214A9C142CC063D26201EBF /* GoogleMobileAds.Common.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GoogleMobileAds.Common.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Common.cpp; sourceTree = SOURCE_ROOT; };
		B2F3A2E815971006D9B012F0 /* FairyGUI__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__6.cpp; sourceTree = SOURCE_ROOT; };
		B315EBF0BC65192DC23290CD /* Newtonsoft.Json__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp; sourceTree = SOURCE_ROOT; };
		B36C010DDFE4675872BFA156 /* GDTMobInterstitialManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = GDTMobInterstitialManager.mm; path = Libraries/Plugins/iOS/GdtMob/GDTMobInterstitialManager.mm; sourceTree = SOURCE_ROOT; };
		B38CA49857850503CBA3E1F4 /* GoogleMobileAds.Core_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = GoogleMobileAds.Core_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Core_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B39436E255FF5376259080BE /* mscorlib__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp; sourceTree = SOURCE_ROOT; };
		B3B3B55ACB389C47124F63DC /* System.Xml__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__9.cpp; sourceTree = SOURCE_ROOT; };
		B428915B5C40C947044F50D4 /* UniWebView-CSharp_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = "UniWebView-CSharp_CodeGen.c"; path = "Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp_CodeGen.c"; sourceTree = SOURCE_ROOT; };
		B58EF12BF9B65BC74A92DC07 /* AppleRequiredReasonCSharpAPIs.txt */ = {isa = PBXFileReference; lastKnownFileType = text; name = AppleRequiredReasonCSharpAPIs.txt; path = UnityFramework/AppleRequiredReasonCSharpAPIs.txt; sourceTree = SOURCE_ROOT; };
		B5A89C2D3588C3D4F6F6AEE2 /* Generics__37.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__37.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__37.cpp; sourceTree = SOURCE_ROOT; };
		B65026F4E0A439EAE4490865 /* UnityEngine.IMGUIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.IMGUIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B662E7588FD9F79EDCF63D17 /* Il2CppCCalculateFieldValues.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp; sourceTree = SOURCE_ROOT; };
		B6B32C7D1EE6F0853DDDAA61 /* Assembly-CSharp__35.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__35.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__35.cpp"; sourceTree = SOURCE_ROOT; };
		B6CCBDA4E2034619F101BAC7 /* Assembly-CSharp-firstpass__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass__3.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__3.cpp"; sourceTree = SOURCE_ROOT; };
		B7DF838F39D5637124C42C27 /* libUniWebView.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libUniWebView.a; path = Libraries/Plugins/iOS/libUniWebView.a; sourceTree = SOURCE_ROOT; };
		B803F94314B18CB6B2655B90 /* UnityEngine.AIModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AIModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		B8217FFE13E391CF878EB017 /* GDTMobBing.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = GDTMobBing.m; path = Libraries/Plugins/iOS/GdtMob/GDTMobBing.m; sourceTree = SOURCE_ROOT; };
		B8A2B55813D1826E10024CCF /* GenericMethods__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__15.cpp; sourceTree = SOURCE_ROOT; };
		B908CEAF09D80A3C3B24423E /* Unity.ResourceManager.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.ResourceManager.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.ResourceManager.cpp; sourceTree = SOURCE_ROOT; };
		B93CEF1C3FE1A7E36FCD1622 /* Unity.Services.Core.Registration.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Registration.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration.cpp; sourceTree = SOURCE_ROOT; };
		B9A1D40057435376AC961B25 /* Generics__33.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__33.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__33.cpp; sourceTree = SOURCE_ROOT; };
		B9A318561A46439EDE55DD9D /* GADTMediumTemplateView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = GADTMediumTemplateView.xib; path = Libraries/Plugins/iOS/NativeTemplates/GADTMediumTemplateView.xib; sourceTree = SOURCE_ROOT; };
		BA0580A085177C256385613D /* GDTRewardVideoAdNetworkConnectorProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTRewardVideoAdNetworkConnectorProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTRewardVideoAdNetworkConnectorProtocol.h; sourceTree = SOURCE_ROOT; };
		BAAB40540DA41C1B454DDA91 /* GDTSplashAd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTSplashAd.h; path = Libraries/Plugins/iOS/GdtMob/GDTSplashAd.h; sourceTree = SOURCE_ROOT; };
		BBD0C53BC75AB46F132FA2D0 /* GoogleMobileAds.Ump.iOS.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GoogleMobileAds.Ump.iOS.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump.iOS.cpp; sourceTree = SOURCE_ROOT; };
		BC1E972BAE1F1493505BFCCD /* IAppsTeamIOSUntilBinding.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = IAppsTeamIOSUntilBinding.m; path = Libraries/Plugins/iOS/IApps/IAppsTeamIOSUntilBinding.m; sourceTree = SOURCE_ROOT; };
		BC2B619F8DB8F40FD73613B7 /* GDTSDKDefines.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTSDKDefines.h; path = Libraries/Plugins/iOS/GdtMob/GDTSDKDefines.h; sourceTree = SOURCE_ROOT; };
		BD54E96698396A5E752F9571 /* Generics__20.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__20.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__20.cpp; sourceTree = SOURCE_ROOT; };
		BE33F4A8ED55CC34EB5BDF80 /* System.Xml__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml__14.cpp; sourceTree = SOURCE_ROOT; };
		BE89081E35159488B1CFEDD6 /* Assembly-CSharp__32.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__32.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__32.cpp"; sourceTree = SOURCE_ROOT; };
		BF026C43C97DF354A316F9F4 /* Generics__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__12.cpp; sourceTree = SOURCE_ROOT; };
		C08898ABC22AC52604E841BD /* Assembly-CSharp__41.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__41.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__41.cpp"; sourceTree = SOURCE_ROOT; };
		C0D23F3F8BED210275372D69 /* Newtonsoft.Json__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp; sourceTree = SOURCE_ROOT; };
		C0FC069B611EACD4E344ED53 /* System.Data__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__5.cpp; sourceTree = SOURCE_ROOT; };
		C1D2C2900B1B1033C50FDC84 /* Assembly-CSharp__22.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__22.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__22.cpp"; sourceTree = SOURCE_ROOT; };
		C20B2680F0033B47C3BB8134 /* UnityEngine.UIElementsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UIElementsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C24D3A15A69D047679E1AF2F /* UnityEngine.AudioModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AudioModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp; sourceTree = SOURCE_ROOT; };
		C3C2D72757B1A8A28AE76822 /* mscorlib__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp; sourceTree = SOURCE_ROOT; };
		C40C9D5FB5363330524518CB /* Assembly-CSharp.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"; sourceTree = SOURCE_ROOT; };
		C531584915BF8819C6496BE0 /* Newtonsoft.Json_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Newtonsoft.Json_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C6120AAF314BB32048EF255D /* GDTLoadAdParams.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTLoadAdParams.h; path = Libraries/Plugins/iOS/GdtMob/GDTLoadAdParams.h; sourceTree = SOURCE_ROOT; };
		C684380BEA113884C86ACE5A /* unity-plugin-library.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "unity-plugin-library.a"; path = "Libraries/Plugins/iOS/unity-plugin-library.a"; sourceTree = SOURCE_ROOT; };
		C6B21A7CC65E934695520510 /* System.Core__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Core__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Core__4.cpp; sourceTree = SOURCE_ROOT; };
		C6D748933A8CB803421827FF /* Unity.Services.Core.Environments.Internal_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Environments.Internal_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C7004552E7186E16DD3B3A69 /* UnityAdsLoadListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UnityAdsLoadListener.h; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsLoadListener.h; sourceTree = SOURCE_ROOT; };
		C74C563E02CE38C5DC9590E7 /* Generics__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp; sourceTree = SOURCE_ROOT; };
		C801E7F3CBAB00070AC52BC6 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		C824AEFE49BF546C3D3A94CF /* GDTMobUPluginUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTMobUPluginUtil.h; path = Libraries/Plugins/iOS/GdtMob/GDTMobUPluginUtil.h; sourceTree = SOURCE_ROOT; };
		C862D3A89A9BA3E4ED7205BD /* System.Configuration_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Configuration_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c; sourceTree = SOURCE_ROOT; };
		C865CD21D06B3F35209C5FA5 /* Generics__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__7.cpp; sourceTree = SOURCE_ROOT; };
		C86635207E96D385500A4A2C /* Assembly-CSharp__38.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__38.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__38.cpp"; sourceTree = SOURCE_ROOT; };
		C89119454D57EDC42A59D87C /* UnityEngine.TextRenderingModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TextRenderingModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp; sourceTree = SOURCE_ROOT; };
		CA2FE0E937B9F3EBF23597D5 /* Il2CppCCTypeValuesTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCTypeValuesTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp; sourceTree = SOURCE_ROOT; };
		CAB37C0B5E85683C4FB82A76 /* mscorlib__17.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__17.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp; sourceTree = SOURCE_ROOT; };
		CB4953DB01619C32AB0D6487 /* Generics__14.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__14.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__14.cpp; sourceTree = SOURCE_ROOT; };
		CB6390412026FA54BD728F2E /* System__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System__3.cpp; sourceTree = SOURCE_ROOT; };
		CCD3C7F4388D085C699D9950 /* Generics__22.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__22.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__22.cpp; sourceTree = SOURCE_ROOT; };
		CD4079BF78197D083920D996 /* UnityEngine.UnityAnalyticsModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.UnityAnalyticsModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		CD891A8178C62435E30998FB /* System.Data__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__2.cpp; sourceTree = SOURCE_ROOT; };
		CD9445190A8424A52C234D9A /* Unity.Services.Core.Environments.Internal.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.Environments.Internal.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal.cpp; sourceTree = SOURCE_ROOT; };
		CDE326D9BC63A811E64F0402 /* System.Data__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__7.cpp; sourceTree = SOURCE_ROOT; };
		CE1CD281A8020E57A0495C3E /* mscorlib__18.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__18.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp; sourceTree = SOURCE_ROOT; };
		CEEF9247F012D6069E397F57 /* Il2CppUnresolvedIndirectCallStubs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppUnresolvedIndirectCallStubs.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp; sourceTree = SOURCE_ROOT; };
		CF08E8E4A81091C5FDB9F66D /* GenericMethods__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp; sourceTree = SOURCE_ROOT; };
		CF27740112D78F14DED48EF7 /* System.Data__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__6.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__6.cpp; sourceTree = SOURCE_ROOT; };
		CF2D9CD79707BC66F0D724D2 /* UnityEngine.TextCoreTextEngineModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextCoreTextEngineModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		CFD9F8BCEB8E8AB7DDA7F121 /* GenericMethods__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__10.cpp; sourceTree = SOURCE_ROOT; };
		D0377ED2DD21A2FE426123F1 /* Assembly-CSharp__40.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__40.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__40.cpp"; sourceTree = SOURCE_ROOT; };
		D0609838D7B79343BB1B66CD /* mscorlib__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__12.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp; sourceTree = SOURCE_ROOT; };
		D1002655EF7EC77F763671BA /* System.Xml.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Xml.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp; sourceTree = SOURCE_ROOT; };
		D36225A528040BD949BFC983 /* Generics__30.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__30.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__30.cpp; sourceTree = SOURCE_ROOT; };
		D36D733DA6A3052F599DC7CE /* mscorlib__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp; sourceTree = SOURCE_ROOT; };
		D3F41D4408A318CCAE49693A /* Il2CppGenericMethodDefinitions.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericMethodDefinitions.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodDefinitions.c; sourceTree = SOURCE_ROOT; };
		D51F4747FAFFCAC3B247BB05 /* Generics__26.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__26.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__26.cpp; sourceTree = SOURCE_ROOT; };
		D52C8290C7A4A82448D38C34 /* Generics__55.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__55.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__55.cpp; sourceTree = SOURCE_ROOT; };
		D556450C3DBEECE838996A3D /* LaunchScreen-iPad.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "LaunchScreen-iPad.png"; sourceTree = SOURCE_ROOT; };
		D5FA3667394130C0BCCF8B0E /* ApplePrivacyManifestsMerge.txt */ = {isa = PBXFileReference; lastKnownFileType = text; name = ApplePrivacyManifestsMerge.txt; path = UnityFramework/ApplePrivacyManifestsMerge.txt; sourceTree = SOURCE_ROOT; };
		D5FC3F6D543FBD571547E70A /* Unity.Services.Core.Configuration_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core.Configuration_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration_CodeGen.c; sourceTree = SOURCE_ROOT; };
		D61973816F5E9F99E95C8C57 /* UnityEngine.IMGUIModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.IMGUIModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp; sourceTree = SOURCE_ROOT; };
		D71616D07C71DE37F669A378 /* ProCamera2D.Runtime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ProCamera2D.Runtime.cpp; path = Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime.cpp; sourceTree = SOURCE_ROOT; };
		D740AE7756D71AACB0CCF7C7 /* Generics__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__8.cpp; sourceTree = SOURCE_ROOT; };
		D7503FB91502F4DD896CF714 /* UnityEngine.UIElementsModule__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__10.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp; sourceTree = SOURCE_ROOT; };
		D7A4EDC694A07687D3E0DA67 /* NativeDialogsPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = NativeDialogsPlugin.h; path = Libraries/Plugins/iOS/NativeDialogsPlugin.h; sourceTree = SOURCE_ROOT; };
		D82DCFBB0E8000A5005D6AD8 /* main.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = main.mm; path = Classes/main.mm; sourceTree = SOURCE_ROOT; };
		D834394372B192753C19BD24 /* FairyGUI__7.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__7.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__7.cpp; sourceTree = SOURCE_ROOT; };
		D8520D972A75A0B3FACF80FD /* Generics__47.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__47.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__47.cpp; sourceTree = SOURCE_ROOT; };
		D8A1C72A0E8063A1000160D3 /* libiPhone-lib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libiPhone-lib.a"; path = "Libraries/libiPhone-lib.a"; sourceTree = SOURCE_ROOT; };
		D904E5E376B4426FDF7E041B /* DOTween.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = DOTween.cpp; path = Il2CppOutputProject/Source/il2cppOutput/DOTween.cpp; sourceTree = SOURCE_ROOT; };
		D9AC6836413CDFEB9D3D4DCA /* UnityEngine.UnityAnalyticsModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UnityAnalyticsModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp; sourceTree = SOURCE_ROOT; };
		DA1731C322C733D4F9215478 /* UnityEngine.AnimationModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AnimationModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp; sourceTree = SOURCE_ROOT; };
		DA514D93A8CEF6AF3DEF3121 /* IAppsTeamIOSUnitl.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = IAppsTeamIOSUnitl.mm; path = Libraries/Plugins/iOS/IApps/IAppsTeamIOSUnitl.mm; sourceTree = SOURCE_ROOT; };
		DB88045514EBA3611759BAB0 /* Assembly-CSharp__39.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__39.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__39.cpp"; sourceTree = SOURCE_ROOT; };
		DC1C002DA32AACE7AD91663B /* GenericMethods__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods__9.cpp; sourceTree = SOURCE_ROOT; };
		DC641E85B09D087E3E294418 /* GenericMethods.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GenericMethods.cpp; path = Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp; sourceTree = SOURCE_ROOT; };
		DC6D25CF35344C8E6CBCC43D /* Generics__71.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__71.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__71.cpp; sourceTree = SOURCE_ROOT; };
		DCC618ACC1C885764FCFF788 /* mscorlib__19.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__19.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp; sourceTree = SOURCE_ROOT; };
		DCFF8C91896C4981029286E5 /* System.Core_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Core_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c; sourceTree = SOURCE_ROOT; };
		DD952DDCAB0485953E3CE902 /* Il2CppCCFieldValuesTable.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCFieldValuesTable.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp; sourceTree = SOURCE_ROOT; };
		DDB2F8BB5DC4E5D804C3C134 /* Il2CppCCalculateFieldValues1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Il2CppCCalculateFieldValues1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp; sourceTree = SOURCE_ROOT; };
		DE35E560F61A1FC277AE3BDA /* Assembly-CSharp__12.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__12.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__12.cpp"; sourceTree = SOURCE_ROOT; };
		DE85ECC8173A198BB20AF9F1 /* Assembly-CSharp-firstpass.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp-firstpass.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass.cpp"; sourceTree = SOURCE_ROOT; };
		DFED97C0BA877B9D186DA8C0 /* GDTAdTestSetting.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTAdTestSetting.h; path = Libraries/Plugins/iOS/GdtMob/GDTAdTestSetting.h; sourceTree = SOURCE_ROOT; };
		E025EE58BA4B0AC13F8239F1 /* HOTween.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = HOTween.cpp; path = Il2CppOutputProject/Source/il2cppOutput/HOTween.cpp; sourceTree = SOURCE_ROOT; };
		E0263673B7642BE0F04DE813 /* UnityEngine.UI.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UI.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp; sourceTree = SOURCE_ROOT; };
		E0994B957EB074EB69BD9ABA /* Assembly-CSharp__6.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__6.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__6.cpp"; sourceTree = SOURCE_ROOT; };
		E28E0AD14FECA0BE6084E4CE /* UnityEngine.TilemapModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.TilemapModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp; sourceTree = SOURCE_ROOT; };
		E2A51863FBA765167579306C /* FairyGUI__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__3.cpp; sourceTree = SOURCE_ROOT; };
		E2D1F12E1B76EBDB3AEA4278 /* FairyGUI__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = FairyGUI__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/FairyGUI__9.cpp; sourceTree = SOURCE_ROOT; };
		E33ECA1CB8CAF0C496E0196E /* Generics__70.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__70.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__70.cpp; sourceTree = SOURCE_ROOT; };
		E518660204D9E27F74306195 /* UnityEngine.TextCoreFontEngineModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.TextCoreFontEngineModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		E63E22A2D76ACB6254C3A017 /* mscorlib__13.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__13.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp; sourceTree = SOURCE_ROOT; };
		E6A068D0AE38570B96122437 /* Assembly-CSharp__10.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__10.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__10.cpp"; sourceTree = SOURCE_ROOT; };
		E6DD1EF92C698A076367A202 /* Generics__40.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__40.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__40.cpp; sourceTree = SOURCE_ROOT; };
		E79F4C19A708A497C9443FB8 /* Assembly-CSharp__20.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__20.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__20.cpp"; sourceTree = SOURCE_ROOT; };
		E7CE71A354C4D7AD8FC71667 /* GDTUnifiedInterstitialAdNetworkConnectorProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedInterstitialAdNetworkConnectorProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedInterstitialAdNetworkConnectorProtocol.h; sourceTree = SOURCE_ROOT; };
		E8A3C67A913D9E240397EFB1 /* UnityEngine.UIElementsModule__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp; sourceTree = SOURCE_ROOT; };
		E8E6BB924BD5BB712770450E /* System.Data__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__1.cpp; sourceTree = SOURCE_ROOT; };
		E93AFE0C4E58671EE56041CE /* Unity.Services.Core.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Services.Core.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.cpp; sourceTree = SOURCE_ROOT; };
		E97C06AF3FC01BD0933580D7 /* GDTUnifiedBannerAdNetworkConnectorProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedBannerAdNetworkConnectorProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedBannerAdNetworkConnectorProtocol.h; sourceTree = SOURCE_ROOT; };
		E9A7659A3EF108621EF5E740 /* mscorlib__23.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__23.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__23.cpp; sourceTree = SOURCE_ROOT; };
		E9C72CBC632293D0FE2E3D56 /* System.Drawing_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = System.Drawing_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/System.Drawing_CodeGen.c; sourceTree = SOURCE_ROOT; };
		EA841D12341C9363E02EE00F /* Unity.TextMeshPro__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp; sourceTree = SOURCE_ROOT; };
		EAFCEA713D2A37FF5D81C6CB /* mscorlib__9.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__9.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp; sourceTree = SOURCE_ROOT; };
		EB3408953E5F4E8B5F088360 /* Generics__62.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__62.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__62.cpp; sourceTree = SOURCE_ROOT; };
		EE54DC7E56FA015DDC9659BD /* libil2cpp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libil2cpp.a; path = Libraries/libil2cpp.a; sourceTree = SOURCE_ROOT; };
		EF1F31341EF3BB1E55473A8D /* UnityAdsInitializationListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = UnityAdsInitializationListener.h; path = Libraries/com.unity.ads/Plugins/iOS/UnityAdsInitializationListener.h; sourceTree = SOURCE_ROOT; };
		F02A78402345158DB659E78E /* UnityEngine.AudioModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.AudioModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F09919364E13EC87792FCBE3 /* Unity.TextMeshPro__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp; sourceTree = SOURCE_ROOT; };
		F103AF1CC7D2D96E788D51D7 /* Assembly-CSharp__30.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__30.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__30.cpp"; sourceTree = SOURCE_ROOT; };
		F1F28F611D1E7EE7CCC25F71 /* mscorlib__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__1.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp; sourceTree = SOURCE_ROOT; };
		F24B06F17D255CA359EB4840 /* UnityEngine.SpriteShapeModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.SpriteShapeModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F2E4BD281D8D2761198E3AA0 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = PrivacyInfo.xcprivacy; name = PrivacyInfo.xcprivacy; path = UnityFramework/PrivacyInfo.xcprivacy; sourceTree = SOURCE_ROOT; };
		F2E5D9A741FB7ADF7324BC5B /* System.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.cpp; sourceTree = SOURCE_ROOT; };
		F311E9EE7D978D35B6A8A0D8 /* Generics__38.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__38.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__38.cpp; sourceTree = SOURCE_ROOT; };
		F31E800B043032E14DC17096 /* Unity.Addressables.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.Addressables.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Addressables.cpp; sourceTree = SOURCE_ROOT; };
		F3BBCEB5ABC530BC378419BE /* LitJson_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = LitJson_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/LitJson_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F479FCE1D625BC61FFD939EF /* Generics__32.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__32.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__32.cpp; sourceTree = SOURCE_ROOT; };
		F4850C2D77359384155CE580 /* HOTween_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = HOTween_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/HOTween_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F4F4286D3BE59E565564055C /* UnityEngine.AssetBundleModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.AssetBundleModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule.cpp; sourceTree = SOURCE_ROOT; };
		F55249155698357E094F40BE /* UnityEngine.UIElementsModule__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.UIElementsModule__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp; sourceTree = SOURCE_ROOT; };
		F5C0D9C036EFC8EF66FE06C4 /* mscorlib__15.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = mscorlib__15.cpp; path = Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp; sourceTree = SOURCE_ROOT; };
		F6361BB52195CE5D00F61766 /* UnitySharedDecls.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnitySharedDecls.h; sourceTree = "<group>"; };
		F6CDB022FB8DC8CFB499BE10 /* Generics__3.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__3.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp; sourceTree = SOURCE_ROOT; };
		F7B9CB2246993CC9923F6456 /* Assembly-CSharp__33.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__33.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__33.cpp"; sourceTree = SOURCE_ROOT; };
		F828452E792C2569D9C775E5 /* Assembly-CSharp__1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = "Assembly-CSharp__1.cpp"; path = "Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"; sourceTree = SOURCE_ROOT; };
		F848B392DAA96A64C479853F /* Generics__52.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__52.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__52.cpp; sourceTree = SOURCE_ROOT; };
		F8628423EEBFA41D56AD3B0A /* GoogleMobileAds.Common_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = GoogleMobileAds.Common_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Common_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F954EB2BCBE7E56336AE19E3 /* LitJson.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = LitJson.cpp; path = Il2CppOutputProject/Source/il2cppOutput/LitJson.cpp; sourceTree = SOURCE_ROOT; };
		F95D837157339CB183E2FC76 /* System.Data__8.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__8.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__8.cpp; sourceTree = SOURCE_ROOT; };
		F98883DE16B476EDC83A25E2 /* UnityEngine.ParticleSystemModule_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = UnityEngine.ParticleSystemModule_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule_CodeGen.c; sourceTree = SOURCE_ROOT; };
		F9A594EF02BB90976F5CC883 /* UnityAnalyticsWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = UnityAnalyticsWrapper.m; path = Libraries/com.unity.ads/Plugins/iOS/UnityAnalyticsWrapper.m; sourceTree = SOURCE_ROOT; };
		F9F1230E7B00F6B2FFC7A8EB /* UnityEngine.Physics2DModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.Physics2DModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp; sourceTree = SOURCE_ROOT; };
		FA0D72F8EDBD0798BCFC9916 /* UnityEngine.ParticleSystemModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = UnityEngine.ParticleSystemModule.cpp; path = Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule.cpp; sourceTree = SOURCE_ROOT; };
		FAA2E88DA984D6D226870B21 /* System.Data__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = System.Data__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/System.Data__4.cpp; sourceTree = SOURCE_ROOT; };
		FAB4D031E79E43D0CC876610 /* Il2CppGenericMethodPointerTable.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Il2CppGenericMethodPointerTable.c; path = Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodPointerTable.c; sourceTree = SOURCE_ROOT; };
		FAFE6F25F97E0825A99B77C2 /* Generics__4.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__4.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp; sourceTree = SOURCE_ROOT; };
		FC0B20A11B7A4F0B00FDFC55 /* OnDemandResources.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = OnDemandResources.mm; sourceTree = "<group>"; };
		FC3D7EBE16D2621600D1BD0D /* CrashReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CrashReporter.h; sourceTree = "<group>"; };
		FC85CCB916C3ED8000BAF7C7 /* CrashReporter.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CrashReporter.mm; sourceTree = "<group>"; };
		FC85CCBA16C3ED8000BAF7C7 /* PLCrashReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLCrashReporter.h; sourceTree = "<group>"; };
		FD0DA59C559B1835D2E1DE26 /* Newtonsoft.Json__5.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Newtonsoft.Json__5.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__5.cpp; sourceTree = SOURCE_ROOT; };
		FD168AB2453FC7718218F844 /* GDTServerBiddingResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTServerBiddingResult.h; path = Libraries/Plugins/iOS/GdtMob/GDTServerBiddingResult.h; sourceTree = SOURCE_ROOT; };
		FD6E23C902477A19B0E251BF /* unity_services_locale.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = unity_services_locale.mm; path = Libraries/com.unity.services.analytics/Runtime/Plugins/iOS/unity_services_locale.mm; sourceTree = SOURCE_ROOT; };
		FE1C49F2D96DA6C9DFE1AB2B /* GDTUnifiedBannerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedBannerView.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedBannerView.h; sourceTree = SOURCE_ROOT; };
		FEFF7B4D5BE7A4AB429E1676 /* Generics__27.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Generics__27.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Generics__27.cpp; sourceTree = SOURCE_ROOT; };
		FF37CE76CE459AC4C81D7B58 /* GDTUnifiedNativeAdDataObject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTUnifiedNativeAdDataObject.h; path = Libraries/Plugins/iOS/GdtMob/GDTUnifiedNativeAdDataObject.h; sourceTree = SOURCE_ROOT; };
		FF68204794520066FE5268D8 /* GDTNativeExpressAdNetworkConnectorProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GDTNativeExpressAdNetworkConnectorProtocol.h; path = Libraries/Plugins/iOS/GdtMob/GDTNativeExpressAdNetworkConnectorProtocol.h; sourceTree = SOURCE_ROOT; };
		FF7E553300BBA68968E5F6CF /* ProCamera2D.Runtime__2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ProCamera2D.Runtime__2.cpp; path = Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime__2.cpp; sourceTree = SOURCE_ROOT; };
		FF80ED27C0BED9BC1BA9070D /* Unity.TextMeshPro.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Unity.TextMeshPro.cpp; path = Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp; sourceTree = SOURCE_ROOT; };
		FF93594860731B504F3020F5 /* Unity.Services.Core_CodeGen.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = Unity.Services.Core_CodeGen.c; path = Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core_CodeGen.c; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1D60588F0D05DD3D006BFB54 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EF5C426DB71727665936F6DE /* StoreKit.framework in Frameworks */,
				987F3D7761A3B7A884300CED /* Pods_Unity_iPhone.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5623C57017FDCB0800090B9E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5623C57717FDCB0800090B9E /* UIKit.framework in Frameworks */,
				5623C57617FDCB0800090B9E /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F4E059D2717216D00A2CBE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25AB99213FB47800354C27 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00000000008063A1000160D3 /* libiPhone-lib.a in Frameworks */,
				8A3831B5246956AA00CD74FD /* Metal.framework in Frameworks */,
				9D690CCF21BFD341005026B1 /* Security.framework in Frameworks */,
				9D690CD021BFD349005026B1 /* MediaToolbox.framework in Frameworks */,
				9D690CD221BFD36C005026B1 /* CoreText.framework in Frameworks */,
				9D690CD321BFD376005026B1 /* AudioToolbox.framework in Frameworks */,
				9D690CD421BFD37E005026B1 /* AVFoundation.framework in Frameworks */,
				8A20382D213D4B3C005E6C56 /* AVKit.framework in Frameworks */,
				9D690CD521BFD388005026B1 /* CFNetwork.framework in Frameworks */,
				9D690CD621BFD391005026B1 /* CoreGraphics.framework in Frameworks */,
				9D690CD721BFD39D005026B1 /* CoreMedia.framework in Frameworks */,
				9D690CD821BFD3A5005026B1 /* CoreMotion.framework in Frameworks */,
				9D690CD921BFD3AC005026B1 /* CoreVideo.framework in Frameworks */,
				9D690CDA21BFD3B5005026B1 /* Foundation.framework in Frameworks */,
				9D690CDB21BFD3BF005026B1 /* OpenAL.framework in Frameworks */,
				9D690CDD21BFD3D0005026B1 /* QuartzCore.framework in Frameworks */,
				9D690CDE21BFD3D9005026B1 /* SystemConfiguration.framework in Frameworks */,
				9D690CDF21BFD3E3005026B1 /* UIKit.framework in Frameworks */,
				7F4E05AB2717219200A2CBE4 /* libGameAssembly.a in Frameworks */,
				9D0A618B21BFE7F30094DC33 /* libiconv.2.tbd in Frameworks */,
				87FDF0A3B0DACAD8C27B6FAA /* libUniWebView.a in Frameworks */,
				108959862548F94A325ECC2D /* unity-plugin-library.a in Frameworks */,
				B20A79306CC31F7F33C97C34 /* libGDTMobSDK.a in Frameworks */,
				1D2669CE14FED93CF852BF28 /* UnityAds.framework in Frameworks */,
				1D45A5CCCCC4E76191AAF1D0 /* libil2cpp.a in Frameworks */,
				63ACF7178776C648B96974EB /* baselib.a in Frameworks */,
				FEF4673A8DD14865BB898CF1 /* StoreKit.framework in Frameworks */,
				28818E3FDD0A6E7A3535D165 /* AdSupport.framework in Frameworks */,
				00B1680459E35383A06DC548 /* CoreTelephony.framework in Frameworks */,
				574A8D1BFDF4103B13A6538A /* GameController.framework in Frameworks */,
				87E9527F8F67B766BCA27955 /* Pods_UnityFramework.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0056F713DC32E98E76CB8D96 /* Plugins */ = {
			isa = PBXGroup;
			children = (
				43AFB15B103A7DDBCA9B69FE /* UnityPurchasing */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		04B0C11757E9C7B105072285 /* Source */ = {
			isa = PBXGroup;
			children = (
				45C76ABEC14C6E10C9CFC809 /* il2cppOutput */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		07BE6E120ECF49C6EDFEEF03 /* Runtime */ = {
			isa = PBXGroup;
			children = (
				E58BE20AAA94EF7EBCF4A7A7 /* Plugins */,
			);
			path = Runtime;
			sourceTree = "<group>";
		};
		08E06EF576D79C4643098BCC /* Il2CppOutputProject */ = {
			isa = PBXGroup;
			children = (
				04B0C11757E9C7B105072285 /* Source */,
			);
			path = Il2CppOutputProject;
			sourceTree = "<group>";
		};
		119493F9038712BFEDCA47D4 /* com.unity.ads */ = {
			isa = PBXGroup;
			children = (
				4F58672C0138D2469518A20A /* Plugins */,
			);
			path = com.unity.ads;
			sourceTree = "<group>";
		};
		187DBE13AC7564F758894056 /* IApps */ = {
			isa = PBXGroup;
			children = (
				1BAA14B679EE49250AFB248E /* CustomAppController.mm */,
				16814697D32894BA4CF74524 /* IAppsTeamIOSUnitl.h */,
				DA514D93A8CEF6AF3DEF3121 /* IAppsTeamIOSUnitl.mm */,
				BC1E972BAE1F1493505BFCCD /* IAppsTeamIOSUntilBinding.m */,
				348EAB3637E0B7146935FAEE /* RSSecrets.h */,
				715BCED89CFA351F61DC42B9 /* RSSecrets.m */,
				A78015B5DCE025229C145EB5 /* Reachability.h */,
				889E696927F9303A40F5E3F4 /* Reachability.m */,
			);
			path = IApps;
			sourceTree = "<group>";
		};
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				1D6058910D05DD3D006BFB54 /* EduApp.app */,
				5623C57317FDCB0800090B9E /* Unity-iPhone Tests.xctest */,
				9D25AB9D213FB47800354C27 /* UnityFramework.framework */,
				7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* CustomTemplate */ = {
			isa = PBXGroup;
			children = (
				D82DCFB50E8000A5005D6AD8 /* Classes */,
				29B97323FDCFA39411CA2CEA /* Frameworks */,
				08E06EF576D79C4643098BCC /* Il2CppOutputProject */,
				D8A1C7220E80637F000160D3 /* Libraries */,
				9DA3B0422174CB96001678C7 /* MainApp */,
				D06AF7B5B35D8943CEEC814F /* Pods */,
				19C28FACFE9D520D11CA2CBB /* Products */,
				5623C57817FDCB0800090B9E /* Unity-iPhone Tests */,
				9D25AB9E213FB47800354C27 /* UnityFramework */,
				AA31BF961B55660D0013FB1B /* Data */,
				3E1B402ABB1754FC384CBC29 /* Dummy.swift */,
				56C56C9717D6015100616839 /* Images.xcassets */,
				8D1107310486CEB800E47090 /* Info.plist */,
				D556450C3DBEECE838996A3D /* LaunchScreen-iPad.png */,
				A6444ABDD2799CC877EBAF7E /* LaunchScreen-iPad.storyboard */,
				5A3A1B4C3FB1FC90023B942E /* LaunchScreen-iPhone.storyboard */,
				1E24B93E9D27D99850693BAF /* LaunchScreen-iPhoneLandscape.png */,
				681AA63C19788ACA381FB8C1 /* LaunchScreen-iPhonePortrait.png */,
			);
			name = CustomTemplate;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				FF435E14D8C85D41F328328F /* com.unity.ads */,
				7F36C11013C5C673007FBDD9 /* AVFoundation.framework */,
				8A20382C213D4B3C005E6C56 /* AVKit.framework */,
				65F37FDAB09705DF055776EF /* AdSupport.framework */,
				8358D1B70ED1CC3700E3A684 /* AudioToolbox.framework */,
				56FD43950ED4745200FE3770 /* CFNetwork.framework */,
				56B7959A1442E0F20026B3DD /* CoreGraphics.framework */,
				5692F3DC0FA9D8E500EBA2F1 /* CoreLocation.framework */,
				7F36C10E13C5C673007FBDD9 /* CoreMedia.framework */,
				56B795C11442E1100026B3DD /* CoreMotion.framework */,
				94CD3AD5F74D4265461E0C6D /* CoreTelephony.framework */,
				AA5D99861AFAD3C800B27605 /* CoreText.framework */,
				7F36C10F13C5C673007FBDD9 /* CoreVideo.framework */,
				1D30AB110D05D00D00671497 /* Foundation.framework */,
				21ECD27EDF8EB21168769097 /* GameController.framework */,
				960391211D6CE46E003BF157 /* MediaToolbox.framework */,
				8A3831B4246956AA00CD74FD /* Metal.framework */,
				83B2574B0E63022200468741 /* OpenAL.framework */,
				766DE0A8CD087FAEDF341E93 /* Pods_UnityFramework.framework */,
				2E1495550DA3888ACBD5B8B0 /* Pods_Unity_iPhone.framework */,
				83B2570A0E62FF8A00468741 /* QuartzCore.framework */,
				5BAD78601F2B5A59006103DE /* Security.framework */,
				C801E7F3CBAB00070AC52BC6 /* StoreKit.framework */,
				56BCBA380FCF049A0030C3B2 /* SystemConfiguration.framework */,
				830B5C100E5ED4C100C7819F /* UIKit.framework */,
				9D0A618A21BFE7F30094DC33 /* libiconv.2.tbd */,
			);
			path = Frameworks;
			sourceTree = "<group>";
		};
		3C85E63663639261B15967D6 /* Runtime */ = {
			isa = PBXGroup;
			children = (
				B1D503123E4E35EC4E8CA9E9 /* Device */,
			);
			path = Runtime;
			sourceTree = "<group>";
		};
		3D69E3BD95DB591F74C9AF1C /* Plugins */ = {
			isa = PBXGroup;
			children = (
				4E2AB83293ADD1F20EF1C60B /* iOS */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		3DD7D953C65EC4A994FC2E5A /* GdtMob */ = {
			isa = PBXGroup;
			children = (
				567AABCBAFF94733CABF9ED7 /* GDTAdParams.h */,
				161CEB4CFA7E0007CC483626 /* GDTAdProtocol.h */,
				DFED97C0BA877B9D186DA8C0 /* GDTAdTestSetting.h */,
				0999F02616A40C59D4204935 /* GDTBaseAdNetworkAdapterProtocol.h */,
				C6120AAF314BB32048EF255D /* GDTLoadAdParams.h */,
				615F1EC7832D2B8CB2005C27 /* GDTLogoView.h */,
				8224070CACC22FB63E254585 /* GDTMediaView.h */,
				B8217FFE13E391CF878EB017 /* GDTMobBing.m */,
				02CBAEF2E6358F55B77CF190 /* GDTMobInterstitial.h */,
				2619CE6330647084F758F15C /* GDTMobInterstitialManager.h */,
				B36C010DDFE4675872BFA156 /* GDTMobInterstitialManager.mm */,
				3A1E61D20C1CA4561B648BDD /* GDTMobManager.h */,
				A9860406B2D3274C13FA2227 /* GDTMobManager.mm */,
				C824AEFE49BF546C3D3A94CF /* GDTMobUPluginUtil.h */,
				06307F0DDC88536113EE103A /* GDTMobUPluginUtil.m */,
				9E192277BBADB145533A1FCC /* GDTMobUTypes.h */,
				5A3A145D9786ACFE6D088E06 /* GDTNativeExpressAd.h */,
				83BDD9D20D2D7764D399F567 /* GDTNativeExpressAdNetworkAdapterProtocol.h */,
				FF68204794520066FE5268D8 /* GDTNativeExpressAdNetworkConnectorProtocol.h */,
				9BF21BC9FDBD95DE7F910C71 /* GDTNativeExpressAdView.h */,
				94AD788D56925343C4E55E99 /* GDTNativeExpressAdViewAdapterProtocol.h */,
				6B4143260DD352AF6D26E4EA /* GDTPrivacyConfiguration.h */,
				9D8D2D85D4E6EE2B82F9C0D1 /* GDTRewardVideoAd.h */,
				51C9F166688B5AF41217532F /* GDTRewardVideoAdNetworkAdapterProtocol.h */,
				BA0580A085177C256385613D /* GDTRewardVideoAdNetworkConnectorProtocol.h */,
				A0EAFA894ED09EBB217A485C /* GDTSDKConfig.h */,
				BC2B619F8DB8F40FD73613B7 /* GDTSDKDefines.h */,
				FD168AB2453FC7718218F844 /* GDTServerBiddingResult.h */,
				2500BFE7D99AC01CA8516A76 /* GDTServerSideVerificationOptions.h */,
				BAAB40540DA41C1B454DDA91 /* GDTSplashAd.h */,
				4BA967BA7CC936378ADB4F4D /* GDTSplashAdNetworkAdapterProtocol.h */,
				7C8160F6348A42D9576710E3 /* GDTSplashAdNetworkConnectorProtocol.h */,
				709BAA32BB8A406717625013 /* GDTUnifiedBannerAdNetworkAdapterProtocol.h */,
				E97C06AF3FC01BD0933580D7 /* GDTUnifiedBannerAdNetworkConnectorProtocol.h */,
				FE1C49F2D96DA6C9DFE1AB2B /* GDTUnifiedBannerView.h */,
				2A03AC66624754249B56C9B3 /* GDTUnifiedInterstitialAd.h */,
				5FF625858089E21BDF0720A0 /* GDTUnifiedInterstitialAdNetworkAdapterProtocol.h */,
				E7CE71A354C4D7AD8FC71667 /* GDTUnifiedInterstitialAdNetworkConnectorProtocol.h */,
				310F5D7A4EECCB4E3739971F /* GDTUnifiedNativeAd.h */,
				FF37CE76CE459AC4C81D7B58 /* GDTUnifiedNativeAdDataObject.h */,
				4397B2765ACE4841ADABF5AF /* GDTUnifiedNativeAdNetworkAdapterProtocol.h */,
				5CD37B9613BB23D856E08103 /* GDTUnifiedNativeAdNetworkConnectorProtocol.h */,
				72831D4D6869FD6E7154268C /* GDTUnifiedNativeAdView.h */,
				726240544A4D431FB28D10E4 /* GDTVideoAdReporter.h */,
				4C378673D5DF403FDFC49E5D /* GDTVideoConfig.h */,
				5C57731C0A13EC17F5128874 /* libGDTMobSDK.a */,
			);
			path = GdtMob;
			sourceTree = "<group>";
		};
		3EAD9489F626B5A5A0405FB4 /* UserIdProviders */ = {
			isa = PBXGroup;
			children = (
				5D2B6D2D12B065A216EB6E1F /* NSUserDefaults.mm */,
			);
			path = UserIdProviders;
			sourceTree = "<group>";
		};
		407ABD7A7578DA24D8BC1440 /* iOS */ = {
			isa = PBXGroup;
			children = (
				47169E11833C56E7D860700C /* UnityEarlyTransactionObserver.h */,
				5E7DE52DB56AC9BD83B13EEB /* UnityEarlyTransactionObserver.mm */,
				2C95D7846077093786C47710 /* UnityPurchasing.h */,
				3B5211DE4BADE84A27EFC97A /* UnityPurchasing.m */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		43AFB15B103A7DDBCA9B69FE /* UnityPurchasing */ = {
			isa = PBXGroup;
			children = (
				407ABD7A7578DA24D8BC1440 /* iOS */,
			);
			path = UnityPurchasing;
			sourceTree = "<group>";
		};
		45C76ABEC14C6E10C9CFC809 /* il2cppOutput */ = {
			isa = PBXGroup;
			children = (
				DE85ECC8173A198BB20AF9F1 /* Assembly-CSharp-firstpass.cpp */,
				732F023F742A46D0095260A9 /* Assembly-CSharp-firstpass_CodeGen.c */,
				5B53B64A88569D3F8EB8A9F1 /* Assembly-CSharp-firstpass__1.cpp */,
				70FE2BDFB277A35F0A3B88EE /* Assembly-CSharp-firstpass__2.cpp */,
				B6CCBDA4E2034619F101BAC7 /* Assembly-CSharp-firstpass__3.cpp */,
				64C9D91441AE69C1D77D155E /* Assembly-CSharp-firstpass__4.cpp */,
				C40C9D5FB5363330524518CB /* Assembly-CSharp.cpp */,
				359EA6DE6F0ADB889FC61B63 /* Assembly-CSharp_CodeGen.c */,
				F828452E792C2569D9C775E5 /* Assembly-CSharp__1.cpp */,
				E6A068D0AE38570B96122437 /* Assembly-CSharp__10.cpp */,
				9C5F4A52C023FBEBCB9B9C51 /* Assembly-CSharp__11.cpp */,
				DE35E560F61A1FC277AE3BDA /* Assembly-CSharp__12.cpp */,
				A882C70BF6B0539484D63840 /* Assembly-CSharp__13.cpp */,
				A239769F708DCF300ED81D67 /* Assembly-CSharp__14.cpp */,
				03A87A26A63177A70A5AC67E /* Assembly-CSharp__15.cpp */,
				1492999617EA37FB6D4727C7 /* Assembly-CSharp__16.cpp */,
				4114445645D738D6E9CC35B8 /* Assembly-CSharp__17.cpp */,
				1F951416510E376441DD5ABD /* Assembly-CSharp__18.cpp */,
				17A88A24B72C4E31367418A7 /* Assembly-CSharp__19.cpp */,
				0625BA89AA0F71807DF4285A /* Assembly-CSharp__2.cpp */,
				E79F4C19A708A497C9443FB8 /* Assembly-CSharp__20.cpp */,
				444A6E27F397365C367A0E17 /* Assembly-CSharp__21.cpp */,
				C1D2C2900B1B1033C50FDC84 /* Assembly-CSharp__22.cpp */,
				223EBC6A186ED13AA8BAEDBA /* Assembly-CSharp__23.cpp */,
				0A99F41A6E79CC9C6BE2D738 /* Assembly-CSharp__24.cpp */,
				3C54469E02AE026A630B4244 /* Assembly-CSharp__25.cpp */,
				39A4F548F86A869BD5CA86C0 /* Assembly-CSharp__26.cpp */,
				465EB73EB7CAF8A291ADD317 /* Assembly-CSharp__27.cpp */,
				93CB0776020C99B75942BABB /* Assembly-CSharp__28.cpp */,
				625C59B7F72E20CA1DAD37D1 /* Assembly-CSharp__29.cpp */,
				3BF0F548641EA67234EF4822 /* Assembly-CSharp__3.cpp */,
				F103AF1CC7D2D96E788D51D7 /* Assembly-CSharp__30.cpp */,
				91B3C31E164148A3C3C440BA /* Assembly-CSharp__31.cpp */,
				BE89081E35159488B1CFEDD6 /* Assembly-CSharp__32.cpp */,
				F7B9CB2246993CC9923F6456 /* Assembly-CSharp__33.cpp */,
				07CA36577698941D4EE4BC70 /* Assembly-CSharp__34.cpp */,
				B6B32C7D1EE6F0853DDDAA61 /* Assembly-CSharp__35.cpp */,
				3EAD2E0234EACBB68F7569A5 /* Assembly-CSharp__36.cpp */,
				5C3AA7AEC8BCC431E5FEEC04 /* Assembly-CSharp__37.cpp */,
				C86635207E96D385500A4A2C /* Assembly-CSharp__38.cpp */,
				DB88045514EBA3611759BAB0 /* Assembly-CSharp__39.cpp */,
				4F97D266790DB44CD01959EC /* Assembly-CSharp__4.cpp */,
				D0377ED2DD21A2FE426123F1 /* Assembly-CSharp__40.cpp */,
				C08898ABC22AC52604E841BD /* Assembly-CSharp__41.cpp */,
				08D9ECBDADDA4DD82C86134D /* Assembly-CSharp__42.cpp */,
				5D2CB0C1599CC73B675915AB /* Assembly-CSharp__43.cpp */,
				25EDC3486A64365C2398DAB5 /* Assembly-CSharp__44.cpp */,
				A849FEA549C242344B5B7C14 /* Assembly-CSharp__45.cpp */,
				5E93FF130E65841D72EE889D /* Assembly-CSharp__46.cpp */,
				188198725C11AA1A9BB3A8BF /* Assembly-CSharp__47.cpp */,
				4A8BF4F505104D1B8AE63E79 /* Assembly-CSharp__48.cpp */,
				43DCC1F9F099008314351765 /* Assembly-CSharp__49.cpp */,
				A34734E12320BFE93D77614B /* Assembly-CSharp__5.cpp */,
				780EF42D0F810F6942D61886 /* Assembly-CSharp__50.cpp */,
				6AF6E713D4C420DE4590CB06 /* Assembly-CSharp__51.cpp */,
				0261C17A7919F2AE5BFC8D0A /* Assembly-CSharp__52.cpp */,
				E0994B957EB074EB69BD9ABA /* Assembly-CSharp__6.cpp */,
				504F10354AA6664ACC10AE55 /* Assembly-CSharp__7.cpp */,
				0B698999CFCA23787EA976B2 /* Assembly-CSharp__8.cpp */,
				7FA39E9CC391DDDACEDDE1E9 /* Assembly-CSharp__9.cpp */,
				D904E5E376B4426FDF7E041B /* DOTween.cpp */,
				992385BAB68D22DD4E5C0DB3 /* DOTweenPro.cpp */,
				1B1F61655F05BA3F7ED2CC6A /* DOTweenPro_CodeGen.c */,
				40C608E7DF3C3442EDC4FE0B /* DOTween_CodeGen.c */,
				41D7183E8E0676D21577C84A /* DOTween__1.cpp */,
				99818D2EBD0B58F62CB9889B /* FairyGUI.cpp */,
				037725F928F56B860897F0C1 /* FairyGUI_CodeGen.c */,
				361242239BA4C93111DF15F3 /* FairyGUI__1.cpp */,
				6124A6655AB8A1E235C3FDCC /* FairyGUI__2.cpp */,
				E2A51863FBA765167579306C /* FairyGUI__3.cpp */,
				14057A4CB8A9CA9F599A65F9 /* FairyGUI__4.cpp */,
				624F1BD53712BEA8AB14E15D /* FairyGUI__5.cpp */,
				B2F3A2E815971006D9B012F0 /* FairyGUI__6.cpp */,
				D834394372B192753C19BD24 /* FairyGUI__7.cpp */,
				272CC69ABD46E8615FA1E8A5 /* FairyGUI__8.cpp */,
				E2D1F12E1B76EBDB3AEA4278 /* FairyGUI__9.cpp */,
				DC641E85B09D087E3E294418 /* GenericMethods.cpp */,
				CF08E8E4A81091C5FDB9F66D /* GenericMethods__1.cpp */,
				CFD9F8BCEB8E8AB7DDA7F121 /* GenericMethods__10.cpp */,
				06D2F6EE203E616EFFD31304 /* GenericMethods__11.cpp */,
				777CD2AF69C4EDDDC8094D5F /* GenericMethods__12.cpp */,
				64E52B1EC636E618B7604298 /* GenericMethods__13.cpp */,
				23F4A86D1E5E4A13C05B0120 /* GenericMethods__14.cpp */,
				B8A2B55813D1826E10024CCF /* GenericMethods__15.cpp */,
				98A7FB6B217C3D47128FE608 /* GenericMethods__16.cpp */,
				9497B750CCFB7578BDEF70E5 /* GenericMethods__2.cpp */,
				A504EDDC3C17119152F24F7F /* GenericMethods__3.cpp */,
				6268E9B44CD8602C2A9D5536 /* GenericMethods__4.cpp */,
				2380B52F90A1138DCA07F2BF /* GenericMethods__5.cpp */,
				AAE63453E56E81C5A6FED0FD /* GenericMethods__6.cpp */,
				9A4AB585B67FED485B96F428 /* GenericMethods__7.cpp */,
				2479439FAEB0099AB1925F36 /* GenericMethods__8.cpp */,
				DC1C002DA32AACE7AD91663B /* GenericMethods__9.cpp */,
				439D39F1FC43F7B0A6C04FFF /* Generics.cpp */,
				8E6728ADD56BA33B896627DA /* Generics__1.cpp */,
				11856605578FD8270FB7624A /* Generics__10.cpp */,
				317B44F3876EC140C859BDEB /* Generics__11.cpp */,
				BF026C43C97DF354A316F9F4 /* Generics__12.cpp */,
				848BF6A7383F7523CFD9F1F2 /* Generics__13.cpp */,
				CB4953DB01619C32AB0D6487 /* Generics__14.cpp */,
				26CA78F1FFFB56AD4EBA84D6 /* Generics__15.cpp */,
				0FD8E595BA80129EA8DD43F4 /* Generics__16.cpp */,
				715BA45FFD028756DA8BD144 /* Generics__17.cpp */,
				37EEDE6C9967DEE1D5EF5912 /* Generics__18.cpp */,
				6943C5DACCA99586C14B905B /* Generics__19.cpp */,
				C74C563E02CE38C5DC9590E7 /* Generics__2.cpp */,
				BD54E96698396A5E752F9571 /* Generics__20.cpp */,
				31B87AF94D0FCE4F2238BC4D /* Generics__21.cpp */,
				CCD3C7F4388D085C699D9950 /* Generics__22.cpp */,
				489B743989EEF3955BDA2C20 /* Generics__23.cpp */,
				0E687189B4541BF06DADF853 /* Generics__24.cpp */,
				358C4B63F8E8BD45D3DC57E3 /* Generics__25.cpp */,
				D51F4747FAFFCAC3B247BB05 /* Generics__26.cpp */,
				FEFF7B4D5BE7A4AB429E1676 /* Generics__27.cpp */,
				88AA051394FF8002BF048FA1 /* Generics__28.cpp */,
				8BF3CC7245E4CCD0D7BC3513 /* Generics__29.cpp */,
				F6CDB022FB8DC8CFB499BE10 /* Generics__3.cpp */,
				D36225A528040BD949BFC983 /* Generics__30.cpp */,
				7C3D9FDE49610AB93793E874 /* Generics__31.cpp */,
				F479FCE1D625BC61FFD939EF /* Generics__32.cpp */,
				B9A1D40057435376AC961B25 /* Generics__33.cpp */,
				6C27159912D87628ECCE5205 /* Generics__34.cpp */,
				6F88E695F4B9054ACDB02B6F /* Generics__35.cpp */,
				18C2DA2269DDF3862E09DE6E /* Generics__36.cpp */,
				B5A89C2D3588C3D4F6F6AEE2 /* Generics__37.cpp */,
				F311E9EE7D978D35B6A8A0D8 /* Generics__38.cpp */,
				8F8539D174DC5F6F066347FF /* Generics__39.cpp */,
				FAFE6F25F97E0825A99B77C2 /* Generics__4.cpp */,
				E6DD1EF92C698A076367A202 /* Generics__40.cpp */,
				961D2F6C098B83FD6726598D /* Generics__41.cpp */,
				650E98E487E282D0BD28CD94 /* Generics__42.cpp */,
				69F1F8EAF4DAB02EAB96CB93 /* Generics__43.cpp */,
				749E2B722F0B2B49039D8675 /* Generics__44.cpp */,
				99204864EEA9F9D90FC4D0E9 /* Generics__45.cpp */,
				94171DB83A0E4A9A61CFBC08 /* Generics__46.cpp */,
				D8520D972A75A0B3FACF80FD /* Generics__47.cpp */,
				325555CE8DEBEC25FFFE051F /* Generics__48.cpp */,
				36BDE40EFAF15832D01C4E85 /* Generics__49.cpp */,
				525CDBA21FAADF221B8950A5 /* Generics__5.cpp */,
				75954FC5A3663951E6786128 /* Generics__50.cpp */,
				9AC5D532B0DA5D61DDBE0A59 /* Generics__51.cpp */,
				F848B392DAA96A64C479853F /* Generics__52.cpp */,
				A8EB050C496A3CF9B48DB001 /* Generics__53.cpp */,
				2262E7A75098C5BC7A54BE96 /* Generics__54.cpp */,
				D52C8290C7A4A82448D38C34 /* Generics__55.cpp */,
				2DC31A2C6BF0EFF3721FCBBB /* Generics__56.cpp */,
				6189F3F814EA67BAEB8664B9 /* Generics__57.cpp */,
				1128E095D2864E4B88CBF20F /* Generics__58.cpp */,
				0E9D8AC8B3C7AE96482477D4 /* Generics__59.cpp */,
				8DF7D168881C3EF89850AD0A /* Generics__6.cpp */,
				816FDCA20CB4A3E8F33CC76A /* Generics__60.cpp */,
				0B7A8CEE2ED092ADC2009F10 /* Generics__61.cpp */,
				EB3408953E5F4E8B5F088360 /* Generics__62.cpp */,
				84AB00D51327D54081DFA7C2 /* Generics__63.cpp */,
				7DB84657C7BE9BD9279A5A0D /* Generics__64.cpp */,
				1972A87589EFBAF4986775C2 /* Generics__65.cpp */,
				8D13621DBE69B85E504D57D6 /* Generics__66.cpp */,
				AF34E8A8B0B8A4140B1BA06F /* Generics__67.cpp */,
				28F47737911BB66213AD93BA /* Generics__68.cpp */,
				81F3C5309AEB771634823147 /* Generics__69.cpp */,
				C865CD21D06B3F35209C5FA5 /* Generics__7.cpp */,
				E33ECA1CB8CAF0C496E0196E /* Generics__70.cpp */,
				DC6D25CF35344C8E6CBCC43D /* Generics__71.cpp */,
				1474DA4D7B44DB6E68618883 /* Generics__72.cpp */,
				D740AE7756D71AACB0CCF7C7 /* Generics__8.cpp */,
				8F13D042492DBEB7CF96DC23 /* Generics__9.cpp */,
				B214A9C142CC063D26201EBF /* GoogleMobileAds.Common.cpp */,
				F8628423EEBFA41D56AD3B0A /* GoogleMobileAds.Common_CodeGen.c */,
				59DBD7F6973027B5CE2179D9 /* GoogleMobileAds.Core.cpp */,
				B38CA49857850503CBA3E1F4 /* GoogleMobileAds.Core_CodeGen.c */,
				94D7711027E56C025D8682E4 /* GoogleMobileAds.Ump.cpp */,
				BBD0C53BC75AB46F132FA2D0 /* GoogleMobileAds.Ump.iOS.cpp */,
				0BAFCF00AED9C44606DB1581 /* GoogleMobileAds.Ump.iOS_CodeGen.c */,
				144481ABCCC0803449B7F050 /* GoogleMobileAds.Ump_CodeGen.c */,
				79D15C82C81C8A9D8853F133 /* GoogleMobileAds.cpp */,
				8DECD8BC6547FA2FC7D9E876 /* GoogleMobileAds.iOS.cpp */,
				ADED06C4E8F915803998EFA2 /* GoogleMobileAds.iOS_CodeGen.c */,
				322D52AD7129B47862C9AEE0 /* GoogleMobileAds_CodeGen.c */,
				E025EE58BA4B0AC13F8239F1 /* HOTween.cpp */,
				F4850C2D77359384155CE580 /* HOTween_CodeGen.c */,
				1B8A50CD128C437C997726BB /* HOTween__1.cpp */,
				DD952DDCAB0485953E3CE902 /* Il2CppCCFieldValuesTable.cpp */,
				CA2FE0E937B9F3EBF23597D5 /* Il2CppCCTypeValuesTable.cpp */,
				B662E7588FD9F79EDCF63D17 /* Il2CppCCalculateFieldValues.cpp */,
				DDB2F8BB5DC4E5D804C3C134 /* Il2CppCCalculateFieldValues1.cpp */,
				A736D24CED71BDA3F309CC8E /* Il2CppCCalculateFieldValues2.cpp */,
				97EFDE40B546C8DD238C8231 /* Il2CppCCalculateFieldValues3.cpp */,
				1EF6732A52CD49B602825AEB /* Il2CppCCalculateTypeValues.cpp */,
				16A72414295A2B23C5F331A7 /* Il2CppCCalculateTypeValues1.cpp */,
				93E6825D15CDF948BB0DCB44 /* Il2CppCCalculateTypeValues2.cpp */,
				67B80E918530D8754535F3F9 /* Il2CppCodeRegistration.cpp */,
				AE25855F1DB729A61745C8CB /* Il2CppGenericAdjustorThunkTable.c */,
				733D2142097013827A6FD7A8 /* Il2CppGenericClassTable.c */,
				1DD696E89C6A943954768877 /* Il2CppGenericInstDefinitions.c */,
				D3F41D4408A318CCAE49693A /* Il2CppGenericMethodDefinitions.c */,
				FAB4D031E79E43D0CC876610 /* Il2CppGenericMethodPointerTable.c */,
				7C2E380E3D2F92B20313DD1D /* Il2CppGenericMethodTable.c */,
				62B31F6AE6313940631A3B13 /* Il2CppInteropDataTable.cpp */,
				0F795415320EF5804B496AD2 /* Il2CppInvokerTable.cpp */,
				5EA521C38706003E7FF399CD /* Il2CppMetadataRegistration.c */,
				0E3F3917340B2949C8B8E209 /* Il2CppMetadataUsage.c */,
				61644B28313FCE72AA0F7731 /* Il2CppReversePInvokeWrapperTable.cpp */,
				238B504DA5717798C8149303 /* Il2CppRgctxTable.c */,
				996B8B35A5892AE2820D8033 /* Il2CppTypeDefinitions.c */,
				CEEF9247F012D6069E397F57 /* Il2CppUnresolvedIndirectCallStubs.cpp */,
				F954EB2BCBE7E56336AE19E3 /* LitJson.cpp */,
				F3BBCEB5ABC530BC378419BE /* LitJson_CodeGen.c */,
				25711C69564281E1F27185F1 /* Mono.Security.cpp */,
				AAC85F52F8C010404109B18C /* Mono.Security_CodeGen.c */,
				54EE3B312E220CA8996C49C7 /* Mono.Security__1.cpp */,
				8EE47B422BDF2BE7441AA757 /* Newtonsoft.Json.cpp */,
				C531584915BF8819C6496BE0 /* Newtonsoft.Json_CodeGen.c */,
				B315EBF0BC65192DC23290CD /* Newtonsoft.Json__1.cpp */,
				3E6AB690991E5D5C419B99F5 /* Newtonsoft.Json__2.cpp */,
				C0D23F3F8BED210275372D69 /* Newtonsoft.Json__3.cpp */,
				414991A97FF59CD6675FD9EB /* Newtonsoft.Json__4.cpp */,
				FD0DA59C559B1835D2E1DE26 /* Newtonsoft.Json__5.cpp */,
				95156BB6430AD1E82A3BB488 /* ProCamera2D.Examples.cpp */,
				086221B24C5434B49FF5CF1B /* ProCamera2D.Examples_CodeGen.c */,
				D71616D07C71DE37F669A378 /* ProCamera2D.Runtime.cpp */,
				3A1BDF828EAFC31251B10CAE /* ProCamera2D.Runtime_CodeGen.c */,
				74388B1F695F09EEC24A1072 /* ProCamera2D.Runtime__1.cpp */,
				FF7E553300BBA68968E5F6CF /* ProCamera2D.Runtime__2.cpp */,
				2D55E3A876DECECA9E2A124C /* System.Configuration.cpp */,
				C862D3A89A9BA3E4ED7205BD /* System.Configuration_CodeGen.c */,
				28C3B8EF48C0EB6F619F326F /* System.Core.cpp */,
				DCFF8C91896C4981029286E5 /* System.Core_CodeGen.c */,
				58C462906CFE1012884EAB16 /* System.Core__1.cpp */,
				86D61A6AC37C64EC49B5261F /* System.Core__2.cpp */,
				4CF8EE25016BB90ABFD83417 /* System.Core__3.cpp */,
				C6B21A7CC65E934695520510 /* System.Core__4.cpp */,
				4BC1C33C24833176E207546D /* System.Data.cpp */,
				817B6BBCBF9F078D00727259 /* System.Data_CodeGen.c */,
				E8E6BB924BD5BB712770450E /* System.Data__1.cpp */,
				CD891A8178C62435E30998FB /* System.Data__2.cpp */,
				2274C9C05B1B4B06D30C0211 /* System.Data__3.cpp */,
				FAA2E88DA984D6D226870B21 /* System.Data__4.cpp */,
				C0FC069B611EACD4E344ED53 /* System.Data__5.cpp */,
				CF27740112D78F14DED48EF7 /* System.Data__6.cpp */,
				CDE326D9BC63A811E64F0402 /* System.Data__7.cpp */,
				F95D837157339CB183E2FC76 /* System.Data__8.cpp */,
				A1B3624D0AECCFE9A3417712 /* System.Drawing.cpp */,
				E9C72CBC632293D0FE2E3D56 /* System.Drawing_CodeGen.c */,
				2E8E2D02765123AF23FC2A10 /* System.Numerics.cpp */,
				2E38E8560CE5D41091E04C4B /* System.Numerics_CodeGen.c */,
				1B92F201E0C11B4944DF0F0B /* System.Runtime.Serialization.cpp */,
				5E6EECA05C59F494EE5D3F45 /* System.Runtime.Serialization_CodeGen.c */,
				0464E0DCE4A0FC27BFF43BB4 /* System.Xml.Linq.cpp */,
				71520C37DB84A0B4A8E0F46F /* System.Xml.Linq_CodeGen.c */,
				D1002655EF7EC77F763671BA /* System.Xml.cpp */,
				16E144C6C0B3E8367F911BA8 /* System.Xml_CodeGen.c */,
				6F9722D0330536FF815E94C5 /* System.Xml__1.cpp */,
				29218B0B79CD49C8515B939E /* System.Xml__10.cpp */,
				7931F0EB3287754DA068276D /* System.Xml__11.cpp */,
				750BF4D08D860C9A70ACE432 /* System.Xml__12.cpp */,
				5CE747857641C3A4C4FD32A4 /* System.Xml__13.cpp */,
				BE33F4A8ED55CC34EB5BDF80 /* System.Xml__14.cpp */,
				4117F67ACFF8CBA17CC65251 /* System.Xml__15.cpp */,
				84C6A84D75C32015E4995C33 /* System.Xml__16.cpp */,
				7EEF5FB367689FB1ECEBB6A7 /* System.Xml__2.cpp */,
				41772366CC01AABF3A141A21 /* System.Xml__3.cpp */,
				043632D66EB0B7E5F3272285 /* System.Xml__4.cpp */,
				1C3F27E9009F948E3EC03458 /* System.Xml__5.cpp */,
				08D218E34CD3080971C43706 /* System.Xml__6.cpp */,
				2D5AF8F8E52C508883AD72E2 /* System.Xml__7.cpp */,
				980A11C75E6594F47A4D0C4E /* System.Xml__8.cpp */,
				B3B3B55ACB389C47124F63DC /* System.Xml__9.cpp */,
				F2E5D9A741FB7ADF7324BC5B /* System.cpp */,
				76E90196E5C6B8BA16A9EB4C /* System_CodeGen.c */,
				35446C8F021B4551882708E5 /* System__1.cpp */,
				561FE8E0DA0AF097A24473A9 /* System__10.cpp */,
				19CC4FC0E5C5AFF6FF9E64FA /* System__11.cpp */,
				9144340CC541DD77AADA21F3 /* System__2.cpp */,
				CB6390412026FA54BD728F2E /* System__3.cpp */,
				058D9CAFDB588D59044A2B25 /* System__4.cpp */,
				56115212599D5E43FF019CAB /* System__5.cpp */,
				782F20A8C011459AB9E0A41E /* System__6.cpp */,
				7771951A12B3A7284778E4C6 /* System__7.cpp */,
				4731C5266AFA863C29647599 /* System__8.cpp */,
				8482C9568E6A49FD58B18615 /* System__9.cpp */,
				999431141A9E39D05E5FE97B /* UniWebView-CSharp.cpp */,
				B428915B5C40C947044F50D4 /* UniWebView-CSharp_CodeGen.c */,
				F31E800B043032E14DC17096 /* Unity.Addressables.cpp */,
				93DCE62BE3E166440E8359CE /* Unity.Addressables_CodeGen.c */,
				45600B8C0ADE759ED6CA9C0D /* Unity.Addressables__1.cpp */,
				B908CEAF09D80A3C3B24423E /* Unity.ResourceManager.cpp */,
				38C36B7E75EBED75318772C6 /* Unity.ResourceManager_CodeGen.c */,
				368713E01A8699AB23E17224 /* Unity.Services.Analytics.cpp */,
				215B46B0AE4EE3D4B0F67064 /* Unity.Services.Analytics_CodeGen.c */,
				69CD4252BE3616B9901D3253 /* Unity.Services.Core.Configuration.cpp */,
				D5FC3F6D543FBD571547E70A /* Unity.Services.Core.Configuration_CodeGen.c */,
				2884252ACE3370EA428CB631 /* Unity.Services.Core.Device.cpp */,
				2AAD40450F6ACA5039B33CD7 /* Unity.Services.Core.Device_CodeGen.c */,
				CD9445190A8424A52C234D9A /* Unity.Services.Core.Environments.Internal.cpp */,
				C6D748933A8CB803421827FF /* Unity.Services.Core.Environments.Internal_CodeGen.c */,
				334180F2099CA4932596C6A5 /* Unity.Services.Core.Internal.cpp */,
				1D4F5F3EC41EFCB9F59CC481 /* Unity.Services.Core.Internal_CodeGen.c */,
				B93CEF1C3FE1A7E36FCD1622 /* Unity.Services.Core.Registration.cpp */,
				80D2FA80EB689285AB336DF4 /* Unity.Services.Core.Registration_CodeGen.c */,
				712E424EC1D2E248B742B6A0 /* Unity.Services.Core.Scheduler.cpp */,
				8CE5D574D9D536B5CB300F91 /* Unity.Services.Core.Scheduler_CodeGen.c */,
				0037EF7247D13977D5A576B2 /* Unity.Services.Core.Telemetry.cpp */,
				162968812633BCE3D7FB740D /* Unity.Services.Core.Telemetry_CodeGen.c */,
				42E075EB26412806102DDFF7 /* Unity.Services.Core.Threading.cpp */,
				5390DEEE9EABDA6BF3A9E027 /* Unity.Services.Core.Threading_CodeGen.c */,
				E93AFE0C4E58671EE56041CE /* Unity.Services.Core.cpp */,
				FF93594860731B504F3020F5 /* Unity.Services.Core_CodeGen.c */,
				FF80ED27C0BED9BC1BA9070D /* Unity.TextMeshPro.cpp */,
				004B7ED377C245FA07EA4CA7 /* Unity.TextMeshPro_CodeGen.c */,
				0FA54142F83E19F3610B1991 /* Unity.TextMeshPro__1.cpp */,
				9664DF812E72471EB20F5443 /* Unity.TextMeshPro__2.cpp */,
				9D3B639B6E4EB4B913EDABF2 /* Unity.TextMeshPro__3.cpp */,
				EA841D12341C9363E02EE00F /* Unity.TextMeshPro__4.cpp */,
				F09919364E13EC87792FCBE3 /* Unity.TextMeshPro__5.cpp */,
				07F77C1BA780C0E1C591423E /* Unity.TextMeshPro__6.cpp */,
				6679B66625DA79D57B50501A /* UnityClassRegistration.cpp */,
				9D0A7564828238ED0C6F4AFD /* UnityEngine.AIModule.cpp */,
				B803F94314B18CB6B2655B90 /* UnityEngine.AIModule_CodeGen.c */,
				DA1731C322C733D4F9215478 /* UnityEngine.AnimationModule.cpp */,
				A7CC23DF498D4EFEEB0AF888 /* UnityEngine.AnimationModule_CodeGen.c */,
				F4F4286D3BE59E565564055C /* UnityEngine.AssetBundleModule.cpp */,
				979CE552E8632B2F59D7A75B /* UnityEngine.AssetBundleModule_CodeGen.c */,
				C24D3A15A69D047679E1AF2F /* UnityEngine.AudioModule.cpp */,
				F02A78402345158DB659E78E /* UnityEngine.AudioModule_CodeGen.c */,
				418A53639A9FDF6906C6BFBC /* UnityEngine.CoreModule.cpp */,
				29DB862887FD80F9544C0F27 /* UnityEngine.CoreModule_CodeGen.c */,
				90114C270D8D46748478032F /* UnityEngine.CoreModule__1.cpp */,
				0D31AAF3C6C0CC11D285C738 /* UnityEngine.CoreModule__2.cpp */,
				A4591E5013E0219646DFFBD4 /* UnityEngine.GameCenterModule.cpp */,
				9EA3E57A19EDE7497E36F3DF /* UnityEngine.GameCenterModule_CodeGen.c */,
				857AE3AA6D7217204F38E177 /* UnityEngine.GridModule.cpp */,
				58D3A7800BA0F8BFFDC7DDAA /* UnityEngine.GridModule_CodeGen.c */,
				D61973816F5E9F99E95C8C57 /* UnityEngine.IMGUIModule.cpp */,
				B65026F4E0A439EAE4490865 /* UnityEngine.IMGUIModule_CodeGen.c */,
				61703DF54F612431C9B2CEC6 /* UnityEngine.IMGUIModule__1.cpp */,
				0FF38D860993A4461BE3D1D6 /* UnityEngine.ImageConversionModule.cpp */,
				A918C8B44A33797E60C31B4D /* UnityEngine.ImageConversionModule_CodeGen.c */,
				677B3A5BE7B2CB7BD477BB0A /* UnityEngine.InputLegacyModule.cpp */,
				62CBF79C3A505CA169C0A2F8 /* UnityEngine.InputLegacyModule_CodeGen.c */,
				9C6571465C64216B59151280 /* UnityEngine.JSONSerializeModule.cpp */,
				544BB825B8624DFAB4CA9C2C /* UnityEngine.JSONSerializeModule_CodeGen.c */,
				FA0D72F8EDBD0798BCFC9916 /* UnityEngine.ParticleSystemModule.cpp */,
				F98883DE16B476EDC83A25E2 /* UnityEngine.ParticleSystemModule_CodeGen.c */,
				F9F1230E7B00F6B2FFC7A8EB /* UnityEngine.Physics2DModule.cpp */,
				9432641D8FAD3395C648341C /* UnityEngine.Physics2DModule_CodeGen.c */,
				7A0126D9A77C3A2BC78EAC19 /* UnityEngine.PhysicsModule.cpp */,
				3B557CF9358F36A58A8F38FC /* UnityEngine.PhysicsModule_CodeGen.c */,
				218A5F5892B6E72316C9FFDA /* UnityEngine.PropertiesModule.cpp */,
				0D74EFEA392C0E65155F7A96 /* UnityEngine.PropertiesModule_CodeGen.c */,
				1562360321C1A01A12D225BC /* UnityEngine.SharedInternalsModule.cpp */,
				ACE173F91BF3866BEA121D66 /* UnityEngine.SharedInternalsModule_CodeGen.c */,
				354C830C3FA3287CFC1D3844 /* UnityEngine.SpriteShapeModule.cpp */,
				F24B06F17D255CA359EB4840 /* UnityEngine.SpriteShapeModule_CodeGen.c */,
				4E33F765E8E51E7DFF5D52A6 /* UnityEngine.TextCoreFontEngineModule.cpp */,
				E518660204D9E27F74306195 /* UnityEngine.TextCoreFontEngineModule_CodeGen.c */,
				01542E4A7351BF537D5F9A2D /* UnityEngine.TextCoreTextEngineModule.cpp */,
				CF2D9CD79707BC66F0D724D2 /* UnityEngine.TextCoreTextEngineModule_CodeGen.c */,
				23225D541B6C1620CBF548E2 /* UnityEngine.TextCoreTextEngineModule__1.cpp */,
				4EEAF035005D81C6A49A7C45 /* UnityEngine.TextCoreTextEngineModule__2.cpp */,
				5B2F54EB41B243A7C1793B5C /* UnityEngine.TextCoreTextEngineModule__3.cpp */,
				C89119454D57EDC42A59D87C /* UnityEngine.TextRenderingModule.cpp */,
				9CE38A0EB9BA95FB48A7537E /* UnityEngine.TextRenderingModule_CodeGen.c */,
				E28E0AD14FECA0BE6084E4CE /* UnityEngine.TilemapModule.cpp */,
				37AC7AC7977FAFF44E7DAA61 /* UnityEngine.TilemapModule_CodeGen.c */,
				E0263673B7642BE0F04DE813 /* UnityEngine.UI.cpp */,
				6D100D31A5258D5757D1271A /* UnityEngine.UIElementsModule.cpp */,
				C20B2680F0033B47C3BB8134 /* UnityEngine.UIElementsModule_CodeGen.c */,
				2B422D1DE14C2A8D3F21065A /* UnityEngine.UIElementsModule__1.cpp */,
				D7503FB91502F4DD896CF714 /* UnityEngine.UIElementsModule__10.cpp */,
				2A4E1F27CBC17CFBC9D90381 /* UnityEngine.UIElementsModule__11.cpp */,
				ABF66CEE0A3ACFFBDA78140E /* UnityEngine.UIElementsModule__12.cpp */,
				6ACBE790647C380298DD6CF0 /* UnityEngine.UIElementsModule__13.cpp */,
				35BA4E1F3D3749F3A77D35B2 /* UnityEngine.UIElementsModule__14.cpp */,
				9CC7631EEE51DCCD5E2C1ABA /* UnityEngine.UIElementsModule__15.cpp */,
				53D50B1E0F71DFEBC9CABD14 /* UnityEngine.UIElementsModule__2.cpp */,
				3CC546E182B89988089AC598 /* UnityEngine.UIElementsModule__3.cpp */,
				E8A3C67A913D9E240397EFB1 /* UnityEngine.UIElementsModule__4.cpp */,
				0F1F382C4530E826AE63D9D6 /* UnityEngine.UIElementsModule__5.cpp */,
				64E2DA6CA86A91002CE87FE3 /* UnityEngine.UIElementsModule__6.cpp */,
				15A19AFEA90DC0B5F336605F /* UnityEngine.UIElementsModule__7.cpp */,
				F55249155698357E094F40BE /* UnityEngine.UIElementsModule__8.cpp */,
				45EFF0285CC84ED02CB30A69 /* UnityEngine.UIElementsModule__9.cpp */,
				34B2FF818AC0E4621CBC5379 /* UnityEngine.UIModule.cpp */,
				727353E0A17F3CD26C387C4B /* UnityEngine.UIModule_CodeGen.c */,
				6762A34FEF91061AC451841F /* UnityEngine.UI_CodeGen.c */,
				672987B2BECEFF858A090C4A /* UnityEngine.UI__1.cpp */,
				89EF3E4A2B0003F14AAD225D /* UnityEngine.UI__2.cpp */,
				2E9FEEBC77FE9D53FD71E76A /* UnityEngine.UI__3.cpp */,
				2A64BC0FB139C78B893FDCAA /* UnityEngine.UnityAnalyticsCommonModule.cpp */,
				90E69A22DD43A16E030D8110 /* UnityEngine.UnityAnalyticsCommonModule_CodeGen.c */,
				D9AC6836413CDFEB9D3D4DCA /* UnityEngine.UnityAnalyticsModule.cpp */,
				CD4079BF78197D083920D996 /* UnityEngine.UnityAnalyticsModule_CodeGen.c */,
				AF0D68325B7E9D496A51734C /* UnityEngine.UnityWebRequestAssetBundleModule.cpp */,
				9E90AE8AEA3FE3DC0C6E0BED /* UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c */,
				996AF65B21117545805EDE7A /* UnityEngine.UnityWebRequestModule.cpp */,
				B13B5CAA12B4643FE811ED64 /* UnityEngine.UnityWebRequestModule_CodeGen.c */,
				4AE6620E3ECDEE30CBB8DF48 /* UnityEngine.UnityWebRequestTextureModule.cpp */,
				76A06ED55BC26ABEC74D9B5E /* UnityEngine.UnityWebRequestTextureModule_CodeGen.c */,
				8ADAC812E52229002239D46B /* UnityEngine.UnityWebRequestWWWModule.cpp */,
				A2562FA24FCA1F0C707324C8 /* UnityEngine.UnityWebRequestWWWModule_CodeGen.c */,
				73E1EB45A942E11178F3915F /* UnityEngine.cpp */,
				1701016BB5DA32953B06ADB5 /* UnityEngine_CodeGen.c */,
				87605856C2B447518B98EAF3 /* UnityICallRegistration.cpp */,
				AED95C519544DEF0103B9C95 /* __Generated.cpp */,
				8FE3E92069BB36F8854D7277 /* __Generated_CodeGen.c */,
				4ED0B579112CB1E12AEF01F5 /* mscorlib.cpp */,
				470F4DBFC6CF22AFB4920379 /* mscorlib_CodeGen.c */,
				F1F28F611D1E7EE7CCC25F71 /* mscorlib__1.cpp */,
				C3C2D72757B1A8A28AE76822 /* mscorlib__10.cpp */,
				1DC31C363AA49E88C15788BB /* mscorlib__11.cpp */,
				D0609838D7B79343BB1B66CD /* mscorlib__12.cpp */,
				E63E22A2D76ACB6254C3A017 /* mscorlib__13.cpp */,
				0084A6F53FDC53D8C88D04BC /* mscorlib__14.cpp */,
				F5C0D9C036EFC8EF66FE06C4 /* mscorlib__15.cpp */,
				69BE7C8970379AE244085D79 /* mscorlib__16.cpp */,
				CAB37C0B5E85683C4FB82A76 /* mscorlib__17.cpp */,
				CE1CD281A8020E57A0495C3E /* mscorlib__18.cpp */,
				DCC618ACC1C885764FCFF788 /* mscorlib__19.cpp */,
				2BCFF7AAB07F012FD2E34C5B /* mscorlib__2.cpp */,
				92E17166D3BDB3EA24239E77 /* mscorlib__20.cpp */,
				3369FBBB1202A2D16B16FA7E /* mscorlib__21.cpp */,
				AB2BF8F7BAFBE6886580905D /* mscorlib__22.cpp */,
				E9A7659A3EF108621EF5E740 /* mscorlib__23.cpp */,
				2BEDAA3AFB931C1E1C406501 /* mscorlib__3.cpp */,
				58D0A5E12D87A4B766719265 /* mscorlib__4.cpp */,
				D36D733DA6A3052F599DC7CE /* mscorlib__5.cpp */,
				B39436E255FF5376259080BE /* mscorlib__6.cpp */,
				65A609DC99CBAA15044D5D88 /* mscorlib__7.cpp */,
				43289C428F36A64040F01B14 /* mscorlib__8.cpp */,
				EAFCEA713D2A37FF5D81C6CB /* mscorlib__9.cpp */,
			);
			path = il2cppOutput;
			sourceTree = "<group>";
		};
		4E2AB83293ADD1F20EF1C60B /* iOS */ = {
			isa = PBXGroup;
			children = (
				23772533C9AE112A1A30D5AA /* UnityAds.framework */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		4F58672C0138D2469518A20A /* Plugins */ = {
			isa = PBXGroup;
			children = (
				D8AD6FBD2F49002D43AF53BF /* iOS */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		53D667C6AFCF5C846FB3B413 /* iOS */ = {
			isa = PBXGroup;
			children = (
				3DD7D953C65EC4A994FC2E5A /* GdtMob */,
				187DBE13AC7564F758894056 /* IApps */,
				94F3BEF5F37D1DCBDD5F9BEE /* NativeTemplates */,
				AF0BD4189D7A5B98C73791B8 /* GADUAdNetworkExtras.h */,
				91E821D27E6EC7332DE9064A /* ISN_InApp.mm */,
				7FE7BDC9191C134A46FF908E /* ISN_NativeCore.h */,
				4B12CB0E9215CEBA025CD056 /* ISN_NativeCore.mm */,
				4A0DA52C8484E22887233B08 /* MBProgressHUD.h */,
				A5B4D102DE21CCAA54308C32 /* MBProgressHUD.m */,
				D7A4EDC694A07687D3E0DA67 /* NativeDialogsPlugin.h */,
				73200F4F052CF846BE8AEF82 /* NativeDialogsPlugin.m */,
				B7DF838F39D5637124C42C27 /* libUniWebView.a */,
				C684380BEA113884C86ACE5A /* unity-plugin-library.a */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		5623C57817FDCB0800090B9E /* Unity-iPhone Tests */ = {
			isa = PBXGroup;
			children = (
				5623C57917FDCB0800090B9E /* Supporting Files */,
				5623C57E17FDCB0900090B9E /* Unity_iPhone_Tests.m */,
			);
			path = "Unity-iPhone Tests";
			sourceTree = "<group>";
		};
		5623C57917FDCB0800090B9E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				5623C57A17FDCB0900090B9E /* Unity-iPhone Tests-Info.plist */,
				5623C58017FDCB0900090B9E /* Unity-iPhone Tests-Prefix.pch */,
				5623C57B17FDCB0900090B9E /* InfoPlist.strings */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		8A3EDDC51615B7C1001839E9 /* UI */ = {
			isa = PBXGroup;
			children = (
				8A9FCB111617295F00C05364 /* ActivityIndicator.h */,
				8A9FCB121617295F00C05364 /* ActivityIndicator.mm */,
				8A142DC41636943E00DD87CA /* Keyboard.h */,
				8A142DC51636943E00DD87CA /* Keyboard.mm */,
				8AC71EC219E7FBA90027502F /* OrientationSupport.h */,
				8AC71EC319E7FBA90027502F /* OrientationSupport.mm */,
				4E090A331F27884B0077B28D /* StoreReview.m */,
				8A4815BF17A287D2003FBFD5 /* UnityAppController+ViewHandling.h */,
				8A4815C017A287D2003FBFD5 /* UnityAppController+ViewHandling.mm */,
				85E5623620F4F4D1001DFEF6 /* UnityView+Keyboard.mm */,
				8A7939FE1ED43EE100B44EF1 /* UnityView+iOS.h */,
				8A7939FF1ED43EE100B44EF1 /* UnityView+iOS.mm */,
				8A793A001ED43EE100B44EF1 /* UnityView+tvOS.h */,
				8A793A011ED43EE100B44EF1 /* UnityView+tvOS.mm */,
				8A851BA516FB2F6D00E911DB /* UnityView.h */,
				8A851BA616FB2F6D00E911DB /* UnityView.mm */,
				8A793A021ED43EE100B44EF1 /* UnityViewControllerBase+iOS.h */,
				8A793A031ED43EE100B44EF1 /* UnityViewControllerBase+iOS.mm */,
				8A793A041ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.h */,
				8A793A051ED43EE100B44EF1 /* UnityViewControllerBase+tvOS.mm */,
				8BEDFA3729C88F86007F26D7 /* UnityViewControllerBase+visionOS.h */,
				8BEDFA3829C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm */,
				8A21AED21622F59300AF8007 /* UnityViewControllerBase.h */,
				8A7939FC1ED2F53200B44EF1 /* UnityViewControllerBase.mm */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		8A5C148F174E662D0006EB36 /* PluginBase */ = {
			isa = PBXGroup;
			children = (
				8AF7755E17997D1300341121 /* AppDelegateListener.h */,
				8AF7755F17997D1300341121 /* AppDelegateListener.mm */,
				8A292A9717992CE100409BA4 /* LifeCycleListener.h */,
				8A292A9817992CE100409BA4 /* LifeCycleListener.mm */,
				8A5C1490174E662D0006EB36 /* RenderPluginDelegate.h */,
				8A5C1491174E662D0006EB36 /* RenderPluginDelegate.mm */,
				AAFE69D019F187C200638316 /* UnityViewControllerListener.h */,
				AAFE69D119F187C200638316 /* UnityViewControllerListener.mm */,
			);
			path = PluginBase;
			sourceTree = "<group>";
		};
		8AF18FE316490981007B4420 /* Unity */ = {
			isa = PBXGroup;
			children = (
				8ABDBCE019CAFCF700A842FF /* AVCapture.h */,
				8AC74A9419B47FEF00019D38 /* AVCapture.mm */,
				8A2AA93316E0978D001FB470 /* CMVideoSampling.h */,
				8A2AA93416E0978D001FB470 /* CMVideoSampling.mm */,
				8A367F5916A6D36F0012ED11 /* CVTextureCache.h */,
				8A367F5A16A6D36F0012ED11 /* CVTextureCache.mm */,
				8ADCE38919C87177006F04F6 /* CameraCapture.h */,
				8ADCE38A19C87177006F04F6 /* CameraCapture.mm */,
				8ACB801B177081D4005D0019 /* DeviceSettings.mm */,
				8A5E0B8F16849D1800CBB6FE /* DisplayManager.h */,
				8A5E0B9016849D1800CBB6FE /* DisplayManager.mm */,
				8A25E6D118D767E20006A227 /* Filesystem.mm */,
				8A16150B1A8E4362006FA788 /* FullScreenVideoPlayer.mm */,
				30347B5D7991A325732A4F10 /* IUnityGraphics.h */,
				894C1B251937BE1BC2B4D36C /* IUnityGraphicsMetal.h */,
				679ACDCF6D14CA6FE6F81479 /* IUnityInterface.h */,
				8A6720A319EEB905006C92E0 /* InternalProfiler.cpp */,
				8A6720A419EEB905006C92E0 /* InternalProfiler.h */,
				1859EA9A19214E7B0022C3D3 /* MetalHelper.mm */,
				8A2BC6DE245061EE00C7C97D /* NoGraphicsHelper.mm */,
				8A6137121A10B57700059EDF /* ObjCRuntime.h */,
				FC0B20A11B7A4F0B00FDFC55 /* OnDemandResources.mm */,
				8A90541019EE8843003D1039 /* UnityForwardDecls.h */,
				8A851BAB16FC875E00E911DB /* UnityInterface.h */,
				8AECDC781950835600CB29E8 /* UnityMetalSupport.h */,
				8AA108C01948732900D0538B /* UnityRendering.h */,
				84DC28F71C51383500BC67D7 /* UnityReplayKit.h */,
				84DC28F51C5137FE00BC67D7 /* UnityReplayKit.mm */,
				848031E01C5160D700FCEAB7 /* UnityReplayKit_Scripting.mm */,
				F6361BB52195CE5D00F61766 /* UnitySharedDecls.h */,
				862C244F20AEC7AC006FB4AD /* UnityWebRequest.mm */,
				8AB3CB3C16D390BA00697AD5 /* VideoPlayer.h */,
				8AB3CB3D16D390BB00697AD5 /* VideoPlayer.mm */,
			);
			path = Unity;
			sourceTree = "<group>";
		};
		8FF3CE8DC62B8DF0A5C36416 /* iOS */ = {
			isa = PBXGroup;
			children = (
				AB3BD440282B7832E28896A1 /* VolumeIOSPlugin.mm */,
				FD6E23C902477A19B0E251BF /* unity_services_locale.mm */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		94F3BEF5F37D1DCBDD5F9BEE /* NativeTemplates */ = {
			isa = PBXGroup;
			children = (
				B9A318561A46439EDE55DD9D /* GADTMediumTemplateView.xib */,
				95579700ADBEB4B7D5EBFB8A /* GADTSmallTemplateView.xib */,
			);
			path = NativeTemplates;
			sourceTree = "<group>";
		};
		9D25AB9E213FB47800354C27 /* UnityFramework */ = {
			isa = PBXGroup;
			children = (
				D5FA3667394130C0BCCF8B0E /* ApplePrivacyManifestsMerge.txt */,
				B58EF12BF9B65BC74A92DC07 /* AppleRequiredReasonCSharpAPIs.txt */,
				9D25ABA0213FB47800354C27 /* Info.plist */,
				F2E4BD281D8D2761198E3AA0 /* PrivacyInfo.xcprivacy */,
				9D25AB9F213FB47800354C27 /* UnityFramework.h */,
			);
			path = UnityFramework;
			sourceTree = "<group>";
		};
		9DA3B0422174CB96001678C7 /* MainApp */ = {
			isa = PBXGroup;
			children = (
				9DA3B0432174CB96001678C7 /* main.mm */,
			);
			path = MainApp;
			sourceTree = SOURCE_ROOT;
		};
		B1D503123E4E35EC4E8CA9E9 /* Device */ = {
			isa = PBXGroup;
			children = (
				3EAD9489F626B5A5A0405FB4 /* UserIdProviders */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		CC894E13A86A24839D6E00CF /* com.unity.purchasing */ = {
			isa = PBXGroup;
			children = (
				0056F713DC32E98E76CB8D96 /* Plugins */,
			);
			path = com.unity.purchasing;
			sourceTree = "<group>";
		};
		D06AF7B5B35D8943CEEC814F /* Pods */ = {
			isa = PBXGroup;
			children = (
				9AEC99D2D5AD154137C30A61 /* Pods-Unity-iPhone.debug.xcconfig */,
				15D71EBFA9562B150B2E69B7 /* Pods-Unity-iPhone.release.xcconfig */,
				6141015D135F7A3CB8A19391 /* Pods-Unity-iPhone.releaseforprofiling.xcconfig */,
				53584FD960B4B411A167C0F4 /* Pods-Unity-iPhone.releaseforrunning.xcconfig */,
				783E22A3A60914DCD509C3A0 /* Pods-UnityFramework.debug.xcconfig */,
				825CA0BD6C0EBAA9DC8AA9D4 /* Pods-UnityFramework.release.xcconfig */,
				43B4BE7C58A2AA60D8FEAD26 /* Pods-UnityFramework.releaseforprofiling.xcconfig */,
				89FC170F9154433D73BADA70 /* Pods-UnityFramework.releaseforrunning.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		D82DCFB50E8000A5005D6AD8 /* Classes */ = {
			isa = PBXGroup;
			children = (
				8A5C148F174E662D0006EB36 /* PluginBase */,
				8A3EDDC51615B7C1001839E9 /* UI */,
				8AF18FE316490981007B4420 /* Unity */,
				FC3D7EBE16D2621600D1BD0D /* CrashReporter.h */,
				FC85CCB916C3ED8000BAF7C7 /* CrashReporter.mm */,
				FC85CCBA16C3ED8000BAF7C7 /* PLCrashReporter.h */,
				8A6720A619EFAF25006C92E0 /* Prefix.pch */,
				8ACB801D177081F7005D0019 /* Preprocessor.h */,
				9D16CD8121C938BB00DD46C0 /* RedefinePlatforms.h */,
				9D16CD8021C938B300DD46C0 /* UndefinePlatforms.h */,
				8AA5D80017ABE9AF007B9910 /* UnityAppController+Rendering.h */,
				8AA5D80117ABE9AF007B9910 /* UnityAppController+Rendering.mm */,
				8A8D90D81A274A7800456C4E /* UnityAppController+UnityInterface.h */,
				8A8D90D91A274A7800456C4E /* UnityAppController+UnityInterface.mm */,
				8A851BA816FB3AD000E911DB /* UnityAppController.h */,
				8A851BA916FB3AD000E911DB /* UnityAppController.mm */,
				8AA6ADDB17818CFD00A1C5F1 /* UnityTrampolineConfigure.h */,
				56DBF99E15E3CE85007A4A8D /* iPhone_Sensors.h */,
				56DBF99C15E3CDC9007A4A8D /* iPhone_Sensors.mm */,
				D82DCFBB0E8000A5005D6AD8 /* main.mm */,
			);
			path = Classes;
			sourceTree = SOURCE_ROOT;
		};
		D8A1C7220E80637F000160D3 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				E65BCBDDB23DA191911454C2 /* Plugins */,
				119493F9038712BFEDCA47D4 /* com.unity.ads */,
				CC894E13A86A24839D6E00CF /* com.unity.purchasing */,
				F021D34CCD890D3E0B21B1DA /* com.unity.services.analytics */,
				EDD56120873895E95A9F955C /* com.unity.services.core */,
				03F528621B447098000F4FB8 /* Il2CppOptions.cpp */,
				AAC3E38B1A68945900F6174A /* RegisterFeatures.cpp */,
				AAC3E38C1A68945900F6174A /* RegisterFeatures.h */,
				7CD8D832DF5E49F3229DE515 /* baselib.a */,
				D8A1C72A0E8063A1000160D3 /* libiPhone-lib.a */,
				EE54DC7E56FA015DDC9659BD /* libil2cpp.a */,
			);
			path = Libraries;
			sourceTree = SOURCE_ROOT;
		};
		D8AD6FBD2F49002D43AF53BF /* iOS */ = {
			isa = PBXGroup;
			children = (
				EF1F31341EF3BB1E55473A8D /* UnityAdsInitializationListener.h */,
				23EAFE12569624894DA33D7B /* UnityAdsInitializationListener.mm */,
				C7004552E7186E16DD3B3A69 /* UnityAdsLoadListener.h */,
				78DCC5B0D7B57EB22AB67E50 /* UnityAdsLoadListener.mm */,
				35326249479B106D3D50D6EB /* UnityAdsNativeObject.m */,
				94F7DED7298B1330B361D3F2 /* UnityAdsShowListener.h */,
				53F0CCEB205A1010D1F75B8A /* UnityAdsShowListener.mm */,
				7DE28CCD862A989C5889A8E5 /* UnityAdsUnityWrapper.m */,
				7CB7FE9F812CDE7E67229FC1 /* UnityAdsUtilities.h */,
				99334BA41D46C76F13A39E7C /* UnityAdsUtilities.m */,
				65A78184B609DFC82D3D4939 /* UnityAdvertisement.swift */,
				F9A594EF02BB90976F5CC883 /* UnityAnalyticsWrapper.m */,
				AF3F17F76B73432AF11E9DA4 /* UnityBannerUnityWrapper.m */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		E58BE20AAA94EF7EBCF4A7A7 /* Plugins */ = {
			isa = PBXGroup;
			children = (
				8FF3CE8DC62B8DF0A5C36416 /* iOS */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		E65BCBDDB23DA191911454C2 /* Plugins */ = {
			isa = PBXGroup;
			children = (
				53D667C6AFCF5C846FB3B413 /* iOS */,
			);
			path = Plugins;
			sourceTree = "<group>";
		};
		EDD56120873895E95A9F955C /* com.unity.services.core */ = {
			isa = PBXGroup;
			children = (
				3C85E63663639261B15967D6 /* Runtime */,
			);
			path = com.unity.services.core;
			sourceTree = "<group>";
		};
		F021D34CCD890D3E0B21B1DA /* com.unity.services.analytics */ = {
			isa = PBXGroup;
			children = (
				07BE6E120ECF49C6EDFEEF03 /* Runtime */,
			);
			path = com.unity.services.analytics;
			sourceTree = "<group>";
		};
		FF435E14D8C85D41F328328F /* com.unity.ads */ = {
			isa = PBXGroup;
			children = (
				3D69E3BD95DB591F74C9AF1C /* Plugins */,
			);
			path = com.unity.ads;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		9D25AB9A213FB47800354C27 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9DC67E8821CBBED5005F9FA1 /* RenderPluginDelegate.h in Headers */,
				9DC67E8521CBBEBB005F9FA1 /* UnityAppController.h in Headers */,
				9D25ABA1213FB47800354C27 /* UnityFramework.h in Headers */,
				9DC67E8621CBBEC7005F9FA1 /* UndefinePlatforms.h in Headers */,
				9DC67E8721CBBEC7005F9FA1 /* RedefinePlatforms.h in Headers */,
				9DC67E8921CBBEDF005F9FA1 /* LifeCycleListener.h in Headers */,
				8BEDFA3929C88F86007F26D7 /* UnityViewControllerBase+visionOS.h in Headers */,
				BD28D362F6E9BB0C5AB19AB3 /* GDTMobManager.h in Headers */,
				29C3C722F6354D4EE671E608 /* GDTPrivacyConfiguration.h in Headers */,
				7859A8900D73BD9B8296F668 /* GDTAdParams.h in Headers */,
				59014FD6537F4A3C1D64FA5B /* MBProgressHUD.h in Headers */,
				69F884C2CB9A942E00E8930F /* ISN_NativeCore.h in Headers */,
				4F6219C6868658EB1B91886B /* GDTUnifiedNativeAdView.h in Headers */,
				DAAF19EECF4756E705B67680 /* GDTRewardVideoAdNetworkAdapterProtocol.h in Headers */,
				030293BF8AB3F381DBE983B4 /* GDTVideoConfig.h in Headers */,
				C3967C465EF23939E44D6B54 /* UnityPurchasing.h in Headers */,
				BE525877766DDC18D1FD9C99 /* GDTServerBiddingResult.h in Headers */,
				3C7226497EFF0130435E7042 /* GDTServerSideVerificationOptions.h in Headers */,
				90358174B2325C48A84899EA /* GDTLogoView.h in Headers */,
				16619FEE818CCF364865C30A /* Reachability.h in Headers */,
				39D5AF21BCD5D72365F18E4B /* GDTSplashAdNetworkConnectorProtocol.h in Headers */,
				B60CD3BA6EEA3D94C738BE63 /* GDTAdProtocol.h in Headers */,
				0F2C9017C0FC279BB5D58EF7 /* GDTUnifiedNativeAd.h in Headers */,
				43B93FC0CB598C780D8C28E7 /* GDTMobUPluginUtil.h in Headers */,
				3D720B7978C6DFDF3C981C34 /* GDTMobInterstitialManager.h in Headers */,
				42FD121083A4C70F0876F159 /* GDTBaseAdNetworkAdapterProtocol.h in Headers */,
				2AFDC3C9885BA5C4A06F07A7 /* GDTUnifiedInterstitialAdNetworkConnectorProtocol.h in Headers */,
				9703160D4A0AEEA22D60F088 /* GDTUnifiedNativeAdNetworkConnectorProtocol.h in Headers */,
				FE7304CADFC302976BEBCAF2 /* GDTUnifiedBannerAdNetworkAdapterProtocol.h in Headers */,
				848B693C6E00A1FED85E262F /* IAppsTeamIOSUnitl.h in Headers */,
				D475686CAFD27A0DA6418053 /* GDTMobInterstitial.h in Headers */,
				C1FB2DD88FDDFC9C8790090F /* GDTUnifiedBannerView.h in Headers */,
				C4BB858072D48F175D4F9386 /* UnityEarlyTransactionObserver.h in Headers */,
				99AF3D66DF97143A5F374744 /* GDTNativeExpressAdNetworkAdapterProtocol.h in Headers */,
				6429802090B2874D35AAF9EA /* GDTUnifiedBannerAdNetworkConnectorProtocol.h in Headers */,
				31801B2E1CF728C094FFDB4D /* UnityAdsLoadListener.h in Headers */,
				E94E990D810869BE1CEEAF3D /* GDTMobUTypes.h in Headers */,
				149790414243CABDCD39AE98 /* GDTVideoAdReporter.h in Headers */,
				52F7727355892063750D8CD0 /* NativeDialogsPlugin.h in Headers */,
				034B0A486E1AFEADBD2E0B9A /* GDTSplashAd.h in Headers */,
				34E040FE918BD0A6C8C0E3D6 /* UnityAdsShowListener.h in Headers */,
				2989A2C045F8EDF22D1ADE69 /* GDTUnifiedNativeAdDataObject.h in Headers */,
				CF2C091D00127205326668D3 /* GDTRewardVideoAd.h in Headers */,
				2A05EDAC7CD6A3B684B25E37 /* GDTAdTestSetting.h in Headers */,
				E77D0542B32B17BD05428C14 /* UnityAdsUtilities.h in Headers */,
				E6BF764B13DDB30BE1080A09 /* GDTLoadAdParams.h in Headers */,
				98370FC6F17417DE352D8849 /* GDTNativeExpressAd.h in Headers */,
				8870BB19A7F961025BF117D7 /* GDTUnifiedInterstitialAd.h in Headers */,
				5D8E0920DEE1C188F000CB6E /* GDTSplashAdNetworkAdapterProtocol.h in Headers */,
				595C5BC77ED98A0781E72599 /* GDTRewardVideoAdNetworkConnectorProtocol.h in Headers */,
				61EE4AFDB2A4DFF0292A2EEA /* UnityAdsInitializationListener.h in Headers */,
				69373607DCBB9849DF0D23F5 /* GDTSDKConfig.h in Headers */,
				A63BDA6A5B55E52118FF67F3 /* GDTNativeExpressAdView.h in Headers */,
				D88E9DC890A98004B2FC7F06 /* GDTNativeExpressAdNetworkConnectorProtocol.h in Headers */,
				6EEEC83C54E5CF29828775FA /* GDTNativeExpressAdViewAdapterProtocol.h in Headers */,
				82DB471255F4AE4DDB0B70B3 /* GDTUnifiedInterstitialAdNetworkAdapterProtocol.h in Headers */,
				7AB6FF91721F38E447B8D517 /* GDTSDKDefines.h in Headers */,
				577641CFC02BF84EC13208C1 /* GADUAdNetworkExtras.h in Headers */,
				A57E0644BB053CF3D7CEAEE4 /* GDTMediaView.h in Headers */,
				C8944D4B3B13BCD6C3C8C91D /* GDTUnifiedNativeAdNetworkAdapterProtocol.h in Headers */,
				7661C37E9B58E3F45481F9EF /* RSSecrets.h in Headers */,
				475B26F38A3A652C6174529D /* IUnityInterface.h in Headers */,
				826E40C67FAA509B29A0EFA3 /* IUnityGraphics.h in Headers */,
				9E277B346B541C2D072C5489 /* IUnityGraphicsMetal.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		1D6058900D05DD3D006BFB54 /* Unity-iPhone */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "Unity-iPhone" */;
			buildPhases = (
				272FCABCE7B13E05C57A7E12 /* ShellScript */,
				1D60588D0D05DD3D006BFB54 /* Resources */,
				83D0C1FD0E6C8D7700EBCE5D /* CopyFiles */,
				1D60588E0D05DD3D006BFB54 /* Sources */,
				1D60588F0D05DD3D006BFB54 /* Frameworks */,
				9D25ABAB213FB47800354C27 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				9D25ABA3213FB47800354C27 /* PBXTargetDependency */,
			);
			name = "Unity-iPhone";
			productName = "iPhone-target";
			productReference = 1D6058910D05DD3D006BFB54 /* EduApp.app */;
			productType = "com.apple.product-type.application";
		};
		5623C57217FDCB0800090B9E /* Unity-iPhone Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5623C58517FDCB0900090B9E /* Build configuration list for PBXNativeTarget "Unity-iPhone Tests" */;
			buildPhases = (
				5623C56F17FDCB0800090B9E /* Sources */,
				5623C57017FDCB0800090B9E /* Frameworks */,
				5623C57117FDCB0800090B9E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5623C58217FDCB0900090B9E /* PBXTargetDependency */,
			);
			name = "Unity-iPhone Tests";
			productName = "Unity-iPhone Tests";
			productReference = 5623C57317FDCB0800090B9E /* Unity-iPhone Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7F4E059F2717216D00A2CBE4 /* GameAssembly */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7F4E05AA2717216D00A2CBE4 /* Build configuration list for PBXNativeTarget "GameAssembly" */;
			buildPhases = (
				7F4E059C2717216D00A2CBE4 /* Sources */,
				7F4E059D2717216D00A2CBE4 /* Frameworks */,
				7F4E059E2717216D00A2CBE4 /* CopyFiles */,
				C62A2A42F32E085EF849CF0B /* ShellScript */,
				C62A2A42F32E085EF849CF0B /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = GameAssembly;
			productName = GameAssembly;
			productReference = 7F4E05A02717216D00A2CBE4 /* libGameAssembly.a */;
			productType = "com.apple.product-type.library.static";
		};
		9D25AB9C213FB47800354C27 /* UnityFramework */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9D25ABA6213FB47800354C27 /* Build configuration list for PBXNativeTarget "UnityFramework" */;
			buildPhases = (
				903C43009E1F890093C3B25F /* ShellScript */,
				9D25AB9A213FB47800354C27 /* Headers */,
				9D25AB98213FB47800354C27 /* Sources */,
				9D25AB99213FB47800354C27 /* Frameworks */,
				9D25AB9B213FB47800354C27 /* Resources */,
				F49FF917054561FE44E4198F /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				7F401A4D272AC77C005AF450 /* PBXTargetDependency */,
			);
			name = UnityFramework;
			productName = UnityFramework;
			productReference = 9D25AB9D213FB47800354C27 /* UnityFramework.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				TargetAttributes = {
					1D6058900D05DD3D006BFB54 = {
						DevelopmentTeam = 77UQX76V75;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.GameControllers.appletvos = {
								enabled = 1;
							};
							com.apple.InAppPurchase = {
								enabled = 1;
							};
						};
						UnityMainTarget = 1;
					};
					5623C57217FDCB0800090B9E = {
						ProvisioningStyle = Manual;
						TestTargetID = 1D6058900D05DD3D006BFB54;
					};
					7F4E059F2717216D00A2CBE4 = {
						CreatedOnToolsVersion = 13.0;
						ProvisioningStyle = Automatic;
					};
					9D25AB9C213FB47800354C27 = {
						CreatedOnToolsVersion = 9.2;
						ProvisioningStyle = Automatic;
						UnityFrameworkTarget = 1;
					};
				};
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Unity-iPhone" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				en,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* CustomTemplate */;
			productRefGroup = 19C28FACFE9D520D11CA2CBB;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1D6058900D05DD3D006BFB54 /* Unity-iPhone */,
				5623C57217FDCB0800090B9E /* Unity-iPhone Tests */,
				9D25AB9C213FB47800354C27 /* UnityFramework */,
				7F4E059F2717216D00A2CBE4 /* GameAssembly */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1D60588D0D05DD3D006BFB54 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9D9DE4EA221D84E60049D9A1 /* Data in Resources */,
				56C56C9817D6015200616839 /* Images.xcassets in Resources */,
				BEE3E21D322649C83B0DD4DC /* LaunchScreen-iPhone.storyboard in Resources */,
				F5C03E9DD3BF40D8EC0F4404 /* LaunchScreen-iPhonePortrait.png in Resources */,
				415674EFD2EC187355BC05B1 /* LaunchScreen-iPhoneLandscape.png in Resources */,
				763ED5D885EABF761B2FE262 /* LaunchScreen-iPad.storyboard in Resources */,
				D085C06443DB1B1B5C6637B6 /* LaunchScreen-iPad.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5623C57117FDCB0800090B9E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5623C57D17FDCB0900090B9E,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25AB9B213FB47800354C27 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A2801FBBBD09C973F2D56DCC /* GADTSmallTemplateView.xib in Resources */,
				4E641CD1A18DDAAB6BDAD632 /* GADTMediumTemplateView.xib in Resources */,
				91C1EB5F348833EAD02A7E9A /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		272FCABCE7B13E05C57A7E12 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Unity-iPhone-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		903C43009E1F890093C3B25F /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UnityFramework-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C62A2A42F32E085EF849CF0B /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [ \"$CONFIGURATION\" = \"Debug\" ];\nthen\n    IL2CPP_CONFIG=\"Debug\"\nelse\n    IL2CPP_CONFIG=\"Release\"\nfi\n\nHOST_ARCH=$(uname -m)\nif [ \"$HOST_ARCH\" = \"arm64\" ];\nthen\n    HOST_ARCH_BEE=\"arm64\"\nelse\n    HOST_ARCH_BEE=\"x64\"\nfi\n\nif [ \"$ARCHS\" = \"arm64\" ];\nthen\n    LIB_ARCH=\"arm64\"\nelif [ \"$ARCHS\" = \"x86_64\" ];\nthen\n   LIB_ARCH=\"x64\"\nelse\n   LIB_ARCH=\"$HOST_ARCH_BEE\"\nfi\n\necho \"Lib arch: $LIB_ARCH\"\necho \"Host arch: $HOST_ARCH\"\necho \"Bee arch: $HOST_ARCH_BEE\"\n\nIL2CPP_DIR=\"$PROJECT_DIR/Il2CppOutputProject/IL2CPP/build/deploy_$HOST_ARCH\"\nIL2CPP=\"$IL2CPP_DIR/il2cpp\"\nchmod +x \"$IL2CPP\"\nchmod +x \"$IL2CPP_DIR/bee_backend/mac-$HOST_ARCH_BEE/bee_backend\"\n\n\"$IL2CPP\" --compile-cpp --platform=iOS --baselib-directory=\"$PROJECT_DIR/Libraries\" --additional-defines=IL2CPP_DEBUG=0 --incremental-g-c-time-slice=3 --dotnetprofile=unityaot-macos --profiler-report --print-command-line --external-lib-il2-cpp=\"$PROJECT_DIR/Libraries/libil2cpp.a\" --generatedcppdir=\"Il2CppOutputProject/Source/il2cppOutput\" --architecture=\"$LIB_ARCH\" --outputpath=\"$CONFIGURATION_BUILD_DIR/libGameAssembly.a\" --cachedirectory=\"$CONFIGURATION_TEMP_DIR/artifacts/$LIB_ARCH\" --configuration=\"$IL2CPP_CONFIG\" \n";
		};
		F49FF917054561FE44E4198F /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UnityFramework/Pods-UnityFramework-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/GoogleMobileAdsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUserMessagingPlatform/UserMessagingPlatformResources.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMobileAdsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/UserMessagingPlatformResources.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UnityFramework/Pods-UnityFramework-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1D60588E0D05DD3D006BFB54 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9DA3B0442174CB96001678C7 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5623C56F17FDCB0800090B9E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5623C57F17FDCB0900090B9E /* Unity_iPhone_Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7F4E059C2717216D00A2CBE4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D25AB98213FB47800354C27 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BEDFA3A29C88F86007F26D7 /* UnityViewControllerBase+visionOS.mm in Sources */,
				862C245020AEC7AC006FB4AD /* UnityWebRequest.mm in Sources */,
				9D25ABE9213FB7CC00354C27 /* Il2CppOptions.cpp in Sources */,
				9D25ABC1213FB6F800354C27 /* CMVideoSampling.mm in Sources */,
				9D25ABB9213FB6E300354C27 /* UnityView+iOS.mm in Sources */,
				9D25ABAD213FB6B700354C27 /* AppDelegateListener.mm in Sources */,
				9D25ABBD213FB6E300354C27 /* UnityViewControllerBase+tvOS.mm in Sources */,
				9D25ABB1213FB6D600354C27 /* UnityView+Keyboard.mm in Sources */,
				9D25ABCB213FB6F800354C27 /* UnityReplayKit.mm in Sources */,
				9D25ABBA213FB6E300354C27 /* UnityView+tvOS.mm in Sources */,
				9D25ABBB213FB6E300354C27 /* UnityViewControllerBase.mm in Sources */,
				9D25ABCC213FB6F800354C27 /* UnityReplayKit_Scripting.mm in Sources */,
				9D25ABB7213FB6E300354C27 /* UnityAppController+ViewHandling.mm in Sources */,
				9D25ABC2213FB6F800354C27 /* CVTextureCache.mm in Sources */,
				9D25ABC4213FB6F800354C27 /* DisplayManager.mm in Sources */,
				9D25ABCD213FB6F800354C27 /* VideoPlayer.mm in Sources */,
				9D25ABC3213FB6F800354C27 /* DeviceSettings.mm in Sources */,
				9D25ABCA213FB6F800354C27 /* FullScreenVideoPlayer.mm in Sources */,
				9D25ABC8213FB6F800354C27 /* InternalProfiler.cpp in Sources */,
				9D25ABB6213FB6E300354C27 /* StoreReview.m in Sources */,
				9D25ABB4213FB6E300354C27 /* OrientationSupport.mm in Sources */,
				9D25ABAF213FB6BE00354C27 /* RenderPluginDelegate.mm in Sources */,
				9D25ABE3213FB76500354C27 /* UnityAppController.mm in Sources */,
				9D25ABBF213FB6F800354C27 /* AVCapture.mm in Sources */,
				8A215703245064AA00E582EB /* NoGraphicsHelper.mm in Sources */,
				9D25ABE1213FB76500354C27 /* iPhone_Sensors.mm in Sources */,
				9D25ABC6213FB6F800354C27 /* Filesystem.mm in Sources */,
				9D25ABB8213FB6E300354C27 /* UnityView.mm in Sources */,
				9DFA7F9D21410F2E00C2880E /* main.mm in Sources */,
				9D25ABBE213FB6F800354C27 /* OnDemandResources.mm in Sources */,
				9D25ABE4213FB76500354C27 /* UnityAppController+Rendering.mm in Sources */,
				9D25ABB3213FB6E300354C27 /* Keyboard.mm in Sources */,
				9D25ABE0213FB76500354C27 /* CrashReporter.mm in Sources */,
				9D25ABE6213FB7C100354C27 /* RegisterFeatures.cpp in Sources */,
				9D25ABBC213FB6E300354C27 /* UnityViewControllerBase+iOS.mm in Sources */,
				9D25ABE5213FB76500354C27 /* UnityAppController+UnityInterface.mm in Sources */,
				9D25ABC0213FB6F800354C27 /* CameraCapture.mm in Sources */,
				9D25ABB0213FB6C400354C27 /* UnityViewControllerListener.mm in Sources */,
				9D25ABAE213FB6BA00354C27 /* LifeCycleListener.mm in Sources */,
				9D25ABB2213FB6E300354C27 /* ActivityIndicator.mm in Sources */,
				9D25ABC9213FB6F800354C27 /* MetalHelper.mm in Sources */,
				D3A2C08A2A0F557085B06193 /* GDTMobInterstitialManager.mm in Sources */,
				A7208CB0DBEF6C66034C47FE /* MBProgressHUD.m in Sources */,
				3035FDE6435EA7A15BFC07E1 /* UnityAnalyticsWrapper.m in Sources */,
				4B22388811C76AA169252B4B /* ISN_InApp.mm in Sources */,
				A207889197A446B92EBEE772 /* UnityPurchasing.m in Sources */,
				DE6F4DDF93991EC95AE6D98A /* UnityAdvertisement.swift in Sources */,
				37FF245A4DB8B002307C4DA0 /* UnityAdsShowListener.mm in Sources */,
				C7587F9DCADED12FAD5AA7B7 /* UnityAdsInitializationListener.mm in Sources */,
				6FD04192B675A7C7EF4EC94F /* UnityEarlyTransactionObserver.mm in Sources */,
				6E97D27850343C6B1E39F1BE /* unity_services_locale.mm in Sources */,
				A16D22C39C8B2731B13498AF /* GDTMobManager.mm in Sources */,
				D0289961826D5AE404C69D0D /* UnityAdsUnityWrapper.m in Sources */,
				4240696C7C0EC9B9869B2FA2 /* IAppsTeamIOSUntilBinding.m in Sources */,
				7BAE7832F743002BBDA5C0C1 /* VolumeIOSPlugin.mm in Sources */,
				E635643506C918F335FC373C /* UnityAdsLoadListener.mm in Sources */,
				01214F5E521901A252D833EC /* GDTMobUPluginUtil.m in Sources */,
				3917572A929C5A7612468C8F /* GDTMobBing.m in Sources */,
				BEA37D3A3BD93FA7472287D0 /* IAppsTeamIOSUnitl.mm in Sources */,
				8D4B7957F6DCEA85F99DF883 /* ISN_NativeCore.mm in Sources */,
				30A38D0D3F66E28D9A919A5E /* NativeDialogsPlugin.m in Sources */,
				C430A98EA348F937212F7C5F /* UnityAdsUtilities.m in Sources */,
				AF2786F2943EBE0A756F21CC /* NSUserDefaults.mm in Sources */,
				FEE05A90163B36DF45DECF77 /* UnityBannerUnityWrapper.m in Sources */,
				A65F336D356D94E457582832 /* RSSecrets.m in Sources */,
				CD08ECCB65CEC6CD4D672F55 /* Reachability.m in Sources */,
				15EDD835E166901FC93365AF /* CustomAppController.mm in Sources */,
				F981FE9AF0502B4560EDD3A4 /* UnityAdsNativeObject.m in Sources */,
				9FCC42758212E45AD1BBDF9A /* Dummy.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		5623C58217FDCB0900090B9E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1D6058900D05DD3D006BFB54 /* Unity-iPhone */;
			targetProxy = 5623C58117FDCB0900090B9E /* PBXContainerItemProxy */;
		};
		7F401A4D272AC77C005AF450 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7F4E059F2717216D00A2CBE4 /* GameAssembly */;
			targetProxy = 7F401A4C272AC77C005AF450 /* PBXContainerItemProxy */;
		};
		9D25ABA3213FB47800354C27 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9D25AB9C213FB47800354C27 /* UnityFramework */;
			targetProxy = 9D25ABA2213FB47800354C27 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		5623C57B17FDCB0900090B9E /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				5623C57C17FDCB0900090B9E /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1D6058940D05DD3E006BFB54 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9AEC99D2D5AD154137C30A61;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 77UQX76V75;
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.iappsteam.zzlixlapp;
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = Debug;
		};
		1D6058950D05DD3E006BFB54 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 15D71EBFA9562B150B2E69B7;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = 77UQX76V75;
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.iappsteam.zzlixlapp;
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = Release;
		};
		5623C58317FDCB0900090B9E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
		5623C58417FDCB0900090B9E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		56E860801D6757FF00A1AB2B /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PRODUCT_NAME_APP = EduApp;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = ReleaseForRunning;
		};
		56E860811D6757FF00A1AB2B /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 53584FD960B4B411A167C0F4;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 77UQX76V75;
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.iappsteam.zzlixlapp;
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = ReleaseForRunning;
		};
		56E860821D6757FF00A1AB2B /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = xctest;
			};
			name = ReleaseForRunning;
		};
		56E860831D67581C00A1AB2B /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PRODUCT_NAME_APP = EduApp;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = ReleaseForProfiling;
		};
		56E860841D67581C00A1AB2B /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6141015D135F7A3CB8A19391;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = 77UQX76V75;
				ENABLE_ON_DEMAND_RESOURCES = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				INFOPLIST_FILE = Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"$(OTHER_CFLAGS)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.iappsteam.zzlixlapp;
				PRODUCT_NAME = "$(PRODUCT_NAME_APP)";
				PROVISIONING_PROFILE = "$(PROVISIONING_PROFILE_APP)";
				PROVISIONING_PROFILE_APP = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
			};
			name = ReleaseForProfiling;
		};
		56E860851D67581C00A1AB2B /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Unity-iPhone Tests/Unity-iPhone Tests-Prefix.pch";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				INFOPLIST_FILE = "Unity-iPhone Tests/Unity-iPhone Tests-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.unity3d.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/$(PRODUCT_NAME_APP).app/$(PRODUCT_NAME_APP)";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = xctest;
			};
			name = ReleaseForProfiling;
		};
		7F4E05A62717216D00A2CBE4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7F4E05A72717216D00A2CBE4 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForProfiling;
		};
		7F4E05A82717216D00A2CBE4 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseForRunning;
		};
		7F4E05A92717216D00A2CBE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9D25ABA7213FB47800354C27 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 825CA0BD6C0EBAA9DC8AA9D4;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.0;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=61",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = Release;
		};
		9D25ABA8213FB47800354C27 /* ReleaseForProfiling */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 43B4BE7C58A2AA60D8FEAD26;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.0;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=61",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = ReleaseForProfiling;
		};
		9D25ABA9213FB47800354C27 /* ReleaseForRunning */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 89FC170F9154433D73BADA70;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1.0;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=61",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = ReleaseForRunning;
		};
		9D25ABAA213FB47800354C27 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 783E22A3A60914DCD509C3A0;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.0;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_ON_DEMAND_RESOURCES = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/com.unity.ads/Plugins/iOS",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Classes/Prefix.pch;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Classes",
					"$(SRCROOT)",
					"$(SRCROOT)/Classes/Native",
					"$(SRCROOT)/Libraries/bdwgc/include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/libil2cpp/pch",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Include",
					"$(SRCROOT)/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include",
					"$(SRCROOT)/Libraries/external/baselib/Platforms/IOS/Include",
				);
				INFOPLIST_FILE = UnityFramework/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Libraries",
					"$(SRCROOT)/Libraries/Plugins/iOS",
					"$(SRCROOT)/Libraries/Plugins/iOS/GdtMob",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fno-strict-overflow",
					"-DUNITY_VERSION_VER=2022",
					"-DUNITY_VERSION_MAJ=3",
					"-DUNITY_VERSION_MIN=61",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-weak_framework",
					CoreMotion,
					"-weak-lSystem",
					"$(OTHER_LDFLAGS_FRAMEWORK)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.unity3d.framework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				STRIP_STYLE = "non-global";
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				UNITY_RUNTIME_VERSION = 2022.3.61f1;
				UNITY_SCRIPTING_BACKEND = il2cpp;
				WARNING_CFLAGS = "-Wno-missing-declarations";
			};
			name = Debug;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME_APP = EduApp;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = arm64;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_CPP_EXCEPTIONS = NO;
				GCC_ENABLE_CPP_RTTI = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_THUMB_SUPPORT = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PRODUCT_NAME_APP = EduApp;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "Unity-iPhone" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D6058950D05DD3E006BFB54 /* Release */,
				56E860841D67581C00A1AB2B /* ReleaseForProfiling */,
				56E860811D6757FF00A1AB2B /* ReleaseForRunning */,
				1D6058940D05DD3E006BFB54 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5623C58517FDCB0900090B9E /* Build configuration list for PBXNativeTarget "Unity-iPhone Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5623C58317FDCB0900090B9E /* Release */,
				56E860851D67581C00A1AB2B /* ReleaseForProfiling */,
				56E860821D6757FF00A1AB2B /* ReleaseForRunning */,
				5623C58417FDCB0900090B9E /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7F4E05AA2717216D00A2CBE4 /* Build configuration list for PBXNativeTarget "GameAssembly" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7F4E05A62717216D00A2CBE4 /* Release */,
				7F4E05A72717216D00A2CBE4 /* ReleaseForProfiling */,
				7F4E05A82717216D00A2CBE4 /* ReleaseForRunning */,
				7F4E05A92717216D00A2CBE4 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9D25ABA6213FB47800354C27 /* Build configuration list for PBXNativeTarget "UnityFramework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9D25ABA7213FB47800354C27 /* Release */,
				9D25ABA8213FB47800354C27 /* ReleaseForProfiling */,
				9D25ABA9213FB47800354C27 /* ReleaseForRunning */,
				9D25ABAA213FB47800354C27 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Unity-iPhone" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF5008A954540054247B /* Release */,
				56E860831D67581C00A1AB2B /* ReleaseForProfiling */,
				56E860801D6757FF00A1AB2B /* ReleaseForRunning */,
				C01FCF4F08A954540054247B /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
