<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="15702" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="UnityLaunchScreen-ViewController">
    <device id="ipad9_7" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15704"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="UnityLaunchScreen-Scene">
            <objects>
                <viewController id="UnityLaunchScreen-ViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="rHs-FS-qul"/>
                        <viewControllerLayoutGuide type="bottom" id="s5L-YT-9mv"/>
                    </layoutGuides>
                    <view key="view" userInteractionEnabled="NO" contentMode="scaleToFill" id="UnityLaunchScreen-RootView" userLabel="RootView">
                        <rect key="frame" x="0.0" y="0.0" width="768" height="1024"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchScreen-iPad.png" translatesAutoresizingMaskIntoConstraints="NO" id="UnityLaunchScreen-ImageView" userLabel="ImageView">
                                <rect key="frame" x="0.0" y="0.0" width="768" height="1024"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="UnityLaunchScreen-ImageView" secondAttribute="bottom" id="Y0U-Pe-L4d"/>
                            <constraint firstItem="UnityLaunchScreen-ImageView" firstAttribute="top" secondItem="UnityLaunchScreen-RootView" secondAttribute="top" id="hwu-wP-OkZ"/>
                            <constraint firstItem="UnityLaunchScreen-ImageView" firstAttribute="leading" secondItem="UnityLaunchScreen-RootView" secondAttribute="leading" id="oOc-bh-ihE"/>
                            <constraint firstAttribute="trailing" secondItem="UnityLaunchScreen-ImageView" secondAttribute="trailing" id="rHJ-TI-xEc"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchScreen-iPad.png" width="2208" height="1242"/>
    </resources>
</document>
