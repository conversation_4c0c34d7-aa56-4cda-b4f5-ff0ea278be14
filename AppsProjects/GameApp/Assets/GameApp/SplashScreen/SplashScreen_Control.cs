using UnityEngine;
using System.Collections;
using DG.Tweening;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using LearningSuite;

public class SplashScreen_Control : MonoBehaviour
{

    public GameObject mainLogo, i_point, i_end0, i_end1, i_end2, loadingText;
    public SplashScreenMusicControl musicControl;

    [Header("启动设置")]
    [SerializeField] private string defaultThemeId = "police_detective";
    [SerializeField] private string mainMenuSceneName = "PE_Home";
    [SerializeField] private Slider progressBar;

    private Vector3 mainLogoOriginPos = new Vector3(0, 0, 0);
    private bool isInitializing = false;
    private bool isLogoAnimationComplete = false;
    //private MMT.MobileMovieTexture m_movieTexture;

    void Awake()
    {
        //PlayerPrefs.DeleteAll();

        GlobalVariable.CheckUserOSLanguage();
        GlobalVariable.GetUserSavedLanguage();

        GlobalVariable.canSound = GameManager.Instance().GetSoundOnOff();
        GlobalVariable.canSpeaker = GameManager.Instance().GetSpeakerOnOff();

        mainLogo = FindChildObject("MainLogo", gameObject);
        i_point = FindChildObject("i_point", gameObject);
        i_end0 = FindChildObject("i_point0", gameObject);
        i_end1 = FindChildObject("i_point (1)", gameObject);
        i_end2 = FindChildObject("i_point (2)", gameObject);

        musicControl = FindObjectOfType<SplashScreenMusicControl>();

        mainLogoOriginPos = mainLogo.transform.position;

        if (Application.platform == RuntimePlatform.IPhonePlayer || Application.platform == RuntimePlatform.Android)
        {

        }
        else
        {
            Debug.Log("Caching.ClearCache()");
            Caching.ClearCache();
        }

        mainLogo.transform.position = GameUtility.VT_AddY(mainLogo.transform.position, 10);
        i_point.transform.localScale = Vector3.zero;
        i_point.transform.position = i_end0.transform.position;
    }

    GameObject FindChildObject(string child_name, GameObject root)
    {
        GameObject findObject = null;

        for (int i = 0; i < root.transform.childCount; i ++)
        {
            GameObject child_object = transform.GetChild(i).gameObject;
            if(child_name == child_object.name)
            {
                findObject = child_object;

                break;
            }
        }

        return findObject;
    }

    void Start()
    {
        // 隐藏加载文本，等待Logo动画完成后再显示
        if (loadingText != null)
        {
            loadingText.SetActive(false);
        }

        // 开始Logo动画
        MainLogoMoveIn();
    }

    void MainLogoMoveIn()
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Prop("position", mainLogoOriginPos);
        parms1.AutoKill(true);
        parms1.Ease(EaseType.EaseInOutBack);
        parms1.Loops(1, LoopType.Yoyo);
        parms1.OnComplete(MainLogoMoveInEnd);
        parms1.Delay(0.05f); //延迟时间
        HOTween.To(mainLogo.transform, 1, parms1);
*/
        mainLogo.transform.DOMove(mainLogoOriginPos, 1).SetEase(Ease.InOutBack).SetLoops(1, LoopType.Yoyo)
                .SetDelay(0.05f).OnComplete(MainLogoMoveInEnd);
    }

    void MainLogoMoveInEnd()
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Prop("localScale", new Vector3(1f, 0.8f, 1f));
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear);
        parms1.Loops(2, LoopType.Yoyo);
        parms1.Delay(0.1f);
        parms1.OnComplete(I_Point_Show);
        HOTween.To(mainLogo.transform, 0.3f, parms1);
*/
        mainLogo.transform.DOScale(new Vector3(1f, 0.8f, 1f), 0.3f).OnComplete(I_Point_Show)
                 .SetLoops(2, LoopType.Yoyo).SetEase(Ease.Linear).SetDelay(0.1f);

    }

    void I_Point_Show()
    {
        PlaySound();
        i_point.transform.parent = mainLogo.transform;

        /*
        TweenParms parms1 = new TweenParms();
        parms1.Prop("localScale", new Vector3(1f, 1f, 1f));
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear);
        parms1.Loops(1, LoopType.Yoyo);
        parms1.Delay(0.1f);
        parms1.OnComplete(I_Point_GoPoint1);
        HOTween.To(i_point.transform, 0.3f, parms1);
*/
        i_point.transform.DOScale(new Vector3(1f, 1f, 1f), 0.3f).OnComplete(I_Point_GoPoint1)
                 .SetLoops(1, LoopType.Yoyo).SetEase(Ease.Linear).SetDelay(0.1f);

    }

    void I_Point_GoPoint1()
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Prop("position", i_end1.transform.position);
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear);
        parms1.Loops(2, LoopType.Yoyo);
        parms1.Delay(0.1f);
        parms1.OnComplete(I_Point_GoPoint2);
        HOTween.To(i_point.transform, 0.3f, parms1);
*/
        i_point.transform.DOMove(i_end1.transform.position, 0.3f).OnComplete(I_Point_GoPoint2)
                 .SetLoops(2, LoopType.Yoyo).SetEase(Ease.Linear).SetDelay(0.1f);
    }

    void I_Point_GoPoint2()
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Prop("position", i_end2.transform.position);
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear);
        parms1.Loops(1, LoopType.Yoyo);
        parms1.Delay(0.1f);
        parms1.OnComplete(I_Point_Scale);
        HOTween.To(i_point.transform, 0.3f, parms1);

*/
        i_point.transform.DOMove(i_end2.transform.position, 0.3f).OnComplete(I_Point_Scale)
                 .SetLoops(1, LoopType.Yoyo).SetEase(Ease.Linear).SetDelay(0.1f);
    }

    void I_Point_Scale()
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Prop("localScale", new Vector3(1.03f, 1.03f, 1.03f));
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear);
        parms1.Loops(2, LoopType.Yoyo);
        parms1.Delay(0.1f);
        parms1.OnComplete(I_Point_ScaleEnd);
        HOTween.To(i_point.transform, 0.3f, parms1);
*/
        i_point.transform.DOScale(new Vector3(1.03f, 1.03f, 1.03f), 0.3f).OnComplete(I_Point_ScaleEnd)
                 .SetLoops(2, LoopType.Yoyo).SetEase(Ease.Linear).SetDelay(0.1f);
    }

    void I_Point_ScaleEnd()
    {
        float t = 0.5f;

        for (int i = 0; i < mainLogo.transform.childCount; i ++)
        {
            GameObject childObject = mainLogo.transform.GetChild(i).gameObject;
            ObjectFadeOut(childObject, t);
        }

        // 标记Logo动画已完成
        isLogoAnimationComplete = true;

        // 显示加载文本（使用DOTween淡入效果）
        //FadeInGameObject(loadingText);

        // 开始初始化游戏
        if (!isInitializing)
        {
            isInitializing = true;
            StartCoroutine(InitializeGame());
        }

        // 调用GoToHomeScene方法（保持原有逻辑，但现在会等待初始化完成）
        Invoke(nameof(GoToHomeScene), t);
    }

    void ObjectFadeOut(GameObject obj, float duration)
    {
        SpriteRenderer sp = obj.GetComponent<SpriteRenderer>();
        if (sp)
        {
            /*
            TweenParms parms1 = new TweenParms();
            parms1.Prop("color", new Color(sp.color.r, sp.color.g, sp.color.b, 0f));
            parms1.Ease(EaseType.Linear);
            parms1.Loops(1, LoopType.Yoyo);
            parms1.Delay(0);
            HOTween.To(sp, duration, parms1);
*/
            sp.DOFade(0, duration).SetEase(Ease.Linear);
        }
        else
        {
            Debug.Log("not component tk2dSprite, please check game object!");
        }
    }


    void PlaySound()
    {
        musicControl.InitSoundSetting();
    }


    void GoToHomeScene()
    {
        if (AspectRatios.GetAspectRatio() == AspectRatio.Aspect4by3)
        {
            Debug.Log("=======is ipad ======");
        }
        else
        {
            Debug.Log("=======is iphone ======");
        }

        // 如果初始化已完成，直接加载主菜单
        if (!isInitializing)
        {
            LoadMainMenu();
        }
        // 否则等待初始化完成（由InitializeGame协程完成后调用LoadMainMenu）
    }

    /// <summary>
    /// 初始化游戏
    /// </summary>
    private IEnumerator InitializeGame()
    {
        // 确保loadingText已显示（如果尚未显示，使用DOTween淡入效果）
        if (loadingText != null && !loadingText.activeSelf)
        {
            //FadeInGameObject(loadingText);
        }

        // 等待一帧，确保所有组件都已初始化
        yield return null;

        // 初始化ThemeManager
        Debug.Log("初始化ThemeManager...");
        //ThemeManager themeManager = ThemeManager.Instance;

        // 更新进度条
        UpdateProgress(0.3f);
        yield return null;

        // 设置默认主题
        Debug.Log($"设置默认主题: {defaultThemeId}");
        //themeManager.SetCurrentTheme(defaultThemeId);

        // 更新进度条
        UpdateProgress(0.6f);
        yield return null;

        // 初始化其他系统
        // ...

        // 更新进度条
        UpdateProgress(0.9f);
        yield return null;

        // 完成初始化
        Debug.Log("初始化完成，准备加载主菜单...");
        UpdateProgress(1.0f);

        // 等待一小段时间，让用户看到加载完成
        yield return new WaitForSeconds(0f);

        // 标记初始化完成
        isInitializing = false;

        // 如果Logo动画已完成，加载主菜单
        if (isLogoAnimationComplete)
        {
            LoadMainMenu();
        }
    }

    /// <summary>
    /// 更新进度条
    /// </summary>
    private void UpdateProgress(float progress)
    {
        if (progressBar != null)
        {
            progressBar.value = progress;
        }
    }

    /// <summary>
    /// 使用DOTween淡入显示游戏对象
    /// </summary>
    private void FadeInGameObject(GameObject gameObject, float duration = 0.8f)
    {
        if (gameObject == null)
            return;

        // 确保游戏对象是激活的
        gameObject.SetActive(true);

        // 获取SpriteRenderer组件（适用于Sprite对象）
        SpriteRenderer spriteRenderer = gameObject.GetComponent<SpriteRenderer>();
        if (spriteRenderer != null)
        {
            // 设置初始透明度为0
            Color spriteColor = spriteRenderer.color;
            spriteRenderer.color = new Color(spriteColor.r, spriteColor.g, spriteColor.b, 0f);

            // 使用DOTween淡入
            spriteRenderer.DOFade(1f, duration).SetEase(Ease.InOutQuad);
            return;
        }

        // 获取文本组件（适用于UI Text）
        Text textComponent = gameObject.GetComponent<Text>();
        if (textComponent != null)
        {
            // 设置初始透明度为0
            Color textColor = textComponent.color;
            textComponent.color = new Color(textColor.r, textColor.g, textColor.b, 0f);

            // 使用DOTween淡入
            textComponent.DOFade(1f, duration).SetEase(Ease.InOutQuad);
            return;
        }

        // 如果没有SpriteRenderer或Text组件，尝试获取CanvasGroup
        CanvasGroup canvasGroup = gameObject.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            // 如果没有CanvasGroup，添加一个
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        }

        // 设置初始透明度为0
        canvasGroup.alpha = 0f;

        // 使用DOTween淡入
        canvasGroup.DOFade(1f, duration).SetEase(Ease.InOutQuad);
    }

    /// <summary>
    /// 加载主菜单
    /// </summary>
    private void LoadMainMenu()
    {
        // 可以选择加载通用的主菜单场景
        GameUtility.GoToSceneName_NotAddSuffix(mainMenuSceneName);

        // 或者直接加载当前主题的首页
        // ThemeManager.Instance.LoadThemeHomepage(defaultThemeId);
    }
}

