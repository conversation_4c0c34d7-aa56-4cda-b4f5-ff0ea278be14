using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using MiniJSON_New;
using System;
using System.Security;
using System.Text;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class LoadAppConfig : MonoBehaviour
{
    //down file
    string appConfigDataUrl = GlobalVariable.GetWebAddr() + "app" + GlobalVariable.AppleAppId + "/appsmapconf1.json";
    string configData = "";
    int configData_version = 1;
    public bool isLoading = false;


    public string net_Crc = "";
    public string sVersion = "";
    public string downloadDataUrl = "";

    public string local_Crc = "";
    public string local_sVersion = "";
    public string local_downloadDataUrl = "";


    public string newApp_net_Crc = "";
    public string newApp_sVersion = "";
    public string newApp_downloadDataUrl = "";

    public string newApp_local_Crc = "";
    public string newApp_local_sVersion = "";
    public string newApp_local_downloadDataUrl = "";

    void Start()
    {
        string local_configData = Resources.Load("appsmapconf").ToString();
        local_Crc = GetKeyValueForConfig("CRC", local_configData);
        local_sVersion = GetKeyValueForConfig("Ver", local_configData);
        local_downloadDataUrl = GetKeyValueForConfig("dataUrl", local_configData);

        newApp_local_Crc = GetKeyValueForConfig("newApp_CRC", local_configData);
        newApp_local_sVersion = GetKeyValueForConfig("newApp_Ver", local_configData);
        newApp_local_downloadDataUrl = GetKeyValueForConfig("newApp_dataUrl", local_configData);

    }

    public void WebDataInit()
    {

       //PlayerPrefs.DeleteAll();
        if (isLoading) return;

        isLoading = true;
        StartCoroutine(LoadAppConfigData());

    }

    string GetLocal_NewConfigData()
    {
        return Resources.Load("appsmapconf").ToString();
    }

    IEnumerator LoadAppConfigData()//load AppConfigData
    {
        Debug.Log("... LoadAppConfigData ......");
        //if(picUrl == "") break;
        Debug.Log("LoadAppConfigData: " + appConfigDataUrl);

        var r = new HTTP.Request("GET", appConfigDataUrl);
        yield return r.Send();
        if (r.exception == null)
        {
            Debug.Log(r.response.status);
            //Debug.Log(r.response.Text);
            configData = r.response.Text;

            if (configData != "")
            {
                Debug.Log("web configData:" + configData);

                net_Crc = GetKeyValueForConfig("CRC", configData);
                sVersion = GetKeyValueForConfig("Ver", configData);
                string web_prex = GetKeyValueForConfig("web_prex", configData);
                downloadDataUrl = web_prex + "://";
                downloadDataUrl += GetKeyValueForConfig("dataUrl", configData);


                newApp_net_Crc = GetKeyValueForConfig("newApp_CRC", configData);
                newApp_sVersion = GetKeyValueForConfig("newApp_Ver", configData);
                web_prex = GetKeyValueForConfig("newApp_web_prex", configData);
                newApp_downloadDataUrl = web_prex + "://";
                newApp_downloadDataUrl += GetKeyValueForConfig("newApp_dataUrl", configData);
            }

        }
        else
        {
            Debug.Log("*-( HTTP error " + r.exception);

            string local_NewConfigData = 

            configData = GetLocal_NewConfigData();
            Debug.Log("load app config www fail, GetLocal_NewConfigData:" + configData);

            net_Crc = GetKeyValueForConfig("CRC", configData);
            sVersion = GetKeyValueForConfig("Ver", configData);
            string web_prex = GetKeyValueForConfig("web_prex", configData);
            downloadDataUrl = web_prex + "://";
            downloadDataUrl += GetKeyValueForConfig("dataUrl", configData);

            newApp_net_Crc = GetKeyValueForConfig("newApp_CRC", configData);
            newApp_sVersion = GetKeyValueForConfig("newApp_Ver", configData);
            web_prex = GetKeyValueForConfig("newApp_web_prex", configData);
            newApp_downloadDataUrl = web_prex + "://";
            newApp_downloadDataUrl = GetKeyValueForConfig("newApp_dataUrl", configData);

        }

    }

    string GetKeyValueForConfig(string skey, string input)
    {
        string str = "";

        try
        {
            Hashtable ht = MiniJsonExtensions.hashtableFromJson(input);
            //Debug.Log("skey: "+skey);
            if (ht.ContainsKey(skey))
            {
                str = ht[skey].ToString();
            }
            else
            {
                configData = GetLocal_NewConfigData();
                Debug.Log("GetLocal_NewConfigData:" + configData);
                Hashtable ht1 = MiniJsonExtensions.hashtableFromJson(configData);
                if (ht1.ContainsKey(skey))
                    str = ht1[skey].ToString();
            }
        }
        catch
        {
            configData = GetLocal_NewConfigData();
            Debug.Log("GetLocal_NewConfigData:" + configData);
            Hashtable ht = MiniJsonExtensions.hashtableFromJson(configData);
            if (ht.ContainsKey(skey))
                str = ht[skey].ToString();
        }
        return str;
    }

    public void ReDownData()
    {
        WebDataInit();
    }

    #region web action
    public static string GetWebAddr()
    {
        string strType = string.Empty;
#if UNITY_IPHONE
        strType = IAppsTeamIOSUntil.GetConfigParams("web_addr");
#endif

        if (strType == "" || strType == null)
        {
            strType = "http://caps2.appfun8.com/";
        }

        return strType;
    }

    #endregion

}
