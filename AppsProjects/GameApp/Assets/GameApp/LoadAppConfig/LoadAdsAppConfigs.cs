using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using MiniJSON_New;
using System;
using System.Security;
using System.Text;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif
using LitJson;


public class LoadAdsAppConfigs : MonoBehaviour
{
    //down file
    //apps/public/index.php? appinfo/getads/1328303722
    string appConfigDataUrl = string.Format("{0}app{1}/{2}.json", GlobalVariable.GetWebAddr_Ads(), GlobalVariable.AppleAppId, GlobalVariable.AdsConfig_Name);

    string configData = "";
    int configData_version = 1, initCount = 10;
    public bool isLoading = false;
    public DateTime nextUpdateTime;

    string flagSaveVersionId = GlobalVariable.AdsConfig_Ver;

    void Start()
    {
        //PlayerPrefs.DeleteAll();
        WebDataInit();
    }

    public void WebDataInit()
    {

        //PlayerPrefs.DeleteAll();
        if (isLoading) return;

        isLoading = true;
        StartCoroutine(LoadAppConfigData());

    }

    IEnumerator LoadAppConfigData()//load AppConfigData
    {
        Debug.Log("... LoadAds_AppConfigData ......");
        Debug.Log("LoadAds_AppConfigData: " + appConfigDataUrl);

        // 添加空值检查
        if (string.IsNullOrEmpty(appConfigDataUrl))
        {
            Debug.LogError("appConfigDataUrl is null or empty");
            SetLocalVersionValueForOne();
            isLoading = false;
            yield break;
        }

        HTTP.Request r = null;

        // 创建HTTP请求
        try
        {
            r = new HTTP.Request("GET", appConfigDataUrl);
            if (r == null)
            {
                Debug.LogError("Failed to create HTTP request");
                SetLocalVersionValueForOne();
                isLoading = false;
                yield break;
            }
            r.timeout = 30f; // 30秒超时
        }
        catch (System.Exception ex)
        {
            Debug.LogError("Failed to create HTTP request: " + ex.Message);
            SetLocalVersionValueForOne();
            isLoading = false;
            yield break;
        }

        // 发送HTTP请求
        yield return r.Send();

        // 检查协程是否被销毁
        if (this == null || !gameObject.activeInHierarchy)
        {
            Debug.LogWarning("GameObject was destroyed during HTTP request");
            isLoading = false;
            yield break;
        }

        // 处理响应
        try
        {
            if (r.exception == null && r.response != null)
            {
                Debug.Log("HTTP Response Status: " + r.response.status);
                configData = r.response.Text;

                if (!string.IsNullOrEmpty(configData))
                {
                    Debug.Log("LoadAds_AppConfigData:" + configData);
                    ProcessConfigData(configData);
                }
                else
                {
                    Debug.LogWarning("Received empty config data from server");
                    SetLocalVersionValueForOne();
                }
            }
            else
            {
                string errorMsg = r.exception != null ? r.exception.Message : "Response is null";
                Debug.LogError("HTTP error: " + errorMsg);
                SetLocalVersionValueForOne();
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError("LoadAppConfigData Exception: " + ex.Message);
            Debug.LogError("Stack Trace: " + ex.StackTrace);
            SetLocalVersionValueForOne();
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ProcessConfigData(string configData)
    {
        try
        {
            JsonData data = JsonMapper.ToObject(configData);
            if (data == null)
            {
                Debug.LogError("Failed to parse JSON data");
                SetLocalVersionValueForOne();
                return;
            }

            // 安全获取版本信息
            string net_ads_version = "1";
            try
            {
                if (data["ver"] != null)
                {
                    net_ads_version = data["ver"].ToString();
                }
            }
            catch
            {
                Debug.LogWarning("Failed to get version from JSON, using default: 1");
            }

            Debug.Log(string.Format("net_ads_version: {0}", net_ads_version));

            string local_version = PlayerPrefs.GetString(flagSaveVersionId, "0");

            if (net_ads_version == local_version)
            {
                Debug.Log(string.Format("the version is same, not to update! Local ads config Version is: {0}", local_version));
            }
            else
            {
                PlayerPrefs.SetString(flagSaveVersionId, net_ads_version);
                PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, configData);
                PlayerPrefs.Save();
                Debug.Log(string.Format("Ads config Update to new version: {0}", net_ads_version));
            }

            // 安全获取刷新时间
            string refreshTimeStr = "30";
            try
            {
                if (data["get"] != null && data["get"]["t"] != null)
                {
                    refreshTimeStr = data["get"]["t"].ToString();
                }
            }
            catch
            {
                Debug.LogWarning("Failed to get refresh time from JSON, using default: 30");
            }

            PlayerPrefs.SetString(GlobalVariable.AdsConfig_Refresh, refreshTimeStr);
            PlayerPrefs.Save();

            double refresh_m = 30;
            if (!double.TryParse(refreshTimeStr, out refresh_m) || refresh_m <= 0)
            {
                refresh_m = 30; // 默认30分钟
            }

            DateTime d1 = DateTime.Now;
            nextUpdateTime = d1.AddMinutes(refresh_m);

            Debug.Log(string.Format("1 : nextUpdateTime: {0}", nextUpdateTime.ToLongTimeString()));
        }
        catch (System.Exception ex)
        {
            Debug.LogError("ProcessConfigData Exception: " + ex.Message);
            SetLocalVersionValueForOne();
        }
    }

    void SetLocalVersionValueForOne()
    {
        try
        {
            string local_version = PlayerPrefs.GetString(flagSaveVersionId, "0");
            Debug.Log($"Current local version: {local_version}");

            if (local_version == "0")
            {
                Debug.Log("Loading local ads config resource...");

                try
                {
                    TextAsset localConfigAsset = Resources.Load<TextAsset>(GlobalVariable.AdsConfig_Name);
                    if (localConfigAsset != null && !string.IsNullOrEmpty(localConfigAsset.text))
                    {
                        PlayerPrefs.SetString(flagSaveVersionId, "1");
                        PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, localConfigAsset.text);
                        PlayerPrefs.Save();
                        Debug.Log("Ads config Set local ads config To V1");
                    }
                    else
                    {
                        Debug.LogError("Failed to load local config resource, using default");
                        SetDefaultConfig();
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError("Error loading local config: " + ex.Message);
                    SetDefaultConfig();
                }
            }
            else
            {
                Debug.Log(string.Format("Local ads config already exists, version: {0}", local_version));
            }

            // 设置下次更新时间（1秒后重试）
            DateTime d1 = DateTime.Now;
            nextUpdateTime = d1.AddSeconds(1);
            Debug.Log(string.Format("2 : nextUpdateTime: {0}", nextUpdateTime.ToLongTimeString()));
        }
        catch (System.Exception ex)
        {
            Debug.LogError("SetLocalVersionValueForOne Exception: " + ex.Message);
            SetDefaultConfig();
        }
    }

    private void SetDefaultConfig()
    {
        try
        {
            string defaultConfig = @"{""ver"":""1"",""get"":{""t"":""30""}}";
            PlayerPrefs.SetString(flagSaveVersionId, "1");
            PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, defaultConfig);
            PlayerPrefs.Save();
            Debug.Log("Set default config");

            DateTime d1 = DateTime.Now;
            nextUpdateTime = d1.AddMinutes(30);
        }
        catch (System.Exception ex)
        {
            Debug.LogError("SetDefaultConfig Exception: " + ex.Message);
        }
    }

    public void ReGetDonwData()
    {
        if(DateTime.Now > nextUpdateTime)
        {
            Debug.Log("ReGetDownData !!!");
            WebDataInit();
        }
        
    }


}
