using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using MiniJSON_New;
using System;
using System.Security;
using System.Text;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif
using LitJson;


public class LoadAdsAppConfigs : MonoBehaviour
{
    //down file
    //apps/public/index.php? appinfo/getads/1328303722
    string appConfigDataUrl = string.Format("{0}app{1}/{2}.json", GlobalVariable.GetWebAddr_Ads(), GlobalVariable.AppleAppId, GlobalVariable.AdsConfig_Name);

    string configData = "";
    int configData_version = 1, initCount = 10;
    public bool isLoading = false;
    public DateTime nextUpdateTime;

    string flagSaveVersionId = GlobalVariable.AdsConfig_Ver;

    void Start()
    {
        //PlayerPrefs.DeleteAll();
        WebDataInit();
    }

    public void WebDataInit()
    {

        //PlayerPrefs.DeleteAll();
        if (isLoading) return;

        isLoading = true;
        StartCoroutine(LoadAppConfigData());

    }

    IEnumerator LoadAppConfigData()//load AppConfigData
    {
        Debug.Log("... LoadAds_AppConfigData ......");
        Debug.Log("LoadAds_AppConfigData: " + appConfigDataUrl);

        try
        {
            var r = new HTTP.Request("GET", appConfigDataUrl);
            yield return r.Send();

            if (r.exception == null)
            {
                Debug.Log("HTTP Response Status: " + r.response.status);
                configData = r.response.Text;

                if (!string.IsNullOrEmpty(configData))
                {
                    Debug.Log("LoadAds_AppConfigData:" + configData);
                    ProcessConfigData(configData);
                }
                else
                {
                    Debug.LogWarning("Received empty config data from server");
                    SetLocalVersionValueForOne();
                }
            }
            else
            {
                Debug.LogError("HTTP error: " + r.exception.Message);
                SetLocalVersionValueForOne();
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError("LoadAppConfigData Exception: " + ex.Message);
            Debug.LogError("Stack Trace: " + ex.StackTrace);
            SetLocalVersionValueForOne();
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ProcessConfigData(string configData)
    {
        try
        {
            JsonData data = JsonMapper.ToObject(configData);

            // 安全地获取版本信息
            string net_ads_version = GetSafeJsonValue(data, "ver", "0");
            Debug.Log(string.Format("net_ads_version: {0}", net_ads_version));

            string local_version = PlayerPrefs.GetString(flagSaveVersionId, "0");

            if (net_ads_version == local_version)
            {
                Debug.Log(string.Format("Version is same, no update needed! Local ads config Version: {0}", local_version));
            }
            else
            {
                PlayerPrefs.SetString(flagSaveVersionId, net_ads_version);
                PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, configData);
                Debug.Log(string.Format("Ads config updated to new version: {0}", net_ads_version));
            }

            // 安全地处理刷新时间
            ProcessRefreshTime(data);
        }
        catch (System.Exception ex)
        {
            Debug.LogError("ProcessConfigData Exception: " + ex.Message);
            Debug.LogError("Stack Trace: " + ex.StackTrace);
            SetLocalVersionValueForOne();
        }
    }

    private void ProcessRefreshTime(JsonData data)
    {
        try
        {
            // 安全地获取刷新时间
            string refreshTimeStr = GetSafeJsonValue(data, new string[] { "get", "t" }, "30");
            PlayerPrefs.SetString(GlobalVariable.AdsConfig_Refresh, refreshTimeStr);

            double refresh_m = 30;
            if (!Double.TryParse(refreshTimeStr, out refresh_m))
            {
                refresh_m = 30; // 默认30分钟
                Debug.LogWarning("Failed to parse refresh time, using default 30 minutes");
            }

            DateTime d1 = DateTime.Now;
            nextUpdateTime = d1.AddMinutes(refresh_m);

            Debug.Log(string.Format("1 : nextUpdateTime: {0}", nextUpdateTime.ToLongTimeString()));
        }
        catch (System.Exception ex)
        {
            Debug.LogError("ProcessRefreshTime Exception: " + ex.Message);
            // 设置默认刷新时间
            DateTime d1 = DateTime.Now;
            nextUpdateTime = d1.AddMinutes(30);
        }
    }

    private string GetSafeJsonValue(JsonData data, string key, string defaultValue)
    {
        try
        {
            if (data != null && data.ContainsKey(key) && data[key] != null)
            {
                return data[key].ToString();
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning($"Failed to get JSON value for key '{key}': {ex.Message}");
        }
        return defaultValue;
    }

    private string GetSafeJsonValue(JsonData data, string[] keys, string defaultValue)
    {
        try
        {
            JsonData current = data;
            foreach (string key in keys)
            {
                if (current != null && current.ContainsKey(key) && current[key] != null)
                {
                    current = current[key];
                }
                else
                {
                    return defaultValue;
                }
            }
            return current.ToString();
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning($"Failed to get nested JSON value for keys '{string.Join(".", keys)}': {ex.Message}");
        }
        return defaultValue;
    }

    void SetLocalVersionValueForOne()
    {
        string local_version = PlayerPrefs.GetString(flagSaveVersionId, "0");
        if (local_version == "0")
        {
            //1 get local ads config
            PlayerPrefs.SetString(flagSaveVersionId, "1");

            PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, Resources.Load(GlobalVariable.AdsConfig_Name).ToString());

            Debug.Log("Ads config Set local ads config To V1");
        }
        else
        {
            Debug.Log(string.Format("not set value, Local ads config Version is: {0}", local_version));
           
        }

        /*
        string local_config_data = PlayerPrefs.GetString(GlobalVariable.AdsConfig_Content);

        JsonData data = JsonMapper.ToObject(local_config_data);

        PlayerPrefs.SetString(GlobalVariable.AdsConfig_Refresh, data["get"]["t"].ToString());
        string str_AdsConfig_Refresh = data["get"]["t"].ToString();
        double refresh_m = 30;
        Double.TryParse(str_AdsConfig_Refresh, out refresh_m);

        DateTime d1 = DateTime.Now;
        */
        double refresh_m = 1;
        DateTime d1 = DateTime.Now;
        nextUpdateTime = d1.AddSeconds(refresh_m);

        Debug.Log(string.Format("2 : nextUpdateTime: {0}", nextUpdateTime.ToLongTimeString()));
    }

    public void ReGetDonwData()
    {
        if(DateTime.Now > nextUpdateTime)
        {
            Debug.Log("ReGetDownData !!!");
            WebDataInit();
        }
        
    }


}
