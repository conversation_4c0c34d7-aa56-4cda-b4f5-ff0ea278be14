using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using MiniJSON_New;
using System;
using System.Security;
using System.Text;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif
using LitJson;


public class LoadAdsAppConfigs : MonoBehaviour
{
    //down file
    //apps/public/index.php? appinfo/getads/1328303722
    string appConfigDataUrl = string.Format("{0}app{1}/{2}.json", GlobalVariable.GetWebAddr_Ads(), GlobalVariable.AppleAppId, GlobalVariable.AdsConfig_Name);

    string configData = "";
    int configData_version = 1, initCount = 10;
    public bool isLoading = false;
    public DateTime nextUpdateTime;

    string flagSaveVersionId = GlobalVariable.AdsConfig_Ver;

    void Start()
    {
        //PlayerPrefs.DeleteAll();
        WebDataInit();
    }

    public void WebDataInit()
    {

        //PlayerPrefs.DeleteAll();
        if (isLoading) return;

        isLoading = true;
        StartCoroutine(LoadAppConfigData());

    }

    IEnumerator LoadAppConfigData()//load AppConfigData
    {
        Debug.Log("... LoadAds_AppConfigData ......");
        //if(picUrl == "") break;
        Debug.Log("LoadAds_AppConfigData: " + appConfigDataUrl);

        var r = new HTTP.Request("GET", appConfigDataUrl);
        yield return r.Send();
        if (r.exception == null)
        {
            Debug.Log(r.response.status);
            //Debug.Log(r.response.Text);
            configData = r.response.Text;

            try
            {
                if (configData != "")
                {
                    Debug.Log("LoadAds_AppConfigData:" + configData);

                    JsonData data = JsonMapper.ToObject(configData);

                    string net_ads_version = data["ver"].ToString();

                    Debug.Log(string.Format("net_ads_version: {0}", net_ads_version));

                    string local_version = PlayerPrefs.GetString(flagSaveVersionId, "0");

                    if(net_ads_version == local_version)
                    {
                        Debug.Log(string.Format("the version is same, not to upadte! Local ads config Version is: {0}", local_version));
                    }
                    else
                    {
                        PlayerPrefs.SetString(flagSaveVersionId, net_ads_version);
                        PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, configData);

                        Debug.Log(string.Format("Ads config Upadte to new version: {0}", net_ads_version));
                    }

                    PlayerPrefs.SetString(GlobalVariable.AdsConfig_Refresh, data["get"]["t"].ToString());
                    string str_AdsConfig_Refresh = data["get"]["t"].ToString();
                    double refresh_m = 30;
                    Double.TryParse(str_AdsConfig_Refresh, out refresh_m);

                    DateTime d1 = DateTime.Now;
                    //refresh_m = 2;
                    nextUpdateTime = d1.AddMinutes(refresh_m);

                    Debug.Log(string.Format("1 : nextUpdateTime: {0}", nextUpdateTime.ToLongTimeString()));
                }
            }
            catch
            {
                Debug.Log("*-( Net Ads Config has Error!");

                SetLocalVersionValueForOne();
            }
        }
        else
        {
            Debug.Log("*-( HTTP error " + r.exception);

            SetLocalVersionValueForOne();

        }

        isLoading = false;
    }

    void SetLocalVersionValueForOne()
    {
        string local_version = PlayerPrefs.GetString(flagSaveVersionId, "0");
        if (local_version == "0")
        {
            //1 get local ads config
            PlayerPrefs.SetString(flagSaveVersionId, "1");

            PlayerPrefs.SetString(GlobalVariable.AdsConfig_Content, Resources.Load(GlobalVariable.AdsConfig_Name).ToString());

            Debug.Log("Ads config Set local ads config To V1");
        }
        else
        {
            Debug.Log(string.Format("not set value, Local ads config Version is: {0}", local_version));
           
        }

        /*
        string local_config_data = PlayerPrefs.GetString(GlobalVariable.AdsConfig_Content);

        JsonData data = JsonMapper.ToObject(local_config_data);

        PlayerPrefs.SetString(GlobalVariable.AdsConfig_Refresh, data["get"]["t"].ToString());
        string str_AdsConfig_Refresh = data["get"]["t"].ToString();
        double refresh_m = 30;
        Double.TryParse(str_AdsConfig_Refresh, out refresh_m);

        DateTime d1 = DateTime.Now;
        */
        double refresh_m = 1;
        DateTime d1 = DateTime.Now;
        nextUpdateTime = d1.AddSeconds(refresh_m);

        Debug.Log(string.Format("2 : nextUpdateTime: {0}", nextUpdateTime.ToLongTimeString()));
    }

    public void ReGetDonwData()
    {
        if(DateTime.Now > nextUpdateTime)
        {
            Debug.Log("ReGetDownData !!!");
            WebDataInit();
        }
        
    }


}
