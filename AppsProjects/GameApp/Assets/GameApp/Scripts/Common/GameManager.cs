using UnityEngine;
using System.Collections;

public sealed class GameManager : ScriptableObject {
 
    public int data1;
    public int data2;
    public string data3;
 
    GameManager()
    {
    }
 
    void Start () {
       
    }
 
    public void Init()
    {
       
    }
   
    // Update is called once per frame
    void Update () {
       
    }
 
    void Awake()
    {
        DontDestroyOnLoad(this);
    }
 
    public static GameManager Instance()
    {
        if (Nested.instance == null)
        {
            Debug.LogError("GameManager instance is null! Creating a new one...");
            Nested.CreateNewInstance();
        }
        return Nested.instance;
    }

    class Nested
    {
        static Nested()
        {
        }
        internal static GameManager instance = null;

        static Nested()
        {
            CreateNewInstance();
        }

        internal static void CreateNewInstance()
        {
            try
            {
                instance = (GameManager)CreateInstance("GameManager");
                if (instance == null)
                {
                    Debug.LogError("Failed to create GameManager with CreateInstance, trying alternative method...");
                    instance = CreateInstance<GameManager>();
                }

                if (instance != null)
                {
                    DontDestroyOnLoad(instance);
                    Debug.Log("GameManager instance created successfully");
                }
                else
                {
                    Debug.LogError("Failed to create GameManager instance!");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError("Exception creating GameManager: " + ex.Message);
                // 创建一个基本的实例作为备用
                instance = CreateInstance<GameManager>();
                if (instance != null)
                {
                    DontDestroyOnLoad(instance);
                }
            }
        }
    }

	public void SaveSoundOnOff()
	{
		PlayerPrefs.SetInt("canSound", GlobalVariable.canSound);
	}
	
	public int GetSoundOnOff()
	{
		return PlayerPrefs.GetInt("canSound", GlobalVariable.canSound);
	}
	
	public void SaveSpeakerOnOff()
	{
		PlayerPrefs.SetInt("canSpeaker", GlobalVariable.canSpeaker);
	}
	
	public int GetSpeakerOnOff()
	{
		return PlayerPrefs.GetInt("canSpeaker", GlobalVariable.canSpeaker);
	}

	public void SaveUserHasRate(int flag)
	{
		PlayerPrefs.SetInt("UserHasRate", flag);
	}
	
	public int GetUserHasRate()
	{
		return PlayerPrefs.GetInt("UserHasRate");
	}
	
	public void SaveRateMeCount()
	{
		int count = PlayerPrefs.GetInt("RateMeCount") + 1;
		PlayerPrefs.SetInt("RateMeCount", count);
	}
	
	public int GetRateMeCount()
	{
		return PlayerPrefs.GetInt("RateMeCount");
	}
	
	public void SaveOsLanguage()
	{
		PlayerPrefs.SetString("OSLanguage", GlobalVariable.osLanguage.ToString());
	}
	
	public string GetUserSaveOsLanguage()
	{
		return PlayerPrefs.GetString("OSLanguage", GlobalVariable.osLanguage.ToString());
	}
	
	public void ClearGift(string type)
	{
		string user = "MyGifts" + type;
		PlayerPrefs.SetString(user, "");

	}
	
	public void SaveGift(string giftName, string type)
	{
		string user = "MyGifts"+ type;
		string gifts = PlayerPrefs.GetString(user);
		
		if(gifts != "")
		{
			string[] giftArray = gifts.Split('|');
			ArrayList aList = new ArrayList();
			foreach(string item in giftArray)
			{
				if(item != "") aList.Add(item);
			}
		
			if(!aList.Contains(giftName))
			{
				gifts += giftName + "|";
				PlayerPrefs.SetString(user, gifts);			
			}
			
		}
		else
		{
			gifts += giftName + "|";
			PlayerPrefs.SetString(user, gifts);
		}
	}
	
	public string GetGifts(string type)
	{
		return PlayerPrefs.GetString("MyGifts"+ type);
	}
	
	public float GetBestScoreForTimeLimit150s()
	{
		return PlayerPrefs.GetFloat("BestScoreimeLimit150s");
	}
	
	public void SaveBestScoreForTimeLimit150s(float newScore)
	{
		float bestScoreLevel = PlayerPrefs.GetFloat("BestScoreimeLimit150s");
		
		if(bestScoreLevel == 0)
		{
			PlayerPrefs.SetFloat("BestScoreimeLimit150s", newScore);
		}
		
		if(newScore > bestScoreLevel)
		{
			PlayerPrefs.SetFloat("BestScoreimeLimit150s", newScore);
		}
	}
	
	public float GetBestScoreLevel(int level)
	{
		return PlayerPrefs.GetFloat("BestScoreLevel"+ level.ToString());
	}
	
	public void SaveBestScoreLevel(int level, float newScore)
	{
		float bestScoreLevel = PlayerPrefs.GetFloat("BestScoreLevel"+ level.ToString());
		
		if(bestScoreLevel == 0)
		{
			PlayerPrefs.SetFloat("BestScoreLevel"+ level.ToString(), newScore);
		}
		
		if(newScore < bestScoreLevel)
		{
			PlayerPrefs.SetFloat("BestScoreLevel"+ level.ToString(), newScore);
		}
	}
	
	public int GetBestStarLevel(int level)
	{
		return PlayerPrefs.GetInt("BestStarLevel"+ level.ToString());
	}
	
	public void SaveBestStarLevel(int level, int starCount)
	{
		int bestStarLevel = PlayerPrefs.GetInt("BestStarLevel"+ level.ToString());
		
		if(starCount > bestStarLevel)
		{
			PlayerPrefs.SetInt("BestStarLevel"+ level.ToString(), starCount);
		}
	}

	public void SaveLevelOnOff(string tag, int flag)
	{
		PlayerPrefs.SetInt("level"+tag, flag);
	}
	
	public int GetLevelOnOff(string tag)
	{
		return PlayerPrefs.GetInt("level"+tag);
	}
	
	public int GetWeatherId()
	{
		return PlayerPrefs.GetInt("WeatherId", 0);
	}
	
	public void SaveWeatherId()
	{
		int weatherId = PlayerPrefs.GetInt("WeatherId") + 1;
		if(weatherId > 2) weatherId = 0;
		PlayerPrefs.SetInt("WeatherId", weatherId);
	}
	
	public string AirPlaneOpenFlag()
	{
		return PlayerPrefs.GetString("AirPlaneOpen", "");
	}
	
	public void SaveAirPlaneOpenFlag()
	{
		PlayerPrefs.SetString("AirPlaneOpen", "xsfdfjosj9993er3ffsf");
	}
	
	#region ParentsZone
	
	public bool GetParentsZoneNewMsgHasRead()
	{
		string newId = "";
		#if UNITY_IPHONE
		newId = IAppsTeamIOSUntil.GetConfigParams("ParentsZoneNewMsgId");
		#endif
		
		if(newId == "" || newId == null) newId = "1";
		Debug.Log("newId:"+newId.ToString());
		int readFlag = PlayerPrefs.GetInt("ParentsZoneNewMsgFlag"+newId, 0);
		
		Debug.Log("readFlag:"+readFlag.ToString());
		if(readFlag == 1)
		{
			return true;
		}
		else
		{
			PlayerPrefs.SetInt("ParentsZoneNewMsgFlag"+newId, 0);
			return false;
		}
	}
	
	public void SaveParentsZoneNewMsgFlag()
	{
		string newId = "";
		#if UNITY_IPHONE
		newId = IAppsTeamIOSUntil.GetConfigParams("ParentsZoneNewMsgId");
		#endif
		
		if(newId == "" || newId == null) newId = "1";
		
		PlayerPrefs.SetInt("ParentsZoneNewMsgFlag"+newId, 1);
	}
	
	
	#endregion

	public void SaveHasGoneToMapFlag()
	{
		PlayerPrefs.SetInt("HasGoneToMapFlag1", 1);
	}
	
	public bool GetHasGoneToMapFlag()
	{
		
		int readFlag = PlayerPrefs.GetInt("HasGoneToMapFlag1", 0);
		
		if(readFlag == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public void SaveHasGoneToD5Flag()
	{
		PlayerPrefs.SetInt("HasGoneToD5", 1);
	}
	
	public bool GetHasGoneToD5Flag()
	{
		
		int readFlag = PlayerPrefs.GetInt("HasGoneToD5", 0);
		
		if(readFlag == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public void SaveHasGoneToNewGameFlag(string gameName)
	{
		PlayerPrefs.SetInt("HasGoneTo"+gameName, 1);
	}
	
	public bool GetHasGoneToNewGameFlag(string gameName)
	{
		
		int readFlag = PlayerPrefs.GetInt("HasGoneTo"+gameName, 0);
		
		if(readFlag == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

    public void SaveDownloadZipSuccess(string flag)
    {
        PlayerPrefs.SetInt("SaveZipSuccess_" + flag, 1);
    }

    public int GetDownloadZipSuccess(string flag)
    {
        return PlayerPrefs.GetInt("SaveZipSuccess_" + flag, 0);
    }

    public void ResetDownloadZipSuccess(string flag)
    {
        PlayerPrefs.SetInt("SaveZipSuccess_" + flag, 0);
    }
}
