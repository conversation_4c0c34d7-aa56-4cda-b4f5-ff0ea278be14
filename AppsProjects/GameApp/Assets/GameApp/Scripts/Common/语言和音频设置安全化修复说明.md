# 语言和音频设置安全化修复说明

## 修改概述

根据您的要求，我参考了音频设置的安全初始化模式，将语言设置的初始化也修改为相同的安全模式。这样可以确保所有的系统设置都有一致的错误处理和安全保障。

## 修改内容

### 1. SplashScreen_Control.Awake() 中的初始化逻辑

**修改前的问题**:
- 语言设置初始化没有检查 GameManager 是否可用
- 缺少与音频设置相同的安全保障机制

**修改后的统一模式**:
```csharp
// 初始化语言设置
try
{
    GameManager gameManager = GameManager.Instance();
    if (gameManager != null)
    {
        GlobalVariable.CheckUserOSLanguage();
        GlobalVariable.GetUserSavedLanguage();
        Debug.Log("Language settings initialized");
    }
    else
    {
        Debug.LogWarning("GameManager is null, using default language settings");
        // 使用默认语言设置
        GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
    }
}
catch (System.Exception ex)
{
    Debug.LogError("Language settings initialization failed: " + ex.Message);
    // 使用默认语言设置
    GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
}

// 初始化音频设置
try
{
    GameManager gameManager = GameManager.Instance();
    if (gameManager != null)
    {
        GlobalVariable.canSound = gameManager.GetSoundOnOff();
        GlobalVariable.canSpeaker = gameManager.GetSpeakerOnOff();
        Debug.Log("Audio settings initialized");
    }
    else
    {
        Debug.LogWarning("GameManager is null, using default audio settings");
        GlobalVariable.canSound = 1;
        GlobalVariable.canSpeaker = 1;
    }
}
catch (System.Exception ex)
{
    Debug.LogError("Audio settings initialization failed: " + ex.Message);
    GlobalVariable.canSound = 1;
    GlobalVariable.canSpeaker = 1;
}
```

### 2. GlobalVariable.CheckUserOSLanguage() 方法增强

**修改前的问题**:
- 没有异常处理
- 没有检查返回值是否有效
- 缺少详细的日志记录

**修改后的安全版本**:
```csharp
public static void CheckUserOSLanguage()
{
    try
    {
        //采用系统的语言
        string osl = IAppsTeamIOSUntil.CheckUserOSLanguage();
        
        if (!string.IsNullOrEmpty(osl))
        {
            if (osl == "sc")
            {
                GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
            }
            else if (osl == "tc")
            {
                GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
            }
            else
            {
                GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
            }
            Debug.Log("OS language detected: " + osl + ", set to: " + GlobalVariable.osLanguage.ToString());
        }
        else
        {
            Debug.LogWarning("OS language detection returned null or empty, using default");
            GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
        }
    }
    catch (System.Exception ex)
    {
        Debug.LogError("CheckUserOSLanguage Exception: " + ex.Message);
        // 使用默认语言
        GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
    }
}
```

### 3. GlobalVariable.GetUserSavedLanguage() 方法优化

**修改后的统一模式**:
```csharp
public static void GetUserSavedLanguage()
{
    try
    {
        GameManager gameManager = GameManager.Instance();
        if (gameManager != null)
        {
            string str = gameManager.GetUserSaveOsLanguage();
            if (str == "SimplifiedChinese")
            {
                GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
            }
            else
            {
                GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
            }
            Debug.Log("User saved language loaded: " + str);
        }
        else
        {
            Debug.LogWarning("GameManager instance is null, using default language");
            GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
        }
    }
    catch (System.Exception ex)
    {
        Debug.LogError("GetUserSavedLanguage Exception: " + ex.Message);
        // 使用默认语言
        GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
    }
}
```

### 4. GlobalVariable.SwitchLanguage() 方法安全化

**修改后的安全版本**:
```csharp
public static void SwitchLanguage()
{
    try
    {
        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
            IAppsTeamIOSUntil.MobClickEvent("ShowTC");
        }
        else
        {
            GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
            IAppsTeamIOSUntil.MobClickEvent("ShowSC");
        }

        GameManager gameManager = GameManager.Instance();
        if (gameManager != null)
        {
            gameManager.SaveOsLanguage();
            Debug.Log("Language switched to: " + GlobalVariable.osLanguage.ToString());
        }
        else
        {
            Debug.LogWarning("GameManager is null, cannot save language setting");
        }
    }
    catch (System.Exception ex)
    {
        Debug.LogError("SwitchLanguage Exception: " + ex.Message);
    }
}
```

## 统一的安全模式特点

### 1. **一致的结构**
- 所有设置初始化都遵循相同的模式
- 先检查 GameManager 是否可用
- 提供默认值作为备用方案
- 添加详细的日志记录

### 2. **错误处理策略**
- 使用 try-catch 包装所有关键操作
- 区分 GameManager 为 null 和其他异常的情况
- 提供有意义的错误信息和警告

### 3. **默认值策略**
- 语言设置默认使用 `TraditionalChinese`
- 音频设置默认启用 (canSound = 1, canSpeaker = 1)
- 确保即使在异常情况下程序也能正常运行

### 4. **日志记录**
- 成功初始化时记录确认信息
- 使用默认值时记录警告信息
- 异常情况时记录详细错误信息

## 修改效果

### ✅ **稳定性提升**
- 所有设置初始化都有相同的安全保障
- 即使 GameManager 创建失败也能正常运行
- 提供了完整的错误恢复机制

### ✅ **一致性改善**
- 语言设置和音频设置使用相同的初始化模式
- 统一的错误处理和日志记录风格
- 相同的默认值提供策略

### ✅ **调试友好**
- 详细的日志信息帮助问题定位
- 清晰区分不同类型的错误情况
- 便于跟踪设置的初始化过程

## 测试建议

1. **正常情况测试**: 验证语言和音频设置都能正常初始化
2. **GameManager 异常测试**: 模拟 GameManager 创建失败的情况
3. **系统语言检测测试**: 在不同语言环境下测试
4. **设置切换测试**: 验证语言切换功能的稳定性

## 总结

通过这次修改，我们实现了：
- 语言设置和音频设置的统一安全初始化模式
- 完整的错误处理和恢复机制
- 一致的代码风格和日志记录
- 更好的稳定性和调试体验

现在所有的系统设置都遵循相同的安全模式，确保了代码的一致性和可靠性。
