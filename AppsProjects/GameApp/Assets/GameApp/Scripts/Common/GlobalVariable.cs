using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using LitJson;
using System.IO;

public class GlobalVariable : MonoBehaviour
{

	public static int right_pos;//当前正确答案的位置，方案判断,从左到右，0为第一个
								//设定设备类型
	public static bool is_IAP = true, isPhone = true;
	public static bool buttonIsLock = false, mapOfButtonHasClick = false;
	public static int canSound = 1;
	public static int canSpeaker = 1;
	public static int level = 101;//101
	public static int FH_Boy_Id = 2, hillType = 0;
	public static string currentConfigDataName = "Transports/A-1";
	public static string tk2dSpriteCollectionName = "GameA1SpriteCollection";
	public static string tk2dFontDataName = "GameA1Fontdata";
	public static string goToNextSceneName = "", goToPrevSceneName = "";
	public static string soundPath = "";

	public static string ourAdsInterstitialUrl = "", ourAdsInterstitialAppId = "";
	public static bool isOurAdsGetLocal = true, needParentsGate = true;

	public static string AppleAppId = "6480421556";
	public const string IAP_ID1 = "s3dsdi3hso2h88", IAP_ProductId1 = "FD1_Pro";

	public static string AdsConfig_Ver = "AdsConfig_Ver", AdsConfig_Content = "AdsConfig_Content", AdsConfig_Name = "ads_v4",
						AdsConfig_Refresh = "AdsConfig_Refresh", AdsConfigFilePath = "";
	public static int showNoramlAdsCount = -1, showInterstitialAdsCount = -1, showNativeAdsCount = -1;
	public static string AdsInter_Id = "Ads_Inter";

	public static bool hasClickCount_Admob_InterAds = false;

	public static bool hasClickCount_GDT_InterAds = false;

	public static string AdsBanner_Id = "Ads_Banner";
	public static bool hasClickCount_Admob_BannerAds = false;

	public static string BaiduAdsInter_Id = "BaiduAds_Inter";
	public static bool hasClickCount_Baidu_InterAds = false;
	public static string BaiduAdsBanner_Id = "BaiduAds_Banner";
	public static bool hasClickCount_Baidu_BannerAds = false;

	public static string DefaultPrice = "$0.99", SavePriceFlag = "AllInOnePrice";
	public static int adsControl_count = 0;

	public static string ts_map_fail_back_scene = "PE_Map", ts_map_zipFolder;
	public static bool ts_map_is_DefaultLocalData = false;

	public static string ParentZone_dataConfig_flag = "Data_ParentZoneV2_Config";

	public enum MoreApps_FromPage
	{
		Home,
		ParentZone
	}
	public static MoreApps_FromPage moreApps_FromPage = MoreApps_FromPage.Home;

	public enum PaidFromPage
	{
		Map,
	}
	public static PaidFromPage paidFromPage = PaidFromPage.Map;

	public enum NormalAdsMobType
	{
		Admob,
		BaiduMob,
		GdtMob,
	}
	public static NormalAdsMobType normalAdsMobType = NormalAdsMobType.Admob;

	public enum InterstitialAdsType
	{
		Admob,
		BaiduMob,
		GdtMob,
		EmptyMob
	}

	public static InterstitialAdsType interstitialAdsType = InterstitialAdsType.Admob;

	public enum NativeAdsType
	{
		Admob,
		GdtMob,
	}

	public static NativeAdsType nativeAdsType = NativeAdsType.Admob;

	public enum OSLanguage
	{
		SimplifiedChinese,
		TraditionalChinese,
		English,
	}
	//public static OSLanguage osLanguage = OSLanguage.English;
	public static OSLanguage osLanguage = OSLanguage.SimplifiedChinese;


	public enum GameState
	{
		BEGIN,
		PLAYING,
		PAUSE,
		END,
	}
	public static GameState gameState = GameState.PLAYING;

	public enum ProjectName
	{
		Transportion,
		Helicopter,
		Construction,
		BusDriver,
		New_Transportion,
		Police
	}
	public static ProjectName projectName = ProjectName.Police;


	public static string GetOSLanguageSuffix()
	{
		string strResult = "-en";
		if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
		{
			strResult = "-sc";
		}
		else if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
		{
			strResult = "-tc";
		}
		else
		{
			strResult = "-en";
		}

		return strResult;
	}

	public static string GetLanguageCode()
	{
		// 语言代码：zh_CN(简体中文)、zh_TW(繁体中文)、en(英文)
		string strResult = "en";
		if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
		{
			strResult = "zh_CN";
		}
		else if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
		{
			strResult = "zh_TW";
		}
		else
		{
			strResult = "en";
		}

		strResult = "zh_CN";

		return strResult;
	}

	public static void SettingSoundOnOffToSaveDisk()
	{
		GlobalVariable.canSound = GameManager.Instance().GetSoundOnOff();
		if (GlobalVariable.canSound == 1)
		{
			GlobalVariable.canSound = 0;
		}
		else
		{
			GlobalVariable.canSound = 1;
		}

		GameManager.Instance().SaveSoundOnOff();
	}

	public static bool CanSoundAction()
	{
		if (GlobalVariable.canSound == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public static bool CanSpeakerAction()
	{
		if (GlobalVariable.canSpeaker == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public static void CheckUserOSLanguage()
	{
		//采用系统的语言
		string osl = IAppsTeamIOSUntil.CheckUserOSLanguage();
		//Debug.Log("ios return osl:" + osl);
		if (osl == "sc")
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
		}
		else if (osl == "tc")
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
		}
		else
		{
			//GlobalVariable.osLanguage = GlobalVariable.OSLanguage.English;
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
		}

		//GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
		//GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
		//GlobalVariable.osLanguage = GlobalVariable.OSLanguage.English;
		//Debug.Log("GlobalVariable.osLanguage:" + GlobalVariable.osLanguage.ToString());
	}

	public static string GetDeviceOSLanguage()
	{
		string str = "";
		//采用系统的语言
#if UNITY_IPHONE
		string osl = IAppsTeamIOSUntil.CheckUserOSLanguage();
		//Debug.Log("ios return osl:" + osl);
		if (osl == "sc")
		{
			str = "sc";
		}
		else if (osl == "tc")
		{
			str = "tc";
		}
		else
		{
			str = "en";
		}
#endif
		return str;
	}

	public static void SwitchLanguage()
	{
		if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
			IAppsTeamIOSUntil.MobClickEvent("ShowTC");
		}
		else
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
			IAppsTeamIOSUntil.MobClickEvent("ShowSC");
		}

		GameManager.Instance().SaveOsLanguage();
	}

	public static void GetUserSavedLanguage()
	{
		string str = GameManager.Instance().GetUserSaveOsLanguage();
		//Debug.Log("GetUserSaveLanguage:"+str);
		if (str == "SimplifiedChinese")
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
		}
		else
		{

			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
		}
	}

	public static string LevelAssetPath()
	{
		string path = "Transports/tk2dSpriteCollectionData/" + GlobalVariable.tk2dSpriteCollectionName;

		return path;
	}

	public static string LevelFontAssetPath()
	{
		string path = "Transports/tk2dFontData/" + GlobalVariable.tk2dFontDataName;

		return path;
	}

	public static void LevelInitSettingData()
	{
		//level 100-199 is fire truck
		if (GlobalVariable.level == 101)
		{
			GlobalVariable.currentConfigDataName = "Transports/A-1";
			GlobalVariable.tk2dSpriteCollectionName = "GameA1SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameA1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-A2";
			GlobalVariable.goToPrevSceneName = "GameTransport-A-Page";
			GlobalVariable.soundPath = "Sounds/FireTruck/";
		}
		else if (GlobalVariable.level == 102)
		{
			GlobalVariable.currentConfigDataName = "Transports/A-2";
			GlobalVariable.tk2dSpriteCollectionName = "GameA2SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameA1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-A3";
			GlobalVariable.goToPrevSceneName = "GameTransport-A1";
			GlobalVariable.soundPath = "Sounds/FireTruck/";

		}
		else if (GlobalVariable.level == 103)
		{
			GlobalVariable.currentConfigDataName = "Transports/A-3";
			GlobalVariable.tk2dSpriteCollectionName = "GameA3SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameA1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-A4";
			GlobalVariable.goToPrevSceneName = "GameTransport-A2";
			GlobalVariable.soundPath = "Sounds/FireTruck/";

		}
		else if (GlobalVariable.level == 104)
		{
			GlobalVariable.currentConfigDataName = "Transports/A-4";
			GlobalVariable.tk2dSpriteCollectionName = "GameA4SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameA1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-A-Page";
			GlobalVariable.goToPrevSceneName = "GameTransport-A3";
			GlobalVariable.soundPath = "Sounds/FireTruck/";

		}
		else if (GlobalVariable.level == 201)
		{
			GlobalVariable.currentConfigDataName = "Transports/B-1";
			GlobalVariable.tk2dSpriteCollectionName = "GameB1SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameB1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-B2";
			GlobalVariable.goToPrevSceneName = "Home";
			GlobalVariable.soundPath = "Sounds/PoliceCar/";

		}
		else if (GlobalVariable.level == 202)
		{
			GlobalVariable.currentConfigDataName = "Transports/B-2";
			GlobalVariable.tk2dSpriteCollectionName = "GameB2SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameB1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-B3";
			GlobalVariable.goToPrevSceneName = "GameTransport-B1";
			GlobalVariable.soundPath = "Sounds/PoliceCar/";

		}
		else if (GlobalVariable.level == 203)
		{
			GlobalVariable.currentConfigDataName = "Transports/B-3";
			GlobalVariable.tk2dSpriteCollectionName = "GameB3SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameB1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-B4";
			GlobalVariable.goToPrevSceneName = "GameTransport-B2";
			GlobalVariable.soundPath = "Sounds/PoliceCar/";

		}
		else if (GlobalVariable.level == 204)
		{
			GlobalVariable.currentConfigDataName = "Transports/B-4";
			GlobalVariable.tk2dSpriteCollectionName = "GameB4SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameA1Fontdata";
			GlobalVariable.goToNextSceneName = "Home";
			GlobalVariable.goToPrevSceneName = "GameTransport-B3";
			GlobalVariable.soundPath = "Sounds/PoliceCar/";
		}
		else if (GlobalVariable.level == 301)
		{
			GlobalVariable.currentConfigDataName = "Transports/C-1";
			GlobalVariable.tk2dSpriteCollectionName = "GameC1SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameC1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-C2";
			GlobalVariable.goToPrevSceneName = "Home";
			GlobalVariable.soundPath = "Sounds/RaceCar/";

		}
		else if (GlobalVariable.level == 302)
		{
			GlobalVariable.currentConfigDataName = "Transports/C-2";
			GlobalVariable.tk2dSpriteCollectionName = "GameC2SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameC1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-C3";
			GlobalVariable.goToPrevSceneName = "GameTransport-C1";
			GlobalVariable.soundPath = "Sounds/RaceCar/";

		}
		else if (GlobalVariable.level == 303)
		{
			GlobalVariable.currentConfigDataName = "Transports/C-3";
			GlobalVariable.tk2dSpriteCollectionName = "GameC3SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameC1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-C4";
			GlobalVariable.goToPrevSceneName = "GameTransport-C2";
			GlobalVariable.soundPath = "Sounds/RaceCar/";
		}
		else if (GlobalVariable.level == 304)
		{
			GlobalVariable.currentConfigDataName = "Transports/C-4";
			GlobalVariable.tk2dSpriteCollectionName = "GameC4SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameC1Fontdata";
			GlobalVariable.goToNextSceneName = "Home";
			GlobalVariable.goToPrevSceneName = "GameTransport-C3";
			GlobalVariable.soundPath = "Sounds/RaceCar/";
		}
		else if (GlobalVariable.level == 401)
		{
			GlobalVariable.currentConfigDataName = "Transports/D-1";
			GlobalVariable.tk2dSpriteCollectionName = "GameD1SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameD1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-D2";
			GlobalVariable.goToPrevSceneName = "GameTransport-D-Page";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 402)
		{
			GlobalVariable.currentConfigDataName = "Transports/D-2";
			GlobalVariable.tk2dSpriteCollectionName = "GameD2SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameD1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-D3";
			GlobalVariable.goToPrevSceneName = "GameTransport-D1";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 403)
		{
			GlobalVariable.currentConfigDataName = "Transports/D-3";
			GlobalVariable.tk2dSpriteCollectionName = "GameD3SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameD1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-D4";
			GlobalVariable.goToPrevSceneName = "GameTransport-D2";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 404)
		{
			GlobalVariable.currentConfigDataName = "Transports/D-4";
			GlobalVariable.tk2dSpriteCollectionName = "GameD4SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameD1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-D-Page";
			GlobalVariable.goToPrevSceneName = "GameTransport-D3";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 501)
		{
			GlobalVariable.currentConfigDataName = "Transports/E-1";
			GlobalVariable.tk2dSpriteCollectionName = "GameE1SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameE1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-E2";
			GlobalVariable.goToPrevSceneName = "Home";
			GlobalVariable.soundPath = "Sounds/AirPlane/";
		}
		else if (GlobalVariable.level == 502)
		{
			GlobalVariable.currentConfigDataName = "Transports/E-2";
			GlobalVariable.tk2dSpriteCollectionName = "GameE2SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameE1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-E3";
			GlobalVariable.goToPrevSceneName = "GameTransport-E1";
			GlobalVariable.soundPath = "Sounds/AirPlane/";
		}
		else if (GlobalVariable.level == 503)
		{
			GlobalVariable.currentConfigDataName = "Transports/E-3";
			GlobalVariable.tk2dSpriteCollectionName = "GameE3SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameE1Fontdata";
			GlobalVariable.goToNextSceneName = "GameTransport-E4";
			GlobalVariable.goToPrevSceneName = "GameTransport-E2";
			GlobalVariable.soundPath = "Sounds/AirPlane/";
		}
		else if (GlobalVariable.level == 504)
		{
			GlobalVariable.currentConfigDataName = "Transports/E-4";
			GlobalVariable.tk2dSpriteCollectionName = "GameE4SpriteCollection";
			GlobalVariable.tk2dFontDataName = "GameE1Fontdata";
			GlobalVariable.goToNextSceneName = "Home";
			GlobalVariable.goToPrevSceneName = "GameTransport-E3";
			GlobalVariable.soundPath = "Sounds/AirPlane/";
		}
		else if (GlobalVariable.level == 601)//d5
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "GameTransport-D6";
			GlobalVariable.goToPrevSceneName = "GameTransport-D-Page";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 602)//d6
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "GameTransport-D7";
			GlobalVariable.goToPrevSceneName = "GameTransport-D5";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 603)//d7
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "GameTransport-D8";
			GlobalVariable.goToPrevSceneName = "GameTransport-D6";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 604)//d8
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "GameTransport-D-Page";
			GlobalVariable.goToPrevSceneName = "GameTransport-D7";
			GlobalVariable.soundPath = "Sounds/Construction/";
		}
		else if (GlobalVariable.level == 701)//fh_a1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_A2";
			GlobalVariable.goToPrevSceneName = "GameTransport-A-Page";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 702)//fh_a2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_B1";
			GlobalVariable.goToPrevSceneName = "FH_A1";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 703)//fh_b1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_B2";
			GlobalVariable.goToPrevSceneName = "FH_A2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 704)//fh_b2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_B3";
			GlobalVariable.goToPrevSceneName = "FH_B1";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 705)//fh_b3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_B4";
			GlobalVariable.goToPrevSceneName = "FH_B2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 706)//fh_b4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_C1";
			GlobalVariable.goToPrevSceneName = "FH_B3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 707)//fh_c1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_C2";
			GlobalVariable.goToPrevSceneName = "FH_B4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 708)//fh_c2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "FH_C3";
			GlobalVariable.goToPrevSceneName = "FH_C1";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 709)//fh_c3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToNextSceneName = "GameTransport-A-Page";
			GlobalVariable.goToPrevSceneName = "FH_C2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 801)//CT_A1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_Map";
			GlobalVariable.goToNextSceneName = "CT_A2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 802)//CT_A2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_A1";
			GlobalVariable.goToNextSceneName = "CT_A3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 803)//CT_A3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_A2";
			GlobalVariable.goToNextSceneName = "CT_A4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 804)//CT_A4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_A3";
			GlobalVariable.goToNextSceneName = "CT_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 805)//CT_B1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_Map";
			GlobalVariable.goToNextSceneName = "CT_B2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 806)//CT_B2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_B1";
			GlobalVariable.goToNextSceneName = "CT_B3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 807)//CT_B3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_B2";
			GlobalVariable.goToNextSceneName = "CT_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 808)//CT_C1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_Map";
			GlobalVariable.goToNextSceneName = "CT_C2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 809)//CT_C2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_C1";
			GlobalVariable.goToNextSceneName = "CT_C3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 810)//CT_C3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_C2";
			GlobalVariable.goToNextSceneName = "CT_C4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 811)//CT_C4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT_C3";
			GlobalVariable.goToNextSceneName = "CT_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 901)//BD_A1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_Map";
			GlobalVariable.goToNextSceneName = "BD_A2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 902)//BD_A2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_A1";
			GlobalVariable.goToNextSceneName = "BD_A3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 903)//BD_A3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_A2";
			GlobalVariable.goToNextSceneName = "BD_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 904)//BD_B1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_Map";
			GlobalVariable.goToNextSceneName = "BD_B2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 905)//BD_B2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_B1";
			GlobalVariable.goToNextSceneName = "BD_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 906)//BD_C1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_Map";
			GlobalVariable.goToNextSceneName = "BD_C2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 907)//BD_C2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_C1";
			GlobalVariable.goToNextSceneName = "BD_C3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 908)//BD_C3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_C2";
			GlobalVariable.goToNextSceneName = "BD_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 909)//BD_D1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_Map";
			GlobalVariable.goToNextSceneName = "BD_D2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 910)//BD_D2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "BD_D1";
			GlobalVariable.goToNextSceneName = "BD_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1001)//FH_D1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "Home";
			GlobalVariable.goToNextSceneName = "FH_D2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1002)//FH_D2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "FH_D1";
			GlobalVariable.goToNextSceneName = "FH_D3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1003)//FH_D3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "FH_D2";
			GlobalVariable.goToNextSceneName = "FH_D4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1004)//FH_D4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "FH_D3";
			GlobalVariable.goToNextSceneName = "Home";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1101)//CT1_D1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "Home";
			GlobalVariable.goToNextSceneName = "CT1_D2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1102)//CT1_D2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT1_D1";
			GlobalVariable.goToNextSceneName = "CT1_D3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1103)//CT1_D3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT1_D2";
			GlobalVariable.goToNextSceneName = "CT1_D4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1104)//CT1_D4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "CT1_D3";
			GlobalVariable.goToNextSceneName = "Home";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1201)//ICT_A1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "Home";
			GlobalVariable.goToNextSceneName = "ICT_A2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1202)//ICT_A2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "ICT_A1";
			GlobalVariable.goToNextSceneName = "ICT_A3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1203)//ICT_A3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "ICT_A2";
			GlobalVariable.goToNextSceneName = "ICT_A4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1204)//ICT_A4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "ICT_A3";
			GlobalVariable.goToNextSceneName = "ICT_A5";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1205)//ICT_A5
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "ICT_A4";
			GlobalVariable.goToNextSceneName = "Home";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1301)//PE_A1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_Map";
			GlobalVariable.goToNextSceneName = "PE_A2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1302)//PE_A2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_A1";
			GlobalVariable.goToNextSceneName = "PE_A3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1303)//PE_A3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_A2";
			GlobalVariable.goToNextSceneName = "PE_A4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1304)//PE_A4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_A3";
			GlobalVariable.goToNextSceneName = "PE_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1305)//PE_B1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_Map";
			GlobalVariable.goToNextSceneName = "PE_B2-A";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1306)//PE_B2-A
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_B1";
			GlobalVariable.goToNextSceneName = "PE_B2-B";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1307)//PE_B2-B
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_B2-A";
			GlobalVariable.goToNextSceneName = "PE_B3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1308)//PE_B3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_B2-B";
			GlobalVariable.goToNextSceneName = "PE_B4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1309)//PE_B4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_B3";
			GlobalVariable.goToNextSceneName = "PE_Map";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1310)//PE_C1
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_Map";
			GlobalVariable.goToNextSceneName = "PE_C2";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1311)//PE_C2
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_C1";
			GlobalVariable.goToNextSceneName = "PE_C3";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1312)//PE_C3
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_C2";
			GlobalVariable.goToNextSceneName = "PE_C4";
			GlobalVariable.soundPath = "";
		}
		else if (GlobalVariable.level == 1313)//PE_C4
		{
			GlobalVariable.currentConfigDataName = "";
			GlobalVariable.tk2dSpriteCollectionName = "";
			GlobalVariable.tk2dFontDataName = "";
			GlobalVariable.goToPrevSceneName = "PE_C3";
			GlobalVariable.goToNextSceneName = "PE_Home";
			GlobalVariable.soundPath = "";
		}

	}




	public static string GetGoodSoundPath()
	{
		int good_id = Random.Range(1, 10);
		string goodSoundPath = "Sounds/Common/good" + good_id.ToString();
		return goodSoundPath;
	}

	public static string GetGoodClothingSoundPath()
	{
		int good_id = Random.Range(1, 6);
		string goodSoundPath = "Sounds/Common/clothing" + good_id.ToString();
		return goodSoundPath;
	}

	public static string GetParentsZoneWebLink()
	{
		string strType = "";
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("ParentsZoneWebPage");
#endif

		if (strType == "" || strType == null)
		{
			strType = "http://aps1.appfun8.com/";
		}

		return strType;
	}

	public static bool GetOpenWeChatFlag()
	{
		string strType = "";
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("OpenWechat");
#endif

		if (strType == "1")
		{
			return true;
		}
		else
		{
			return false;
		}

	}

	public static bool CanOpenShareContent()
	{
		string strType = "";
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("p_share");
#endif

		if (strType == "1")
		{
			return true;
		}
		else
		{
			return false;
		}

	}

	#region web action
	public static string GetWebAddr()
	{
		string strType = string.Empty;
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("web_addr");
#endif

		if (strType == "" || strType == null)
		{
			strType = "http://caps2.appfun8.com/";
		}

		return strType;
	}

	public static string GetWebAddr_Ads()
	{
		string strType = string.Empty;
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("web_addr_ads");
#endif

		if (strType == "" || strType == null)
		{
			strType = "http://cona.appfun8.com/";
		}

		return strType;
	}

	#endregion

	public static string GetDevNameUrl()
	{
		string url_link = "https://apps.apple.com/developer/%E6%99%BA%E5%AA%9B-%E5%86%AF/id420919803?uo=4";
		try
		{
			string default_configData = Resources.Load(GlobalVariable.AdsConfig_Name).ToString();
			string config_data = PlayerPrefs.GetString(GlobalVariable.AdsConfig_Content, default_configData);

			JsonData data = JsonMapper.ToObject(config_data);

			string str_value = data["dev"]["n"].ToString();

			if (str_value.Equals("1"))
			{
				url_link = "http://appstore.com/iappsteam";
			}
		}
		catch
		{
			Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", "dev", "n"));

		}

		return url_link;
	}

	public static bool GetICP_NO()
	{
		bool v = true;

		if (GetDeviceOSLanguage() == "sc")
		{
			try
			{
				string default_configData = Resources.Load(GlobalVariable.AdsConfig_Name).ToString();
				string config_data = PlayerPrefs.GetString(GlobalVariable.AdsConfig_Content, default_configData);

				JsonData data = JsonMapper.ToObject(config_data);

				string str_value = data["dev"]["ic"].ToString();

				if (str_value.Equals("0"))
				{
					v = false;
				}
				else
				{
					v = true;
				}

			}
			catch
			{
				Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", "ic", "n"));

			}
		}
		else
		{
			v = false;
		}

		return v;
	}
	
	public static string LoadAppConfig()
	{
		string path = PlayerPrefs.GetString(GlobalVariable.AdsConfigFilePath, string.Empty);
		if (!string.IsNullOrEmpty(path) && File.Exists(path))
		{
			return File.ReadAllText(path);
		}
		else
		{
			string default_configData = Resources.Load(GlobalVariable.AdsConfig_Name).ToString();
			Debug.LogWarning("Config file not found or path is empty. Use Default Config");
			return default_configData;
		}
	}
}
