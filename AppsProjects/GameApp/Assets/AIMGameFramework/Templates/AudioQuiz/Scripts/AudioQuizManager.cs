using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.IO;
using System.Collections;
using System;
using Newtonsoft.Json.Linq;
using AIMGameFramework;
using AIMGameFramework.Layout;

namespace AIMGameFramework.Templates.AudioQuiz
{
    [System.Serializable]
    public class LevelData
    {
        public List<BasicInfo> basic_info;
        public List<QuestionItem> question_set;
        public List<LanguageItem> language_set;
        public List<PrexQuestionText> prex_question_text;
    }

    [System.Serializable]
    public class BasicInfo
    {
        public string field;
        public string value;
        public string Resources; // 添加Resources路径字段
    }

    [System.Serializable]
    public class QuestionItem
    {
        public string id;
        public string speak;
        public bool use_suffix; // 添加是否使用后缀的标记
        public string vocabulary_key; // 添加词汇键字段
    }

    [System.Serializable]
    public class LanguageItem
    {
        public string key;
        public string en;
        public string zh_CN;
        public string zh_TW;
    }

    [System.Serializable]
    public class PrexQuestionText
    {
        public string en;
        public string zh_CN;
        public string zh_TW;
        public string sound_name; // 添加音频文件名字段
        public bool is_first_question; // 添加是否是第一个问题的标记
        public bool use_suffix; // 添加是否使用后缀的标记
        public string phrase_key; // 添加短语键字段
    }

    public class AudioQuizManager : MonoBehaviour
    {
        [SerializeField] private GameObject answerChoicePrefab;
        [SerializeField] private Transform questionContainer;
        [SerializeField] private Transform choicesContainer;
        [SerializeField] private AudioSource audioSource;

        [Header("布局设置")]
        [SerializeField] private float choiceSpacing = 3.5f; // 选择项之间的间距
        [SerializeField] private AIMGameFramework.Layout.ArrangementType choiceArrangementType = AIMGameFramework.Layout.ArrangementType.Vertical; // 选择项排列方式
        [SerializeField] private Vector2 choiceContainerSize = new Vector2(10f, 5f); // 选择项容器大小
        [SerializeField] private bool autoAdjustLayout = true; // 是否自动调整布局
        [SerializeField] private bool randomizeChoices = true; // 是否随机排序选择项

        [Header("语言设置")]
        [SerializeField] private string languageCode = "zh_CN"; // 语言代码：zh_CN(简体中文)、zh_TW(繁体中文)、en(英文)

        [Header("词汇库设置")]
        [SerializeField] private string defaultVocabularyLibrary = "vocabulary-library"; // 默认词汇库名称
        [SerializeField] private string defaultVocabularyPath = "Configs"; // 默认词汇库路径

        [Header("调试设置")]
        [SerializeField] private bool debugMode = true; // 调试模式
        [SerializeField] private bool showCorrectAnswer = true; // 显示正确答案

        private LevelData currentLevelData;
        private string currentQuestion;
        private string correctAnswer;
        private List<string> answerChoices = new List<string>();
        private List<QuestionItem> remainingQuestions = new List<QuestionItem>(); // 剩余问题列表

        // 题目显示相关
        private TextMesh questionTextMesh;

        // 游戏完成事件
        public event Action<bool> OnGameCompleted;

        // 游戏状态
        private bool _gameCompleted = false;
        private bool _isProcessingAnswer = false; // 是否正在处理答案
        private bool _isGameOver = false;

        // 获取当前类名，用于日志
        private string _className => $"[{this.GetType().Name}]";

        // 调试文本对象
        private TextMesh debugTextMesh;

        // 保存资源路径
        private Dictionary<string, string> resourcePaths = new Dictionary<string, string>();

        // 音频队列
        private Queue<AudioClip> audioQueue = new Queue<AudioClip>();
        private bool isPlayingAudioQueue = false;

        // 功能按钮
        private GameObject hintButton;
        private GameObject replayButton;

        // 当前语音剪辑
        private List<AudioClip> currentAudioClips = new List<AudioClip>();

        private void OnEnable()
        {
            // 注册选择项点击事件
            AnswerChoiceClickable.OnChoiceClicked += HandleChoiceClicked;
        }

        private void OnDisable()
        {
            // 取消注册选择项点击事件
            AnswerChoiceClickable.OnChoiceClicked -= HandleChoiceClicked;
        }

        private void Start()
        {
            // 如果开启了调试模式，创建调试信息显示
            if (debugMode && showCorrectAnswer)
            {
                CreateDebugInfoDisplay();
            }

            if (!debugMode)
            {
                languageCode = GlobalVariable.GetLanguageCode();
            }
            
            // 初始化词汇管理器的默认词库
            VocabularyManager vocabularyManager = FindObjectOfType<VocabularyManager>();
            if (vocabularyManager != null)
            {
                vocabularyManager.DefaultLibraryName = defaultVocabularyLibrary;
                vocabularyManager.DefaultLibraryPath = defaultVocabularyPath;
                
                // 如果词汇库未加载，尝试加载默认词汇库
                if (!string.IsNullOrEmpty(defaultVocabularyLibrary) && !string.IsNullOrEmpty(defaultVocabularyPath))
                {
                    string fullPath = $"{defaultVocabularyPath}/{defaultVocabularyLibrary}";
                    bool loaded = vocabularyManager.LoadVocabularyLibrary(defaultVocabularyLibrary, fullPath);
                    if (loaded)
                    {
                        Debug.Log($"{_className} 成功加载默认词汇库: {defaultVocabularyLibrary}");
                    }
                }
            }
            else
            {
                Debug.LogWarning($"{_className} 未找到VocabularyManager实例，无法初始化词汇库");
            }

            // 初始化问题显示区域
            InitializeQuestionDisplay();
        }

        /// <summary>
        /// 初始化问题显示区域
        /// </summary>
        private void InitializeQuestionDisplay()
        {
            if (questionContainer == null)
            {
                Debug.LogError($"{_className} questionContainer为空，无法初始化问题显示");
                return;
            }

            // 查找或创建QuestionBar对象
            Transform questionBarTransform = questionContainer.Find("QuestionBar");
            GameObject questionBar;

            if (questionBarTransform == null)
            {
                // 创建QuestionBar对象
                questionBar = new GameObject("QuestionBar");
                questionBar.transform.SetParent(questionContainer);
                questionBar.transform.localPosition = Vector3.zero;
            }
            else
            {
                questionBar = questionBarTransform.gameObject;
            }

            // 添加TextMesh组件
            questionTextMesh = questionBar.GetComponent<TextMesh>();
            if (questionTextMesh == null)
            {
                questionTextMesh = questionBar.AddComponent<TextMesh>();
                questionTextMesh.fontSize = 34;
                questionTextMesh.alignment = TextAlignment.Center;
                questionTextMesh.anchor = TextAnchor.MiddleCenter;
                questionTextMesh.color = Color.black;
                questionTextMesh.characterSize = 0.1f;
            }

            // 设置初始文本
            questionTextMesh.text = "";
        }

        /// <summary>
        /// 创建重播按钮
        /// </summary>

        /// <summary>
        /// 显示提示
        /// </summary>
        public void ShowHint()
        {
            if (_isGameOver || isPlayingAudioQueue) return;

            Debug.Log($"{_className} 显示提示");

            // 播放问题语音
            ReplayQuestionAudio();

            // 找到正确答案对象
            if (!string.IsNullOrEmpty(correctAnswer))
            {
                GameObject choiceObj = GameObject.Find($"Choice_{correctAnswer}");
                if (choiceObj != null)
                {
                    // 获取AnswerChoiceClickable组件
                    AnswerChoiceClickable clickable = choiceObj.GetComponent<AnswerChoiceClickable>();
                    if (clickable != null)
                    {
                        // 播放提示动画（缩放3次）
                        StartCoroutine(PlayHintAnimation(clickable));
                    }
                }
            }
        }

        /// <summary>
        /// 播放提示动画
        /// </summary>
        private IEnumerator PlayHintAnimation(AnswerChoiceClickable clickable)
        {
            // 播放3次缩放动画
            for (int i = 0; i < 3; i++)
            {
                // 使用缩放动画而不是震动动画
                yield return StartCoroutine(clickable.PlayClickAnimation());
                yield return new WaitForSeconds(0.2f);
            }
        }

        /// <summary>
        /// 重播问题音频
        /// </summary>
        public void ReplayQuestionAudio()
        {
            if (_isGameOver || isPlayingAudioQueue) return;

            Debug.Log($"{_className} 重播问题音频");

            // 如果有保存的音频剪辑，直接播放
            if (currentAudioClips.Count > 0)
            {
                // 清空现有队列
                audioQueue.Clear();

                // 将保存的音频添加到队列
                foreach (var clip in currentAudioClips)
                {
                    audioQueue.Enqueue(clip);
                }

                // 开始播放
                StartCoroutine(PlayAudioQueue());
            }
            else if (!string.IsNullOrEmpty(correctAnswer))
            {
                // 如果没有保存的音频，重新加载并播放
                PlayQuestionAudio(correctAnswer);
            }
        }

        /// <summary>
        /// 创建调试信息显示
        /// </summary>
        private void CreateDebugInfoDisplay()
        {
            // 创建一个游戏对象用于显示调试信息
            GameObject debugObj = new GameObject("DebugInfo");
            debugObj.transform.SetParent(transform);
            debugObj.transform.localPosition = new Vector3(0, 2f, 0); // 放在屏幕顶部

            // 添加TextMesh组件
            debugTextMesh = debugObj.AddComponent<TextMesh>();
            debugTextMesh.color = Color.black;
            debugTextMesh.fontSize = 16;
            debugTextMesh.alignment = TextAlignment.Center;
            debugTextMesh.anchor = TextAnchor.MiddleCenter;
            debugTextMesh.characterSize = 0.1f;

            // 默认情况下，只在编辑器中或Debug构建中显示
#if !UNITY_EDITOR
            debugObj.SetActive(Debug.isDebugBuild);
#endif
        }

        /// <summary>
        /// 更新调试信息显示
        /// </summary>
        private void UpdateDebugInfo()
        {
            if (debugTextMesh != null && debugMode && showCorrectAnswer)
            {
                string localizedAnswer = GetLocalizedText(correctAnswer);
                string remaining = remainingQuestions.Count > 0 ? $"剩余问题: {remainingQuestions.Count}" : "全部完成";
                debugTextMesh.text = $"当前问题: {currentQuestion}\n正确答案: {correctAnswer} ({localizedAnswer})\n{remaining}";
            }
        }

        /// <summary>
        /// 处理选择项点击事件
        /// </summary>
        private void HandleChoiceClicked(string choiceText, bool isCorrect)
        {
            // 如果正在处理答案或游戏已结束，忽略点击
            if (_isProcessingAnswer || _isGameOver)
                return;

            // 播放单词语音
            PlayWordAudio(choiceText);

            if (isCorrect)
            {
                Debug.Log($"{_className} 回答正确! 选项: {choiceText}");
                // 标记正在处理答案
                _isProcessingAnswer = true;

                // 正确答案，播放震动效果，然后显示下一题
                StartCoroutine(PlayCorrectAnswerEffect(choiceText));
            }
            else
            {
                Debug.Log($"{_className} 回答错误! 选项: {choiceText}");

                // 错误答案，点击动画已在AnswerChoiceClickable中自动播放
                // 不需要额外处理
            }
        }

        /// <summary>
        /// 播放单词语音
        /// </summary>
        /// <param name="wordId">单词ID</param>
        private void PlayWordAudio(string wordId)
        {
            if (audioSource == null || string.IsNullOrEmpty(wordId))
            {
                Debug.LogError($"{_className} 无法播放单词语音：audioSource为空或wordId为空");
                return;
            }

            try
            {
                // 获取当前语言
                string currentLanguage = languageCode;

                // 首先尝试从词汇库加载音频
                bool playedFromVocabulary = false;
                
                VocabularyManager vocabularyManager = FindObjectOfType<VocabularyManager>();
                if (vocabularyManager != null)
                {
                    // 查找对应的问题项，获取vocabulary_key
                    string vocabularyKey = wordId;
                    foreach (var question in currentLevelData.question_set)
                    {
                        if (question.id == wordId)
                        {
                            vocabularyKey = question.vocabulary_key ?? wordId;
                            break;
                        }
                    }
                    
                    // 从词汇库获取音频路径
                    string audioPath = vocabularyManager.GetVocabularyAudioPath(null, vocabularyKey, currentLanguage);
                    if (!string.IsNullOrEmpty(audioPath))
                    {
                        AudioClip clip = Resources.Load<AudioClip>(audioPath);
                        if (clip != null)
                        {
                            Debug.Log($"{_className} 从词汇库加载单词语音: {audioPath}");
                            audioSource.Stop();
                            audioSource.clip = clip;
                            audioSource.Play();
                            playedFromVocabulary = true;
                        }
                    }
                }
                
                // 如果从词汇库加载失败，使用传统方式
                if (!playedFromVocabulary)
                {
                // 获取语音资源路径
                string soundsPath = GetResourcePath(currentLanguage);
                if (string.IsNullOrEmpty(soundsPath))
                {
                    Debug.LogError($"{_className} 无法获取语音资源路径，当前语言: {currentLanguage}");
                    return;
                }

                // 获取语言后缀
                string languageSuffix = GetLanguageSuffix(currentLanguage);

                // 加载单词语音，添加语言后缀
                string wordSoundPath = Path.Combine(soundsPath, wordId + languageSuffix);
                AudioClip wordClip = Resources.Load<AudioClip>(wordSoundPath);

                if (wordClip != null)
                {
                    Debug.Log($"{_className} 加载单词语音: {wordSoundPath}");
                    // 直接播放，不使用队列
                    audioSource.Stop();
                    audioSource.clip = wordClip;
                    audioSource.Play();
                }
                else
                {
                    Debug.LogWarning($"{_className} 无法加载带后缀的单词语音: {wordSoundPath}，尝试不使用后缀");
                    // 尝试不使用后缀加载
                    wordSoundPath = Path.Combine(soundsPath, wordId);
                    wordClip = Resources.Load<AudioClip>(wordSoundPath);

                    if (wordClip != null)
                    {
                        Debug.Log($"{_className} 成功加载不带后缀的单词语音: {wordSoundPath}");
                        // 直接播放，不使用队列
                        audioSource.Stop();
                        audioSource.clip = wordClip;
                        audioSource.Play();
                    }
                    else
                    {
                        Debug.LogError($"{_className} 无法加载单词语音，所有尝试均失败: {wordId}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"{_className} 播放单词语音时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 播放正确答案效果并进入下一题
        /// </summary>
        private IEnumerator PlayCorrectAnswerEffect(string choiceText)
        {
            if (_isGameOver) yield break;

            // 找到正确答案对象
            GameObject choiceObj = GameObject.Find($"Choice_{choiceText}");
            if (choiceObj != null)
            {
                // 确保CheckMark初始为不激活状态
                Transform checkMark = choiceObj.transform.Find("CheckMark");
                if (checkMark != null)
                {
                    checkMark.gameObject.SetActive(true);
                }

                AIMGameFramework.Effects.StarBurstEffect.Create(choiceObj.transform.position);

                // 获取AnswerChoiceClickable组件
                AnswerChoiceClickable clickable = choiceObj.GetComponent<AnswerChoiceClickable>();
                if (clickable != null)
                {
                    // 播放点击动画
                    yield return StartCoroutine(clickable.PlayShakeAnimation());

                    // 播放正确反馈效果
                    yield return StartCoroutine(clickable.PlayCorrectFeedback());

                    // 等待一小段时间
                    yield return new WaitForSeconds(0.5f);

                    // 显示下一题
                    if (remainingQuestions.Count > 0)
                    {
                        SetupNextQuestion();
                    }
                    else
                    {
                        // 所有问题已回答完毕，游戏完成
                        _gameCompleted = true;
                        Debug.Log($"{_className} 所有问题已回答完毕，游戏完成!");

                        // 确保在主线程中调用事件
                        CompleteGame(true);
                    }
                }
            }

            // 取消标记，允许再次点击
            _isProcessingAnswer = false;
        }

        /// <summary>
        /// 完成游戏并触发事件
        /// </summary>
        /// <param name="success">是否成功完成</param>
        public void CompleteGame(bool success)
        {
            if (!_gameCompleted)
            {
                _gameCompleted = true;
            }

            // 触发游戏完成事件
            if (OnGameCompleted != null)
            {
                Debug.Log($"{_className} 触发游戏完成事件，结果: {success}");
                OnGameCompleted.Invoke(success);
            }
            else
            {
                Debug.LogWarning($"{_className} 游戏完成事件未注册，无法触发");
            }
        }

        /// <summary>
        /// 游戏是否已完成
        /// </summary>
        public bool IsGameCompleted => _gameCompleted;

        /// <summary>
        /// 异步初始化音频问答游戏
        /// </summary>
        /// <param name="levelData">关卡数据</param>
        /// <param name="callback">初始化完成后的回调</param>
        /// <returns></returns>
        public IEnumerator InitializeAsync(JObject levelData, Action<bool> callback = null)
        {
            Debug.Log($"{_className} 开始异步初始化");

                if (levelData == null)
                {
                    Debug.LogError($"{_className} 初始化失败：关卡数据为空");
                    callback?.Invoke(false);
                    yield break;
                }

            // 获取VocabularyManager实例
            VocabularyManager vocabularyManager = FindObjectOfType<VocabularyManager>();
            if (vocabularyManager == null)
            {
                Debug.LogWarning($"{_className} InitializeAsync: VocabularyManager未找到，尝试创建");
                GameObject vocabManagerObj = new GameObject("VocabularyManager");
                vocabularyManager = vocabManagerObj.AddComponent<VocabularyManager>();
                DontDestroyOnLoad(vocabManagerObj);
                Debug.Log($"{_className} InitializeAsync: 已创建VocabularyManager");
            }
            
            // 检查是否有词汇库配置
            string vocabularyLibraryName = null;
            string vocabularyLibraryPath = null;
            
            if (levelData["basic_info"] != null)
            {
                foreach (var info in levelData["basic_info"])
                {
                    if (info["field"]?.ToString() == "vocabulary_library")
                    {
                        vocabularyLibraryName = info["value"]?.ToString();
                        string resourcesPath = info["Resources"]?.ToString();
                        
                        if (!string.IsNullOrEmpty(resourcesPath) && !string.IsNullOrEmpty(vocabularyLibraryName))
                        {
                            vocabularyLibraryPath = $"{resourcesPath}/{vocabularyLibraryName}";
                            Debug.Log($"{_className} InitializeAsync: 从关卡数据中找到词汇库配置: {vocabularyLibraryName}, 路径: {vocabularyLibraryPath}");
                            break;
                        }
                    }
                }
            }
            
            // 如果找不到词汇库配置，使用默认值
            if (string.IsNullOrEmpty(vocabularyLibraryName) || string.IsNullOrEmpty(vocabularyLibraryPath))
            {
                vocabularyLibraryName = defaultVocabularyLibrary;
                vocabularyLibraryPath = defaultVocabularyPath;
                Debug.LogWarning($"{_className} InitializeAsync: 未找到词汇库配置，使用默认值: {vocabularyLibraryName}, 路径: {vocabularyLibraryPath}");
            }
            
            // 如果找到词汇库配置，确保加载词汇库
            if (!string.IsNullOrEmpty(vocabularyLibraryName) && !string.IsNullOrEmpty(vocabularyLibraryPath) && vocabularyManager != null)
            {
                // 设置默认词汇库
                vocabularyManager.DefaultLibraryName = vocabularyLibraryName;
                vocabularyManager.DefaultLibraryPath = vocabularyLibraryPath;
                
                // 异步加载词汇库
                Debug.Log($"{_className} InitializeAsync: 开始加载词汇库: {vocabularyLibraryName}, 路径: {vocabularyLibraryPath}");
                bool vocabularyLoaded = false;
                yield return vocabularyManager.LoadVocabularyLibraryAsync(
                    vocabularyLibraryName, 
                    vocabularyLibraryPath, 
                    success => vocabularyLoaded = success
                );
                
                if (vocabularyLoaded)
                {
                    Debug.Log($"{_className} InitializeAsync: 成功加载词汇库: {vocabularyLibraryName}");
                }
                else
                {
                    Debug.LogWarning($"{_className} InitializeAsync: 无法异步加载词汇库: {vocabularyLibraryName}，尝试直接加载");
                    // 尝试直接加载
                    bool loaded = vocabularyManager.LoadVocabularyLibrary(vocabularyLibraryName, vocabularyLibraryPath);
                    Debug.Log($"{_className} InitializeAsync: 直接加载词汇库结果: {loaded}");
                }
            }
            else
            {
                Debug.LogError($"{_className} InitializeAsync: 无法加载词汇库，名称或路径为空，或VocabularyManager未初始化");
            }

            try
            {
                // 解析JSON数据到LevelData对象
                currentLevelData = ParseLevelData(levelData);

                // 设置游戏对象
                SetupGameObjects();

                // 初始化剩余问题列表
                if (currentLevelData.question_set != null && currentLevelData.question_set.Count > 0)
                {
                    remainingQuestions = new List<QuestionItem>(currentLevelData.question_set);

                    // 随机打乱问题顺序
                    ShuffleList(remainingQuestions);
                }

                // 设置游戏对象
                SetupNextQuestion();

                // 重置游戏状态
                _gameCompleted = false;

                Debug.Log($"{_className} 初始化成功");
                callback?.Invoke(true);
            }
            catch (Exception ex)
            {
                Debug.LogError($"{_className} 初始化时发生错误: {ex.Message}");
                callback?.Invoke(false);
            }
        }

        void SetupGameObjects()
        {
            if (currentLevelData == null) return;

            // 获取场景中的精灵管理器
            SceneSpriteGroupManager spriteManager = FindObjectOfType<SceneSpriteGroupManager>();
            if (spriteManager == null)
            {
                Debug.LogError("[AudioQuizManager] 无法找到SceneSpriteGroupManager组件");
                return;
            }
            else
            {
                // 从当前关卡数据中获取sprites_group信息
                string sprites_group_path = "";

                if (currentLevelData != null && currentLevelData.basic_info != null)
                {
                    foreach (var info in currentLevelData.basic_info)
                    {
                        if (info.field == "sprites_group")
                        {
                            // 获取sprites_group的值
                            string spritesGroupValue = info.value;

                            // 如果有Resources路径，则组合完整路径
                            if (!string.IsNullOrEmpty(info.Resources))
                            {
                                sprites_group_path = System.IO.Path.Combine(info.Resources, spritesGroupValue);

                                // 保存到资源路径字典中
                                resourcePaths["sprites_group"] = sprites_group_path;
                            }
                            else
                            {
                                sprites_group_path = spritesGroupValue;
                                resourcePaths["sprites_group"] = sprites_group_path;
                            }

                            Debug.Log($"[AudioQuizManager] 找到sprites_group路径: {sprites_group_path}");
                            break;
                        }
                    }
                }

                // 如果没有找到路径，使用默认值
                if (string.IsNullOrEmpty(sprites_group_path))
                {
                    sprites_group_path = "Default";
                    resourcePaths["sprites_group"] = sprites_group_path;
                    Debug.LogWarning($"[AudioQuizManager] 未找到sprites_group路径，使用默认值: {sprites_group_path}");
                }

                // 设置精灵管理器使用Resources路径
                List<string> paths = new List<string> { sprites_group_path };
                spriteManager.SetUseResourcesPaths(true, paths);
            }
        }

        /// <summary>
        /// 设置下一个问题
        /// </summary>
        private void SetupNextQuestion()
        {
            if (_isGameOver) return;

            // 清除现有内容
            foreach (Transform child in choicesContainer)
            {
                Destroy(child.gameObject);
            }

            // 从剩余问题中取出一个
            if (remainingQuestions.Count == 0)
            {
                Debug.LogWarning($"{_className} 没有更多问题了");
                return;
            }

            QuestionItem nextQuestion = remainingQuestions[0];
            remainingQuestions.RemoveAt(0);

            // 获取正确答案的本地化文本
            string correctAnswerId = nextQuestion.id;
            string correctAnswerText = GetLocalizedText(correctAnswerId);

            // 设置当前问题和正确答案
            currentQuestion = GetQuestionText() + correctAnswerText; // 例如："请选出苹果"
            correctAnswer = correctAnswerId;

            // 更新问题显示
            if (questionTextMesh != null)
            {
                // 确保显示完整的问题文本，包括前缀和答案
                string questionPrefix = GetQuestionText();
                string answerText = GetLocalizedText(correctAnswerId);

                // 组合完整问题文本
                string fullQuestion = questionPrefix + answerText;
                questionTextMesh.text = fullQuestion;

                // 调整字体大小以适应文本长度
                if (fullQuestion.Length > 20)
                {
                    questionTextMesh.fontSize = 38; // 较长文本使用小一点的字体
                }
                else if (fullQuestion.Length > 15)
                {
                    questionTextMesh.fontSize = 40; // 中等长度文本
                }
                else
                {
                    questionTextMesh.fontSize = 44; // 较短文本使用大一点的字体
                }

                // 输出调试信息
                Debug.Log($"{_className} 问题文本: '{fullQuestion}', 前缀: '{questionPrefix}', 答案: '{answerText}'");
            }

            Debug.Log($"{_className} 设置新问题ID: {correctAnswerId}, 本地化文本: {correctAnswerText}");

            // 更新调试信息显示
            UpdateDebugInfo();

            // 播放语音提示
            PlayQuestionAudio(correctAnswerId);

            // 准备答案选项（1个正确答案，2个错误答案）
            answerChoices.Clear();

            // 添加正确答案
            answerChoices.Add(correctAnswerId);

            // 从currentLevelData.question_set中随机选择2个错误答案
            List<QuestionItem> allQuestions = new List<QuestionItem>(currentLevelData.question_set);

            // 移除正确答案
            allQuestions.RemoveAll(q => q.id == correctAnswerId);

            // 随机打乱剩余选项
            ShuffleList(allQuestions);

            // 只取前两个作为错误选项
            int maxWrongAnswers = Mathf.Min(2, allQuestions.Count);
            for (int i = 0; i < maxWrongAnswers; i++)
            {
                answerChoices.Add(allQuestions[i].id);
            }

            // 如果启用随机排序，打乱答案选项
            if (randomizeChoices)
            {
                ShuffleList(answerChoices);
            }

            int choiceCount = answerChoices.Count;
            if (choiceCount == 0) return;

            // 估算选择项大小
            float choiceSize = LayoutManager.Instance.EstimateObjectSize(answerChoicePrefab);

            // 使用LayoutManager计算选择项位置
            Vector2[] choicePositions = LayoutManager.Instance.CalculateLayout(
                choiceCount,
                choicesContainer.position,
                choiceArrangementType,
                choiceSpacing,
                choiceContainerSize,
                choiceSize,
                autoAdjustLayout
            );

            // 获取场景中的精灵管理器
            SceneSpriteGroupManager spriteManager = FindObjectOfType<SceneSpriteGroupManager>();
            if (spriteManager == null)
            {
                Debug.LogError($"{_className} 无法找到SceneSpriteGroupManager组件");
                return;
            }

            // 创建答案选项
            for (int i = 0; i < answerChoices.Count; i++)
            {
                string choiceId = answerChoices[i];
                bool isCorrect = (choiceId == correctAnswerId);

                GameObject choiceObj = Instantiate(answerChoicePrefab, choicesContainer);
                choiceObj.name = $"Choice_{choiceId}";

                // 设置位置
                if (i < choicePositions.Length)
                {
                    choiceObj.transform.position = choicePositions[i];
                }

                // 设置图片
                GameObject pic = choiceObj.transform.Find("Pic")?.gameObject;
                if (pic != null)
                {
                    SpriteRenderer spriteRenderer = pic.GetComponent<SpriteRenderer>();
                    if (spriteRenderer != null)
                    {
                        // 从精灵管理器获取精灵
                        Sprite sprite = spriteManager.GetSprite("Default", choiceId);
                        if (sprite != null)
                        {
                            spriteRenderer.sprite = sprite;
                        }
                        else
                        {
                            Debug.LogWarning($"{_className} 无法从精灵管理器获取精灵: {choiceId}");
                        }
                    }
                }

                // 添加点击组件
                AnswerChoiceClickable clickable = choiceObj.AddComponent<AnswerChoiceClickable>();
                clickable.choiceText = choiceId;
                clickable.isCorrect = isCorrect;

                Debug.Log($"{_className} 创建选项: {choiceId}, 是否正确: {isCorrect}, 位置: {choiceObj.transform.position}");
            }
        }

        /// <summary>
        /// 播放问题语音
        /// </summary>
        /// <param name="questionId">问题ID</param>
        private void PlayQuestionAudio(string questionId)
        {
            if (audioSource == null)
            {
                Debug.LogError($"{_className} 无法播放语音：audioSource为空");
                return;
            }

            // 清空现有的音频队列和保存的音频剪辑
            audioQueue.Clear();
            currentAudioClips.Clear();
            isPlayingAudioQueue = false;

            try
            {
                // 获取VocabularyManager实例
                VocabularyManager vocabularyManager = FindObjectOfType<VocabularyManager>();
                if (vocabularyManager == null)
                {
                    Debug.LogWarning($"{_className} VocabularyManager未找到，尝试创建");
                    GameObject vocabManagerObj = new GameObject("VocabularyManager");
                    vocabularyManager = vocabManagerObj.AddComponent<VocabularyManager>();
                    
                    // 设置默认词汇库
                    vocabularyManager.DefaultLibraryName = defaultVocabularyLibrary;
                    vocabularyManager.DefaultLibraryPath = defaultVocabularyPath;
                    string fullPath = $"{defaultVocabularyPath}/{defaultVocabularyLibrary}";
                    bool loaded = vocabularyManager.LoadVocabularyLibrary(defaultVocabularyLibrary, fullPath);
                    Debug.Log($"{_className} 创建并加载词汇库: {defaultVocabularyLibrary}, 结果: {loaded}");
                }

                // 获取当前语言
                string currentLanguage = languageCode;

                // 获取问题前缀文本对象
                PrexQuestionText prexText = null;
                if (currentLevelData.prex_question_text != null && currentLevelData.prex_question_text.Count > 0)
                {
                    // 根据当前语言选择合适的前缀文本
                    foreach (var text in currentLevelData.prex_question_text)
                    {
                        if (currentLanguage == "zh_CN" && !string.IsNullOrEmpty(text.zh_CN))
                        {
                            prexText = text;
                            break;
                        }
                        else if (currentLanguage == "zh_TW" && !string.IsNullOrEmpty(text.zh_TW))
                        {
                            prexText = text;
                            break;
                        }
                        else if (currentLanguage == "en" && !string.IsNullOrEmpty(text.en))
                        {
                            prexText = text;
                            break;
                        }
                    }

                    // 如果没有找到对应语言的前缀文本，使用第一个
                    if (prexText == null && currentLevelData.prex_question_text.Count > 0)
                    {
                        prexText = currentLevelData.prex_question_text[0];
                        Debug.LogWarning($"{_className} 未找到当前语言({currentLanguage})的前缀文本，使用第一个");
                    }
                }

                if (prexText != null)
                {
                    // 检查是否是第一个问题
                    bool isFirstQuestion = prexText.is_first_question;

                    // 判断是否需要播放前缀语音
                    bool shouldPlayPrefixAudio = false;

                    // 如果是第一个问题且设置了is_first_question为true，则只在第一题播放前缀
                    // 如果is_first_question为false，则每题都播放前缀
                    if (isFirstQuestion)
                    {
                        // 第一题时播放前缀
                        shouldPlayPrefixAudio = remainingQuestions.Count == currentLevelData.question_set.Count - 1;
                    }
                    else
                    {
                        // 每题都播放前缀
                        shouldPlayPrefixAudio = true;
                    }

                    // 如果需要播放前缀语音
                    if (shouldPlayPrefixAudio)
                    {
                        // 从词汇库获取短语音频
                        string phraseKey = prexText.phrase_key;
                        Debug.Log($"{_className} 尝试从词汇库获取前缀语音，键值为: {phraseKey}");
                        
                        // 如果phrase_key为空，使用默认值"please_select"
                        if (string.IsNullOrEmpty(phraseKey))
                        {
                            phraseKey = "please_select";
                            Debug.LogWarning($"{_className} phrase_key为空，使用默认值: {phraseKey}");
                        }
                        
                        if (vocabularyManager != null)
                        {
                            // 从词汇库获取音频路径
                            string audioPath = vocabularyManager.GetPhraseAudioPath(null, phraseKey, currentLanguage);
                            if (!string.IsNullOrEmpty(audioPath))
                            {
                                Debug.Log($"{_className} 从词汇库获取前缀语音路径: {audioPath}");
                                AudioClip clip = Resources.Load<AudioClip>(audioPath);

                                if (clip != null)
                            {
                                    Debug.Log($"{_className} 成功加载前缀语音: {audioPath}");
                                    audioQueue.Enqueue(clip);
                                    currentAudioClips.Add(clip);
                            }
                            else
                            {
                                    Debug.LogError($"{_className} 无法加载前缀语音: {audioPath}");
                            }
                        }
                            else
                            {
                                Debug.LogError($"{_className} 词汇库中未找到短语 {phraseKey} 的音频路径");
                            }
                        }
                        else
                        {
                            Debug.LogError($"{_className} VocabularyManager未初始化");
                        }
                    }

                    // 从词汇库获取词汇音频
                    string vocabularyKey = null;
                    foreach (var question in currentLevelData.question_set)
                    {
                        if (question.id == questionId)
                    {
                            vocabularyKey = question.vocabulary_key ?? questionId;
                            break;
                        }
                    }
                    
                    // 如果vocabularyKey为空，使用questionId作为默认值
                    if (string.IsNullOrEmpty(vocabularyKey))
                    {
                        vocabularyKey = questionId;
                        Debug.LogWarning($"{_className} vocabulary_key为空，使用questionId作为默认值: {vocabularyKey}");
                    }
                    
                    if (vocabularyManager != null)
                    {
                        Debug.Log($"{_className} 尝试从词汇库获取词汇 {vocabularyKey} 的音频路径");
                        string audioPath = vocabularyManager.GetVocabularyAudioPath(null, vocabularyKey, currentLanguage);
                        if (!string.IsNullOrEmpty(audioPath))
                        {
                            Debug.Log($"{_className} 从词汇库获取词语语音路径: {audioPath}");
                            AudioClip clip = Resources.Load<AudioClip>(audioPath);

                            if (clip != null)
                    {
                                Debug.Log($"{_className} 成功加载词语语音: {audioPath}");
                                audioQueue.Enqueue(clip);
                                currentAudioClips.Add(clip);
                    }
                    else
                    {
                                Debug.LogError($"{_className} 无法加载词语语音: {audioPath}，请确认Resources文件夹中存在此音频文件");
                            }
                        }
                        else
                        {
                            Debug.LogError($"{_className} 词汇库中未找到词汇 {vocabularyKey} 的音频路径，请检查词汇库配置");
                        }
                    }
                    else
                    {
                        Debug.LogError($"{_className} VocabularyManager未初始化，无法加载词汇音频");
                    }
                    }

                    // 开始播放音频队列
                    if (audioQueue.Count > 0)
                    {
                        StartCoroutine(PlayAudioQueue());
                    }
                else
                {
                    Debug.LogWarning($"{_className} 没有音频需要播放");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"{_className} 播放问题语音时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取语言对应的文件后缀
        /// </summary>
        private string GetLanguageSuffix(string language)
        {
            // 英文不使用后缀，中文使用后缀
            switch (language)
            {
                case "en":
                    return ""; // 英文不使用后缀
                case "zh_CN":
                case "zh_TW":
                    return "-ma"; // 中文使用-ma后缀
                default:
                    return "-ma"; // 默认使用-ma后缀
            }
        }

        /// <summary>
        /// 播放音频队列
        /// </summary>
        private IEnumerator PlayAudioQueue()
        {
            if (isPlayingAudioQueue) yield break;

            isPlayingAudioQueue = true;

            while (audioQueue.Count > 0)
            {
                AudioClip clip = audioQueue.Dequeue();

                if (clip != null)
                {
                    audioSource.clip = clip;
                    audioSource.Play();

                    // 等待音频播放完成
                    yield return new WaitForSeconds(clip.length + 0.1f);
                }
            }

            isPlayingAudioQueue = false;
        }

        /// <summary>
        /// 获取指定语言的资源路径
        /// </summary>
        private string GetResourcePath(string language)
        {
            string pathKey = "";

            // 根据语言选择对应的资源路径键
            if (language == "zh_CN")
            {
                pathKey = "sounds_zh_CN";
            }
            else if (language == "zh_TW")
            {
                pathKey = "sounds_zh_TW";
            }
            else if (language == "en")
            {
                pathKey = "sounds_en";
            }
            else
            {
                // 默认使用简体中文
                pathKey = "sounds_zh_CN";
            }

            // 从资源路径字典中获取路径
            if (resourcePaths.TryGetValue(pathKey, out string path))
            {
                return path;
            }

            // 如果字典中没有，尝试从当前关卡数据中获取
            if (currentLevelData != null && currentLevelData.basic_info != null)
            {
                foreach (var info in currentLevelData.basic_info)
                {
                    if (info.field == pathKey)
                    {
                        string resourcePath = "";

                        // 如果有Resources路径，则组合完整路径
                        if (!string.IsNullOrEmpty(info.Resources))
                        {
                            resourcePath = Path.Combine(info.Resources, info.value);
                        }
                        else
                        {
                            resourcePath = info.value;
                        }

                        // 缓存到字典中
                        resourcePaths[pathKey] = resourcePath;

                        return resourcePath;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 将JObject解析为LevelData对象
        /// </summary>
        private LevelData ParseLevelData(JObject data)
        {
            LevelData levelData = new LevelData();

            // 解析basic_info
            if (data["basic_info"] != null)
            {
                levelData.basic_info = new List<BasicInfo>();
                foreach (var item in data["basic_info"])
                {
                    BasicInfo info = new BasicInfo
                    {
                        field = item["field"]?.ToString(),
                        value = item["value"]?.ToString(),
                        Resources = item["Resources"]?.ToString()
                    };

                    // 保存资源路径到字典中
                    if (!string.IsNullOrEmpty(info.field) && !string.IsNullOrEmpty(info.value))
                    {
                        string resourcePath = info.value;

                        // 如果有Resources路径，则组合完整路径
                        if (!string.IsNullOrEmpty(info.Resources))
                        {
                            resourcePath = Path.Combine(info.Resources, info.value);
                        }

                        resourcePaths[info.field] = resourcePath;

                        if (debugMode)
                        {
                            Debug.Log($"{_className} 保存资源路径: {info.field} -> {resourcePath}");
                        }
                    }

                    levelData.basic_info.Add(info);
                }
            }

            // 解析question_set - 问题集合
            if (data["question_set"] != null)
            {
                levelData.question_set = new List<QuestionItem>();
                foreach (var item in data["question_set"])
                {
                    QuestionItem questionItem = new QuestionItem
                    {
                        id = item["id"]?.ToString(),
                        speak = item["speak"]?.ToString(),
                        use_suffix = item["use_suffix"] != null && (bool)item["use_suffix"],
                        vocabulary_key = item["vocabulary_key"]?.ToString()
                    };
                    levelData.question_set.Add(questionItem);
                }
            }

            // 解析language_set - 语言翻译
            if (data["language_set"] != null)
            {
                levelData.language_set = new List<LanguageItem>();
                foreach (var item in data["language_set"])
                {
                    LanguageItem langItem = new LanguageItem
                    {
                        key = item["key"]?.ToString(),
                        en = item["en"]?.ToString(),
                        zh_CN = item["zh_CN"]?.ToString(),
                        zh_TW = item["zh_TW"]?.ToString()
                    };
                    levelData.language_set.Add(langItem);
                }
            }

            // 解析prex_question_text - 问题前缀文本
            if (data["prex_question_text"] != null)
            {
                levelData.prex_question_text = new List<PrexQuestionText>();
                foreach (var item in data["prex_question_text"])
                {
                    PrexQuestionText text = new PrexQuestionText
                    {
                        en = item["en"]?.ToString(),
                        zh_CN = item["zh_CN"]?.ToString(),
                        zh_TW = item["zh_TW"]?.ToString(),
                        sound_name = item["sound_name"]?.ToString(),
                        is_first_question = item["is_first_question"] != null && (bool)item["is_first_question"],
                        use_suffix = item["use_suffix"] != null && (bool)item["use_suffix"],
                        phrase_key = item["phrase_key"]?.ToString()
                    };
                    levelData.prex_question_text.Add(text);
                }
            }

            return levelData;
        }

        /// <summary>
        /// 重置游戏状态
        /// </summary>
        public void Reset()
        {
            // 清除所有游戏对象
            foreach (Transform child in questionContainer)
            {
                if (child.name != "QuestionBar") // 保留QuestionBar
                {
                    Destroy(child.gameObject);
                }
            }

            foreach (Transform child in choicesContainer)
            {
                Destroy(child.gameObject);
            }

            // 重置游戏状态
            _gameCompleted = false;
            _isProcessingAnswer = false;
            _isGameOver = false;

            // 清空当前问题和答案
            currentQuestion = null;
            correctAnswer = null;
            answerChoices.Clear();

            // 重置问题显示
            if (questionTextMesh != null)
            {
                questionTextMesh.text = "Ready ...";
            }

            // 重新初始化剩余问题列表
            if (currentLevelData != null && currentLevelData.question_set != null)
            {
                remainingQuestions = new List<QuestionItem>(currentLevelData.question_set);
                ShuffleList(remainingQuestions);
                Debug.Log($"{_className} 重置游戏状态，重新加载所有题目，题目数量: {remainingQuestions.Count}");
            }
        }

        /// <summary>
        /// 获取问题的前缀文本（例如"请选择"）
        /// </summary>
        private string GetQuestionText()
        {
            try
            {
                // 检查是否存在prex_question_text数据
                if (currentLevelData == null || currentLevelData.prex_question_text == null || currentLevelData.prex_question_text.Count == 0)
                {
                    Debug.LogWarning($"{_className} 未找到问题前缀文本");
                    return ""; // 如果没有配置，返回空字符串
                }

                // 获取当前设置的语言
                string currentLanguage = languageCode;
                string result = "";

                // 首先尝试从VocabularyManager获取短语文本
                string phraseKey = null;
                
                // 查找phrase_key
                foreach (var prexText in currentLevelData.prex_question_text)
                {
                    if (!string.IsNullOrEmpty(prexText.phrase_key))
                    {
                        phraseKey = prexText.phrase_key;
                        break;
                    }
                }
                
                // 如果找到phrase_key，从词汇库获取文本
                VocabularyManager vocabularyManager = FindObjectOfType<VocabularyManager>();
                if (!string.IsNullOrEmpty(phraseKey) && vocabularyManager != null)
                {
                    result = vocabularyManager.GetPhraseText(null, phraseKey, currentLanguage);
                    
                    // 如果成功获取到文本（结果不等于键名），直接返回
                    if (result != phraseKey)
                    {
                        // 确保结尾有空格，但只在结果不为空时添加
                        if (!string.IsNullOrEmpty(result) && !result.EndsWith(" "))
                        {
                            result += " ";
                        }
                        return result;
                    }
                }
                
                // 如果从词汇库获取失败，使用传统方式
                // 遍历所有前缀文本，查找与当前语言匹配的项
                foreach (var prexText in currentLevelData.prex_question_text)
                {
                    if (currentLanguage == "zh_CN" && !string.IsNullOrEmpty(prexText.zh_CN))
                    {
                        result = prexText.zh_CN;
                        break;
                    }
                    else if (currentLanguage == "zh_TW" && !string.IsNullOrEmpty(prexText.zh_TW))
                    {
                        result = prexText.zh_TW;
                        break;
                    }
                    else if (currentLanguage == "en" && !string.IsNullOrEmpty(prexText.en))
                    {
                        result = prexText.en;
                        break;
                    }
                }

                // 确保结尾有空格，但只在结果不为空时添加
                if (!string.IsNullOrEmpty(result) && !result.EndsWith(" "))
                {
                    result += " ";
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.LogError($"{_className} 获取问题前缀文本时发生错误: {ex.Message}");
                return ""; // 发生错误时返回空字符串
            }
        }

        /// <summary>
        /// 根据ID获取本地化文本
        /// </summary>
        private string GetLocalizedText(string id)
        {
            // 首先尝试从VocabularyManager获取本地化文本
            VocabularyManager vocabularyManager = FindObjectOfType<VocabularyManager>();
            if (vocabularyManager != null)
            {
                // 查找对应的问题项，获取vocabulary_key
                string vocabularyKey = id;
                foreach (var question in currentLevelData.question_set)
                {
                    if (question.id == id)
                    {
                        vocabularyKey = question.vocabulary_key ?? id;
                        break;
                    }
                }
                
                // 获取当前设置的语言
                string currentLanguage = languageCode;
                
                // 从词汇库获取文本
                string text = vocabularyManager.GetVocabularyText(null, vocabularyKey, currentLanguage);
                if (text != vocabularyKey) // 如果获取成功（返回值不等于键名）
                {
                    return text;
                }
            }
            
            // 如果从VocabularyManager获取失败，使用传统方式
            if (currentLevelData.language_set != null)
            {
                foreach (var langItem in currentLevelData.language_set)
                {
                    if (langItem.key == id)
                    {
                        // 获取当前设置的语言
                        string currentLanguage = languageCode;

                        // 根据语言选择合适的文本
                        if (currentLanguage == "zh_CN")
                        {
                            return langItem.zh_CN;
                        }
                        else if (currentLanguage == "zh_TW")
                        {
                            return langItem.zh_TW;
                        }
                        else if (currentLanguage == "en")
                        {
                            return langItem.en;
                        }
                        else
                        {
                            // 默认使用简体中文
                            return langItem.zh_CN;
                        }
                    }
                }
            }

            return id; // 如果没有找到本地化文本，返回原始ID
        }

        /// <summary>
        /// 随机打乱列表
        /// </summary>
        private void ShuffleList<T>(List<T> list)
        {
            int n = list.Count;
            for (int i = 0; i < n; i++)
            {
                int r = i + UnityEngine.Random.Range(0, n - i);
                T temp = list[i];
                list[i] = list[r];
                list[r] = temp;
            }
        }

        public void StopGame()
        {
            _isGameOver = true;
            _gameCompleted = true;
            StopAllCoroutines();
        }
    }
}


