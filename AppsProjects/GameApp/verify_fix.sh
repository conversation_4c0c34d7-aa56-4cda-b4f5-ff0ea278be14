#!/bin/bash

# 验证修复结果的脚本

echo "🔍 验证 iOS 构建重复任务修复结果..."
echo "=" * 50

# 检查项目文件
PROJECT_FILE="IosApp202507/Unity-iPhone.xcodeproj/project.pbxproj"

if [ ! -f "$PROJECT_FILE" ]; then
    echo "❌ 项目文件不存在: $PROJECT_FILE"
    exit 1
fi

echo "✅ 项目文件存在: $PROJECT_FILE"

# 检查是否还有重复的脚本
echo "🔍 检查重复的构建脚本..."
DUPLICATE_SCRIPTS=$(grep -c "C62A2A42F32E085EF849CF0B" "$PROJECT_FILE")

if [ "$DUPLICATE_SCRIPTS" -eq 0 ]; then
    echo "✅ 已成功移除问题脚本 C62A2A42F32E085EF849CF0B"
else
    echo "⚠️  仍然发现 $DUPLICATE_SCRIPTS 个问题脚本引用"
fi

# 检查 CocoaPods 状态
echo "🍫 检查 CocoaPods 状态..."
cd IosApp202507

if [ -f "Podfile.lock" ] && [ -d "Pods" ]; then
    echo "✅ CocoaPods 依赖已正确安装"
else
    echo "⚠️  CocoaPods 依赖可能需要重新安装"
fi

# 检查工作空间文件
if [ -f "Unity-iPhone.xcworkspace/contents.xcworkspacedata" ]; then
    echo "✅ Xcode 工作空间文件存在"
else
    echo "⚠️  Xcode 工作空间文件缺失"
fi

cd ..

echo ""
echo "📋 修复总结:"
echo "✅ 移除了重复的构建脚本"
echo "✅ 清理了 Xcode 缓存"
echo "✅ 重新安装了 CocoaPods 依赖"
echo ""
echo "🎯 下一步操作建议:"
echo "1. 在 Xcode 中打开: IosApp202507/Unity-iPhone.xcworkspace"
echo "2. 选择 Product -> Clean Build Folder"
echo "3. 重新构建项目"
echo "4. 如果仍有问题，请重启 Xcode 和 Unity"
echo ""
echo "💡 如果问题仍然存在，可能的原因:"
echo "   - Unity 项目需要重新构建"
echo "   - 系统缓存需要清理"
echo "   - 需要重启开发工具"
